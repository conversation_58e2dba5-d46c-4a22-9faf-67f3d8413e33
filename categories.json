[{"name": "Ignored tests", "messageRegex": ".*ignored.*", "matchedStatuses": ["skipped"]}, {"name": "Server Error: 500 Response: Internal Server Error", "messageRegex": ".*Server Error: 500 Response.*", "matchedStatuses": ["failed"]}, {"name": "ConnectionError", "messageRegex": ".*Remote end closed connection without response.*", "matchedStatuses": ["failed", "broken"]}, {"name": "Function Not Found", "messageRegex": ".*KeyError: 'Function ID'.*", "matchedStatuses": ["failed", "broken"]}, {"name": "Wrong Metrics", "messageRegex": ".*ValueError: invalid literal for int() with base 10: '—'.*", "matchedStatuses": ["failed", "broken"]}, {"name": "Element Not Unique", "messageRegex": ".*Locator.text_content: Error: strict mode violation.*", "matchedStatuses": ["failed", "broken"]}, {"name": "Locator Timeout", "messageRegex": ".*playwright._impl._errors.TimeoutError: Locator.*", "matchedStatuses": ["failed", "broken"]}, {"name": "Deployment Failure", "messageRegex": ".*AssertionError: Deploying failed.*", "matchedStatuses": ["failed", "broken"]}, {"name": "Locator Not Visible", "messageRegex": ".*Locator expected to be visible Actual value: <element(s) not found>.*", "matchedStatuses": ["failed", "broken"]}, {"name": "Locator Disabled", "messageRegex": ".*Locator expected to be disabled E Actual value: <element(s) not found>.*", "matchedStatuses": ["failed", "broken"]}, {"name": "Non-GFN backends no Capacity", "messageRegex": ".*pod terminated due to state instance-terminated-no-capacity.*", "matchedStatuses": ["failed", "broken"]}, {"name": "Task Not Completed", "messageRegex": ".*AssertionError: The task .* is not Completed as expected.*", "matchedStatuses": ["failed", "broken"]}]