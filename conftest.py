import logging
import os
from base64 import b64encode
from pathlib import Path
from typing import Dict
from datetime import datetime
from copy import deepcopy
from bs4 import BeautifulSoup, Comment

import pytest
from cloudia.utils.tools import Tools
from cloudia.utils.file_handler import file_handler
from cloudia.ui.sign_page import SignPage
from cloudia.utils.backend_service.nvcf.nvcf_const import NVCF_DATA
from playwright.sync_api import Browser, BrowserContext, <PERSON>, sync_playwright
from playwright.sync_api import TimeoutError as PlaywrightTimeoutError
from cloudia.utils.tools import merge_dict
from cloudia.utils.vault_service.vault_client import CredentialType
from cloudia.utils.account.test_account import BaseAccountManager
from cloudia.api.http.api_session import APISession

from config.consts import (
    DEFAULT_PERMISSIONS,
    DEFAULT_VIEWPORT_SIZE,
    GLOBAL_TIMEOUT,
    MARKERS_DIR,
    CURRENT_ENV,
    API_KEY_LIST,
    EMAIL_LIST,
    CURRENT_ORG,
    LOCAL_CONFIG,
    SSA_HOST_LIST,
    CURRENT_ORG_SECONDARY,
)

from config.scope_consts import (
    NVCT_SCOPE_ALL,
    NVCT_ADMIN_SCOPE_ALL,
    TELEMETRY_ADMIN_SCOPE_ALL,
    BYOO_KRATOS_THANOS_SCOPE_ALL,
)

from utils import update_marker
from utils.common.tools import get_cases_by_status, send_ci_report
from utils.debug_helper import DebugHelper
from utils.test_utils import merge_config
from cloudia.utils.account.test_account import VaultAccountSelector
from cloudia.api.utils.ssa_token_utils import SSAToken

logging.getLogger("asyncio").setLevel(logging.WARNING)
logger = logging.getLogger(__name__)

CURRENT_DIR = Path(__file__).parent.resolve()
LOG_DIR = CURRENT_DIR / Path("log")

UPDATE_MARKER = True  # Hard code this part is always update marker first


def retrieve_api_key(user_role):
    if CURRENT_ENV == "staging" and user_role == "nvcf_gmail_owner":
        account = BaseAccountManager(
            user_role=user_role,
            user_org=CURRENT_ORG_SECONDARY,
            service_type=CURRENT_ENV,
            credential_type=CredentialType.API_KEY,
        )
    else:
        account = BaseAccountManager(
            user_role=user_role,
            user_org=CURRENT_ORG,
            service_type=CURRENT_ENV,
            credential_type=CredentialType.API_KEY,
        )
    return account.appkey


def retrieve_email_account(user_role):
    if CURRENT_ENV == "staging" and user_role == "nvcf_gmail_owner":
        account = BaseAccountManager(
            user_role=user_role,
            user_org=CURRENT_ORG_SECONDARY,
            service_type=CURRENT_ENV,
            credential_type=CredentialType.EMAIL,
        )
    else:
        account = BaseAccountManager(
            user_role=user_role,
            user_org=CURRENT_ORG,
            service_type=CURRENT_ENV,
            credential_type=CredentialType.EMAIL,
        )
    return account.email


def retrieve_email_password(user_role):
    if CURRENT_ENV == "staging" and user_role == "nvcf_gmail_owner":
        account = BaseAccountManager(
            user_role=user_role,
            user_org=CURRENT_ORG_SECONDARY,
            service_type=CURRENT_ENV,
            credential_type=CredentialType.EMAIL,
        )
    else:
        account = BaseAccountManager(
            user_role=user_role,
            user_org=CURRENT_ORG,
            service_type=CURRENT_ENV,
            credential_type=CredentialType.EMAIL,
        )
    return account.password


def get_api_key_dict():
    key_dict = {}
    for key in API_KEY_LIST:
        key_dict[key] = {"api_key": retrieve_api_key(key)}
    return key_dict


def get_email_dict():
    key_dict = {}
    for key in EMAIL_LIST:
        key_dict[key] = {
            "email": retrieve_email_account(key),
            "password": retrieve_email_password(key),
        }
    return key_dict


def get_ssa_host_dict():
    key_dict = {}
    for key in SSA_HOST_LIST:
        key_dict[key] = {"authorization": retrieve_ssa_host(key)}
    return key_dict


def retrieve_ssa_host(ssa_client_name):
    account_list = VaultAccountSelector().query_accounts_by_condition(
        {
            "env": CURRENT_ENV,
            "ssa_service_id": "1",
            "ssa_client_name": ssa_client_name,
            "credential_type": CredentialType.SSA_CLIENT,
        }
    )
    return account_list[0]._ssa_client_secret


def generate_running_config():
    return merge_dict(
        LOCAL_CONFIG, get_api_key_dict(), get_email_dict(), get_ssa_host_dict()
    )


@pytest.fixture(scope="session", autouse=True)
def running_config():
    return generate_running_config()


@pytest.fixture(scope="session", autouse=True)
def session_nvcf_admin_sak(running_config):
    return {
        "type": "sak",
        "nvcf": APISession(
            api_host=running_config["api_host_nvcf"],
            api_key=running_config["nvcf_admin"]["api_key"],
        ),
        "ngc": APISession(
            api_host=running_config["api_host_ngc"],
            api_key=running_config["nvcf_admin"]["api_key"],
        ),
        "ngc_org": APISession(
            api_host=running_config["api_host_ngc_org"],
            api_key=running_config["nvcf_admin"]["api_key"],
        ),
        "ngc_gpus": APISession(
            api_host=running_config["api_host_gpus"],
            api_key=running_config["nvcf_admin"]["api_key"],
        ),
    }


@pytest.fixture(scope="function")
def session_nvct_ssa_allscope(running_config):
    ssa_token = SSAToken(running_config["nvct_ssa_info"], NVCT_SCOPE_ALL).ssa_token
    return {
        "type": "ssa",
        "nvct": APISession(api_host=running_config["api_host_nvct"], api_key=ssa_token),
    }


@pytest.fixture(scope="function")
def session_nvct_ssa_admin_allscope(running_config):
    ssa_token = SSAToken(running_config["nvct_ssa_info"], NVCT_ADMIN_SCOPE_ALL).ssa_token
    return {
        "type": "ssa",
        "nvct": APISession(
            api_host=running_config["api_host_nvct_admin"], api_key=ssa_token
        ),
    }


@pytest.fixture(scope="function")
def session_service_key_ssa_admin_allscope(running_config):
    ssa_token = SSAToken(
        running_config["telemetry_ssa_info"], TELEMETRY_ADMIN_SCOPE_ALL
    ).ssa_token
    return {
        "type": "ssa",
        "ngc": APISession(api_host=running_config["api_host_nvcf"], api_key=ssa_token),
    }


@pytest.fixture(scope="function")
def session_gmail_org_nvcf_admin(running_config):
    return {
        "type": "sak",
        "ngc": APISession(
            api_host=running_config["api_host_ngc"],
            api_key=running_config["nvcf_gmail_owner"]["api_key"],
        ),
        "nvcf": APISession(
            api_host=running_config["api_host_nvcf"],
            api_key=running_config["nvcf_gmail_owner"]["api_key"],
        ),
        "ngc_org": APISession(
            api_host=running_config["api_host_ngc_org"],
            api_key=running_config["nvcf_gmail_owner"]["api_key"],
        ),
    }


def pytest_configure(config: pytest.Config):
    if UPDATE_MARKER and os.name != "nt":
        update_marker.update_markers_file(update_marker.list_templates())

    MARKERS_DICT = file_handler(MARKERS_DIR).read()
    for markes, markers_desc in MARKERS_DICT.items():
        config.addinivalue_line("markers", f"{markes}: {markers_desc}")


def pytest_addoption(parser: pytest.Parser):
    parser.addoption(
        "--extra-capture",
        action="store_true",
        dest="CAP_DEBUG",
        help="The total switch of extra captures. Such as har and video while browser running.",
    )
    parser.addoption(
        "--disable-extra-video",
        action="store_false",
        dest="CAP_VIDEO",
        help="The capture video while browser running is enabled when --extra-capture is added. If add --disable-extra-video, will disable the video capture.",
    )
    parser.addoption(
        "--disable-extra-har",
        action="store_false",
        dest="CAP_HAR",
        help="The capture har while browser running is enabled when --extra-capture is added. If add --disable-extra-har, will disable the har capture.",
    )
    parser.addoption("--gpu", action="store", default=os.environ.get("GPU"), type=str)
    parser.addoption(
        "--backend",
        action="store",
        default=os.environ.get("BACKEND"),
        type=str,
    )
    parser.addoption(
        "--instanceType",
        action="store",
        default=os.environ.get("INSTANCE_TYPE"),
        type=str,
    )
    parser.addoption(
        "--minInstances",
        action="store",
        default=os.environ.get("MIN_INSTANCES"),
        type=int,
    )
    parser.addoption(
        "--maxInstances",
        action="store",
        default=os.environ.get("MAX_INSTANCES"),
        type=int,
    )
    parser.addoption(
        "--maxConcurrency",
        action="store",
        default=os.environ.get("MAX_CONCURRENCY"),
        type=int,
    )
    parser.addoption(
        "--clusters",
        action="store",
        default=os.environ.get("CLUSTERS"),
        type=str,
    )
    parser.addoption(
        "--func_name",
        action="store",
        default=os.environ.get("FUNC_NAME"),
        type=str,
    )
    parser.addoption(
        "--func_id",
        action="store",
        default=os.environ.get("FUNC_ID"),
        type=str,
    )
    parser.addoption(
        "--marker",
        action="store",
        default=os.environ.get("MARKER"),
        type=str,
        help="Run tests with the specified marker",
    )
    parser.addoption(
        "--api_key",
        action="store",
        default=os.environ.get("API_KEY"),
        type=str,
    )


# def pytest_collection_modifyitems(config, items):
#     marker = config.getoption("--marker")
#     if marker:
#         selected_items = []
#         deselected_items = []
#         for item in items:
#             if any(m.name == marker for m in item.iter_markers()):
#                 selected_items.append(item)
#             else:
#                 deselected_items.append(item)
#         config.hook.pytest_deselected(items=deselected_items)
#         items[:] = selected_items


def pytest_collection_modifyitems(config, items):
    markers = config.getoption("--marker")
    if markers:
        markers = markers.split(",")
        selected_items = []
        deselected_items = []
        for item in items:
            if any(marker in item.keywords for marker in markers):
                selected_items.append(item)
            else:
                deselected_items.append(item)
        config.hook.pytest_deselected(items=deselected_items)
        items[:] = selected_items


def pytest_runtest_teardown(item: pytest.Item):
    if item.rep_setup.passed and item.rep_call.passed:
        if "tmpdir" in item.funcargs:
            tmpdir = item.funcargs["tmpdir"]  # retrieve tmpdir
            logging.debug(f"[Teardown] Delete the temporary path: {tmpdir}")
            if tmpdir.check():
                tmpdir.remove()


def pytest_runtest_setup(item: pytest.Item):
    name = item.name
    logging.info(f"The case name: {name}")
    markers = list()
    for mark in item.iter_markers():
        if mark.name == "parametrize":
            # print(f"Skipped: {mark}")
            continue
        # print(f"Appended: {mark}")
        markers.append(mark)
    logger.info(f"The case markers: {markers}")


@pytest.fixture(scope="function")
def browser_context_args(
    browser_context_args: Dict, request: pytest.FixtureRequest
) -> Dict:
    default_context_config = {
        **browser_context_args,
        "viewport": DEFAULT_VIEWPORT_SIZE,
        "permissions": DEFAULT_PERMISSIONS,
    }

    user_role = None
    logger.debug(f"Node markers: {[marker.name for marker in request.node.iter_markers()]}")
    for marker in request.node.iter_markers("parametrize"):
        if marker.args and marker.args[0] == "get_user_page":
            user_role = marker.args[1][0]  # Get the first parameter value
            break

    if user_role is not None and os.path.exists(f"./{user_role}.json"):
        default_context_config["storage_state"] = f"./{user_role}.json"

    default_context_config = DebugHelper.generate_debug_config(
        default_context_config, request, LOG_DIR
    )
    logger.debug(f"[Setup] Browser Conetext args: {default_context_config}")
    return default_context_config


@pytest.fixture(scope="session")
def conf(running_config):
    merged_config = merge_config(running_config)
    return merged_config


@pytest.fixture(scope="function")
def context(browser: Browser, browser_context_args: Dict):
    context = browser.new_context(**browser_context_args)
    yield context
    context.close()


@pytest.fixture(scope="class")
def org_context(browser: Browser, browser_context_args: Dict):
    context = browser.new_context(**browser_context_args)
    yield context
    context.close()


@pytest.fixture(scope="class")
def team_context(browser: Browser, browser_context_args: Dict):
    context = browser.new_context(**browser_context_args)
    yield context
    context.close()


@pytest.fixture(scope="class")
def org_team_context(browser: Browser, browser_context_args: Dict):
    context = browser.new_context(**browser_context_args)
    yield context
    context.close()


@pytest.fixture(scope="function")
def page(context: BrowserContext, conf):
    page = context.new_page()
    yield page
    page.close()


@pytest.fixture(scope="class")
def org_page(org_context: BrowserContext):
    page = org_context.new_page()
    yield page
    page.close()


@pytest.fixture(scope="class")
def team_page(team_context: BrowserContext):
    page = team_context.new_page()
    yield page
    page.close()


@pytest.fixture(scope="class")
def org_team_page(org_team_context: BrowserContext):
    page = org_team_context.new_page()
    yield page
    page.close()


def get_environment_info(login_info: dict) -> dict:
    env_info = {
        "environment": "unknown",
        "user_role": "unknown",
    }

    base_url = login_info.get("base_url", "")
    if "nvcf.ngc.nvidia.com" in base_url:
        env_info["environment"] = "production"
    elif "nvcf.stg.ngc.nvidia.com" in base_url:
        env_info["environment"] = "staging"
    elif "nvcf.canary.ngc.nvidia.com" in base_url:
        env_info["environment"] = "canary"

    email = login_info.get("email", "").lower()
    if email:
        if "nvcfadmin" in email:
            env_info["user_role"] = "admin"
        elif "nvcfviewer" in email:
            env_info["user_role"] = "viewer"
        elif "nvcfadmin+pruser1" in email:
            env_info["user_role"] = "nvcf_admin_pr_user"

    return env_info


def login_with_page(page: Page, login_info):
    try:
        page.set_default_timeout(GLOBAL_TIMEOUT)
        setattr(page, "base_url", login_info["base_url"])
        SignPage(page=page, **login_info).login()
        page.wait_for_load_state(state="load")
    except Exception as e:
        logger.error(f"Login failed: {str(e)}")


@pytest.fixture(scope="function")
def guest_page(page: Page, conf: Dict):
    try:
        page.set_default_timeout(GLOBAL_TIMEOUT)
        page.goto(conf["guest"]["base_url"], wait_until="load")
    except PlaywrightTimeoutError as e:
        logger.error(f"Failed to open ngc guest page: {e.message}")
        pytest.fail(f"Fail to open ngc guest page: {e.message}")
    yield page
    page.close()


@pytest.fixture(scope="function")
def user01_page(page: Page, conf: Dict):
    login_with_page(page, conf["user01"])
    yield page
    page.close()


@pytest.fixture(scope="class")
def team_context_page(team_page: Page, conf: Dict):
    login_with_page(team_page, conf["team_user"])
    yield team_page
    team_page.close()


@pytest.fixture(scope="class")
def org_context_page(org_page: Page, conf: Dict):
    login_with_page(org_page, conf["org_user"])
    yield org_page
    org_page.close()


@pytest.fixture(scope="class")
def org_team_context_page(org_team_page: Page, conf: Dict):
    login_with_page(org_team_page, conf["org_team_user"])
    yield org_team_page
    org_team_page.close()


@pytest.fixture(scope="function")
def user_fc_page(page: Page, conf: Dict):
    login_with_page(page, conf["user_fc"])
    yield page
    page.close()


def check_test_status(request, page=None):
    # exact the marker that starts "T" and others are numbers
    marker = [
        marker
        for marker in request.node.iter_markers()
        if marker.name.startswith("T") and marker.name[1:].isdigit()
    ]
    template = marker[0].name
    node_name = request.node.name

    if hasattr(request.node, "rep_call"):
        if request.node.rep_call.passed:
            logging.info("Test PASSED.")
        else:
            logging.info("Test FAILED: Debug in real time")
            if page is None:
                page = request.node.funcargs["page"]
            dump_page_content(template, node_name, page)
            take_screenshot(template, node_name, page)


def dump_page_content(template: str, node_name: str, page: Page):
    if page.is_closed():
        return

    # Create reports/dump/templateId directory if it doesn't exist
    dump_dir = os.path.join("reports", "dump", template)
    os.makedirs(dump_dir, exist_ok=True)

    html_content = page.content()
    # Remove meta and script tags from HTML content
    soup = BeautifulSoup(html_content, "html.parser")
    for tag in soup.find_all(["meta", "script", "style", "link"]):
        tag.decompose()
    # Remove comments <!-- > from HTML content
    for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
        comment.extract()
    cleaned_html = soup.prettify()

    file_name = f"{node_name}_page_dump.html"
    file_path = os.path.join(dump_dir, file_name)
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(cleaned_html)
    logging.info(f"Page content saved to {file_path}")
    return file_path


def take_screenshot(template: str, node_name: str, page: Page):
    try:
        if not page.is_closed():
            # Create reports/dump/templateId directory if it doesn't exist
            dump_dir = os.path.join("reports", "dump", template)
            os.makedirs(dump_dir, exist_ok=True)

            # Save accessibility snapshot
            snapshot = page.accessibility.snapshot()
            aria_file = os.path.join(dump_dir, f"{node_name}_aria.txt")
            with open(aria_file, "w", encoding="utf-8") as f:
                import json

                json.dump(snapshot, f, indent=2)
            logging.info(f"Aria snapshot saved to {aria_file}")

            # Save screenshot
            screenshot_file = os.path.join(dump_dir, f"{node_name}.png")
            page.screenshot(path=screenshot_file)
            logging.info(f"Screen snapshot saved to {screenshot_file}")
    except Exception as e:
        logging.error(f"Failed to get aria snapshot: {e}")


@pytest.fixture(scope="function")
def get_user_page(request: pytest.FixtureRequest, page: Page, conf: Dict):
    user = request.param
    logging.info("login user -- %s", request.param)
    login_with_page(page, conf[user])
    yield page
    check_test_status(request)
    page.close()


@pytest.fixture(scope="function")
def get_multiple_user_pages(
    request, browser: Browser, browser_context_args: Dict, conf: Dict
):
    user_list = request.param
    logging.debug("get_multiple_user_pages receive parameter type -- %s", type(user_list))
    logging.info("login with multiple users -- %s", user_list)
    page_list = []
    for user in user_list:
        context = browser.new_context(**browser_context_args)
        page = context.new_page()
        logging.info("login user info  -- %s", user)
        login_with_page(page, conf[user])
        page_list.append(page)
    yield page_list
    for page in page_list:
        page.close()


@pytest.fixture(scope="function")
def user_qs_page(page: Page, conf: Dict):
    login_with_page(page, conf["user_qs"])
    yield page
    page.close()


@pytest.fixture(scope="function")
def user_org_admin_page(page: Page, conf: Dict):
    login_with_page(page, conf["org_admin"])
    yield page
    page.close()


@pytest.fixture(scope="function")
def user_org_owner_page(page: Page, conf: Dict):
    login_with_page(page, conf["org_owner"])
    yield page
    page.close()


def pytest_terminal_summary(terminalreporter):
    tests_collected = terminalreporter._session.testscollected
    stats = terminalreporter.stats
    passed_cases = [i.head_line for i in stats.get("passed", []) if i.when == "call"]
    failed_cases = get_cases_by_status(stats, "failed")
    error_cases = get_cases_by_status(stats, "error")
    skipped_cases = get_cases_by_status(stats, "skipped")
    results = {
        "Total": tests_collected,
        "PASS": len(passed_cases),
        "FAIL": {"number": len(failed_cases), "cases": failed_cases},
        "ERROR": {"number": len(error_cases), "cases": error_cases},
        "SKIPPED": len(skipped_cases),
    }
    logger.info(results)
    send_ci_report(terminalreporter, results)


@pytest.hookimpl(hookwrapper=True)
def pytest_runtest_makereport(item, call):
    # Access the pytest-html plugin if it's available
    pytest_html = item.config.pluginmanager.getplugin("html")
    outcome = yield
    report = outcome.get_result()
    extras = getattr(report, "extras", [])
    screenshot_dir = Path("reports/screenshots")
    browser_name = item.funcargs.get("browser_name", "null")
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    screenshot_taken = False  # Indicates whether a screenshot was taken

    # Only attempt to take a screenshot if the test did not pass
    if (
        not (hasattr(report, "passed") and report.passed)
        or ("streaming_test" in report.keywords and report.when != "teardown")
        or ("streaming_invoke" in report.keywords and report.when != "teardown")
    ):
        # Ensure the screenshot directory exists
        screenshot_dir.mkdir(exist_ok=True, parents=True)
        screenshot_base = f"{item.nodeid.replace('::', '_').replace('/', '_')}_{browser_name}_{report.when}_{timestamp}"
        screenshot_files = list()

        # Attempt to take a screenshot if the page object is available
        page_entries = ["page", "chrome_page"]
        if any(
            entry in item.funcargs and not item.funcargs[entry].is_closed()
            for entry in page_entries
        ):
            if "page" in item.funcargs:
                page = item.funcargs["page"]
            elif "chrome_page" in item.funcargs:
                page = item.funcargs["chrome_page"]
            logging.info(f"Taking screenshot for {item.nodeid} on page URL: {page.url}")
            screenshot_file = screenshot_dir / f"{screenshot_base}.png"
            try:
                page.screenshot(path=screenshot_file, timeout=10000)  # 10 second timeout
                screenshot_taken = True
                screenshot_files.append(screenshot_file)
            except Exception as e:
                logging.warning(f"Failed to take screenshot for {item.nodeid}: {e}")
                screenshot_taken = False
        # Handle cases where a browser context with multiple pages is involved
        elif "browser" in item.funcargs:
            browser_context_id = 0
            for browser_context in item.funcargs["browser"].contexts:
                page_id = 0
                for page in browser_context.pages:
                    if not page.is_closed():
                        logging.info(
                            f"Taking screenshot for {item.nodeid} on page URL: {page.url} in context {browser_context_id}"
                        )
                        screenshot_file = (
                            screenshot_dir
                            / f"{screenshot_base}_context{browser_context_id}_page{page_id}.png"
                        )
                        try:
                            page.screenshot(
                                path=screenshot_file, timeout=10000
                            )  # 10 second timeout
                            page_id += 1
                            screenshot_taken = True
                            screenshot_files.append(screenshot_file)
                        except Exception as e:
                            logging.warning(
                                f"Failed to take screenshot for {item.nodeid} in context {browser_context_id}: {e}"
                            )
                            page_id += 1
                    browser_context_id += 1

        # Add the screenshot to the HTML report if a screenshot was taken
        if screenshot_taken:
            try:
                # Add the screenshots to the html report
                for screenshot_file in screenshot_files:
                    with open(screenshot_file, "rb") as fileop:
                        base64_string = b64encode(fileop.read()).decode()
                extras.append(pytest_html.extras.png(base64_string, "Screenshots"))
            except Exception as e:
                logging.error(f"Failed to add screenshot to report: {e}")

    if len(extras) > 0:
        if hasattr(report, "extras"):
            report.extras += extras
        else:
            report.extras = extras


@pytest.fixture()
def chrome_page():
    with sync_playwright() as p:
        browser = p.chromium.launch(
            executable_path="/usr/bin/google-chrome", headless=False
        )
        context = browser.new_context(
            viewport=DEFAULT_VIEWPORT_SIZE,
            permissions=DEFAULT_PERMISSIONS,
        )
        page = context.new_page()
        yield page
        page.close()
        context.close()
        browser.close()


@pytest.fixture(scope="session")
def streaming_deploy_info_gfn():
    if CURRENT_ENV == "staging":
        gfn_test_data = Tools.load_case_data(NVCF_DATA, "DEPLOY_FUNCTION_A10G")
    elif CURRENT_ENV == "production":
        gfn_test_data = Tools.load_case_data(NVCF_DATA, "DEPLOY_FUNCTION_L40S")
    else:
        gfn_test_data = {}
    return deepcopy(gfn_test_data)


@pytest.fixture(scope="session")
def streaming_deploy_info_cluster(request, running_config):
    test_data = Tools.load_case_data(NVCF_DATA, "DEPLOY_FUNCTION_STREAMING_CLUSTER")

    gpu = request.config.getoption("--gpu")
    instance_type = request.config.getoption("--instanceType")
    backend = request.config.getoption("--backend")
    clusters = request.config.getoption("--clusters")

    deploy_data = {}
    if gpu is None and instance_type is None and backend is None and clusters is None:
        for item in ["gpu", "instanceType", "backend", "minInstances", "maxInstances"]:
            deploy_data[item] = running_config["streaming_cluster"][item]
    elif gpu is None or instance_type is None:
        raise ValueError("Parameters --gpu and --instanceType must be provided")
    else:
        min_instances = request.config.getoption("--minInstances", 1)
        max_instances = request.config.getoption("--maxInstances", 1)
        max_request_concurrency = request.config.getoption("--maxRequestConcurrency", 1)
        deploy_data["gpu"] = gpu
        deploy_data["instanceType"] = instance_type
        if backend:
            deploy_data["backend"] = backend

        if clusters:
            deploy_data["clusters"] = clusters.split(",")

        if min_instances:
            deploy_data["minInstances"] = min_instances

        if max_instances:
            deploy_data["maxInstances"] = max_instances

        if max_request_concurrency:
            deploy_data["maxRequestConcurrency"] = max_request_concurrency

    test_data["exp"]["description"] = "NVCF Deploy Streaming functions on cluister API"
    test_data["req"]["json"]["deploymentSpecifications"][0] = deploy_data
    return deepcopy(test_data)


@pytest.fixture(scope="session")
def streaming_invoke_function_id(request):
    func_id = request.config.getoption("--func_id")
    return func_id


@pytest.fixture(scope="session")
def streaming_invoke_api_key(request):
    api_key = request.config.getoption("--api_key")
    return api_key


@pytest.fixture(scope="session")
def test_cluster_params(request):
    params = {
        "gpu": request.config.getoption("--gpu"),
        "backend": request.config.getoption("--backend"),
        "instanceType": request.config.getoption("--instanceType"),
        "minInstances": request.config.getoption("--minInstances"),
        "maxInstances": request.config.getoption("--maxInstances"),
        "maxConcurrency": request.config.getoption("--maxConcurrency"),
        "clusters": request.config.getoption("--clusters"),
        "func_name": request.config.getoption("--func_name"),
    }
    return params


@pytest.fixture(scope="session")
def cluster_validation_deploy_info(test_cluster_params):
    test_data = Tools.load_case_data(NVCF_DATA, "DEPLOY_FUNCTION_STREAMING_CLUSTER")

    deploy_data = {}

    deploy_data["gpu"] = test_cluster_params["gpu"]
    deploy_data["instanceType"] = test_cluster_params["instanceType"]

    if test_cluster_params["clusters"]:
        deploy_data["clusters"] = test_cluster_params["clusters"].split(",")

    if test_cluster_params["minInstances"]:
        deploy_data["minInstances"] = test_cluster_params["minInstances"]

    if test_cluster_params["maxInstances"]:
        deploy_data["maxInstances"] = test_cluster_params["maxInstances"]

    if test_cluster_params["maxConcurrency"]:
        deploy_data["maxRequestConcurrency"] = test_cluster_params["maxConcurrency"]

    test_data["exp"]["description"] = "NVCF Deploy Streaming functions on cluister API"
    test_data["req"]["json"]["deploymentSpecifications"][0] = deploy_data
    return deepcopy(test_data)


@pytest.fixture(scope="function")
def kratos_thanos_ssa_token(running_config):
    ssa_token = SSAToken(
        running_config["kratos_thanos_ssa_info"], BYOO_KRATOS_THANOS_SCOPE_ALL
    ).ssa_token
    return ssa_token
