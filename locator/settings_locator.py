class SettingsPageLocators:
    # General Settings Page Elements
    SETTINGS_HEADING = "h2:has-text('Settings')"

    # Clusters Section Elements
    TELEMETRY_ENDPOINTS_HEADING = "text=Telemetry Endpoints"
    SEARCH_TABLE = "[placeholder='Search table']"
    CLUSTERS_TABLE = "table >> nth=0"  # First table on the page

    # View Documentation Link
    VIEW_DOCUMENTATION_LINK = "a:has-text('View Documentation')"

    def CLUSTER_ROW(self, cluster_name):
        return f"tr:has-text('{cluster_name}')"

    CLUSTER_STATUS_CELL = "td >> nth=1"  # Second column is Status
    CLUSTER_ACTIONS_BUTTON = (
        "td:last-child button"  # The three-dot menu button in the last cell
    )
    RESUME_REGISTRATION_OPTION = "text=Resume Registration"

    # Cluster Actions Elements
    ACTIONS_MENU = "[role='menu']"
    DELETE_CONFIRMATION_DIALOG = "div:has-text('Are you sure you want to delete')"
    CANCEL_DELETE_BUTTON = "button:has-text('Cancel')"

    # Telemetry Endpoints Section Elements
    FILTER_BUTTON = "role=button[name='Filter']"
    TELEMETRY_ENDPOINTS_TABLE = "table >> nth=1"  # Second table on the page

    # Filter Menu Elements
    FILTER_MENU_ITEMS = "[role='menuitem']"
    PROVIDER_MENU_ITEM = "role=menuitem[name='Provider']"
    TELEMETRY_TYPE_MENU_ITEM = "role=menuitem[name='Telemetry Type']"
    MENU_ITEM_CHECKBOXES = "[role='menuitemcheckbox']"
    PROVIDER_DATADOG_CHECKBOX = "[role='menuitemcheckbox'] >> nth=0"
    PROVIDER_GRAFANA_CHECKBOX = "[role='menuitemcheckbox'] >> nth=1"
    LOGS_CHECKBOX = "role=menuitemcheckbox[name='Logs']"
    METRICS_CHECKBOX = "role=menuitemcheckbox[name='Metrics']"
    APPLY_FILTER_BUTTON = "role=button[name='Apply Filter']"
    FILTER_MENU = "[role='menu']"

    # Filter Tags
    PROVIDER_FILTER_TAG = "text=Provider:"
    TELEMETRY_TYPE_FILTER_TAG = "text=Telemetry Type:"

    # Table cell indexes for Telemetry Endpoints
    PROVIDER_CELL_INDEX = 1  # The provider column index
    TELEMETRY_TYPE_CELL_INDEX = 2  # The telemetry type column index
    NAME_CELL_INDEX = 0  # The name column index
    CREATED_ON_CELL_INDEX = 4  # The created on column index
