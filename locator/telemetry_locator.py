"""Locators for the Telemetry feature in the Settings page."""


class TelemetryLocator:
    # Telemetry Page Elements
    TELEMETRY_HEADING = "role=heading[name='Telemetry Endpoints']"
    ADD_ENDPOINT_BUTTON = "role=link[name='Add Endpoint']"

    # Add Endpoint Form
    NAME_INPUT = "role=textbox[name='Enter a name']"
    PROVIDER_DROPDOWN = "role=combobox:has-text('Select a provider')"
    GRAFANA_OPTION = "role=option[name='Grafana Cloud']"
    INSTANCE_ID_INPUT = "role=textbox[name='Enter instance ID']"
    ENDPOINT_URL_INPUT = "role=textbox[name='Enter endpoint']"
    KEY_INPUT = "role=textbox[name='Enter key']"
    LOGS_CHECKBOX = "role=checkbox[name='Logs']"
    METRICS_CHECKBOX = "role=checkbox[name='Metrics']"
    PROTOCOL_DROPDOWN = "role=combobox:has-text('Select a communication protocol')"
    HTTP_OPTION = "role=option[name='HTTP']"
    SAVE_BUTTON = "role=button[name='Save Configuration']"

    # Feedback Messages
    CREATING_MESSAGE = "text=Endpoint is being created..."
    SUCCESS_MESSAGE = "text=Endpoint created successfully"

    # Table Elements
    SEARCH_TABLE_INPUT = "data-testid=kui-grid >> role=textbox[name='Search table']"
    ENDPOINT_TABLE = "data-testid=kui-grid"
    ENDPOINT_TABLE_ROWS = "data-testid=kui-grid >> role=row"
    ENDPOINT_TABLE_BODY = "data-testid=kui-grid >> role=rowgroup:not(:first-child)"

    # Filter Elements
    FILTER_BUTTON = "role=button[name='Filter']"
    FILTER_MENU_ITEMS = "[role='menuitem']"
    PROVIDER_MENU_ITEM = "role=menuitem[name='Provider']"
    TELEMETRY_TYPE_MENU_ITEM = "role=menuitem[name='Telemetry Type']"
    MENU_ITEM_CHECKBOXES = "[role='menuitemcheckbox']"
    PROVIDER_DATADOG_CHECKBOX = "[role='menuitemcheckbox'] >> nth=0"
    PROVIDER_GRAFANA_CHECKBOX = "[role='menuitemcheckbox'] >> nth=1"
    LOGS_CHECKBOX_FILTER = "role=menuitemcheckbox[name='Logs']"
    METRICS_CHECKBOX_FILTER = "role=menuitemcheckbox[name='Metrics']"
    APPLY_FILTER_BUTTON = "role=button[name='Apply Filter']"
    FILTER_MENU = "[role='menu']"

    # Filter Tags
    PROVIDER_FILTER_TAG = "text=Provider:"
    TELEMETRY_TYPE_FILTER_TAG = "text=Telemetry Type:"

    @staticmethod
    def get_endpoint_row_locator(name):
        """Returns a locator for a specific endpoint row by name

        Args:
            name: The endpoint name to search for

        Returns:
            str: A locator that can be used to find the specific endpoint row
        """
        # Direct row locator - most reliable
        return f"data-testid=kui-grid >> role=rowgroup:not(:first-child) >> role=row:has-text('{name}')"

    @staticmethod
    def get_endpoint_cell_locator(name):
        """Returns a locator for a specific endpoint cell by name

        Args:
            name: The endpoint name to search for

        Returns:
            str: A locator that can be used to find the specific endpoint cell
        """
        return f"data-testid=kui-grid >> role=cell:has-text('{name}')"
