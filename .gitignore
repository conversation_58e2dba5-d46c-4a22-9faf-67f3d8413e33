# Automatically generated by `hgimportsvn`
.svn
.hgsvn

# Ignore local virtualenvs
lib/
bin/
include/
.Python/
.env

# These lines are suggested according to the svn:ignore property
# Feel free to enable them by uncommenting them
*.pyc
*.pyo
*.swp
*.class
*.orig
*~
.hypothesis/


# autogenerated
src/_pytest/_version.py
# setuptools
.eggs/

doc/*/_build
doc/*/.doctrees
doc/*/_changelog_towncrier_draft.rst
build/
dist/
*.egg-info
htmlcov/
issue/
env/
.env/
.venv/
/pythonenv*/
3rdparty/
.tox
.cache
.pytest_cache
.mypy_cache
.coverage
.coverage.*
coverage.xml
.ropeproject
.idea
.hypothesis
.pydevproject
.project
.settings
.vscode
__pycache__/

# generated by pip
pip-wheel-metadata/

# pytest debug logs generated via --debug
pytestdebug.log

# execution log dir
log/

# pytest report
reports/

# machine.config
machine.config
/*.config

# cookies
cookies/

# template.py
template.py
report_content.html

# mac tempo files
.DS_Store
