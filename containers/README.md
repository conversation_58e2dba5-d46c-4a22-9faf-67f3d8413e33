# Background
This dodcument is dedicated for cluster validation usage. All test cases are under testcases/cluster/test_cluster.py

## Build a container

1. Prepare the .cloudia.config file
```toml
# .cloudia.config
[Test Envrionment]
# test environment, should be staging, canary or production
type = production

[Account Source]
# data source for retrieve test account, now the only can be  'DataBase' and 'Vault'
type = Vault

# Vault config
[Cloudia Vault]
vault_addr = https://cqax.nvidia.com:6789/
vault_token = <your_vault_token>
mount_path = nvcf_secrets
```

2. Build the container
```bash
export GIT_TOKEN=<your_gitlab_token>    #Required
export CI_COMMIT_REF_NAME="main"
```
```bash
# Make sure .cloudia.config is in the same directory as Dockerfile
sudo docker build --build-arg GIT_TOKEN=$GIT_TOKEN\
       -t test-ui:latest .
```

## Run test in a container

### Prepare test data

1. Prepare the test data for cluster validation
``` bash
export GPU=    # Required
export CLUSTERS=    # Required
export INSTANCE_TYPE=    # Required
export MIN_INSTANCES=1
export MAX_INSTANCES=1
export MAX_CONCURRENCY=32
export FUNC_NAME=cv-auto-ui
```
2. Generate the docker envfile
Optional step: User can use `docker -e` instead of create a file
```bash
cat <<EOF > envfile
GPU=$GPU
CLUSTERS=$CLUSTERS
INSTANCE_TYPE=$INSTANCE_TYPE
MIN_INSTANCES=$MIN_INSTANCES
MAX_INSTANCES=$MAX_INSTANCES
MAX_CONCURRENCY=$MAX_CONCURRENCY
FUNC_NAME=$FUNC_NAME
EOF
```

### Run in a container
Here are the command samples to run testcases in a container.

1. Run all the cluster validation testcases directly.

```bash
export OUTPUT_DIR="."
sudo docker run \
     -v .:/nvcf-ui-test/reports/  \
     --env-file envfile  \
     test-ui:latest
```

2. Run specified test cases use with template id.

```bash
sudo docker run \
     -v .:/nvcf-ui-test/reports/  \
     --env-file envfile  \
     test-ui:latest \
     -m Txxxxxxx
```

3. [Optional] Compatible pytest parmeters samples

```bash
sudo docker run \
     -v .:/nvcf-ui-test/reports/  \
     --env-file envfile \
     test-ui:latest \
     -rerun 2
```

4. Use `docker -e` instead of envfile
```bash
    sudo docker run \
          -v .:/nvcf-ui-test/reports/  \
          -e GPU=$GPU \
          -e INSTANCE_TYPE=$INSTANCE_TYPE \
          -e CLUSTERS=$CLUSTERS \
          -e MIN_INSTANCES=$MIN_INSTANCES\
          -e MAX_INSTANCES=$MAX_INSTANCES \
          -e MAX_CONCURRENCY=$MAX_CONCURRENCY \
          -e FUNC_NAME=$FUNC_NAME \
          <container-image:tag>
```

### Example
Example1
```bash
sudo docker run -v .:/nvcf-ui-test/reports/ --env-file <envfile>  <container-image:tag>
```

Example2
```
sudo docker run \
     -v .:/nvcf-ui-test/reports/  \
     -e GPU=$GPU \
     -e INSTANCE_TYPE=$INSTANCE_TYPE \
     -e CLUSTERS=$CLUSTERS \
     -e MIN_INSTANCES=$MIN_INSTANCES \
     -e MAX_INSTANCES=$MAX_INSTANCES \
     -e MAX_CONCURRENCY=$MAX_CONCURRENCY \
     -e FUNC_NAME=$FUNC_NAME \
     <container-image:tag>
```
