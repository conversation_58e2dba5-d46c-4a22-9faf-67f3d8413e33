ARG BASE_IMAGE=python:3.11
FROM ${BASE_IMAGE}

ARG GIT_TOKEN
ARG CI_COMMIT_REF_NAME=main
ARG NVCF_UI_REPO=https://gitlab-ci-token:${GIT_TOKEN}@gitlab-master.nvidia.com/cloud-service-qa/nvcf/nvcf-ui-test.git
RUN git clone --branch ${CI_COMMIT_REF_NAME} ${NVCF_UI_REPO} --depth=1 /nvcf-ui-test

WORKDIR /nvcf-ui-test/

RUN python3 -m venv .venv
RUN . .venv/bin/activate
RUN pip install poetry
RUN poetry install
RUN poetry run playwright install
RUN poetry run playwright install-deps

RUN echo 'source $(poetry env info --path)/bin/activate && exec "$@"'  >> ~/.bashrc

COPY e2e_ui_entrypoint.sh /nvcf-ui-test/.
COPY .cloudia.config /root/.cloudia.config

# Set the entrypoint to the run script
ENTRYPOINT ["/nvcf-ui-test/e2e_ui_entrypoint.sh"]
