#!/bin/bash

# Activate the Python virtual environment and execute the given command
source "$(poetry env info --path)/bin/activate"

REQUIRED_VARS=("GPU" "CLUSTERS" "INSTANCE_TYPE")
MISSING_VARS=()

for VAR in "${REQUIRED_VARS[@]}"; do
  if [ -z "${!VAR}" ]; then
    MISSING_VARS+=("$VAR")
  fi
done

if [ ${#MISSING_VARS[@]} -ne 0 ]; then
  echo
  echo "Error: The following environment variables must be set:"
  echo "    ${MISSING_VARS[*]}"
  echo
  echo "Usage:Use --env-file or -e prepare your test data "
  echo "Example1"
  echo "    sudo docker run --env-file <envfile> \\"
  echo "                    <container-image:tag> \\"
  echo "                    --html=report.html \\"
  echo "                    -rerun 2"
  echo
  echo "Example2"
  echo "    sudo docker run -e GPU=\$GPU \\"
  echo "                    -e INSTANCE_TYPE=\$INSTANCE_TYPE \\"
  echo "                    -e CLUSTERS=\$CLUSTERS \\"
  echo "                    -e MIN_INSTANCES=\$MIN_INSTANCES\\"
  echo "                    -e MAX_INSTANCES=\$MAX_INSTANCES \\"
  echo "                    -e MAX_CONCURRENCY=\$MAX_CONCURRENCY \\"
  echo "                    -e FUNC_NAME=\$FUNC_NAME \\"
  echo "                    <container-image:tag>"
  echo
  exit 1
fi

# Default values for optional environment variables
MIN_INSTANCES=${MIN_INSTANCES:=1}
MAX_INSTANCES=${MAX_INSTANCES:=1}
MAX_CONCURRENCY=${MAX_CONCURRENCY:=32}
FUNC_NAME=${FUNC_NAME:=cv-auto-ui}

env

# Run pytest with the specified markers and arguments
echo "======================================="
echo "Test started: $(date -u +"%Y-%m-%dT%H:%M:%SZ")"
echo "GPU: ${GPU}"
echo "INSTANCE_TYPE: ${INSTANCE_TYPE}"
echo "MIN_INSTANCES: ${MIN_INSTANCES}"
echo "MAX_INSTANCES: ${MAX_INSTANCES}"
echo "MAX_CONCURRENCY: ${MAX_CONCURRENCY}"
echo "FUNC_NAME_PREFIX: ${FUNC_NAME}"
echo "CLUSTERS: ${CLUSTERS}"
echo "======================================="
echo pytest testcases/cluster/test_cluster.py -m "cluster_validation" $@
pytest testcases/cluster/test_cluster.py -m "cluster_validation" $@
