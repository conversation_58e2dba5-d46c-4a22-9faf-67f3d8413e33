checks:
  - check: message
    regex: '^(build|chore|ci|docs|feat|fix|perf|refactor|revert|style|test){1}(\([\w\-\.]+\))?(!)?: ([\w ])+([\s\S]*)|(Merge).*|(fixup!.*)'
    error: |
      "The commit message should be structured as follows:
      <type>[optional scope]: <description>
      [optional body]
      [optional footer(s)]
      More details please refer to https://www.conventionalcommits.org"
    suggest: please check your commit message whether matches above regex

  - check: branch
    regex: ^(bugfix|feature|release|hotfix|task)\/.+|(master)|(main)|(HEAD)|(PR-.+)
    error: "Branches must begin with these types: bugfix/ feature/ release/ hotfix/ task/"
    suggest: run command `git checkout -b type/branch_name`

  - check: author_name
    regex: ^[A-Za-z ,.\'\-\(\)]+$|.*(\[bot])
    error: The committer name seems invalid
    suggest: run command `git config user.name "Your Name"`

  - check: author_email
    regex: ^\S+@\S+\.\S+$
    error: The committer email seems invalid
    suggest: run command `git config user.email <EMAIL>`

  - check: commit_signoff
    regex: Signed-off-by
    error: Signed-off-by not found in latest commit
    suggest: run command `git commit -m "conventional commit message" --signoff`
