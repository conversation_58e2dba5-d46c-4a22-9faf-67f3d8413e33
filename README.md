# NGC UI automation test

## How to use it

You can follow the instructions to setup the development and testing environment.

### 1. Clone this repo

```bash
git clone ssh://****************************:12051/cloud-service-qa/nvcf/nvcf-ui-test.git
```

2. Create a `.cloudia.config` file file under your home directory

```
touch .cloudia.config
```

```toml
# .cloudia.config

[Test Envrionment]
# test environment, should be staging, canary or production
type = staging

[Account Source]
# data source for retrieve test account, now the only can be  'DataBase' and 'Vault'
type = Vault

# Vault config
[Cloudia Vault]
vault_addr = https://cqax.nvidia.com:6789/
vault_token = <Vault Token>
mount_path = nvcf_secrets
```

### 3. Set up venv environment

The poetry can help about this step. Install the poetry by pip then setup and install the dependencies.

```shell
pip install poetry
poetry install
```

### 4. Install the requirements

```shell
poetry run playwright install
poetry run playwright install-deps
```

### 5. Set up pre-commit check and static code check

For the developers, the static code check is required.

```shell
poetry run pre-commit install
```
There are 2 methods to work with static code check.

#### 5.1. Manual trigger pre-commit check

```shell
poetry run pre-commit run --all-files
```

#### 5.2. Ruff with Visual Studio Code

If your IDE is Visual Studio Code, can install the Ruff plugin.

### 6. Run the automation
For the test execution, should be under the poetry environment. Can enable the poetry env by command `poetry shell` under current Shell. Or you can run the pytest command with `poetry run`, that can help run the command under a new Shell with poetry env. The sample is `poetry shell`.

```shell
poetry shell
pytest
pytest -m regression # this is the marker option, can get available Marker list from pyproject.toml or 6.1. paragraph.
```

#### 6.1. Marker list

    P0: For P0 level test cases
    P1: For P1 level test cases
    P2: For P2 level test cases
    Sanity: For Sanity test cases
    canary: For Canary test cases
    prod: For the case will run on Prodcution Service
    prod_sanity: For the case will run sanity test on Production Service
    CloudFunctions: For Cloud Functions test cases
    nvct: For nvct ui cases running on prod and canary

#### 6.2. Headed mode for firefox

The default browser is Chrome for Playwright. This sample is for Firefox, the Edge and WebKit should be same.

```shell
pytest --headed --browser firefox
```

## Debug

Check the `pytest.log` file under the `./reports` directory

### Enable debug log about HAR and video captures

The HAR and video file can attach to UI bugs.

```shell
  --extra-capture       The total switch of extra captures. Such as har and video while browser running.
  --disable-extra-video
                        The capture video while browser running is enabled when --extra-capture is added. If add --disable-extra-video, will disable the video capture.
  --disable-extra-har   The capture har while browser running is enabled when --extra-capture is added. If add --disable-extra-har, will disable the har capture.
```

## Generate code

```bash
playwright codegen --target pytest -o template.py https://stg.llm.ngc.nvidia.com/ --save-storage cookies/user01_cookies.json
```

## Load cookies to locate element

```bash
playwright codegen --target python-pytest -o template.py https://stg.llm.ngc.nvidia.com/ --load-storage cookies/user01_cookies.json
```

## Login fixture used as a test setup
### Login with single user
```python
  logon_user = ['org_admin']
  @pytest.mark.parametrize("get_user_page", "logon user name(in machine.config)", indirect=True)
  def you_test_method(self, get_user_page, ....)
    ngc_user_page = get_user_page
```

### Login with multiple user
```python
  logon_user_list = [('org_admin', 'org_admin-team_admin', 'org_user', 'org_user-team_user')]
  @pytest.mark.parametrize("get_multiple_user_pages", logon_user_list, indirect=True)
  def you_test_method(self, get_multiple_user_pages, ....)
    page_list = get_multiple_user_pages
    org_admin_page = page_list[0]
    org_user_page = page_list[1]
    team_admin_page = page_list[2]
    team_user_page = page_list[3]
```

## pytest-rerunfailures
pytest-rerunfailures is a plugin for `pytest` that re-runs tests to eleminate intermittent failures.
### Re-run all failures
To re-run all test failures, use the `--reruns` command line option with the maximum number of times you’d like the tests to run:

```bash
$ pytest --reruns 2
```
Failed fixture or setup_class will also be re-executed.

To add a delay time between re-runs use the `--reruns-delay` command line option with the amount of seconds that you would like wait before the next test re-run is launched:

```bash
$ pytest --reruns 2 --reruns-delay 1
```
### Re-run all failures matching certain expressions
To re-run only those failures that match a certain list of expressions, use the `--only-rerun` flag and pass it a regular expression. For example, the following would only rerun those errors that match `AssertionError`:
```bash
$ pytest --reruns 2 --only-rerun AssertionError
```
Passing the flag multiple times accumulates the arguments, so the following would only rerun those errors that match `AssertionError` or `ValueError`:
```bash
$ pytest --reruns 2 --only-rerun AssertionError --only-rerun ValueError
```
[Click here for more information regarding `pytest-rerunfailures`](https://pypi.org/project/pytest-rerunfailures/#pytest-rerunfailuresm)

## Work with NGC Service type

If your cases are needed to check with the three types, you can get the service type in runtime from sample code, based on the pytest fixture ["request"](https://docs.pytest.org/en/6.2.x/reference.html#std-fixture-request)

Test code, sample from [test_cloudfunctions.py](testcases/CloudFunctions/test_cloudfunctions.py)
```python
    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3765383
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_search_logs_in_function_version_details_page(
        self, test_data, get_user_page: Page
    ):
        search_str = "stdout"
        func_info = test_data["fastapi_echo_function"]
        fastapi_version_page = FunctionVerDetailPage(
            get_user_page,
            func_info["name"],
            func_info["func_id"],
            func_info["func_vers_id"],
        )
        fastapi_version_page.navigate_to_page()
        fastapi_version_page.SwitchBar.toggle_tab(
            fastapi_version_page.SwitchBar.elements["Logs"]
        )
        fastapi_version_page.LogsSearchBar.search(search_str)
        page_data = fastapi_version_page.LogsPaginationTable.get_current_page_data()
        for log_item in page_data:
            assert (
                search_str in log_item["Message"]
            ), "Searched string not listed in log message."
```

## Auto Screenshots when get failed result

The feature about auto capture screenshots when get failed result.

1. There will generate a PNG file in `reports/screenshots` path, name rule: `{case_name}_{browser_name}_{report_phase}_{report_timestamp}.png`
2. If HTML reporter enabled, will add the screenshot into the HTML report.

## Maintain the poetry.lock

The poetry.lock file is poetry package management package file. Can help poetry get dependencies information and quickly install dependencies.

```bash
poetry update # install the latest packages
poetry lock # lock the verion in poetry.lock
```
