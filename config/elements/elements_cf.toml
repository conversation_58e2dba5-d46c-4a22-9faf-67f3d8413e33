[SideBar]
# For all entry of Cloud Functions
Functions = "//a[text()='Functions']"
CreateFunction = "//a[text()='Create Function']/../parent::button"
Settings = "//a[text()='Settings']"

[NavigationBar]
Title = "//h2"

[DeployVersion.Form]
ReviewDeployBtn = "//button[text()='Review Deployment']"
DeployVersionBtn = "//button[text()='Deploy Version']"
DeployFuncButton = "//button[text()='Deploy Function']"
BackendSelector = "//label[text()='Backend']/../following-sibling::button[@role='combobox']"
FirstOptChoose = "(//div[@role='presentation']/div[@role='option'])[1]"
GPUSelector = "//label[text()='GPU']/../following-sibling::button[@role='combobox']"
InstanceTypeSelector = "//label[text()='Instance Type']/../following-sibling::button[@role='combobox']"
MinInstanceInput = "//input[contains(@name,'minInstances')]"
MaxInstanceInput = "//input[contains(@name,'maxInstances')]"
BackendEntryGFN = "//div[@role='presentation']/div[@role='option']/span[text()='GFN']"
BackendEntryGCP = "//div[@role='presentation']/div[@role='option']/span[text()='gcp-asia-se-1a']"
GPUEntryL40G = "//div[@role='presentation']/div[@role='option']/span[text()='L40G']"
ReviewConfigTitle = "//span[text()='Review Configuration']"


[Functions.NavigationBar]
Documentation = "//button[text()='Documentation']"
GeneratePersonalAPIkey = "//button[text()='Generate Personal API Key']"
CreateFunction = "//button[text()='Create Function']"
ReviewFunctionBtn = "//button[text()='Review Function']"

[Functions.SwitchBar]
MyFunctions = "//button[text()='My Functions']"
SharedFunctions = "//button[text()='Shared Functions']"
CreateByNVIDIA = "//button[text()='Create by NVIDIA']"

[Functions.Pagination]
TotalFunctions = "//span[contains(normalize-space(), 'Results') or contains(normalize-space(), 'Result')]"
# search box related elems
SearchBox = "//input[@placeholder]"
SearchedFunctionNamebyName = "//tr/td/a/span[text()='{0}']"
CancelBtn = "//input/following-sibling::button"
FailureTitle = "//span[text()='Failures']"

# pagination edition related elems
RefreshButton = "//button[contains(text(), 'Refresh')]"
EditColumnsButton = "//button[contains(text(), 'Edit Columns')]"
DisplayedRows = "//div[@data-testid='kui-pagination-page-size']//button/div/span[1]"

# displayed page related elems
SwitchDisplayedRows = "//div[@data-testid='kui-pagination-page-size']//button"
TotalPages = "//span[contains(normalize-space(), 'of')]"
PrevPageButton = "//div[@data-testid='kui-pagination-number-range']/button[1]"
NextPageButton = "//div[@data-testid='kui-pagination-number-range']/button[2]"
SelectPagesNum = "//div[@role='presentation' and @data-testid='kui-select-viewport']/div/span[text()='{0}']"
SortFunctionByStatus = "//*[@id='data-view-header-status']"
CurrentSortStatus = "//*[@id='data-view-header-status']/button/*[name()='svg']"

# function pannel related elems
FuncTable = "//table"
TableHeader = "//thead//tr//th"
TableBody = "//table//tbody"
TableItems = "//table//tbody//tr"
TableItemsRow = "//table/tbody//tr[@data-testid='kui-table-row']"
SearchBarInput = "//input[@type='text']"
KaizenLoader = "//div[contains(@class,'kaizen-ui-loader')]"
FunctionNameByName = "//span[text()='{0}']/parent::a"
FunctionStatusByName = "//table/tbody/tr/td[1]//span[text()='{0}']/../../following-sibling::td[2]/div"
# action button related elems
ActionBtnByName = "//span[text()='{0}']/parent::a/../..//button"
EditDeployBtn = "//div[@role='menuitem'][text()='Edit Deployment']"
CancelDeployBtn = "//div[@role='menuitem'][text()='Cancel Deployment']"
DeployVersionBtn = "//div[@role='menuitem' and text()='Deploy Version']"
DisableVersionBtn = "//div[@role='menuitem' and text()='Cancel Deployment']"
CreateNewVersionBtn = "//div[@role='menuitem' and text()='Create New Version']"
ViewFunctionDetailsBtn = "//div[@role='menuitem' and text()='View Function Details']"
ViewVersionDetailsBtn = "//div[@role='menuitem' and text()='View Version Details']"
ViewVersionLogsBtn = "//div[@role='menuitem' and text()='View Version Logs']"
CloneFuncBtn = "//div[@role='menuitem' and text()='Clone Function']"
DelVersBtn = "//div[@role='menuitem' and text()='Delete Version']"
ManageSecBtn = "//div[@role='menuitem' and text()='Manage Secrets']"
RedeployVersBtn = "//div[@role='menuitem' and text()='Redeploy Version']"

# delete function related elements
ConfirmDeleteBtn = "//button[text()='Delete Version']"
FunctionDeleteBanner = "//span[contains(text(), 'successfully deleted')]"

# disable version related elements
DisableVersBtnInDialog = "//button[text()='Cancel Deployment']"
AllowTaskCompleteMsg = "//input[@id='graceful-termination']/following-sibling::label"
SucessDisableMsg = "//span[contains(text(), 'will be canceled once current tasks are completed.')]"

[CreateFunction.Form]
ContainerTypeChoose = "#functionRadio-container-Container"
NameInput = "//input[@name='name']"
ContainerInput = "//label[text()='Container']/../..//div[text()='Select one']/..//input"
TagsInput = "//input[@aria-autocomplete='list']"
TagsInfo = "//button[text()='{0}']"
TagInput = "//label[text()='Tag']/../..//div[text()='Select container tag']/..//input"
SelectorChoosebyName = "//div[text()='{0}']"
SelectModel = "//button[@aria-label='Select Model(s)']"
SelectModelOpen = "//button[@aria-label='Select Model(s)' and @data-state='open']"
ModelInput = "//div[text()='Select a container first']/..//input"
ModelVersionInput = "//label[text()='Model Version']/../..//div[text()='Select version']/..//input"
ModelNameInput = "//label[text()='Name']/../..//input[contains(@name, 'models')]"
SelectModelContainer = "//div[contains(@class, 'placeholder') and text()='Select a container first']"
SelectModelVersion = "//div[contains(@class, 'placeholder') and text()='Select a model first']"
SelectModelName = "//input[@placeholder='Select a model first']"
AddModelButton = "(//label[text()='Model']/../../../../..)[last()]/..//button"
ResourceSelect = "//button[@aria-label='Select Resource(s)']"
ResourceSelectOpen = "//button[@aria-label='Select Resource(s)' and @data-state='open']"
ResourceInput = "//label[text()='Resource']/../..//div[text()='Select a container first']/..//input"
ResourceVersionInput = "//label[text()='Resource Version']/../..//div[text()='Select version']/..//input"
ResourceNameInput = "//label[text()='Name']/../..//input[contains(@name, 'recipes')]"
AddResourceButton = "(//label[text()='Resource']/../../../../..)[last()]/..//button"
InferenceProtInput = "//label[text()='Inference Protocol']/../..//div[text()='Select protocol']/..//input"
HealthProtInput = "//label[text()='Health Protocol']/../..//div[text()='Select protocol']/..//input"
PortInput = "//input[@name='inferencePort']"
InferenceEndpointInput = "//input[@name='inferenceUrl']"
HealthEndpointInput = "//input[@name='healthUri']"
TelemetrySelect = "//button[@aria-label='Telemetry Endpoints']"
TelemetrySelectOpen = "//button[@aria-label='Telemetry Endpoints' and @data-state='open']"
TelemetryLogsInput = "//div[text()='Select a logs endpoint']/..//input"
TelemetryMetricsInput = "//div[text()='Select a metrics endpoint']/..//input"
TelemetryTracesInput = "//label[text()='Traces']/../following-sibling::div//input[@type='text' and @role='combobox']"

AddEnvButton = "//button[contains(., 'Add Another Environment Variable')]"
EnvKeySelect = "//button[@aria-label='Environment Variables']"
EnvKeySelectOpen = "//button[@aria-label='Environment Variables' and @data-state='open']"
EnvKeyInputBySeq = "//input[starts-with(@name, 'containerEnvironment') and substring(@name, string-length(@name) - string-length('key') + 1) = 'key']"
EnvValueInputBySeq = "//input[starts-with(@name, 'containerEnvironment') and substring(@name, string-length(@name) - string-length('value') + 1) = 'value']"
AddSecButton = "//button[@data-testid='kui-button' and @type='button' and text()=' Add Another Secret']"
SecKeySelect = "//button[@aria-label='Secrets']"
SecKeySelectOpen = "//button[@aria-label='Secrets' and @data-state='open']"
SecKeyInputBySeq = "//input[starts-with(@name, 'secrets') and substring(@name, string-length(@name) - string-length('name') + 1) = 'name']"
SecValueInputBySeq = "//textarea[@data-testid='kui-text-area-element']"
RunComandSelect = "//button[@aria-label='Container Run Command']"
RunComandSelectOpen = "//button[@aria-label='Container Run Command' and @data-state='open']"
RunCommandTextArea = "//textarea[@name='containerArgs']"
ReviewFunctionBtn = "//button[text()='Review Function']"
CreateDeployFunctionBtn = "//button[text()='Create and Deploy Function']"
CreateFunctionBtn = "//button[text()='Create Function Without Deploying' or text()='Create Function']"
ReviewConfigTitle = "//span[text()='Review Configuration']"
FunctionCreateBanner = "//span[contains(text(), 'successfully created')]"
FunConfigurationTitle = "//span[text()='Function Configuration']"
FunctionConfigurationTitle = "//span[text()='Function Configuration']"

[Functions.Detail.NavigationBar]
CreateNewVersion = "//button[text()='Create New Version']"
MoreOption = "//button[@data-testid='kui-menu-trigger']/*[name()='svg']"
CloneFuncBtn = "//div[@role='menuitem'][text()='Clone Function']"
FuncListBtninBreadcrumb = "//div[@data-testid='kui-breadcrumb-item']/a"
CloneVersion = "//div[text()='Confirm Version to Clone']/following-sibling::div[1]//button/div[1]"
ExpandBtn = "//div[text()='Confirm Version to Clone']/following-sibling::div[1]//button/div[2]"
AllVersions = "//div[contains(@class,'c-PJLV') and @data-testid='kui-theme']"
CheckVersion = "//div[@data-testid='kui-select-item' and @data-state='checked']//span"
UnCheckVersion = "//div[@data-testid='kui-select-item' and @data-state='unchecked']//span"
ContinueBtn = "//button[@data-testid='kui-button' and contains(text(), 'Continue')]"

[Functions.Detail.Statistics.AggregatedInvocations]
Title = "//h4[@title='Aggregated Invocations']"
DisplayedSpanBtn = "//button[@role='combobox']"
StatisticAttrCell = "//div[@data-testid='kui-select-root']"
totalinvoAttr = "//span[@title='Total Invocations']"
totalinvoVal = "//span[@title='Total Invocations']/../../span[@data-testid='kui-text']"
averinferenceTAttr = "//span[@title='Average Inference Time']"
averinferenceTVal = "//span[@title='Average Inference Time']/../../span[@data-testid='kui-text']"
totalinstanceCountAttr = "//span[@title='Total Instance Count']"
totalinstanceCountVal = "//span[@title='Total Instance Count']/../../span[@data-testid='kui-text']"
failureAttr = "//span[@title='Failures']"
failureVal = "//span[@title='Failures']/../../span[@data-testid='kui-text']"
StatisticValueCell = "//h4[@title='Aggregated Invocations']/../../div[2]/span"
FailuresExclaMark = "//span[@title='Failures']/following-sibling::button"
#FailuresText = "//*[@role='dialog']/div/span[1]"
FailuresText = "//*[@role='dialog']//span[contains(text(), 'Here we only capture invocation request failures originating from your inference container.')]"
TotalInstanceCountAttr = "//span[@title='Total Instance Count']"

[Functions.Detail.Statistics.AggregatedInstances]
Title = "//h4[@title='Aggregated Instances']"
statisticAttrCell = "//h4[@title='Aggregated Instances']/../../following-sibling::div/span"
statisticValueCell = "//h4[@title='Aggregated Instances']/../../div[2]/span"


[Functions.Detail.Pagination]
# pagination edition related elems
RefreshButton = "//button[contains(text(), 'Refresh')]"
EditColumnsButton = "//button[contains(text(), 'Edit Columns')]"
DisplayedRows = "//div[@data-testid='kui-pagination-page-size']//button/div/span[1]"
VersNameTitle = "//*[@id='data-view-header-name'][text()='Version Name']"
FailuresExclaMark = "//span[text()='Failures']/button"

# displayed page related elems
SwitchDisplayedRows = "//div[@data-testid='kui-pagination-page-size']//button"
TotalPages = "//span[contains(normalize-space(), 'of')]"
PrevPageButton = "//div[@data-testid='kui-pagination-number-range']/button[1]"
NextPageButton = "//div[@data-testid='kui-pagination-number-range']/button[2]"

# function pannel related elems
FuncTable = "//table"
TableHeader = "//thead//tr//th"
TableBody = "//table//tbody"
TableItems = "//table//tbody//tr"
SearchBarInput = "//input[@placeholder='Search table']"
KaizenLoader = "//div[contains(@class,'kaizen-ui-loader')]"
VersionNameByName = "//table/tbody/tr/td[1][text()='{0}']"
VersionStatusByName = "//table/tbody/tr/td[1][text()='{0}']/following-sibling::td[1]"

# action button related elems
ActionBtnByName = "//table//a[text()='{0}']/../..//button"
CopyVersionId = "//div[@role='menuitem' and text()='Copy Version ID']"
ViewVersionDetailsBtn = "//div[@role='menuitem' and text()='View Version Details']"
ViewInvokCmd = "//div[@role='menuitem' and text()='View Invoke Command']"
DeployVersionBtn = "//div[@role='menuitem' and text()='Deploy Version']"
UndeployVersionBtn = "//div[@role='menuitem' and text()='Un-Deploy Version']"
DelVersBtn = "//div[@role='menuitem' and text()='Delete Version']"
ManageSecBtn = "//div[@role='menuitem' and text()='Manage Secrets']"
RedeployVersBtn = "//div[@role='menuitem' and text()='Redeploy Version']"

ViewInvoCmdDiag = "//div[@data-testid='kui-theme']//div[text()='View Invoke Command']"

# un-deploy version related elements
DisableVersBtnInDialog = "//button[text()='Cancel Deployment']"
SucessDisableMsg = "//span[contains(text(), 'has been disabled.')]"

# delete function related elements
ConfirmDeleteBtn = "//button[text()='Delete Version']"
FunctionDeleteBanner = "//span[contains(text(), 'successfully deleted')]"

[Functions.Detail.CollapsedTable.BasicDetails]
Title = "//span[text()='Basic Details']"
ExpandBtn = "//span[text()='Basic Details']/../.."
DetailsAttr = '//*[@id="__next"]//div[1]/div[position() >= 1 and position() <= 5]/label'
# DetailsVal = '//*[@id="__next"]/div/div[1]/div/div[3]/div[2]/div[3]/div/div/div/div[2]/div[1]/div[2]/div[1]/div[position() >= 1 and position() <= 5]/*[name() = "span" or name() = "div"]'
DetailsVal = '//*[@id="__next"]//div[2]/div[1]/div[2]/div[1]/div[position() >= 1 and position() <= 5]/*[name() = "span" or name() = "div"]'
VersDetailPageVersionID = "//label[text()='Version ID']/following-sibling::span"
StatusTitleinBasicDetails = "//h4[text()='Basic Details']/ancestor::div[@data-testid='kui-flex']/following-sibling::div//label[text()='Status']"

[Functions.Detail.CollapsedTable.RuntimeDetails]
Title = "//span[text()='Runtime Details']"
ExpandBtn = "//span[text()='Runtime Details']/../.."
DetailsAttr = "//span[text()='Runtime Details']/../../..//div[@data-testid='kui-accordion-content']/div/div//label"
DetailsVal = "//span[text()='Runtime Details']/../../..//div[@data-testid='kui-accordion-content']/div/div//span"

[Functions.Detail.CollapsedTable.ModelDetails]
Title = "//span[text()='Model Details']"
ExpandBtn = "//span[text()='Model Details']/../.."
DetailsAttr = "//span[text()='Model Details']/../../..//div[@data-testid='kui-accordion-content']/div/div/label"
DetailsVal = "//span[text()='Model Details']/../../..//div[@data-testid='kui-accordion-content']/div/div/span"

[Functions.Detail.CollapsedTable.EnvironmentVariables]
Title = "//span[text()='Environment Variables']"
ExpandBtn = "//span[text()='Environment Variables']/../.."
DetailsAttr = "//span[text()='Environment Variables']/../../..//div[@data-testid='kui-accordion-content']/div/div/label"
DetailsVal = "//span[text()='Environment Variables']/../../..//div[@data-testid='kui-accordion-content']/div/div/span"

[Functions.VerDetail.NavigationBar]
DeployFuncBtn = "//button[text()='Deploy Version']"
DisableVers = "//button[text()='Disable Version']"
moreOpts = "//button[@data-testid='kui-menu-trigger']"
ViewInvokCmd = "//div[@role='menuitem'][text()='View Invoke Command']"
NewVersionBtn = "//div[@role='menuitem'][text()='Create New Version']"
CloneFuncBtn = "//div[@role='menuitem'][text()='Clone Version']"
EditDeployBtn = "//div[@role='menuitem'][text()='Edit Deployment']"
DelVersBtn = "//div[@role='menuitem'][text()='Delete Version']"
FuncBtninBreadcrumb = "//div[@data-testid='kui-breadcrumb-item'][2]/a"
RedeployVersBtn = "//div[@role='menuitem'][text()='Redeploy Version']"

# delete version related elements
DelVersConfirmBtn = "//button[text()='Delete Version']"
SucessDelMsg = "//span[contains(text(), 'has been disabled.')]"

[Functions.VerDetail.SwitchBar]
Overview = "//button[text()='Overview']"
FunctionDetails = "//button[text()='Function Details']"
DeploymentDetails = "//button[text()='Deployment Details']"
Logs = "//button[text()='Logs']"
MetricsTabButton = '//button[text()="Metrics"]'

[Functions.VerDetail.DeploymentDetailsTab.Details]
DetailsAttr = "//span[text()='Deployment Specifications']/../../../div[@data-testid='kui-flex']//label"
DetailsVal = "//span[text()='Deployment Specifications']/../../../div[@data-testid='kui-flex']//label/../span"
DeploymentSpecifications = "//span[text()='Deployment Specifications']"

[Functions.VerDetail.LogsTab]
ResultCountText = "//span[contains(normalize-space(.), 'Result')]"
Delay_info = "//span[@data-testid='kui-text' and text()='Please wait. It may take a few minutes for logs to load.']"

[Functions.VerDetail.LogsTimeDropDownSelector]
Selector = "//div[@data-testid='kui-flex']/div[@data-testid='kui-select-root']"
LastOneHourEntry = "//span[text()='Last 1 hour']"
SelectEntrybyName = "//div[@data-testid='kui-select-item']/span[text()='{0}']"
LoadingBtn = "//button[@aria-label='Loading']"
RefreshBtn = "//button[@aria-label='Refresh']"
ExpandIcon = "//button[descendant::text()[contains(., 'Expand')]]"
CollapseIcon = "//button[descendant::text()[contains(., 'Collapse')]]"
ExpandSVG = "//table/tbody/tr[1]/td[3]//*[name()='svg' and @data-icon-name='shapes-chevron-up']"
CollapseSVG = "//table/tbody/tr[1]/td[3]//*[name()='svg' and @data-icon-name='shapes-chevron-down']"

[Functions.VerDetail.LogsSearchBar]
SearchBarInput = "//input[@placeholder='Search Logs']"
LoadingBtn = "//button[@aria-label='Loading']"
RefreshBtn = "//button[@aria-label='Refresh']"
SwitchBtn = "//button[contains(text(), 'Switch To Window View')]"
FirstRawRow = "//div[@data-testid='kui-box' and @data-index='0']//span[@data-testid='kui-text']"
FirstTableRow = "//table[@data-testid='kui-table']//tr[@data-index='0']//td[@headers='data-view-header-Message']"

[Functions.VerDetail.LogsPaginationTable]
LogsDisplayedItems = "//table/tbody/tr"
DisplayedItembyRow = "//table/tbody/tr[{0}]/td"
CopyLogBtn = "//div[text()='Copy Log']"

[Functions.VerDetail.LogsCountDropDownSelector]
Selector = "//span[text()='Show']/following-sibling::div//button"
SelectEntrybyName = "//div[@data-testid='kui-select-item']/span[text()='{0}']"
RefreshBtn = "//button[@aria-label='Refresh']"
SelectedbyName = "//span[text()='{0}']"

[Functions.VerDetail.CollapsedChart.InvocActAndQueDpth]
Title = "//span[text()='Invocation Activity and Queue Depth']"
ExpandBtn = "//span[text()='Invocation Activity and Queue Depth']/../.."
SVGChart = "//span[text()='Invocation Activity and Queue Depth']/ancestor::div[@data-testid='kui-accordion']//*[name()='svg' and @class='recharts-surface']"
averinferenceTVal = "//span[@title='Average Inference Time']/../../span[@data-testid='kui-text']"
invocationsAttr = "//span[text()='Invocations' and contains(@class,'c-kAArxJ-dZaMxF-variant-small')]"
invocationsVal = "//div[contains(@class, 'recharts-tooltip-wrapper')]//span[contains(text(), 'Invocations')]"
QueueDepthAttr = "//span[text()='Queue Depth' and contains(@class,'c-kAArxJ-dZaMxF-variant-small')]"
QueueDepthVal = "//div[contains(@class, 'recharts-tooltip-wrapper')]//span[contains(text(), 'Queue Depth')]"
FirstxAxis = "(//span[text()='Invocation Activity and Queue Depth']/ancestor::div[@data-testid='kui-accordion']//*[local-name()='g'][contains(@class,'recharts-xAxis')]//*[local-name()='tspan'])[1]"
LastxAxis = "(//span[text()='Invocation Activity and Queue Depth']/ancestor::div[@data-testid='kui-accordion']//*[local-name()='g'][contains(@class,'recharts-xAxis')]//*[local-name()='tspan'])[last()]"

[Functions.VerDetail.CollapsedChart.AvgInferTime]
Title = "//span[text()='Average Inference Time']"
ExpandBtn = "//button//span[text()='Average Inference Time']/../.."
SVGChart = "//span[text()='Average Inference Time']/ancestor::div[@data-testid='kui-accordion']//*[name()='svg' and @class='recharts-surface']"
AvgInferTimeVal = "//div[contains(@class, 'recharts-tooltip-wrapper')]//span[contains(text(), 'Average Inference Time')]"
FirstxAxis = "(//span[text()='Average Inference Time']/ancestor::div[@data-testid='kui-accordion']//*[local-name()='g'][contains(@class,'recharts-xAxis')]//*[local-name()='tspan'])[1]"
LastxAxis = "(//span[text()='Average Inference Time']/ancestor::div[@data-testid='kui-accordion']//*[local-name()='g'][contains(@class,'recharts-xAxis')]//*[local-name()='tspan'])[last()]"

[Functions.VerDetail.CollapsedChart.Instances]
Title = "//span[text()='Instances']"
ExpandBtn = "//span[text()='Instances Over Time']"
SVGChart = "//span[text()='Instances Over Time']/ancestor::div[@data-testid='kui-accordion']//*[name()='svg' and @class='recharts-surface']"
InstancesVal = "//div[contains(@class, 'recharts-tooltip-wrapper')]//span[contains(text(), 'Instances')]"
FirstxAxis = "(//span[text()='Instances Over Time']/ancestor::div[@data-testid='kui-accordion']//*[local-name()='g'][contains(@class,'recharts-xAxis')]//*[local-name()='tspan'])[1]"
LastxAxis = "(//span[text()='Instances Over Time']/ancestor::div[@data-testid='kui-accordion']//*[local-name()='g'][contains(@class,'recharts-xAxis')]//*[local-name()='tspan'])[last()]"

[Functions.VerDetail.CollapsedChart.SuccessRate]
Title = "//span[text()='Success Rate']"
ExpandBtn = "//span[text()='Success Rate']"
SVGChart = "//span[text()='Success Rate']/ancestor::div[@data-testid='kui-accordion']//*[name()='svg' and @class='recharts-surface']"
SuccessRateVal = "//div[contains(@class, 'recharts-tooltip-wrapper')]//span[contains(text(), 'Success Rate')]"
FirstxAxis = "(//span[text()='Success Rate']/ancestor::div[@data-testid='kui-accordion']//*[local-name()='g'][contains(@class,'recharts-xAxis')]//*[local-name()='tspan'])[1]"
LastxAxis = "(//span[text()='Success Rate']/ancestor::div[@data-testid='kui-accordion']//*[local-name()='g'][contains(@class,'recharts-xAxis')]//*[local-name()='tspan'])[last()]"

[Functions.VerDetail.ActionMenu]
ActionBtn = "//button[text()='Cancel Deployment']/following-sibling::button"
ActionEntryByName = "//div[@role='menuitem'][text()='{0}']"

[Functions.VerDetail.FunctionDetails]
Container = "//label[@data-testid='kui-label' and text()='Container']"
ContainerVal = "//label[@data-testid='kui-label' and text()='Container']/following-sibling::*[name()='span']"
Models = "//label[@data-testid='kui-label' and normalize-space(text())='Models']"
ModelsVal = "//label[@data-testid='kui-label' and text()='Models']/following-sibling::*[1]"
Resources = "//label[@data-testid='kui-label' and normalize-space(text())='Resources']"
ResourcesVal = "//label[@data-testid='kui-label' and normalize-space(text())='Resources']/following-sibling::*[1]"
ModelMountPoints = "//label[@data-testid='kui-label' and normalize-space(text())='Model Mount Points']"
ModelMountPointsVal = "//label[@data-testid='kui-label' and normalize-space(text())='Model Mount Points']/following-sibling::div/span"
ResourceMountPoints = "//label[@data-testid='kui-label' and normalize-space(text())='Resource Mount Points']"
ResourceMountPointsVal = "//label[@data-testid='kui-label' and normalize-space(text())='Resource Mount Points']/following-sibling::div/span"
InferenceProtocol = "//label[@data-testid='kui-label' and text()='Health Protocol']"
InferenceProtocolVal = "//label[@data-testid='kui-label' and text()='Health Protocol']/following-sibling::*[name()='span']"
InferencePort = "//label[@data-testid='kui-label' and text()='Inference Port']"
InferencePortVal = "//label[@data-testid='kui-label' and text()='Inference Port']/following-sibling::*[name()='span']"
InferenceEndpoint = "//label[@data-testid='kui-label' and text()='Inference Endpoint']"
InferenceEndpointVal = "//label[@data-testid='kui-label' and text()='Inference Endpoint']/following-sibling::*[name()='span']"
HealthPort = "//label[@data-testid='kui-label' and text()='Health Port']"
HealthPortVal = "//label[@data-testid='kui-label' and text()='Health Port']/following-sibling::*[name()='span']"
HealthEndpoint = "//label[@data-testid='kui-label' and text()='Health Endpoint']"
HealthEndpointVal = "//label[@data-testid='kui-label' and text()='Health Endpoint']/following-sibling::*[name()='span']"
RunCommandOverrides = "//label[@data-testid='kui-label' and text()='Container Run Command']"
RunCommandOverridesVal = "//label[@data-testid='kui-label' and text()='Container Run Command']/following-sibling::*[1]"
EnvVar = "//label[@data-testid='kui-label' and text()='Environment Variables']"
EnvVarVal = "//label[@data-testid='kui-label' and text()='Environment Variables']/following-sibling::*[name()='span' or name()='div']"
LogsEndpoint = "//label[@data-testid='kui-label' and text()='Logs Endpoint']"
LogsEndpointVal = "//label[@data-testid='kui-label' and text()='Logs Endpoint']/following-sibling::*[name()='span' or name()='div']"
MetricsEndpoint = "//label[@data-testid='kui-label' and text()='Metrics Endpoint']"
MetricsEndpointVal = "//label[@data-testid='kui-label' and text()='Metrics Endpoint']/following-sibling::*[name()='span' or name()='div']"
TracesEndpoint = "//label[@data-testid='kui-label' and text()='Traces Endpoint']"
TracesEndpointVal = "//label[@data-testid='kui-label' and text()='Traces Endpoint']/following-sibling::*[name()='span' or name()='div']"
SecretsEndpoint = "//label[@data-testid='kui-label' and text()='Secrets']"
SecretsEndpointVal = "//label[@data-testid='kui-label' and text()='Secrets']/following-sibling::*[name()='span' or name()='div']"
LowTatencyStreaming = "//label[@data-testid='kui-label' and text()='Low Latency Streaming']"
LowTatencyStreamingVal = "//label[@data-testid='kui-label' and text()='Low Latency Streaming']/following-sibling::*[name()='span' or name()='div']"

[Functions.VerDetail.Overview.BasicDetail]
FunctionID = "//div[@data-testid='kui-box']/label[@data-testid='kui-label' and text()='Function ID']"
FunctionIDVal = "//div[@data-testid='kui-box']/label[@data-testid='kui-label' and text()='Function ID']/following-sibling::*[name()='div' or name()='span']"
VersionID = "//div[@data-testid='kui-box']/label[@data-testid='kui-label' and text()='Version ID']"
VersionIDVal = "//div[@data-testid='kui-box']/label[@data-testid='kui-label' and text()='Version ID']/following-sibling::*[name()='div' or name()='span']"
Descriptions = "//label[@data-testid='kui-label' and text()='Description']"
DescriptionsVal = "//label[@data-testid='kui-label' and text()='Description']/following-sibling::*[name()='span']"
Tags = "//label[@data-testid='kui-label' and text()='Tags']"
TagsVal = "//label[@data-testid='kui-label' and text()='Tags']/following-sibling::*[name()='div' or name()='span']"

[Functions.VerDetail.InstanceType]
GPU = "//button[@role='tab' and @data-state='active']"
InstanceType = "//label[@data-testid='kui-label' and text()='GPUs']"
InstanceTypeVal = "//label[@data-testid='kui-label' and text()='GPUs']/following-sibling::*[name()='span']"
MinMaxInstance = "//label[@data-testid='kui-label' and text()='Min / Max Instances']"
MinMaxInstanceVal = "//label[@data-testid='kui-label' and text()='Min / Max Instances']/following-sibling::*[name()='span']"
MaxConcurrency = "//label[@data-testid='kui-label' and text()='Max Concurrency']"
MaxConcurrencyVal = "//label[@data-testid='kui-label' and text()='Max Concurrency']/following-sibling::*[name()='span']"
TargetRegions = "//label[@data-testid='kui-label' and text()='Target Regions']"
TargetRegionsVal = "//label[@data-testid='kui-label' and text()='Target Regions']/following-sibling::*[name()='span']"