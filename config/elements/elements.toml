# ====== Rules to add elements =====
# Follow below ruls to add elements
# 1. Follow snake_case rule to use lower case and "_"
# 2. Use descriptive and meaningful names
# 3. Use proper shorthand
# 4. Avoid long elements
# 5. Group the elements make it clear
# 6. To be added...
# For example, update_visib_dlg

title = '//h1[@class="title"]'

[Common]
emailInputBox = '//input[@id="email"]'
continueBtn = 'button:has-text("Continue")'
passwordInputBox = '//input[@id="signinPassword"]'
loginBtn = 'button:has-text("Log In")'
orgSearchBox = "//input[@id='org-search-input']"
orgSEL = 'li:has-text("{}")'
userMenu = "xpath=//div[2]/span >> nth=0"
currentOrg = "xpath=//div[2]/span[2]"
contextMenu = "//div[@data-testid='kui-logo']/../..//div[@data-testid='kui-avatar'] | //span[text()='Welcome Guest']"
signOutOpt = "//div[@role='menu']//div[text()='Sign Out']"
contactAdmin = "//div[@role='menu']//div[text()='Contact Admin']"
accountSettings = "//div[@role='menu']//div[text()='Account Settings']"
setup = "//div[@role='menu']//div[text()='Setup']"
organization = "//div[@role='menu']//div[text()='Organization']"
termsOfUse = "//div[@role='menu']//div[text()='Terms of Use']"
privacyPolicy = "//div[@role='menu']//div[text()='Privacy Policy']"
contactAdminWindow = "//div[@role='dialog']/div[text()='Contact Admin']"
welcomeDlg = "[data-testid='kui-modal']:has-text('Welcome to NeMo LLM')"
acceptAnalytics = "#accept-analytics-usage"
acceptCrash = "#accept-crash-usage"

notificationDialog = "//div[text()='Set Email Preferences For Your Services']"

[Organization.Users]
invite_user_btn = "//button[contains(text(), 'Add User')]"
confirmed_users_btn = "//button[text()='Confirmed Users']"
pending_invation_btn = "//button[text()='Pending Invitations']"
filter_bar = "//div[@data-testid='kui-tagEditor']"
user_table = "//table[@data-testid='k11-kui-table']"
users_table_header_cells = "//thead/tr[@data-testid='kui-table-row']/th"
user_items = "//tbody/tr[@data-testid='kui-table-row']"
email_filter = "//div[@data-testid='kui-text-input-root']//button"
email_filter_Email_option = "//div[@role='menu']/div/div[text()='Email']"
email_filter_input = "//div[@data-testid='kui-text-input-root']//span[text()='Email']/../input"
per_page_numer = "//input[@class='css-1hac4vs-dummyInput']"
current_page = "//input[@class='page-input']"
max_page = "//div[@data-testid='page-state']"
first_page = "//div[@data-testid='paginationNavigation']/button[1]"
previous_page = "//div[@data-testid='paginationNavigation']/button[2]"
next_page = "//div[@data-testid='paginationNavigation']/button[3]"
last_page = "//div[@data-testid='paginationNavigation']/button[4]"
delete_invite_action_btn = "//div[text()='Delete Invitation' and @role='menuitem']"
delete_invite_dlg = "//div[@class='modal-title-bar']/div[text()='Delete Invitation']/../.."
delete_invite_confirm_btn = "//button//span[text()='Delete Invite']/../.."
cancle_btn = "//button//span[text()='Cancel']/../.."
disable_user_keys_button = "//div[@role=\"menu\"]//div[text()=\"Disable User's Keys\"]"
remove_user_button = "//div[@role='menu']//div[text()='Remove User']"
user_name = "//div[text()='{0}']"
user_related_action_button = "//div[text()='{0}']/../..//button"

[Organization.Users.User]
personal_keys_bar = "xpath=//button[text()='Personal Keys']"
personal_keys_table = "//table[@data-testid='k11-kui-table']"
personal_key_active_button = "//button[@role='switch']"
personal_key_reactive_success_notification = "//span[contains(text(), 'successfully active')]"
personal_Key_warnning_sign = "//td[text()='{0}']/..//*[local-name()='svg' and namespace-uri()='http://www.w3.org/2000/svg' and @data-icon-name='common-warning']"
personal_keys_filter_columns_button = "//div[@data-testid='kui-table']//div/button[@shape]"
no_personal_keys_display_content = "//h2[text()='No Personal Keys Configured']"
teams_bar = "//button[text()='Teams']"
teams_table_header_cells = "//thead/tr[@data-testid='kui-table-row']/th"
teams_table = "//table[@data-testid='k11-kui-table']"
personal_keys_table_header_cells = "//thead/tr[@data-testid='kui-table-row']/th"
no_teams_display_content = "//h2[text()='No results found']"
user_detail_page_title = "//h2[text()='{0}']"
edit_membership_button = "//button[text()='Edit Membership']"
remove_user_button = "//button[text()='Remove User']"
user_logo = "//div[@data-testid='kui-side-panel-content']//*[local-name()='svg' and @data-icon-name='social-profile']"
user_email = "//div[@data-testid='kui-side-panel-content']//span[text()='Email']"
user_roles = "//div[@data-testid='kui-side-panel-content']//span[text()='Roles']"
user_date_created = "//div[@data-testid='kui-side-panel-content']//span[text()='Date Created']"
user_last_login = "//div[@data-testid='kui-side-panel-content']//span[text()='Last Login']"

[PersonalKey]
generate_personal_key_entry = "//button[text()='Generate Personal Key']"
generate_personal_key_button = "//div[@class='ngc-layout-header']//button"
personal_key_name_fillbox = "//label[text()='Key Name']/../..//input"
personal_key_Expiration_fillbox = "//button[@role='combobox']"
personal_key_custom_time_button = "//div[@role='dialog']//div[@data-testid='kui-box']"
personal_key_custom_time_year_month_button = "//div[@data-testid='kui-datepicker-calendar']//button[@role='combobox']"
personal_key_service_included_fillbox = "//label[text()='Services Included']/../..//input"
personal_Key_warnning_sign = "//td[text()='{0}']/..//*[local-name()='svg' and namespace-uri()='http://www.w3.org/2000/svg' and @data-icon-name='common-warning']"
generate_personal_key_confirm_button = "//div[@role='dialog']//button[text()='Generate Personal Key']"
update_personal_key_confirm_button = "//div[@role='dialog']//button[text()='Update Authorizations']"
generate_personal_key_dialog_close_button = "//div[@role='dialog']/div/button"
update_personal_key_close_button = "//div[@role='dialog']/div/button"
update_personal_key_success_message = "//div[@data-testid='ngc-toast']/div/span"
personal_keys_page_numbers = "//input[@class='page-input']/../span"
cur_page_key_numbers_and_total_sum = "//div[@data-testid='kui-table']/div//div[contains(text(), 'Showing')]"
filter_columns_icon = "//div[contains(text(), 'Showing')]/..//button"
personal_key_title = "//h2[text()='Personal Keys']"
personal_Keys_table = "//table"
personal_keys_table_header_cells = "//thead/tr/th"
personal_keys_table_actions_button = "//tbody/tr[1]/td/button"
personal_key_active_button = "//button[@role='switch']"
delete_personal_key_button = "//div[text()='Delete Personal Key']"
update_personal_key_button = "//div[text()='Update Authorizations']"
delete_personal_key_double_confirm_button = "//button[normalize-space()='Delete Personal Key']"
no_personal_keys_configured_title = "//h2[text()='No Personal Keys Configured']"
first_page = "//div[@data-testid='paginationNavigation']/button[1]"
prev_page = "//div[@data-testid='paginationNavigation']/button[2]"
next_page = "//div[@data-testid='paginationNavigation']/button[3]"
last_page = "//div[@data-testid='paginationNavigation']/button[4]"

[ServiceKey]
service_key_entry = "//button//a[text()='Service Keys']"
create_service_key_button = "//div[@class='ngc-layout-header']//button"
service_key_name_fillbox = "//label[text()='Name']/../..//input"
service_key_service_fillbox = "//div[text()='Select service']/..//input"
service_key_select_service = "//div[text()='Select service']"
service_key_scope_fillbox = "//div[text()='Select one or more scopes']/..//input"
service_key_entity_type_fillbox = "//div[text()='Select entity type']/..//input"
service_key_entity_value_fillbox = "//div[text()='Select entity value']/..//input"
service_key_next_step_button = "//button[text()='Next Step']"
service_key_cancel_button = "//button[text()='Cancel']"
service_key_confirm_button = "//button[text()='Confirm']"
create_service_key_dialog_close_button = "//div[@role='dialog']/div/button"
service_Keys_table = "//div[@role='table']"
no_service_keys_configured_title = "//h2[text()='No Service Keys Configured']"
service_key_title = "//h2[text()='Service Keys']"
service_keys_table_actions_button = "//div[@role='table']/div[@role='row'][2]//button[not(@role='switch')]"
delete_service_key_button = "//span[text()='Delete Service Key']"
update_service_key_button = "//span[text()='Update Authorizations']"
delete_service_key_double_confirm_button = "//button[normalize-space()='Delete Service Key']"
service_key_dialog_close_button = "//div[@role='dialog']/div/button"
update_service_key_next_step_button = "//div[@role='dialog']//button[text()='Next Step']"
update_service_key_Expiration_fillbox = "//label[text()='Expiration']/../..//input"
service_key_active_button = "//button[@role='switch']"
success_toast_message = "//div[@data-testid='ngc-toast']/div/span"
service_keys_detail_actions_button = "//label[text()='SERVICE KEY ACTIVE']/../../button"
delete_service_key_from_detail_button = "//div[@role='menuitem'][text()='Delete Service Key']"
service_keys_table_header_cells = "//div[@role='table']//div/div[@role='columnheader']"
invalid_key_name_warning = "//span[contains(text(), 'Key name cannot start with')]"
invalid_update_authorizations_warning = "//div[contains(text(), 'Cannot Update Authorizations')]"
deactivate_warning = "//div[@data-state='delayed-open']"
rotate_service_key_warning = "//div[contains(text(), 'Rotate Service Key Warning')]"
delete_service_key_warning = "//div[contains(text(), 'Cannot Delete Service Key')]"
rotate_byoc_service_key_warning = "//div[text()='Cannot Rotate Service Key Here']"

[Organization.Users.InviteNewUser]
create_user_btn = "//button[text()='Create User' or text()='Invite User' or text()='Add User']"
add_role_btn = "//button//span[text()='Add Role']"
first_name_input = "//input[@name='firstName']"
last_name_input = "//input[@name='lastName']"
email_input = "//input[@name='email']"
org_radio = "//input[@id='orgRadio'][@name='orgRadio']"
team_radio = "//input[@id='teamRadio'][@name='teamRadio']"
team_selector = "//div[@data-testid='kui-select']/div/input/../div"
team_list = "//div[@id='react-select-2-listbox']"
select_account_role_button_stg = "//button/div/span[text()='Select Role']"
user_invitation_send_button_stg = "//button[text()='Add User and Send Invitation']"

bc_role_group = "//div[@data-testid='base-command-platform-role-group']"
bca_role_radio = "//input[@value='BASE_COMMAND_ADMIN']"
bcu_role_radio = "//input[@value='BASE_COMMAND_USER']"
bcv_role_radio = "//input[@value='BASE_COMMAND_VIEWER']"

[Organization.Teams]
create_team_button = "//div[@class='ngc-layout-header']//span[text()='Create Team']"
invite_user_btn = "//button//span[text()='Invite New User']"
team_table = "//div[@role='table']"
team_items = "//div[@role='row']"
per_page_numer = "//input[@class='css-1hac4vs-dummyInput']"
current_page = "//input[@class='page-input']"
max_page = "//div[@data-testid='page-state']/input"
first_page = "//div[@data-testid='paginationNavigation']/button[1]"
previous_page = "//div[@data-testid='paginationNavigation']/button[2]"
next_page = "//div[@data-testid='paginationNavigation']/button[3]"
last_page = "//div[@data-testid='paginationNavigation']/button[4]"

[Organization.Teams.CreateTeam]
create_team_dialog = "//div[@class='modal-title'][text()='Create Team']/../.."
name_input = "//div[@class='modal-content']//input[@placeholder='Enter team name']"
desc_input = "//div[@class='modal-content']//input[@placeholder='Describe this team']"
close_icon = "//div[@class='modal-title'][text()='Create Team']/../button"
create_team_button = "//div[@class='modal-footer']//button//span[text()='Create Team']"
cancel_button = "//div[@class='modal-footer']//button[text()='Cancel']"

[Organization.Teams.Details]
actions_button = "//div[@data-testid='kui-page-header-actions']/div/div/div/button"
delete_team_button = "//button/span[text()='Delete Team']"
delete_confirm_dialog = "//div[@class='modal-title'][text()='Delete Team']/../.."
delete_confirm_button = "//div[@data-testid='kui-modal']//button//span[text()='Delete Team']"
delete_cancel_button = "//div[@data-testid='kui-modal']//button//span[text()='Cancel']"
delete_close_icon = "//div[@class='modal-title']//button"
