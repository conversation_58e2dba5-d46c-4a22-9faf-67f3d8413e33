[SideBar]
# For all entry of Cloud Functions
Functions = "//a[text()='Functions']"
CreateFunction = "//a[text()='Create Function']/../parent::button"
Settings = "//a[text()='Settings']"

[NavigationBar]
Title = "//h2"

[Deployments.ListPage]
DeployFuncVersBtn = "//button[text()='Deploy Version']"
GenerateKeyButton = "//button[text()='Generate Personal API Key']"
KeyNameInput = "//label[text()='Key Name']/../..//input[@name='name']"
GenerateKeyConfirmButton = "//button[text()='Generate Personal API Key' and @form = 'generate-personal-key-form']"
CopyKeyAndClose = "//button[text()='Copy Key and Close']"
TotalInvocationsTitle= "//span[text()='Total Invocations']"
KeyExpirationBox = "//label[text()='Expiration']/../..//span[contains(text(), '12 months')]"
KeyExpirationDropDownSelector = "//label[text()='Expiration']/../..//*[@data-icon-name='shapes-chevron-down']"
SelectEntrybyName = "//div[@data-testid='kui-select-item']/span[text()='{0}']"
SearchBarInput = "//button[contains(text(),'Refresh')]/preceding-sibling::div//input"
FirstFuncVersIDinTablebyFuncVersID = "//tr[1]/td[3][text()='{0}']"
DeploymentsItems = "//table/tbody/tr"
RefreshBtn = "//button[@aria-label='Refresh']"
MyFunctionsBtn = "//button[text()='My Functions']"
SharedFunctionsBtn = "//button[text()='Shared Functions']"
CreateByNVIDIABtn = "//button[text()='Create by NVIDIA']"
TableItems = "//table//tbody//tr"
EditDeploymentBtn = "//div[@role='menuitem' and text()='Edit Deployment']"
CancelDeployBtn = "//div[@role='menuitem' and text()='Cancel Deployment']"
ViewFunctionDetailsBtn = "//div[@role='menuitem' and text()='View Function Details']"
ViewVersionDetailsBtn = "//div[@role='menuitem' and text()='View Version Details']"
NextPageButton = "//div[@data-testid='kui-pagination-number-range']/button[2]"

[Deployments.CreatePage]
DeployVersionBtn = "//button[text()='Deploy Version']"
ReviewDeployBtn = "//button[text()='Review Deployment']"
FuncNameInput = "(//div[contains(@class, 'c-cKCGFj')]//input)[1]"
FirstSelectEntry = "//div[@role='listbox']/div[1]"
FuncVersionInput = "(//div[contains(@class, 'c-cKCGFj')]//input)[last()]"
GPUTypeCheckInputbyName = "//input[@value='{0}']"
InstanceTypeExpandBtn = "//span[text()='Instance Type Settings']/../parent::button"
DeploymentSpecBtn = "//span[text()='Deployment Specifications']/../parent::button"
TableItems = "//table//tbody//tr"
RefreshBtn = "//button[@aria-label='Refresh']"
InstanceTypeTableRightArrowBtn = "//*[name()='svg' and @data-icon-name='shapes-chevron-right']/parent::button"
InstanceTypeRegionsFilterSelectorBeforeChoose = "//label[text()='Regions']/following-sibling::div//div[text()='Any region']"
InstanceTypeRegionsFilterSelector = "(//label[text()='Target Regions']/parent::div/following-sibling::div//*[@data-icon-name='shapes-chevron-down'])[1]"
InstanceTypeClustersFilterSelectorBeforeChoose = "//label[text()='Clusters']/following-sibling::div//div[text()='Any Cluster']"
InstanceTypeClustersFilterSelector = "(//label[text()='Clusters']/parent::div/following-sibling::div//*[@data-icon-name='shapes-chevron-down'])[2]"
InstanceTypeAttributesFilterSelectorBeforeChoose = "//span[starts-with(text(), 'Filter and select')]/following-sibling::div//label[text()='Attributes']/following-sibling::div//div[text()='Any attributes']"
InstanceTypeAttributesFilterSelector = "//label[starts-with(text(), 'Clusters')]/../..//following-sibling::div//label[text()='Attributes']/../following-sibling::div//*[@data-icon-name='shapes-chevron-down']"
MultiDropDownSelectEntries = "//div[@aria-multiselectable='true']/div/div"
MultiDropDownSelectEntryByName = "//div[text()='{0}']/parent::div"
RegionSelectorInDeploymentSpecbyInstanceBeforeChoose = "//span/../following-sibling::div[1]//div[text()='Any region']"
RegionSelectorInDeploymentSpecbyInstance = "(//span[starts-with(text(), '{0}')]/../following-sibling::div[1]//*[@data-icon-name='shapes-chevron-down'])[1]"
ClusterSelectorInDeploymentSpecbyInstanceBeforeChoose = "//span/../following-sibling::div[1]//div[text()='—']"
ClusterSelectorInDeploymentSpecbyInstance = "(//span[starts-with(text(), '{0}')]/../following-sibling::div[1]//*[@data-icon-name='shapes-chevron-down'])[2]"
MinInstanceInputInDeploymentSpecbyInstance = "//span[starts-with(text(), '{0}')]/../following-sibling::div[3]//input"
MaxInstanceInputInDeploymentSpecbyInstance = "//span[starts-with(text(), '{0}')]/../following-sibling::div[4]//input"
MaxConcurrencyInputInDeploymentSpecbyInstance = "//span[starts-with(text(), '{0}')]/../following-sibling::div[5]//input"
AttributeSelectorInDeploymentSpecbyInstanceBeforeChoose = "//span/../following-sibling::div[6]//div[text()='Any attributes']"
AttributeSelectorInDeploymentSpecbyInstance = "(//span[starts-with(text(), '{0}')])[2]/../following-sibling::div[2]//*[@data-icon-name='shapes-chevron-down']"
FunctionVersionTextInReviewPage = "//label[text()='Function Version']/following-sibling::span"
DeployFunctionBtn = "//button[text()='Deploy Function Version']"
DeployFuncVersBtn = "//button[text()='Deploy Version']"
DeployFuncVersEnableBtn = "//button[@id='deploy-button' and not(@disabled)]"
EditConfigurationBtn = "//button[text()='Edit Configuration']"
AnyBanner = "//div[@data-testid='ngc-toast']"
DeployingBanner = "//span[contains(text(), 'is being deployed')]"
FirstMaxConcurrencyInput = "//label[text()='Max Concurrency']/../following-sibling::div/input"
MaxConcurrencyTextinReviewPage = "//label[text()='Max Concurrency']/following-sibling::span"
HelmChartOverridesExpandBtn = "//span[starts-with(text(), 'Helm Chart')]/../parent::button"
HelmChartOverridesInput = "//div[@contenteditable='true']"
FirstMaxInstancesInput = "//label[text()='Max Instances']/../following-sibling::div/input"
ErrorHintText = "//label[text()='Max Instances']/../../following-sibling::span"

[Deployments.EditPage]
EditDeploymentTitle = "//div[text()='Edit Deployment']"
FirstMaxConcurrencyLabel = "(//label[text()='Max Concurrency'])[1]"
FuncNameText = "//label[text()='Function Name']/following-sibling::span"
FuncIDText = "//label[text()='Function ID']/following-sibling::span"
FuncVersionText = "//label[text()='Version Name & ID']/following-sibling::span"
DeploymentItem = "//span[contains(text(),'Selected Instance Type Settings')]/../following-sibling::div"
InstanceTypeText = "//span[@data-testid='kui-text' and contains(@class, 'font-medium')]"
GPUText = "//label[text()='GPU']/following-sibling::span"
DedicatedClusterText = "//label[text()='Dedicated Clusters']/following-sibling::*//span"
BackendText = "//label[text()='Backend']/following-sibling::span"
MinInstanceInput = "//label[text()='Min Instances']/../following-sibling::div/input"
MaxInstanceInput = "//label[text()='Max Instances']/../following-sibling::div/input"
MaxConcurrencyInput = "//label[text()='Max Concurrency']/../following-sibling::div/input"
MinInstanceLargeError = "//label[text()='Min Instances']/../../..//span[contains(text(),'Cannot be more than max instances')]"
MaxInstanceSmallError = "//label[text()='Max Instances']/../../..//span[contains(text(),'Cannot be less than')]"
MaxInstanceExceedError = "//label[text()='Max Instances']/../../..//span[contains(text(),'You will be over your allocation by')]"
SaveChangesBtn = "//button[text()='Save Changes']"
CancelBtn = "//button[text()='Cancel']"
AnyBanner = "//div[@data-testid='ngc-toast']"
EditDeploymmentBanner = "//span[contains(text(), 'successfully updated')]"
TotalMaxConcurrencyText = "//label[text()='Total Max Concurrency']/following-sibling::span"
MaxConcurrencyText = "//label[text()='Max Concurrency']/following::div[@data-testid='kui-text-input-container']//input"
DeployVersionBtn = "//button[text()='Deploy Version']"
