[NavigationBar]
Title = "//h2"

[Tasks.ListPage]
CreateTaskBtn = "//a[contains(@class, 'c-TKGAm') and text()='Create Task']"
TasksTitle = "h2.c-kAArxJ[data-testid='kui-text']"

[Tasks.CreatePage]
CreateTaskBtn = "//button[text()='Create Task']"

[CreateTasks.Form]
NameInput = "//input[@name='name']"
ContainerInput = "//label[text()='Container']/../..//div[text()='Select a container']/..//input"
TagInput = "//label[text()='Tag']/../..//div[text()='Select a tag']/..//input"
SelectorChoosebyName = "//div[text()='{0}']"
ModelExpandBtn = "//button[.//span[text()='Select Model(s)']]"
ResourceExpandBtn = "//button[.//span[text()='Select Resource(s)']]"
SecretsExpandBtn = "//button[.//span[text()='Secrets']]"
RunCommandExpandBtn = "//button[.//span[text()='Container Run Command']]"
EnvExpandBtn = "//button[.//span[text()='Environment Variables']]"

ModelInput = "//label[text()='Model']/../..//div[text()='Select a model']/..//input"
ModelVersionInput = "//label[text()='Model Version']/../..//div[text()='Select a model version']/..//input"
ModelNameInput = "(//label[text()='Name']/../..//input[contains(@name, 'models')])"
AddModelButton = "//button[contains(., 'Add Another Model')]"

ResourceInput = "//label[text()='Resource']/../..//div[text()='Select a resource']/..//input"
ResourceVersionInput = "//label[text()='Resource Version']/../..//div[text()='Select a resource version']/..//input"
ResourceNameInput = "//label[text()='Name']/../..//input[contains(@name, 'resources')]"
AddResourceButton = "//button[contains(., 'Add Another Resource')]"

AddEnvButton = "//button[contains(., 'Add Another Environment Variable')]"
EnvKeyInputBySeq = "//input[starts-with(@name, 'containerEnvironment') and substring(@name, string-length(@name) - string-length('key') + 1) = 'key']"
EnvValueInputBySeq = "//input[starts-with(@name, 'containerEnvironment') and substring(@name, string-length(@name) - string-length('value') + 1) = 'value']"

AddSecButton = "//button[contains(., 'Add Secret')]"
SecKeyInputBySeq = "//input[starts-with(@name, 'secrets') and substring(@name, string-length(@name) - string-length('name') + 1) = 'name']"
SecValueInputBySeq = "//textarea[@data-testid='kui-text-area-element']"

RunCommandTextArea = "//textarea[@name='containerArgs']"
GpuTypeInput = "//label[text()='GPU Type']/../..//div[text()='Select a GPU type']/..//input"
InstanceTpyeInput = "//label[text()='Instance Type']/../..//div[text()='Select an instance type']/..//input"
ClustersInput = "//div[text()='Select a cluster(s)']/parent::*/following-sibling::div"


MaxRuntimeDurationBox = "//label[text()='Maximum Runtime Duration']/../..//span[contains(text(), 'runs forever')]"
MaxQueDurationBox = "//label[text()='Maximum Queued Duration']/../..//span[contains(text(), '72 hours')]"
TerminationGraceDurationBox = "//label[text()='Termination Grace Period Duration']/../..//span[contains(text(), '1 hour')]"

ResultsUploadNoneChoose = "#resultHandlingStrategyNone"
ResultsUploadModelNameInput = "//input[@name='resultLocationModelName']"
ApiKeyInput = "//input[@name='apiKey']"
GenerateKeyButton = "//button[text()='Generate Personal API Key']"
KeyNameInput = "//label[text()='Key Name']/../..//input[@name='name']"
KeyExpirationBox = "//label[text()='Expiration']/../..//span[contains(text(), '12 months')]"
GenerateKeyConfirmButton = "//button[text()='Generate Personal API Key' and @form = 'generate-personal-key-form']"
CopyKeyAndClose = "//button[text()='Copy Key and Close']"

CreateTaskBtn = "//button[text()='Create Task']"
TaskCreateBanner = "//span[contains(text(), 'succesfully created')]"
SelectEntrybyName = "//div[@data-testid='kui-select-item']/span[text()='{0}']"
SelectedbyName = "//span[text()='{0}']"
TaskCreateBannerNonForMaxruntimeGFN = "//span[contains(text(), 'Upstream Spot Instance Service responded with status code') and contains(text(), '400')]"
TaskCreateBannerGraceperiodlargerthanmaxruntimeGFN = "//span[contains(text(), 'terminationGracePeriodDuration') and contains(text(), 'cannot exceed') and contains(text(), 'maxRuntimeDuration')]"
TaskCreateBannerKeyWithoutPROrExpiredKey = "//span[contains(text(), 'Cannot create results') and contains(text(), 'checkpoints using the specified secret NGC_API_KEY in the org') and contains(text(), 'resultsLocation') and contains(text(), '403')]"
TaskCreateBannerNegativeModelName = "//span[contains(text(), 'Model name must begin with a lowercase alpha, and following characters must be alphanumeric. Use periods, underscores or hyphens to separate words')]"
TaskCreateBannerHelmRevalService = "//div[text()='Task Cannot Be Created']"