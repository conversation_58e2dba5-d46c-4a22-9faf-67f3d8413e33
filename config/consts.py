from pathlib import Path
from cloudia.utils.file_handler import file_handler, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from cloudia.utils.env_service import get_current_env

CURRENT_PATH = Path(__file__).parent.resolve()
WORKSPACE_PATH = Path(".").resolve()
TEST_DATA_PATH = Path(CURRENT_PATH.parent.resolve(), "data")
ElE_CONF_PATH = Path(CURRENT_PATH, "elements/elements.toml")
MARKERS_DIR = Path(WORKSPACE_PATH, "config/markers/custom_markers.toml")

CURRENT_ENV = get_current_env() or "staging"

if CURRENT_ENV == "staging":
    NCA_ID = "taLfaNPSpq7LV7vM1ueLJ2526IsX3be1MAjZ58GCQoc"
else:
    NCA_ID = "e15g_kOXsdLwXAA4qA4LJ-QCHSv1X0Xozto1VTsaoa4"

if CURRENT_ENV == "staging":
    API_KEY_LIST = [
        "nvcf_admin",
        "nvcf_gmail_owner",
        "grafana_apikey",
        "datadog_apikey",
        "datadog_appkey",
        "servicenow_key",
        "lightstep_apikey",
        "azure_monitor_application_id",
        "azure_monitor_instrumentation_key",
        "kratos_thanos_clientkey",
        "kratos_thanos_clientcert",
        "prometheus_remote_write_clientkey",
        "prometheus_remote_write_clientcert",
        "prometheus_remote_write_cafile",
    ]
else:
    API_KEY_LIST = [
        "nvcf_admin",
        "grafana_apikey",
        "datadog_apikey",
        "datadog_appkey",
        "servicenow_key",
        "lightstep_apikey",
        "azure_monitor_application_id",
        "azure_monitor_instrumentation_key",
        "kratos_thanos_clientkey",
        "kratos_thanos_clientcert",
        "prometheus_remote_write_clientkey",
        "prometheus_remote_write_clientcert",
        "prometheus_remote_write_cafile",
    ]

if CURRENT_ENV == "staging":
    EMAIL_LIST = [
        "nvcf_admin",
        "nvcf_viewer",
        "nvcf_admin_pr_user",
        "grafana_metrics",
        "grafana_logs",
        "grafana_traces",
        "nvcf_gmail_owner",
    ]
else:
    EMAIL_LIST = [
        "nvcf_admin",
        "nvcf_viewer",
        "nvcf_admin_pr_user",
        "grafana_metrics",
        "grafana_logs",
        "grafana_traces",
    ]

SSA_HOST_LIST = [
    "ssa_host_info",
    "nvct_ssa_info",
    "telemetry_ssa_info",
    "kratos_thanos_ssa_info",
]

if CURRENT_ENV == "staging":
    API_KEY_LIST += [
        "nvcf_admin_without_pr",
        "nvcf_admin_expired",
        "nvcf_admin_alternative",
    ]
if CURRENT_ENV == "canary" or CURRENT_ENV == "production":
    API_KEY_LIST += [
        "nvcf_admin_without_pr",
        "nvcf_admin_expired",
        "nvcf_admin_alternative",
        "nvcf_without_private_registry_scope",
    ]


# Plase get the current env from this const CURRENT_ENV
if CURRENT_ENV == "staging":
    LOCAL_CONFIG = TomlHandler(Path("data", "config_data", "config_data_stg.toml")).read()
elif CURRENT_ENV == "canary":
    LOCAL_CONFIG = TomlHandler(
        Path("data", "config_data", "config_data_canary.toml")
    ).read()
elif CURRENT_ENV == "production":
    LOCAL_CONFIG = TomlHandler(Path("data", "config_data", "config_data_prod.toml")).read()

STG_ORGS = ["tadiathdfetp", "0737195995831534"]
CANARY_ORGS = ["rw983xdqtcdp"]
PRODUCTION_ORGS = ["rw983xdqtcdp"]

DEAFULT_TEST_ORG = {
    "staging": STG_ORGS[0],
    "canary": CANARY_ORGS[0],
    "production": PRODUCTION_ORGS[0],
}

DEAFULT_TEST_ORG_SECONDARY = {
    "staging": STG_ORGS[1],
    "canary": CANARY_ORGS[0],
    "production": PRODUCTION_ORGS[0],
}

# Please get the current org from this const CURRENT_ORG
CURRENT_ORG = DEAFULT_TEST_ORG.get(CURRENT_ENV)
CURRENT_ORG_SECONDARY = DEAFULT_TEST_ORG_SECONDARY.get(CURRENT_ENV)

NGC_REGISTRY_DOMAIN = {
    "staging": "stg.nvcr.io",
    "canary": "canary.nvcr.io",
    "production": "nvcr.io",
}

ELE_CONF_PATH_CF = Path(CURRENT_PATH, "elements/elements_cf.toml")
ELE_CONF_PATH_DP = Path(CURRENT_PATH, "elements/elements_dp.toml")
ELE_CONF_PATH_TASKS = Path(CURRENT_PATH, "elements/elements_tasks.toml")
ELE_CONF_PATH_STREAMING = Path(CURRENT_PATH, "elements/elements_streaming_client.toml")

TEST_DATA_PATH_CF = Path(TEST_DATA_PATH, "test_data_cf.yaml")
TEST_DATA_PATH_TASKS = Path(TEST_DATA_PATH, "test_data_tasks.yaml")
TEST_DATA_PATH_SETTINGS = Path(TEST_DATA_PATH, "test_data_settings.yaml")
TEST_DATA_CF = file_handler(TEST_DATA_PATH_CF).read()
FUNC_INVOC_DATA_PATH = Path(TEST_DATA_PATH, "function_invocation.yaml")
FUNCTION_INVOCATION_DATA = YamlHandler(FUNC_INVOC_DATA_PATH).read()

GPU_GXCACHE_SUPPORTED_LIST = ["L40", "AD102GL"]
GPU_KATA_SUPPORTED_LIST = ["L40", "AD102GL"]

# for general
GLOBAL_TIMEOUT = 60000

DEFAULT_VIEWPORT_SIZE = {
    "width": 1920,
    "height": 1080,
}

DEFAULT_PERMISSIONS = ["clipboard-read", "clipboard-write"]

# The NVCloud Functions const names
SIDEBAR_APP_CLOUDFUNCTIONS = "Cloud Functions"
SIDEBAR_CLOUDFUNCTIONS_ENTRY_FUNCTIONS = "Functions"
SIDEBAR_CLOUDFUNCTIONS_ENTRY_CREATE_FUNCTION = "Create Function"
SIDEBAR_CLOUDFUNCTIONS_ENTRY_DEPLOYMENTS = "Deployments"
SIDEBAR_CLOUDFUNCTIONS_ENTRY_SETTINGS = "Settings"
SIDEBAR_CLOUDFUNCTIONS_ENTRY_TASKS = "Tasks"
SIDEBAR_CLOUDFUNCTIONS_ENTRY_GPU_CAPACITY = "GPU Capacity"

# The ORGANIZATION const names
SIDEBAR_APP_ORGANIZATION = "Organization"
SIDEBAR_ORGANIZATION_ENTRY_DASHBOARD = "Dashboard"
SIDEBAR_ORGANIZATION_ENTRY_AUDIT = "Audit"
SIDEBAR_ORGANIZATION_ENTRY_ORGANIZATION_PROFILE = "Organization Profile"
SIDEBAR_ORGANIZATION_ENTRY_TEAMS = "Teams"
SIDEBAR_ORGANIZATION_ENTRY_USAGE = "Usage"
SIDEBAR_ORGANIZATION_ENTRY_USERS = "Users"
SIDEBAR_ORGANIZATION_ENTRY_ACTIVATE_SUBSCRIPTION = "Activate Subscription"
SIDEBAR_ORGANIZATION_ENTRY_SUBSCRIPTIONS = "Subscriptions"
SIDEBAR_ORGANIZATION_ENTRY_SERVICE_KEYS = "Service Keys"
SIDEBAR_ORGANIZATION_ENTRY_EXTERNAL_IDP = "External IdP"

# The Console const names
SIDEBAR_APP_CONSOLE = "Console"
SIDEBAR_CONSOLE_ENTRY_EXPLORE = "Explore"

# The mappings about app and entry
APP_ENTRY_URL_MAPPING = {
    SIDEBAR_APP_CLOUDFUNCTIONS: {
        "HOSTNAME": "nvcf",
        "ENTRY": {
            SIDEBAR_CLOUDFUNCTIONS_ENTRY_FUNCTIONS: "functions",
            SIDEBAR_CLOUDFUNCTIONS_ENTRY_CREATE_FUNCTION: "create",
            SIDEBAR_CLOUDFUNCTIONS_ENTRY_SETTINGS: "settings",
        },
    },
    SIDEBAR_APP_ORGANIZATION: {
        "HOSTNAME": "org",
        "ENTRY": {
            SIDEBAR_ORGANIZATION_ENTRY_DASHBOARD: "",
            SIDEBAR_ORGANIZATION_ENTRY_AUDIT: "audit",
            SIDEBAR_ORGANIZATION_ENTRY_ORGANIZATION_PROFILE: "profile",
            SIDEBAR_ORGANIZATION_ENTRY_TEAMS: "teams",
            SIDEBAR_ORGANIZATION_ENTRY_USERS: "users",
            SIDEBAR_ORGANIZATION_ENTRY_SERVICE_KEYS: "service-keys",
            SIDEBAR_ORGANIZATION_ENTRY_EXTERNAL_IDP: "external-idp",
            SIDEBAR_ORGANIZATION_ENTRY_ACTIVATE_SUBSCRIPTION: "activate",
            SIDEBAR_ORGANIZATION_ENTRY_SUBSCRIPTIONS: "subscriptions",
        },
    },
    SIDEBAR_APP_CONSOLE: {
        "HOSTNAME": "console",
        "ENTRY": {
            SIDEBAR_CONSOLE_ENTRY_EXPLORE: "",
        },
    },
}


# nvct telemetry names
LOGS_METRICS_GRPC_GRAFANA_TELEMETRY_ID = "logs_metrics_grpc_Grafana_nvct_daily_run"

LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID = "logs_metrics_http_Grafana_nvct_daily_run"

LOGS_METRICS_GRPC_DATADOG_TELEMETRY_ID = "logs_metrics_grpc_Datadog_nvct_daily_run"

LOGS_METRICS_HTTP_DATADOG_TELEMETRY_ID = "logs_metrics_http_Datadog_nvct_daily_run"

METRICS_HTTP_THANOS_TELEMETRY_ID = "metrics_http_Thanos_nvct_daily_run"

METRICS_GRPC_THANOS_TELEMETRY_ID = "metrics_grpc_Thanos_nvct_daily_run"

if CURRENT_ENV == "staging":
    NCA_ID = "taLfaNPSpq7LV7vM1ueLJ2526IsX3be1MAjZ58GCQoc"
elif CURRENT_ENV == "production" or CURRENT_ENV == "canary":
    NCA_ID = "e15g_kOXsdLwXAA4qA4LJ-QCHSv1X0Xozto1VTsaoa4"
