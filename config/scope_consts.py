NVCF_SCOPE_ALL = {
    "register_function": True,
    "update_function": True,
    "delete_function": True,
    "list_functions": True,
    "deploy_function": True,
    "invoke_function": True,
    "queue_details": True,
    "authorize_clients": True,
    "list_functions_details": True,
    "update_secrets": True,
}

NVCT_SCOPE_ALL = {
    "list_results": True,
    "task_details": True,
    "delete_task": True,
    "list_events": True,
    "cancel_task": True,
    "list_tasks": True,
    "update_secrets": True,
    "launch_task": True,
}

NVCT_ADMIN_SCOPE_ALL = {
    "admin:cancel_task ": True,
    "admin:delete_task": True,
    "admin:launch_task": True,
    "admin:list_events": True,
    "admin:list_results": True,
    "admin:list_tasks": True,
    "admin:task_details ": True,
    "admin:update_secrets": True,
}

TELEMETRY_SCOPE_ALL = {
    "manage_telemetries": True,
    "register_function": True,
    "update_function": True,
    "delete_function": True,
    "list_functions": True,
    "deploy_function": True,
    "invoke_function": True,
    "queue_details": True,
    "update_secrets": True,
    "list_functions_details": True,
    "account_setup": True,
}


TELEMETRY_ADMIN_SCOPE_ALL = {
    "admin:manage_telemetries ": True,
    "admin:update_secrets": True,
    "admin:register_function": True,
    "admin:list_functions_details": True,
    "account_setup": True,
}

BYOO_KRATOS_THANOS_SCOPE_ALL = {
    "data-retrieve": True,
}
