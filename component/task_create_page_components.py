from component.components import NavigationBar, Form, PageMisc, DropDownSelector
from element.tasks_create_page_elements import (
    TasksCreatePageMiscElements,
    TasksCreatePageMaximumRuntimeDurationDropDownSelectorElements,
    TasksCreatePageMaximumQueuedDurationDropDownSelectorElements,
    TasksCreatePageTerminationGracePeriodDropDownSelectorElements,
    TasksCreatePageKeyExpirationDropDownSelectorElements,
    TaskCreateOptionENUM,
    CreateTaskFormElements,
)
from playwright.sync_api import Page, expect
from typing import Optional, Dict
import logging
import json
from config.consts import CURRENT_ENV


class TasksCreateNavigationBar(NavigationBar):
    """Elements and related operation for Navigation Bar on Clone Function Page."""

    title = "//h2[starts-with(text(),'Create Task')]"

    def __init__(self, page: Page):
        super().__init__(page)


class TasksCreatePageMisc(PageMisc):
    """Contains all the elements and operations of the side bar on the Functions List Page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, TasksCreatePageMiscElements())


class TasksCreatePageMaximumRuntimeDurationDropDownSelector(DropDownSelector):
    """Elements and related functionalities of the regions selector.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(
            page, TasksCreatePageMaximumRuntimeDurationDropDownSelectorElements()
        )


class TasksCreatePageMaximumQueuedDurationDropDownSelector(DropDownSelector):
    """Elements and related functionalities of the regions selector.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(
            page, TasksCreatePageMaximumQueuedDurationDropDownSelectorElements()
        )


class TasksCreatePageTerminationGracePeriodDropDownSelector(DropDownSelector):
    """Elements and related functionalities of the regions selector.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(
            page, TasksCreatePageTerminationGracePeriodDropDownSelectorElements()
        )


class TasksCreatePageKeyExpirationDropDownSelector(DropDownSelector):
    """Elements and related functionalities of the regions selector.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, TasksCreatePageKeyExpirationDropDownSelectorElements())


class TasksCreatePageForm(Form):
    """Elements and related operation for Form on Tasks Create Page."""

    def __init__(self, page: Page):
        super().__init__(page, CreateTaskFormElements())
        self.MaximumRuntimeDurationDropDownSelector = (
            TasksCreatePageMaximumRuntimeDurationDropDownSelector(page)
        )
        self.MaximumQueuedDurationDropDownSelector = (
            TasksCreatePageMaximumQueuedDurationDropDownSelector(page)
        )
        self.TerminationGracePeriodDropDownSelector = (
            TasksCreatePageTerminationGracePeriodDropDownSelector(page)
        )
        self.KeyExpirationDropDownSelector = TasksCreatePageKeyExpirationDropDownSelector(
            page
        )

    def create_task(
        self,
        name: str,
        container: str,
        tag: str,
        gpu_type: str,
        instance_type: str,
        upload_results: bool = True,
        taskCreateOption: TaskCreateOptionENUM = TaskCreateOptionENUM.CREATTASKSUCCESS,
        clusters: Optional[str] = None,
        maximum_runtime_duration: Optional[str] = None,
        maximum_queued_duration: Optional[str] = None,
        termination_grace_period_duration: Optional[str] = None,
        ResultsUpload_model_name: Optional[str] = None,
        Personal_key: Optional[str] = None,
        model: Optional[list] = None,
        resource: Optional[list] = None,
        secrets: Optional[Dict] = None,
        env: Optional[Dict] = None,
        command: Optional[str] = None,
        generate_personal_key: Optional[bool] = False,
        generate_person_key_list: Optional[list] = None,
        telemetry_logs: Optional[str] = None,
        telemetry_metrics: Optional[str] = None,
        telemetry_traces: Optional[str] = None,
    ):
        """Create a new container function.

        parameters:
        -----------
        name : `str`
            The name of the task.

        container : `str`
            The container in the task.

        tag : `str`
            The tag of container in the task.

        gpu_type : `str`
            The gpu_type this task will be deployed.

        instance_type : `str`
            The instance_type this task will be deployed.

        upload_results: 'bool', default is Ture.
            True: Upload results to NGC private registry.
            False: Don't upload results to NGC private registry.

        taskCreateOption: 'TaskCreateOptionENUM', default is TaskCreateOptionENUM.CREATTASKSUCCESS
            choose the specific TaskCreateOptionENUM for negative cases.

        maximum_runtime_duration: 'str' optional
            maximum_runtime_duration set in UI

        maximum_queued_duration:'str' optional
            maximum_queued_duration set in UI

        termination_grace_period_duration:'str' optional
            termination_grace_period_duration set in UI

        ResultsUpload_model_name:'str' optional
            If set upload_results = Ture, need to configure ResultsUpload_model_name

        Personal_key:'str' optional
            Use existing key to fill in UI

        model : `list`, optional
            Model info as a list, each element is a list of model, model_version and model_name, default is None.

        resource: `list`, optional
            Resource info as a list, each element is a list of model, model_version and model_name, default is None.

        secrets: `Dict`, optional
            Secrets info as a dict.

        env : `Dict`, optional
            Environments, key value pairs, default is None.

        command : `str`, optional
            The run command of the task, default is None.

        generate_personal_key: 'bool', optional
            If need to generate key in the create task page, set it as True.
            Default is False

        generate_person_key_list: 'list', optional
            If need to generate key in the create task page. provide the generate_person_key_list.
        Default is None
        Returns:
        --------
        None
        """
        logging.info(f"Creating Task {name}...")

        # Fill Function Name
        self.page.locator(self.elements.NameInput).fill(name)

        # Select Container and Tag
        self.page.locator(self.elements.ContainerInput).fill(container)
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(container)).click()
        self.page.locator(self.elements.TagInput).fill(tag)
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(tag)).click()

        # Select Models
        if model:
            self.page.locator(self.elements.ModelExpandBtn).click()
            self.page.wait_for_load_state("load")
            model_count = len(model)
            mode_seq = 1
            for model_entry in model:
                self.page.locator(self.elements.ModelInput).fill(model_entry[0])
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(model_entry[0])
                ).nth(-1).click()
                self.page.locator(self.elements.ModelVersionInput).fill(model_entry[1])
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(model_entry[1])
                ).nth(-1).click()
                self.page.locator(self.elements.ModelNameInput).nth(mode_seq - 1).fill(
                    model_entry[2]
                )
                if mode_seq == model_count:
                    break
                else:
                    self.page.locator(self.elements.AddModelButton).click()
                    mode_seq += 1

        # Select resource
        if resource:
            self.page.locator(self.elements.ResourceExpandBtn).click()
            self.page.wait_for_load_state("load")
            resource_count = len(resource)
            resource_seq = 1
            for resource_entry in resource:
                self.page.locator(self.elements.ResourceInput).fill(resource_entry[0])
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(resource_entry[0])
                ).nth(-1).click()
                self.page.locator(self.elements.ResourceVersionInput).fill(
                    resource_entry[1]
                )
                self.page.wait_for_load_state("load")
                self.page.wait_for_timeout(5000)

                # selector = "div.c-gHdmVn.css-1nmdiq5-menu"
                # element = self.page.locator(selector)
                # outer_html = element.evaluate("el => el.outerHTML")
                # print("outer_html:")
                # print(outer_html)
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(resource_entry[1])
                ).nth(-1).click()
                self.page.locator(self.elements.ResourceNameInput).nth(
                    resource_seq - 1
                ).fill(resource_entry[2])
                if resource_seq == resource_count:
                    break
                else:
                    self.page.get_by_role("button", name="Add Another Resource").click()
                    resource_seq += 1

        # Telemetry Logs
        if telemetry_logs or telemetry_metrics or telemetry_traces:
            self._expand_telemetry_section()
            if telemetry_logs:
                self._select_logs_endpoint(telemetry_logs)
            if telemetry_metrics:
                self._select_metrics_endpoint(telemetry_metrics)
            if telemetry_traces:
                self._select_traces_endpoint(telemetry_traces)

        # Set Secrets
        if secrets:
            self.page.locator(self.elements.SecretsExpandBtn).click()
            self.page.wait_for_load_state("load")
            secrets_count = len(secrets)
            secrets_seq = 1
            logging.info(f"secrets_count: {secrets_count}")
            for key, value in secrets.items():
                logging.info(f"key: {key}, value: {value}")
                self.page.locator(self.elements.SecKeyInputBySeq).nth(secrets_seq - 1).fill(
                    key
                )
                if isinstance(value, dict):
                    value = json.dumps(value)
                self.page.locator(self.elements.SecValueInputBySeq).nth(-1).fill(value)
                if secrets_seq == secrets_count:
                    break
                else:
                    self.page.get_by_role("button", name="Add Another Secret").click()
                    secrets_seq += 1

        # Container Run Command
        if command:
            self.page.locator(self.elements.RunCommandExpandBtn).click()
            self.page.wait_for_load_state("load")
            self.page.locator(self.elements.RunCommandTextArea).fill(command)

        # Set Environment
        if env:
            self.page.locator(self.elements.EnvExpandBtn).click()
            self.page.wait_for_load_state("load")
            env_count = len(env)
            env_seq = 1
            for key, value in env.items():
                self.page.locator(self.elements.EnvKeyInputBySeq).nth(-1).fill(key)
                self.page.locator(self.elements.EnvValueInputBySeq).nth(-1).fill(value)
                if env_seq == env_count:
                    break
                else:
                    self.page.locator(self.elements.AddEnvButton).click()
                    env_seq += 1

        # set GPU type and Instance Types
        self.page.locator(self.elements.GpuTypeInput).fill(gpu_type)
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(gpu_type)).click()
        self.page.locator(self.elements.InstanceTpyeInput).fill(instance_type)
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(instance_type)).click()

        # Select Clusters for non-gfn tasks
        if clusters:
            self.page.wait_for_load_state("networkidle")
            self.page.wait_for_timeout(1000)
            self.page.locator(self.elements.ClustersInput).click()
            self.page.wait_for_load_state("load")
            self.page.wait_for_timeout(5000)
            self.page.get_by_text(clusters).click()

        # set maximum_runtime_duration,maximum_queued_duration,termination_grace_period_duration
        if maximum_runtime_duration:
            MaxRuntimeDurationBox_element = (
                self.MaximumRuntimeDurationDropDownSelector.elements[
                    "MaxRuntimeDurationBox"
                ]
            )
            self.MaximumRuntimeDurationDropDownSelector.select(
                MaxRuntimeDurationBox_element, maximum_runtime_duration
            )
        if maximum_queued_duration:
            MaxQueDurationBox_element = self.MaximumQueuedDurationDropDownSelector.elements[
                "MaxQueDurationBox"
            ]
            self.MaximumRuntimeDurationDropDownSelector.select(
                MaxQueDurationBox_element, maximum_queued_duration
            )
        if termination_grace_period_duration:
            TerminationGraceDurationBox_element = (
                self.TerminationGracePeriodDropDownSelector.elements[
                    "TerminationGraceDurationBox"
                ]
            )
            self.MaximumRuntimeDurationDropDownSelector.select(
                TerminationGraceDurationBox_element, termination_grace_period_duration
            )

        if upload_results is False:
            self.page.locator(self.elements.ResultsUploadNoneChoose).click()
        else:
            if generate_personal_key is False:
                self.page.locator(self.elements.ResultsUploadModelNameInput).fill(
                    ResultsUpload_model_name
                )
                self.page.locator(self.elements.ApiKeyInput).fill(Personal_key)
            else:
                self.page.locator(self.elements.GenerateKeyButton).click()
                self.page.wait_for_load_state("load")
                self.page.locator(self.elements.KeyNameInput).fill(
                    generate_person_key_list[0]
                )
                KeyExpirationBox_element = (
                    self.TerminationGracePeriodDropDownSelector.elements["KeyExpirationBox"]
                )
                self.KeyExpirationDropDownSelector.select(
                    KeyExpirationBox_element, generate_person_key_list[1]
                )
                self.page.locator(self.elements.GenerateKeyConfirmButton).click()
                self.page.wait_for_load_state("load")
                self.page.locator(self.elements.CopyKeyAndClose).click()
                self.page.wait_for_load_state("load")
                copied_key = self.page.evaluate("navigator.clipboard.readText()")
                self.page.locator(self.elements.ApiKeyInput).fill(copied_key)

        if taskCreateOption == TaskCreateOptionENUM.NEGATIVE_MODEL_NAME:
            self.page.wait_for_selector(
                self.elements.TaskCreateBannerNegativeModelName,
                state="visible",
            )
            return

        # Click Create Function Button
        self.page.locator(self.elements.CreateTaskBtn).click()

        if taskCreateOption == TaskCreateOptionENUM.CREATTASKSUCCESS:
            self.page.wait_for_selector(self.elements.TaskCreateBanner, state="visible")
            self.page.wait_for_selector(self.elements.TaskCreateBanner, state="hidden")

        elif (
            taskCreateOption == TaskCreateOptionENUM.NONEFORMAXRUNTIMEDURATIONGFN
            or taskCreateOption == TaskCreateOptionENUM.RUNTIMELARGERTHAN8
        ):
            self.page.wait_for_selector(
                self.elements.TaskCreateBannerNonForMaxruntimeGFN, state="visible"
            )
            self.page.wait_for_selector(
                self.elements.TaskCreateBannerNonForMaxruntimeGFN, state="hidden"
            )
        elif taskCreateOption == TaskCreateOptionENUM.GRACELARGERTHANRUNTIME:
            self.page.wait_for_selector(
                self.elements.TaskCreateBannerGraceperiodlargerthanmaxruntimeGFN,
                state="visible",
            )
            self.page.wait_for_selector(
                self.elements.TaskCreateBannerGraceperiodlargerthanmaxruntimeGFN,
                state="hidden",
            )
        elif taskCreateOption == TaskCreateOptionENUM.WITHOUTPR_OR_EXPIRED:
            self.page.wait_for_selector(
                self.elements.TaskCreateBannerKeyWithoutPROrExpiredKey,
                state="visible",
            )
            self.page.wait_for_selector(
                self.elements.TaskCreateBannerKeyWithoutPROrExpiredKey,
                state="hidden",
            )
        else:
            raise TypeError("Invalid taskCreateOption!")

    def create_task_helm(
        self,
        name: str,
        Helm_Chart: str,
        Helm_Chart_Version: str,
        gpu_type: str,
        instance_type: str,
        upload_results: bool = True,
        taskCreateOption: TaskCreateOptionENUM = TaskCreateOptionENUM.CREATTASKSUCCESS,
        maximum_runtime_duration: Optional[str] = None,
        maximum_queued_duration: Optional[str] = None,
        termination_grace_period_duration: Optional[str] = None,
        ResultsUpload_model_name: Optional[str] = None,
        Personal_key: Optional[str] = None,
        helm_chart_overrides_data: Optional[dict] = None,
        model: Optional[list] = None,
        resource: Optional[list] = None,
        secrets: Optional[Dict] = None,
        generate_personal_key: Optional[bool] = False,
        generate_person_key_list: Optional[list] = None,
        telemetry_logs: Optional[str] = None,
        telemetry_metrics: Optional[str] = None,
        telemetry_traces: Optional[str] = None,
        clusters: Optional[str] = None,
    ):
        """Create a new container function.

        parameters:
        -----------
        name : `str`
            The name of the task.

        Helm_Chart : `str`
            The container in the task.

        Helm_Chart_Version : `str`
            The tag of container in the task.

        gpu_type : `str`
            The gpu_type this task will be deployed.

        instance_type : `str`
            The instance_type this task will be deployed.

        upload_results: 'bool', default is Ture.
            True: Upload results to NGC private registry.
            False: Don't upload results to NGC private registry.

        taskCreateOption: 'TaskCreateOptionENUM', default is TaskCreateOptionENUM.CREATTASKSUCCESS
            choose the specific TaskCreateOptionENUM for negative cases.

        maximum_runtime_duration: 'str' optional
            maximum_runtime_duration set in UI

        maximum_queued_duration:'str' optional
            maximum_queued_duration set in UI

        termination_grace_period_duration:'str' optional
            termination_grace_period_duration set in UI

        ResultsUpload_model_name:'str' optional
            If set upload_results = Ture, need to configure ResultsUpload_model_name

        Personal_key:'str' optional
            Use existing key to fill in UI

        model : `list`, optional
            Model info as a list, each element is a list of model, model_version and model_name, default is None.

        resource: `list`, optional
            Resource info as a list, each element is a list of model, model_version and model_name, default is None.

        secrets: `Dict`, optional
            Secrets info as a dict.

        helm_chart_overrides_data: `dict`, optional
            Helm chart overrides data as a dict.

        generate_personal_key: 'bool', optional
            If need to generate key in the create task page, set it as True.
            Default is False

        generate_person_key_list: 'list', optional
            If need to generate key in the create task page. provide the generate_person_key_list.
        Default is None
        Returns:
        --------
        None
        """
        logging.info(f"Creating Helm-based Task {name}...")
        self.page.get_by_role("radio", name="Custom Helm Chart").click()
        expect(self.page.get_by_role("heading", name="Helm Chart Details")).to_be_visible()
        # Fill Function Name
        self.page.locator(self.elements.NameInput).fill(name)
        # Select Helm Chart and Helm Chart Version
        self.page.locator(
            "//label[text()='Helm Chart']/../..//div[text()='Select one']/..//input"
        ).fill(Helm_Chart)
        self.page.wait_for_load_state("load")
        self.page.locator("//div[text()='{0}']".format(Helm_Chart)).click()
        self.page.locator(
            "//label[text()='Helm Chart Version']/../..//div[text()='Select a version']/..//input"
        ).fill(Helm_Chart_Version)
        self.page.wait_for_load_state("load")
        self.page.locator("//div[text()='{0}']".format(Helm_Chart_Version)).click()

        # Select Models
        if model:
            self.page.locator(self.elements.ModelExpandBtn).click()
            self.page.wait_for_load_state("load")
            model_count = len(model)
            mode_seq = 1
            for model_entry in model:
                self.page.locator(self.elements.ModelInput).fill(model_entry[0])
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(model_entry[0])
                ).click()
                self.page.locator(self.elements.ModelVersionInput).fill(model_entry[1])
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(model_entry[1])
                ).click()
                self.page.locator(self.elements.ModelNameInput).fill(model_entry[2])
                if mode_seq == model_count:
                    break
                else:
                    self.page.locator(self.elements.AddModelButton).click()
                    mode_seq += 1

        # Select resource
        if resource:
            self.page.locator(self.elements.ResourceExpandBtn).click()
            self.page.wait_for_load_state("load")
            resource_count = len(resource)
            resource_seq = 1
            for resource_entry in resource:
                self.page.locator(self.elements.ResourceInput).fill(resource_entry[0])
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(resource_entry[0])
                ).click()
                self.page.locator(self.elements.ResourceVersionInput).fill(
                    resource_entry[1]
                )
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(resource_entry[1])
                ).nth(1).click()
                self.page.locator(self.elements.ResourceNameInput).fill(resource_entry[2])
                if resource_seq == resource_count:
                    break
                else:
                    self.page.locator(self.elements.AddResourceButton).click()
                    resource_seq += 1
        # Telemetry Logs
        if telemetry_logs or telemetry_metrics or telemetry_traces:
            self._expand_telemetry_section()
            if telemetry_logs:
                self._select_logs_endpoint(telemetry_logs)
            if telemetry_metrics:
                self._select_metrics_endpoint(telemetry_metrics)
            if telemetry_traces:
                self._select_traces_endpoint(telemetry_traces)
        # Set Secrets
        if secrets:
            self.page.locator(self.elements.SecretsExpandBtn).click()
            self.page.wait_for_load_state("load")
            secrets_count = len(secrets)
            secrets_seq = 1
            logging.info(f"secrets_count: {secrets_count}")
            for key, value in secrets.items():
                logging.info(f"key: {key}, value: {value}")
                self.page.locator(self.elements.SecKeyInputBySeq).nth(secrets_seq - 1).fill(
                    key
                )
                if isinstance(value, dict):
                    value = json.dumps(value)
                self.page.locator(self.elements.SecValueInputBySeq).nth(-1).fill(value)
                if secrets_seq == secrets_count:
                    break
                else:
                    self.page.get_by_role("button", name="Add Another Secret").click()
                    secrets_seq += 1

        # set GPU type and Instance Types
        self.page.locator(self.elements.GpuTypeInput).fill(gpu_type)
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(gpu_type)).click()
        self.page.locator(self.elements.InstanceTpyeInput).fill(instance_type)
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(instance_type)).click()
        # Select Clusters for non-gfn tasks
        if clusters:
            self.page.wait_for_load_state("networkidle")
            self.page.wait_for_timeout(1000)
            self.page.locator(self.elements.ClustersInput).click()
            self.page.wait_for_load_state("load")
            self.page.wait_for_timeout(5000)
            self.page.get_by_text(clusters).click()

        # Set Helm Chart Overrides
        if helm_chart_overrides_data:
            helm_chart_overrides_btn = self.page.locator(
                'button[aria-label="Helm Chart Overrides"]'
            )
            if helm_chart_overrides_btn.get_attribute("aria-expanded") == "false":
                helm_chart_overrides_btn.click()
            helm_chart_overrides_str = json.dumps(helm_chart_overrides_data)
            self.page.get_by_role("textbox").filter(
                has_text="Enter helm chart overrides in"
            ).fill(helm_chart_overrides_str)

        # set maximum_runtime_duration,maximum_queued_duration,termination_grace_period_duration
        if maximum_runtime_duration:
            MaxRuntimeDurationBox_element = (
                self.MaximumRuntimeDurationDropDownSelector.elements[
                    "MaxRuntimeDurationBox"
                ]
            )
            self.MaximumRuntimeDurationDropDownSelector.select(
                MaxRuntimeDurationBox_element, maximum_runtime_duration
            )
        if maximum_queued_duration:
            MaxQueDurationBox_element = self.MaximumQueuedDurationDropDownSelector.elements[
                "MaxQueDurationBox"
            ]
            self.MaximumRuntimeDurationDropDownSelector.select(
                MaxQueDurationBox_element, maximum_queued_duration
            )
        if termination_grace_period_duration:
            TerminationGraceDurationBox_element = (
                self.TerminationGracePeriodDropDownSelector.elements[
                    "TerminationGraceDurationBox"
                ]
            )
            self.MaximumRuntimeDurationDropDownSelector.select(
                TerminationGraceDurationBox_element, termination_grace_period_duration
            )

        if upload_results is False:
            self.page.locator(self.elements.ResultsUploadNoneChoose).click()
        else:
            if generate_personal_key is False:
                self.page.locator(self.elements.ResultsUploadModelNameInput).fill(
                    ResultsUpload_model_name
                )
                self.page.locator(self.elements.ApiKeyInput).fill(Personal_key)
            else:
                self.page.locator(self.elements.GenerateKeyButton).click()
                self.page.wait_for_load_state("load")
                self.page.locator(self.elements.KeyNameInput).fill(
                    generate_person_key_list[0]
                )
                KeyExpirationBox_element = (
                    self.TerminationGracePeriodDropDownSelector.elements["KeyExpirationBox"]
                )
                self.KeyExpirationDropDownSelector.select(
                    KeyExpirationBox_element, generate_person_key_list[1]
                )
                self.page.locator(self.elements.GenerateKeyConfirmButton).click()
                self.page.wait_for_load_state("load")
                self.page.locator(self.elements.CopyKeyAndClose).click()
                self.page.wait_for_load_state("load")
                copied_key = self.page.evaluate("navigator.clipboard.readText()")
                self.page.locator(self.elements.ApiKeyInput).fill(copied_key)

        # Click Create Function Button
        self.page.locator(self.elements.CreateTaskBtn).click()

        if taskCreateOption == TaskCreateOptionENUM.CREATTASKSUCCESS:
            self.page.wait_for_selector(self.elements.TaskCreateBanner, state="visible")
            self.page.wait_for_selector(self.elements.TaskCreateBanner, state="hidden")

        elif (
            taskCreateOption == TaskCreateOptionENUM.NONEFORMAXRUNTIMEDURATIONGFN
            or taskCreateOption == TaskCreateOptionENUM.RUNTIMELARGERTHAN8
        ):
            self.page.wait_for_selector(
                self.elements.TaskCreateBannerNonForMaxruntimeGFN, state="visible"
            )
            self.page.wait_for_selector(
                self.elements.TaskCreateBannerNonForMaxruntimeGFN, state="hidden"
            )
        elif taskCreateOption == TaskCreateOptionENUM.GRACELARGERTHANRUNTIME:
            self.page.wait_for_selector(
                self.elements.TaskCreateBannerGraceperiodlargerthanmaxruntimeGFN,
                state="visible",
            )
            self.page.wait_for_selector(
                self.elements.TaskCreateBannerGraceperiodlargerthanmaxruntimeGFN,
                state="hidden",
            )
        elif taskCreateOption == TaskCreateOptionENUM.WITHOUTPR_OR_EXPIRED:
            self.page.wait_for_selector(
                self.elements.TaskCreateBannerKeyWithoutPROrExpiredKey,
                state="visible",
            )
            self.page.wait_for_selector(
                self.elements.TaskCreateBannerKeyWithoutPROrExpiredKey,
                state="hidden",
            )
        elif taskCreateOption == TaskCreateOptionENUM.HELM_REVAL_SERVICE:
            self.page.wait_for_selector(
                self.elements.TaskCreateBannerHelmRevalService,
                state="visible",
            )
        else:
            raise TypeError("Invalid taskCreateOption!")
        self.page.wait_for_load_state("load")

    def check_tag_input_box_in_create_task_page(self):
        """
        Check the tag input box in create task page
        """
        tags_section = self.page.locator('label:has-text("Tags")').locator("xpath=../../..")
        max_tags = 64
        added = 0
        unique_y = set()

        for i in range(1, max_tags + 1):
            tag = f"test{i}"
            input_elem = tags_section.locator('input[role="combobox"]')
            input_elem.fill(tag)
            input_elem.press("Enter")
            added += 1

            tag_elements = tags_section.locator('button[data-testid="kui-tag"]')
            count = tag_elements.count()
            y_positions = []
            for j in range(count):
                box = tag_elements.nth(j).bounding_box()
                y_positions.append(int(box["y"]))
            unique_y = set(y_positions)
            logging.info(f"tags_num:{added}, unique_y: {unique_y}")

            if len(unique_y) > 1:
                logging.info(f"Tags wrapped after adding {added} tags.")
                return True

        return False

    def check_tag_input_description_in_create_task_page(self):
        """
        Check the tag input description in create task page
        """
        description_span = self.page.locator(
            'span[data-testid="kui-text"]',
            has_text="Type a tag then press space or enter to confirm. Up to 64 tags can be added per task.",
        )
        return description_span.is_visible()

    def select_telemetry_endpoints(
        self,
        logs_endpoint: Optional[str] = None,
        metrics_endpoint: Optional[str] = None,
        traces_endpoint: Optional[str] = None,
    ):
        """
        Select telemetry endpoints for logs, metrics, and traces.

        Args:
            logs_endpoint: Name of the logs endpoint to select
            metrics_endpoint: Name of the metrics endpoint to select
            traces_endpoint: Name of the traces endpoint to select
        """
        logging.info("Configuring telemetry endpoints...")

        # First, ensure Telemetry Endpoints section is expanded
        self._expand_telemetry_section()

        # Select each endpoint if provided
        if logs_endpoint:
            self._select_logs_endpoint(logs_endpoint)

        if metrics_endpoint:
            self._select_metrics_endpoint(metrics_endpoint)

        if traces_endpoint:
            self._select_traces_endpoint(traces_endpoint)

    def _expand_telemetry_section(self):
        """Expand the Telemetry Endpoints section if not already open."""
        telemetry_button = self.page.locator('button[aria-label="Telemetry Endpoints"]')

        # Check if section is already expanded
        if telemetry_button.get_attribute("aria-expanded") != "true":
            logging.info("Expanding Telemetry Endpoints section...")
            telemetry_button.click()
            self.page.wait_for_load_state("load")

    def _select_logs_endpoint(self, endpoint_name: str):
        """Select a logs endpoint from the dropdown."""
        logging.info(f"Selecting logs endpoint: {endpoint_name}")

        # Click the logs dropdown
        logs_dropdown = self.page.locator(
            '//div[text()="Select a logs endpoint"]/..//input[@role="combobox"]'
        )
        logs_dropdown.click()
        self.page.wait_for_load_state("load")

        # Select the specific endpoint
        endpoint_option = self.page.get_by_role("option", name=endpoint_name)
        endpoint_option.click()

        logging.info(f"Successfully selected logs endpoint: {endpoint_name}")

    def _select_metrics_endpoint(self, endpoint_name: str):
        """Select a metrics endpoint from the dropdown."""
        logging.info(f"Selecting metrics endpoint: {endpoint_name}")

        # Click the metrics dropdown
        metrics_dropdown = self.page.locator(
            '//div[text()="Select a metrics endpoint"]/..//input[@role="combobox"]'
        )
        metrics_dropdown.click()
        self.page.wait_for_load_state("load")

        # Select the specific endpoint
        endpoint_option = self.page.get_by_role("option", name=endpoint_name)
        endpoint_option.click()

        logging.info(f"Successfully selected metrics endpoint: {endpoint_name}")

    def _select_traces_endpoint(self, endpoint_name: str):
        """Select a traces endpoint from the dropdown."""
        logging.info(f"Selecting traces endpoint: {endpoint_name}")

        # Click the traces dropdown
        traces_dropdown = self.page.locator(
            '//div[text()="Select a traces endpoint"]/..//input[@role="combobox"]'
        )
        traces_dropdown.click()
        self.page.wait_for_load_state("load")

        # Select the specific endpoint
        endpoint_option = self.page.get_by_role("option", name=endpoint_name)
        endpoint_option.click()

        logging.info(f"Successfully selected traces endpoint: {endpoint_name}")

    def check_the_pop_up_validation_issues_in_create_task_page(self):
        """
        Check the pop up validation issues in create task page
        return: check_flag: True or False
                error_message: list of check error message
                real_message: list of helm reval error message
        """
        check_flag = True
        error_message = []
        real_message = []
        # Check issues count
        issues_count = self.page.locator('xpath=//p[contains(text(), "Issues (")]')
        if not issues_count.is_visible():
            error_message.append("Issues count not visible")
            check_flag = False
        logging.info("✓ Issues count verified")
        issues_count_text = issues_count.text_content()
        issues_count_text = issues_count_text.split("(")[1].split(")")[0]
        logging.info(f"issues_count_text: {issues_count_text}")
        next_button = self.page.locator(
            "xpath=//button[@data-testid='kui-button']//*[local-name()='svg' and @data-icon-name='shapes-chevron-right']"
        )
        while True:
            issue_code = self.page.locator("//pre//code").all()
            for code in issue_code:
                issue_code_text = code.text_content()
                logging.info(f"issue_code_text: {issue_code_text}")
                if self.page.get_by_text(issue_code_text).count() == 2:
                    real_message.append(issue_code_text)
                else:
                    error_message.append(f"issue_code_text: {issue_code_text} not found")
                    check_flag = False
            if not next_button.is_disabled():
                next_button.click()
                self.page.wait_for_load_state("load")
            else:
                break
        if len(real_message) != int(issues_count_text):
            error_message.append(
                f"real_message: {real_message} not found, expected: {issues_count_text}"
            )
            check_flag = False

        # Check copy button exists
        copy_button = self.page.locator(
            "xpath=//button[@data-testid='kui-button']//*[local-name()='svg' and @data-icon-name='common-copy-generic']"
        ).first
        if not copy_button.is_visible():
            error_message.append("Copy button not visible")
            check_flag = False
        logging.info("✓ Copy button found")

        # Check navigation buttons
        prev_button = self.page.locator(
            "xpath=//button[@data-testid='kui-button']//*[local-name()='svg' and @data-icon-name='shapes-chevron-left']"
        )

        page_number = self.page.locator(
            "xpath=//button[@data-testid='kui-button' and text()='1']"
        )

        if not prev_button.is_visible():
            error_message.append("Previous button not found")
            check_flag = False
        if not next_button.is_visible():
            error_message.append("Next button not found")
            check_flag = False
        if not page_number.is_visible():
            error_message.append("Page number button not found")
            check_flag = False
        logging.info("✓ Navigation buttons verified")

        # Check last updated timestamp
        timestamp = self.page.locator('xpath=//p[contains(text(), "Last updated:")]')
        if not timestamp.is_visible():
            error_message.append("Timestamp not visible")
            check_flag = False
        logging.info(f"✓ Last updated timestamp: {timestamp.text_content()}")

        # Check Helm Chart Security Guidelines link
        security_link = self.page.locator(
            'xpath=//a[contains(text(), "Helm Chart Security Guidelines")]'
        ).last
        if not security_link.is_visible():
            error_message.append("Security guidelines link not visible")
            check_flag = False
        logging.info("✓ Security guidelines link verified")

        with self.page.context.expect_page() as new_page_info:
            security_link.click()
        new_page = new_page_info.value
        new_page.wait_for_selector(
            "//h3[text()='Limitations']", state="visible", timeout=100000
        )
        new_page.bring_to_front()

        new_url = new_page.url
        logging.info(f"New page URL: {new_url}")
        expected_path = "https://docs.nvidia.com/cloud-functions/user-guide/latest/cloud-function/function-creation.html#limitations"
        if expected_path not in new_url:
            error_message.append(
                f"Expected URL path '{expected_path}' not found in '{new_url}'"
            )
            check_flag = False
        logging.info(f"✓ Expected URL path '{expected_path}' found in '{new_url}'")

        self.page.bring_to_front()

        # Check Okay button
        okay_button = self.page.locator(
            'xpath=//div[@data-testid="kui-modal-action"]//button[@data-testid="kui-button"]'
        )
        if not okay_button.is_visible():
            error_message.append("Okay button not visible")
            check_flag = False
        logging.info("✓ Okay button verified")
        okay_button.click()

        return check_flag, error_message, real_message

    def check_the_view_validation_issues_in_create_task_page(self):
        check_flag = True
        error_message = []
        self.page.locator("//button[text()=' View Validation Issues']").click()

        dialog_title = self.page.locator(
            'xpath=//div[@data-testid="kui-modal-title" and contains(text(), "View Validation Issues")]'
        )
        if not dialog_title.is_visible():
            error_message.append("Dialog title not visible")
            check_flag = False
        logging.info("✓ View Validation Issues dialog verified")

        # Check issues count
        issues_count = self.page.locator('xpath=//p[contains(text(), "Issues (")]')
        if not issues_count.is_visible():
            error_message.append("Issues count not visible")
            check_flag = False
        logging.info("✓ Issues count verified")

        # Check last updated timestamp
        timestamp = self.page.locator('xpath=//p[contains(text(), "Last updated:")]')
        if not timestamp.is_visible():
            error_message.append("Timestamp not visible")
            check_flag = False
        logging.info(f"✓ Last updated timestamp: {timestamp.text_content()}")

        # Click copy button for the issue
        copy_button = self.page.locator(
            'xpath=//button[@data-testid="kui-button"]//*[local-name()="svg" and @data-icon-name="common-copy-generic"]'
        ).first
        if not copy_button.is_visible():
            error_message.append("Copy button not visible")
            check_flag = False
        copy_button.click()
        logging.info("✓ Copy button clicked")

        # Check navigation buttons
        prev_button = self.page.locator(
            "xpath=//button[@data-testid='kui-button']//*[local-name()='svg' and @data-icon-name='shapes-chevron-left']"
        )
        next_button = self.page.locator(
            "xpath=//button[@data-testid='kui-button']//*[local-name()='svg' and @data-icon-name='shapes-chevron-right']"
        )
        page_number = self.page.locator(
            "xpath=//button[@data-testid='kui-button' and text()='1']"
        )

        if not prev_button.is_visible():
            error_message.append("Previous button not found")
            check_flag = False
        if not next_button.is_visible():
            error_message.append("Next button not found")
            check_flag = False
        if not page_number.is_visible():
            error_message.append("Page number button not found")
            check_flag = False
        logging.info("✓ Navigation buttons verified")

        # Click Helm Chart Security Guidelines link
        security_link = self.page.locator(
            'xpath=//a[contains(text(), "Helm Chart Security Guidelines")]'
        ).last
        if not security_link.is_visible():
            error_message.append("Security guidelines link not visible")
            check_flag = False

        # Click the link and wait for new page
        with self.page.context.expect_page() as new_page_info:
            security_link.click()
        new_page = new_page_info.value

        # Wait for the new page to load
        new_page.wait_for_selector(
            "//h3[text()='Limitations']", state="visible", timeout=100000
        )
        new_page.bring_to_front()

        # Verify the URL
        new_url = new_page.url
        logging.info(f"✓ New page URL: {new_url}")
        expected_path = "https://docs.nvidia.com/cloud-functions/user-guide/latest/cloud-function/function-creation.html#limitations"
        if expected_path not in new_url:
            error_message.append(
                f"Expected URL path '{expected_path}' not found in '{new_url}'"
            )
            check_flag = False
        logging.info("✓ Security guidelines page verified")

        # Close the new page and return to original page
        new_page.close()
        self.page.bring_to_front()

        # Verify we're back on the original dialog
        self.page.wait_for_selector(
            'xpath=//div[@role="dialog" and @data-testid="kui-modal-container"]',
            timeout=5000,
        )
        logging.info("✓ Returned to original dialog")

        # Step 9: Click Okay button to close dialog
        logging.info("Step 9: Clicking Okay button...")
        okay_button = self.page.locator(
            'xpath=//div[@data-testid="kui-modal-action"]//button[@data-testid="kui-button"]'
        )
        if not okay_button.is_visible():
            error_message.append("Okay button not visible")
            check_flag = False
        okay_button.click()
        logging.info("✓ Okay button clicked")

        logging.info("🎉 All validation issues workflow tests passed!")
        return check_flag, error_message

    def check_the_pre_populate_model_resource_name(
        self,
    ):
        self.page.locator(self.elements.ModelExpandBtn).click()
        self.page.wait_for_load_state("load")

        model_name = self.page.locator(self.elements.ModelNameInput).get_attribute(
            "placeholder"
        )
        model_name_disabled = self.page.locator(self.elements.ModelNameInput).is_disabled()
        if model_name != "Select a model version first" and not model_name_disabled:
            return False
        if CURRENT_ENV == "staging":
            model = [
                ["tadiathdfetp/model_nvcf_qa", "0.1", "tadiathdfetp-model_nvcf_qa_0.1"]
            ]
            resource = [
                [
                    "tadiathdfetp/resource_nvcf_qa",
                    "0.1",
                    "tadiathdfetp-resource_nvcf_qa_0.1",
                ]
            ]
        else:
            model = [
                ["rw983xdqtcdp/model_nvcf_qa", "0.1", "rw983xdqtcdp-model_nvcf_qa_0.1"]
            ]
            resource = [
                [
                    "rw983xdqtcdp/resource_nvcf_qa",
                    "0.1",
                    "rw983xdqtcdp-resource_nvcf_qa_0.1",
                ]
            ]
        self.page.locator(self.elements.ModelInput).fill(model[0][0])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(model[0][0])).nth(
            -1
        ).click()
        self.page.locator(self.elements.ModelVersionInput).fill(model[0][1])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(model[0][1])).nth(
            -1
        ).click()
        pre_populate_model_name = self.page.locator(
            self.elements.ModelNameInput
        ).get_attribute("value")
        logging.info(f"pre_populate_model_name: {pre_populate_model_name}")
        if pre_populate_model_name != model[0][2]:
            return False

        # Select resource
        self.page.locator(self.elements.ResourceExpandBtn).click()
        self.page.wait_for_load_state("load")
        resource_name = self.page.locator(self.elements.ResourceNameInput).get_attribute(
            "placeholder"
        )
        resource_name_disabled = self.page.locator(
            self.elements.ResourceNameInput
        ).is_disabled()
        if (
            resource_name != "Select a resource version first"
            and not resource_name_disabled
        ):
            return False
        self.page.locator(self.elements.ResourceInput).fill(resource[0][0])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(resource[0][0])).nth(
            -1
        ).click()
        self.page.locator(self.elements.ResourceVersionInput).fill(resource[0][1])
        self.page.wait_for_load_state("load")
        self.page.wait_for_timeout(5000)
        self.page.locator(self.elements.SelectorChoosebyName.format(resource[0][1])).nth(
            -1
        ).click()
        pre_populate_resource_name = self.page.locator(
            self.elements.ResourceNameInput
        ).get_attribute("value")
        logging.info(f"pre_populate_resource_name: {pre_populate_resource_name}")
        if pre_populate_resource_name != resource[0][2]:
            return False
        return True

    def check_the_model_resource_name_content(self):
        self.page.locator(self.elements.ModelExpandBtn).click()
        self.page.wait_for_load_state("load")

        if CURRENT_ENV == "staging":
            model = [
                ["tadiathdfetp/model_nvcf_qa", "0.1", "tadiathdfetp-model_nvcf_qa_0.1"]
            ]
            resource = [
                [
                    "tadiathdfetp/resource_nvcf_qa",
                    "0.1",
                    "tadiathdfetp-resource_nvcf_qa_0.1",
                ]
            ]
        else:
            model = [
                ["rw983xdqtcdp/model_nvcf_qa", "0.1", "rw983xdqtcdp-model_nvcf_qa_0.1"]
            ]
            resource = [
                [
                    "rw983xdqtcdp/resource_nvcf_qa",
                    "0.1",
                    "rw983xdqtcdp-resource_nvcf_qa_0.1",
                ]
            ]
        self.page.locator(self.elements.ModelInput).fill(model[0][0])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(model[0][0])).nth(
            -1
        ).click()
        self.page.locator(self.elements.ModelVersionInput).fill(model[0][1])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(model[0][1])).nth(
            -1
        ).click()
        self.page.get_by_role("button", name="Add Another Model").click()
        self.page.locator(self.elements.ModelInput).fill(model[0][0])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(model[0][0])).nth(
            -1
        ).click()
        self.page.locator(self.elements.ModelVersionInput).fill(model[0][1])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(model[0][1])).nth(
            -1
        ).click()
        if not self.page.locator("//span[text()='Model name must be unique']").is_visible():
            logging.info("There is no error message for model name is not unique")
            return False
        self.page.locator(self.elements.ModelNameInput).nth(-1).fill("0.0.2")
        if not self.page.locator(
            "//span[text()='Model name must begin with a lowercase alpha, and following characters must be alphanumeric. Use periods, underscores or hyphens to separate words.']"
        ).is_visible():
            logging.info("There is no error message for model name is not valid")
            return False

        # Select resource
        self.page.locator(self.elements.ResourceExpandBtn).click()
        self.page.wait_for_load_state("load")

        self.page.locator(self.elements.ResourceInput).fill(resource[0][0])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(resource[0][0])).nth(
            -1
        ).click()
        self.page.locator(self.elements.ResourceVersionInput).fill(resource[0][1])
        self.page.wait_for_load_state("load")
        self.page.wait_for_timeout(5000)
        self.page.locator(self.elements.SelectorChoosebyName.format(resource[0][1])).nth(
            -1
        ).click()
        self.page.get_by_role("button", name="Add Another Resource").click()
        self.page.locator(self.elements.ResourceInput).fill(resource[0][0])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(resource[0][0])).nth(
            -1
        ).click()
        self.page.locator(self.elements.ResourceVersionInput).fill(resource[0][1])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(resource[0][1])).nth(
            -1
        ).click()
        if not self.page.locator(
            "//span[text()='Resource name must be unique']"
        ).is_visible():
            logging.info("There is no error message for resource name is not unique")
            return False
        self.page.locator(self.elements.ResourceNameInput).nth(-1).fill("0.0.2")
        if not self.page.locator(
            "//span[text()='Resource name must begin with a lowercase alpha, and following characters must be alphanumeric. Use periods, underscores or hyphens to separate words.']"
        ).is_visible():
            logging.info("There is no error message for resource name is not valid")
            return False
        return True

    def check_the_description_in_create_task_page(self, long_description: str):
        import re

        DescriptionInput = "//textarea[@placeholder='Enter a description']"
        description_span = self.page.locator(DescriptionInput).get_attribute("style")
        match = re.search(r"height:\s*(\d+)px", description_span)
        if match:
            initial_height = int(match.group(1))

        logging.info(f"initial_height: {initial_height}")
        self.page.locator(DescriptionInput).fill(long_description)
        self.page.wait_for_load_state("load")
        self.page.wait_for_timeout(5000)
        description_span = self.page.locator(DescriptionInput).get_attribute("style")
        match = re.search(r"height:\s*(\d+)px", description_span)
        if match:
            final_height = int(match.group(1))
            logging.info(f"final_height: {final_height}")
            if final_height > initial_height:
                return True
            else:
                return False
