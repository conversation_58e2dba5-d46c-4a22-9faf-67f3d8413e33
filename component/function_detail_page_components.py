from component.components import (
    NavigationBar,
    StatisticalTable,
    PaginationTable,
    CollapsedTable,
)
from element.function_detail_page_elements import (
    FunctionDetailPageNavigationBarElements,
    FunctionDetailPageAgrInvoElements,
    FunctionDetailPageAgrInsElements,
    FunctionDetailPagePaginationElements,
    FunctionsDetailPageBasicDetailsElements,
    FunctionsDetailPageRuntimeDetailsElements,
    FunctionsDetailPageModelDetailsElements,
    FunctionsDetailPageEnvironmentVariablesElements,
)
from component.create_function_page_components import (
    CreateFunctionPageForm,
)
from component.deployments_list_page_components import (
    DeployVersionForm,
)

from playwright.sync_api import Page, Locator, TimeoutError
from typing import Dict, Literal, Union, Tuple
import logging
import random
import time
# import asyncio


class FunctionDetailPageNavigationBar(NavigationBar):
    """Elements and related functionalities for Navigation Bar on Function Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionDetailPageNavigationBarElements())

    def create_new_version(self):
        """Click the 'Create New Version' button on the Navigation Bar.

        Returns:
        --------
        uuid : `str`
            The version ID.
        """
        self.page.locator(self.elements.CreateNewVersion).click()
        self.page.wait_for_load_state()
        form = CreateFunctionPageForm(self.page)
        return form.add_function_version()

    def navigate_to_function_list_page(self) -> bool:
        """Navigate to the function list page.

        Returns:
        --------
        `bool`
            True if the function list page is opened successfully, otherwise False.
        """
        self.page.get_by_test_id("kui-breadcrumb-root").get_by_role(
            "link", name="Functions", exact=True
        ).click()
        self.page.wait_for_load_state("load")
        from component.function_list_page_components import (
            FunctionListPageNavigationBar,
        )

        func_list_page = FunctionListPageNavigationBar(self.page)
        title = self.page.locator(func_list_page.elements.Title).text_content()
        return title == "Functions"

    def click_clone_function_for_two_versions(self):
        """Click the 'Clone Function' button on the Navigation Bar for two versions.

        Returns:
        --------
        check_version_id : `str`
            The version ID of the checked version
        un_check_version_id : `str`
            The version ID of the unchecked version
        """
        logging.info("Clone the function from function detail page...")
        self.page.locator(self.elements.MoreOption).first.click()
        self.page.wait_for_timeout(2000)
        self.page.locator(self.elements.CloneFuncBtn).click()
        self.page.locator(self.elements.CloneVersion).wait_for(state="visible")
        self.page.locator(self.elements.ExpandBtn).click()
        self.page.wait_for_load_state()
        check_version = self.page.locator(self.elements.CheckVersion).text_content()
        un_check_version = self.page.locator(self.elements.UnCheckVersion).text_content()
        self.page.wait_for_timeout(2000)
        pattern = r"\((.*?)\)"
        import re

        check_version_id = re.search(pattern, check_version).group(1)
        un_check_version_id = re.search(pattern, un_check_version).group(1)
        logging.info(f"check_version: {check_version_id}")
        logging.info(f"un_check_version: {un_check_version_id}")
        self.page.locator(self.elements.CheckVersion).click()
        self.page.locator(self.elements.ContinueBtn).click()
        self.page.wait_for_timeout(2000)
        return check_version_id, un_check_version_id

    def check_function_details_page_title(self, func_id: str) -> bool:
        """Check the function details page title.

        parameters:
        -----------
        func_id: `str`
            The function ID

        Returns:
        --------
        list: `list`
            The function details page title.
        """
        breadcrumb_root = self.page.locator("//div[@data-testid='kui-breadcrumb-root']")
        children = breadcrumb_root.locator("xpath=./*")
        count = children.count()
        result = []
        for i in range(count):
            child = children.nth(i)
            data_testid = child.get_attribute("data-testid")
            if data_testid == "kui-breadcrumb-item":
                text = child.inner_text().strip()
                result.append(text)
            elif data_testid == "kui-breadcrumb-separator":
                svg = child.locator("svg")
                if svg.count() > 0:
                    icon_name = svg.first.get_attribute("data-icon-name")
                    result.append(icon_name)
        logging.info(f"result: {result}")
        check_result_flag = True
        if result[0] != "Functions":
            check_result_flag = False
        elif result[1] != "shapes-chevron-right":
            check_result_flag = False
        elif result[2] != f"Function ID: {func_id}":
            check_result_flag = False
        return check_result_flag


class FunctionDetailPageStatics(StatisticalTable):
    """Elements and related functionalities for the statistics on Function Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    elements : `Elements`
        The elements of the statistics.
    """

    def __init__(self, page: Page, elements):
        super().__init__(page, elements)

    def get_all_data(self):
        """Get all the statistical data.

        Returns:
        --------
        data : `dict`
            statistical attrs and related values.
        """
        data = dict()
        attrs = self.page.locator(self.elements.DetailsAttr).all()
        values = self.page.locator(self.elements.DetailsVal).all()

        for attr, value in zip(attrs, values):
            data[attr.text_content()] = value.text_content()

        return data


class FunctionDetailPageAgrInvoStatics(FunctionDetailPageStatics):
    """Elements and related functionalities for the aggreated invocations statistics on Function Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionDetailPageAgrInvoElements())

    def wait_for_invo_data_visible(self, tag=None):
        self.page.wait_for_selector(
            "//span[text()='Total Invocations']", state="visible", timeout=12000
        )
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.StatisticAttrCell).nth(0).wait_for(state="visible")
        if tag == "func_version_invo":
            self.page.locator(self.elements.TotalInstanceCountAttr).wait_for(
                state="visible"
            )
        self.page.wait_for_load_state("load")

    def set_display_span(
        self,
        span: Literal[
            "Past 1 Minute",
            "Past 5 Minutes",
            "Past 15 Minutes",
            "Past 30 Minutes",
            "Past 1 Hour",
            "Past 3 Hours",
            "Past 6 Hours",
            "Past 12 Hours",
            "Past 1 Day",
            "Past 2 Days",
            "Past 7 Days",
        ],
    ):
        self.page.locator(self.elements.DisplayedSpanBtn).click()
        self.page.get_by_text(span).click()
        self.page.locator(self.elements.StatisticAttrCell).nth(0).wait_for(state="visible")
        self.page.wait_for_load_state("load")

    def set_custom_range(self):
        start_button = self.page.locator(
            "(//*[local-name()='svg' and @data-icon-name='common-calendar'])[1]"
        )
        start_button.click()
        start_date_btn = self.page.locator(
            "//div[@data-testid='kui-datepicker-calendar']//button[@name='day' and not(@disabled)][1]"
        )
        start_date_btn.click()
        start_date_input = self.page.locator("//input[@title='startDate']")
        start_date_value = start_date_input.input_value()
        start_time_input = self.page.locator("//input[@name='startTime']")
        start_time_value = "00:00:00"
        start_time_input.fill(start_time_value)

        end_button = self.page.locator(
            "(//*[local-name()='svg' and @data-icon-name='common-calendar'])[2]"
        )
        end_button.click()
        end_date_btn = self.page.locator(
            "//div[@data-testid='kui-datepicker-calendar']//button[@name='day' and not(@disabled)][last()]"
        )
        end_date_btn.click()
        end_date_input = self.page.locator("//input[@title='endDate']")
        end_date_value = end_date_input.input_value()
        end_time_input = self.page.locator("//input[@name='endTime']")
        end_time_value = "23:59:59"
        end_time_input.fill(end_time_value)

        ApplyBtn = self.page.locator("//button[text()='Apply']")
        ApplyBtn.click()
        self.page.wait_for_load_state("load")
        return {
            "start_date": start_date_value,
            "start_time": start_time_value,
            "end_date": end_date_value,
            "end_time": end_time_value,
        }

    def set_custom_range_negative(self):
        start_button = self.page.locator(
            "(//*[local-name()='svg' and @data-icon-name='common-calendar'])[1]"
        )
        start_button.click()
        start_date_btn = self.page.locator(
            "//div[@data-testid='kui-datepicker-calendar']//button[@name='day' and not(@disabled)][last()]"
        )
        start_date_btn.click()

        start_time_input = self.page.locator("//input[@name='startTime']")
        start_time_value = "23:59:59"
        start_time_input.fill(start_time_value)

        end_button = self.page.locator(
            "(//*[local-name()='svg' and @data-icon-name='common-calendar'])[2]"
        )
        end_button.click()
        end_date_btn = self.page.locator(
            "//div[@data-testid='kui-datepicker-calendar']//button[@name='day' and not(@disabled)][1]"
        )
        end_date_btn.click()

        end_time_input = self.page.locator("//input[@name='endTime']")
        end_time_value = "00:00:00"
        end_time_input.fill(end_time_value)

        error_msg = self.page.locator(
            "//span[text()='End time should be greater than start time.']"
        )
        if not error_msg.is_visible():
            return False

        start_time_input.fill(end_time_value)
        end_time_input.fill(start_time_value)
        self.page.wait_for_timeout(1000)

        if error_msg.is_visible():
            return False

        return True

    def get_all_invo_data(self):
        """Get all the statistical data.

        Returns:
        --------
        data : `dict`
            statistical attrs and related values.
        """
        data = dict()
        attrs = self.page.locator(self.elements.StatisticAttrCell).all()
        values = self.page.locator(self.elements.StatisticValueCell).all()

        for attr, value in zip(attrs, values):
            data[attr.text_content()] = value.text_content()

        return data

    def get_all_invo_data_new(self, protocol: str = None):
        """Get Total Invocations, Average Inference Time, Total Instance Count and Failures in Metrics Box part.

        Returns:
        --------
        data : `dict`
            statistical attrs and related values.
        """
        data = dict()
        if protocol == "HTTP":
            self.page.locator(self.elements.averinferenceTVal).wait_for(
                timeout=6000, state="visible"
            )
            attrs = [
                self.page.locator(self.elements.totalinvoAttr),
                self.page.locator(self.elements.totalinstanceCountAttr),
                self.page.locator(self.elements.failureAttr),
                self.page.locator(self.elements.averinferenceTAttr),
            ]
            values = [
                self.page.locator(self.elements.totalinvoVal),
                self.page.locator(self.elements.totalinstanceCountVal),
                self.page.locator(self.elements.failureVal),
                self.page.locator(self.elements.averinferenceTVal),
            ]
        elif protocol == "GRPC":
            attrs = [
                self.page.locator(self.elements.totalinvoAttr),
                self.page.locator(self.elements.totalinstanceCountAttr),
                self.page.locator(self.elements.failureAttr),
            ]
            values = [
                self.page.locator(self.elements.totalinvoVal),
                self.page.locator(self.elements.totalinstanceCountVal),
                self.page.locator(self.elements.failureVal),
            ]
        else:
            return dict()
        self.page.locator(self.elements.totalinvoVal).wait_for(state="visible")
        self.page.locator(self.elements.totalinstanceCountVal).wait_for(state="visible")
        self.page.locator(self.elements.failureVal).wait_for(state="visible")

        for attr, value in zip(attrs, values):
            data[attr.text_content()] = value.text_content()

        return data


class FunctionDetailPageAgrInsStatics(FunctionDetailPageStatics):
    """Elements and related functionalities for the aggregated instances statistics on Function Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionDetailPageAgrInsElements())

    def get_all_ins_data(self):
        """Get all the statistical data.

        Returns:
        --------
        data : `dict`
            statistical attrs and related values.
        """
        data = dict()
        attrs = self.page.locator(self.elements.statisticAttrCell).all()
        values = self.page.locator(self.elements.statisticValueCell).all()

        for attr, value in zip(attrs, values):
            data[attr.text_content()] = value.text_content()

        return data


class FunctionDetailPagePagination(PaginationTable):
    """Elements and related functionalities for the pagination on Function Detail Page.

    parameters:
    -----------
        The page object.
    """

    clone_func_btn = "//div[@role='menuitem' and text()='Clone Function']"
    action_btn = "//button[@data-testid='kui-menu-trigger' and @aria-haspopup='menu']/*[name()='svg']"
    SaveSecretBtn = (
        "//button[@data-testid='kui-button' and normalize-space()='Save Secrets']"
    )
    SucessSecretMsg = (
        "//span[@data-testid='kui-text' and contains(text(), 'successfully updated')]"
    )

    def __init__(self, page: Page):
        super().__init__(page, FunctionDetailPagePaginationElements())
        self.filter_columns = {
            "vers_name": "Name",
            "vers_id": "Version ID",
            "status": "Status",
            "create_date": "Created Date",
            "type": "Type",
            "streaming": "Streaming",
        }

    def get_version_by_name(self, name: str) -> Dict:
        """Get version item locator by name

        parameters:
        -----------
        name: `str`
            The name of the function

        Returns:
        --------
        func_info: `Dict`
            The properties of the function
        """
        func_items = self._page.locator(self.elements.TableItems).all()
        self.edit_column_filters()
        for func_item in func_items:
            func_info = self.parse_vers_item(func_item)
            if func_info["Version Name"] == name:
                return func_info
        return dict()

    def get_version_by_uuid(self, uuid: str) -> Dict:
        """Get version item locator by name

        parameters:
        -----------
        uuid: `str`
            The uuid of the version

        Returns:
        --------
        `Locator`
            The locator of the function item
        """
        # self.edit_column_filters()
        if uuid:
            logging.info(
                "search and get version info in the search table based on Version ID..."
            )
            self._page.locator(self.elements.SearchBarInput).fill(uuid)
            self._page.locator(self.elements.RefreshButton).click()
            if self._page.locator(self.elements.TableItems).count() > 0:
                ver_item = self._page.locator(self.elements.TableItems).first
                func_info = self.parse_vers_item(ver_item)
                return func_info
        return dict()

    def parse_vers_item(self, vers_item: Locator) -> Dict:
        """Parse each function properties from the table row.

        parameters:
        -----------
        vers_item: `Locator`
            The locator of the version item

        Returns:
        --------
        vers_info: `Dict`
            include the version properties
        """
        # func_properties = vers_item.locator("//td")
        vers_info = {
            "Version Name": vers_item.locator("//td[@headers='data-view-header-name']")
            .text_content()
            .strip(),
            "Version ID": vers_item.locator("//td[@headers='data-view-header-versionId']")
            .text_content()
            .strip(),
            "Status": vers_item.locator("//td[@headers='data-view-header-status']")
            .text_content()
            .strip(),
            "Created Date": vers_item.locator("//td[@headers='data-view-header-createdAt']")
            .text_content()
            .strip(),
            "Action": vers_item.locator("//td[@headers='data-view-header-row-actions']"),
        }
        return vers_info

    def get_first_version_info(self) -> Dict:
        """Return the first version info

        Returns:
        --------
        vers_info: `Dict`
            The first function info included in the function details panel
        """
        if self.is_empty():
            logging.info("No version item found.")
            return dict()
        vers_item = self._page.locator(self.elements.TableItems).first
        return self.parse_vers_item(vers_item)

    def parse_vers_item_list(self, vers_item: Locator) -> list:
        """Parse each function properties from the table row.

        parameters:
        -----------
        vers_item: `Locator`
            The locator of the version item

        Returns:
        --------
        vers_info: `list`
            include the version properties
        """
        func_properties = vers_item.locator("//td")
        list_item = []
        for i in range(func_properties.count()):
            element = func_properties.nth(i)
            text = element.text_content().strip()
            list_item.append(text)
        return list_item

    def show_first_version_info_list(self) -> list:
        """Show the first function details panel, and return the function info

        Returns:
        --------
        `list`
            The first function info included in the function version details panel
        """
        if self._page.locator(self.elements.TableItems).count() == 0:
            logging.info("No version item found.")
            return list()
        version_item = self._page.locator(self.elements.TableItems).first
        return self.parse_vers_item_list(version_item)

    def check_if_vers_exists(self, vers_name: str) -> bool:
        """Check if the version exists by name.

        parameters:
        -----------
        vers_name: `str`
            The name of the version

        Returns:
        --------
        `bool`
            True if the version exists, otherwise False.
        """
        logging.info(f"Checking if the function {vers_name} exists...")
        while True:
            if (
                self.page.locator(self.elements.VersionNameByName.format(vers_name)).count()
                == 1
            ):
                return True
            if not self.next_page():
                return False

    def copy_vers_id(self, vers_id: str) -> str:
        """Copy the version id by name.

        parameters:
        -----------
        vers_id: `str`
            The uuid of the version

        Returns:
        --------
        `str`
            The version id
        """
        logging.info(f"Copying the function version id {vers_id}...")
        while True:
            func_dict = self.get_version_by_uuid(vers_id)
            if func_dict:
                logging.info(f"Function detail dict is {func_dict}...")
                func_dict["Action"].click()
                self.page.locator(self.elements.CopyVersionId).click()
                return self.page.evaluate("navigator.clipboard.readText()")
            if not self.next_page():
                return ""

    def _delete_version(self):
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.ConfirmDeleteBtn).click()
        self.page.locator(self.elements.FunctionDeleteBanner).wait_for(state="visible")
        self.page.locator(self.elements.FunctionDeleteBanner).wait_for(state="hidden")
        self.page.wait_for_load_state("load")

    def delete_version(self, vers_id: str) -> bool:
        """Delete the version by name.

        parameters:
        -----------
        vers_id: `str`
            The uuid of the version

        Returns:
        --------
        `bool`
            True if the version is deleted successfully, otherwise False.
        """

        logging.info(f"Deleting the function version {vers_id}...")

        while True:
            func_dict = self.get_version_by_uuid(vers_id)
            if func_dict:
                logging.info(f"Function detail dict is {func_dict}...")
                func_dict["Action"].click()
                self.page.locator(self.elements.DelVersBtn).click()
                self._delete_version()
                return True
            if not self.next_page():
                return False

    def deploy_function_version(
        self,
        vers_name: str,
        min_instances: int,
        max_instances: int,
        vers_id: str,
        backend: str = None,
        gpu: str = None,
        instance_type: str = None,
        max_cocurrency: int = None,
    ) -> bool:
        """Deploy the function version.

        parameters:
        -----------
        vers_name: `str`
            The name of the version
        min_instances: `int`
            The min instances of the function
        max_instances: `int`
            The max instances of the function
        vers_id: `str`
            The uuid of the function version
        backend: `str`
            The backend of the function
        gpu: `str`
            The gpu of the function
        instance_type: `str`
            The instance type of the function
        max_cocurrency: `int`
            The max cocurrency of the function

        Returns:
        --------
        `bool`
            True if the function version is deployed successfully, otherwise False.
        """

        logging.info(f"Search the function {vers_id}...")
        while True:
            func_dict = self.get_version_by_uuid(vers_id)
            if func_dict:
                logging.info(f"Function detail dict is {func_dict}...")
                status = func_dict["Status"]
                if status != "INACTIVE":
                    logging.error(f"The function {vers_id} has deployed, please check...")
                    return False
                else:
                    func_dict["Action"].click()
                    self.page.locator(self.elements.DeployVersionBtn).click()
                    self.page.wait_for_load_state("load")
                    deploy_form = DeployVersionForm(page=self.page)
                    deploy_form.deploy_function_in_detail(
                        func_name=vers_name,
                        min_instances=min_instances,
                        max_instances=max_instances,
                        func_version=vers_id,
                        backend=backend,
                        gpu=gpu,
                        instance_type=instance_type,
                        max_concurrency=max_cocurrency,
                    )
                    return True
            if not self.next_page():
                return False

    def wait_version_to_expected_status(
        self,
        vers_name: str,
        expected_status: str,
        time_out: int,
    ) -> bool:
        """Wait the version to expected status in timeout.

        parameters:
        -----------
        func_name: `str`
            The name of the version
        expected_status: `str`
            The expected status of the version
        time_out: `int`
            The timeout in seconds

        Returns:
        --------
        `bool`
            True if the version is in expected status in timeout, otherwise False.
        """

        logging.info(f"Start waiting {vers_name} to expected status {expected_status}...")
        while True:
            if (
                self.page.locator(
                    self.elements.FunctionNameByName.format(vers_name)
                ).count()
                == 1
            ):
                current_status = self.page.locator(
                    self.elements.FunctionStatusByName.format(vers_name)
                ).text_content()
                if current_status == expected_status:
                    logging.info(
                        f"Function {vers_name} is in expected status {expected_status} now..."
                    )
                    return True
                else:
                    logging.info(
                        f"Waiting {vers_name} to expected status {expected_status}, current as {current_status}, time left {time_out}s..."
                    )
                    self.page.wait_for_timeout(20000)
                    time_out -= 20
                    if time_out > 0:
                        self.page.reload()
                        continue
                    else:
                        return False
            if not self.next_page():
                return False

    def is_empty(self) -> bool:
        """Check if the function pagination is empty.

        Returns:
        --------
        `bool`
            True if the function pagination is empty, otherwise False.
        """
        return self.get_total_function_count() == 0

    def get_current_page_data(self):
        pass

    def get_total_page_data(self):
        pass

    def get_total_pages(self):
        pass

    def go_to_page(self, page_number):
        pass

    def next_page(self) -> bool:
        """Go to the next page if available.

        Returns:
        --------
        `bool`
            True if the next page is available, otherwise False.
        """
        if self.page.locator(self.elements.NextPageButton).is_enabled():
            self.page.locator(self.elements.NextPageButton).click()
            self.page.wait_for_load_state()
            return True
        return False

    def previous_page(self) -> bool:
        """Go to the previous page if available.

        Returns:
        --------
        `bool`
            True if the previous page is available, otherwise False.
        """
        if self.page.locator(self.elements.PrevPageButton).is_enabled():
            self.page.locator(self.elements.PrevPageButton).click()
            self.page.wait_for_load_state()
            return True
        return False

    def edit_column_filters(
        self,
        vers_name: bool = True,
        vers_id: bool = True,
        status: bool = True,
        create_date: bool = True,
        type: bool = True,
        streaming: bool = True,
    ) -> bool:
        """Perform column filters editing

        parameters:
        -----------
        vers_name : `bool`
            Set the version name column display state
        vers_id : `bool`
            Set the version id column display state
        status : `bool`
            Set the status column is display state
        create_date : `bool`
            Set the create date column is display state

        Returns:
        --------
        status : `bool`
            return True if the editing is successful
        """
        logging.info("Start editing column filters...")
        self.page.wait_for_timeout(2000)
        self.page.locator(self.elements.EditColumnsButton).click()
        for k, v in self.filter_columns.items():
            filter = self.page.locator(f"//span[text()='{v}']/..")
            if filter.is_checked() != locals()[k]:
                filter.click()
        try:
            self.page.locator(self.elements.EditColumnsButton).click(
                force=True, timeout=3000
            )
        except TimeoutError:
            pass
        if (
            self.page.locator(self.elements.EditColumnsButton).get_attribute(
                "aria-expanded"
            )
            == "true"
        ):
            self.page.locator(self.elements.EditColumnsButton).click()
        logging.info("Finish editing column filters")
        return True

    def get_displayed_rows(self):
        """Get the displayed rows based on current page

        Returns:
        --------
        rows : `list`
            The displayed rows
        """
        pass

    def set_displayed_rows(self, rows):
        """Set the displayed rows based on provided data

        parameters:
        -----------
        rows : `list`
            The rows to be displayed

        Returns:
        --------
        status : `bool`
            if the setting is successful or not
        """
        pass

    def navigate_to_vers_detail_page(
        self,
        vers_id: str = None,
        vers_name: str = None,
    ) -> bool:
        """Go to the version detail page by version uuid.

        parameters:
        -----------
        vers_id: `str`
            The uuid of the version
        vers_name: `str`
            The name of the version

        return:
        -----------
        `bool`
            True if the version detail page is opened successfully, otherwise False.
        """
        # Determine search text - prefer vers_id over vers_name
        if vers_id:
            search_text = vers_id
        elif vers_name:
            search_text = vers_name
        else:
            logging.error("Either vers_id or vers_name must be provided")
            return False

        logging.info("Search version info in the search table based on Version ID...")
        self._page.locator(self.elements.SearchBarInput).fill(search_text)
        self._page.locator(self.elements.RefreshButton).click()
        if self._page.locator(self.elements.TableItems).count() > 0:
            version_name = (
                self._page.locator(self.elements.TableItems).first.locator("//td").first
            )
            version_name.wait_for(state="visible")
            version_name.click()
            return True
        return False

    def navigate_to_vers_detail_page_random(
        self, active=True, return_vers_id=False
    ) -> Union[bool, Tuple[bool, str]]:
        all_items = self.page.locator(self.elements["TableItems"]).all()

        if active is None:
            random_item = random.choice(all_items)
        else:
            status = "ACTIVE" if active else "INACTIVE"

            filter_items = [
                item for item in all_items if self.parse_vers_item(item)["Status"] == status
            ]

            random_item = random.choice(filter_items)

        # self.edit_column_filters()
        vers_id = self.parse_vers_item(random_item)["Version ID"]
        self.navigate_to_vers_detail_page(vers_id)
        status_title = self.page.locator(
            FunctionDetailPageBasicDetails(self.page).elements["StatusTitleinBasicDetails"]
        )
        status_title.wait_for()
        success = status_title.count() == 1

        if return_vers_id:
            return success, vers_id
        return success

    def action_to(
        self,
        name: str = None,
        uuid: str = None,
        entry: Literal[
            "Copy Version ID",
            "View Version Details",
            "View Invoke Command",
            "Manage Secrets",
            "Deploy Version",
            "Un-Deploy Version",
            "Delete Version",
            "Redeploy Version",
        ] = "View Function Details",
    ) -> Page:
        """Perform action to the current function.

        parameters:
        -----------
        name: `str`
            The name of the function
        uuid: `str`
            The uuid of the function
        entry: `Literal["Copy Version ID", "View Version Details", "View Invoke Command", "Manage Secrets", "Deploy Version", "Un-Deploy Version", "Delete Version"]`
            The action to be performed

        returns:
        -----------
        page : `Playwright.sync_api.page.Page`
            The page object
        """
        entry_dict = {
            "Copy Version ID": {
                "btn": self.elements.CopyVersionId,
            },
            "View Version Details": {
                "btn": self.elements.ViewVersionDetailsBtn,
            },
            "View Invoke Command": {
                "btn": self.elements.ViewInvokCmd,
            },
            "Manage Secrets": {
                "btn": self.elements.ManageSecBtn,
            },
            "Deploy Version": {
                "btn": self.elements.DeployVersionBtn,
            },
            "Un-Deploy Version": {
                "btn": self.elements.UndeployVersionBtn,
            },
            "Delete Version": {
                "btn": self.elements.DelVersBtn,
            },
            "Redeploy Version": {
                "btn": self.elements.RedeployVersBtn,
            },
        }
        if name:
            vers_dict = self.get_version_by_name(name)
        elif uuid:
            vers_dict = self.get_version_by_uuid(uuid)
        else:
            raise ValueError("Either name or uuid must be provided.")
        max_retries = 5
        retry_delay = 0.5

        for attempt in range(max_retries):
            vers_dict["Action"].click()
            try:
                btn_locator = self.page.locator(entry_dict[entry]["btn"])
                btn_locator.wait_for(state="visible", timeout=2000)
                btn_locator.click()
                break
            except TimeoutError:
                if attempt == max_retries - 1:
                    return False
            time.sleep(retry_delay)
        self.page.wait_for_load_state("load")
        if entry == "Copy Version ID":
            return self.page.evaluate("navigator.clipboard.readText()")
        elif entry == "View Invoke Command":
            self.page.locator(self.elements.ViewInvoCmdDiag).wait_for(state="visible")
        elif entry == "Un-Deploy Version":
            self.page.locator(self.elements.DisableVersBtnInDialog).click()
            self.page.locator(self.elements.SucessDisableMsg).wait_for(state="visible")
        elif entry == "Delete Version":
            self._delete_version()
        elif entry == "Manage Secrets":
            self.page.locator('div[role="dialog"]').wait_for(state="visible")
        elif entry == "Redeploy Version":
            self.page.locator("//button[text()='Redeploy Version']").click()
            self.page.locator("//span[contains(text(), 'is being redeployed')]").wait_for(
                state="visible"
            )
        if entry_dict[entry].get("sign"):
            self.page.locator(entry_dict[entry]["sign"]).wait_for(state="visible")
        return self.page

    def add_secret(self, secret: tuple):
        """Add a secret to the function.

        parameters:
        -----------
        secret: `tuple`
            The secret to be added

        Returns:
        --------
        `bool`
            True if the secret is added successfully, otherwise False.
        """
        self.page.fill('input[placeholder="Enter a key"]', f"{secret[0]}")
        self.page.fill(
            'textarea[placeholder="Enter a value as a text string or JSON"]',
            f"{secret[1]}",
        )
        self.page.click('button:has-text("Save Secrets")')
        self.page.wait_for_timeout(5000)
        if self.page.locator(
            "//span[@data-testid='kui-text' and normalize-space(text())='Secrets successfully updated.']"
        ).is_visible():
            return True
        else:
            return False

    def modify_secrets(self, secrets_list: list[dict]) -> bool:
        """Modify the secrets for the task.

        parameters:
        -----------
            secrets_list: `list`
            The list of secrets to manage
        """

        self.nameinput = "//div[@data-testid='kui-text-input-root'][.//input[@value='{0}']]"

        self.keyinput = "//div[@data-testid='kui-text-input-root'][.//input[@value='{0}']]/following-sibling::div[@data-testid='kui-text-area-root']//textarea[@data-testid='kui-text-area-element']"

        for secret in secrets_list:
            for key, value in secret.items():
                self.nameinputlocate = self.nameinput.format(key)
                logging.info(f"nameinputlocate: {self.nameinputlocate}")
                if self.page.locator(self.nameinputlocate).is_visible():
                    logging.info(f"Found Secret Name: {key}")
                    self.keyinputlocate = self.page.locator(self.keyinput.format(key))
                    self.keyinputlocate.fill(value)
                    logging.info(f"Filled Secret: {key}")
                else:
                    logging.info(f"Secret Name: {key} not found")
                    return False
        self.page.locator(self.SaveSecretBtn).click()
        logging.info("Clicked Save Secret button")
        self.page.wait_for_load_state("load")
        self.page.locator(self.SucessSecretMsg).wait_for(state="visible")
        if self.page.locator(self.SucessSecretMsg).is_visible():
            return True
        else:
            return False

    def clone_function(self, func_name: str):
        """Clone the function by name.

        parameters:
        -----------
        func_name: `str`
            The name of the function

        Raises:
        -------
        Exception:
            If the function cannot be found
        """

        logging.info(f"Clone the function from function detail page: {func_name}...")
        self.page.locator(FunctionDetailPagePagination.action_btn).first.click()
        self.page.locator(FunctionDetailPagePagination.clone_func_btn).click()
        self.page.wait_for_load_state()


class FunctionDetailPageBasicDetails(CollapsedTable):
    """Elements and related functionalities for the basic details on Function Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionsDetailPageBasicDetailsElements())

    def check_func_name(self, exp_func_name: str):
        cur_func_name = self.get_all_data()["Description"]
        assert (
            cur_func_name == exp_func_name
        ), f"Expected function name: {exp_func_name}, Actual function name: {cur_func_name}"

    def get_all_data_nim(self):
        data = {}

        section = self.page.locator(
            "//h4[text()='Basic Details']/ancestor::div[@data-testid='kui-flex'][1]/following-sibling::div"
        )
        labels = section.locator("label[data-testid='kui-label']").all()
        for label in labels:
            key = label.text_content().strip()

            value_elem = label.locator(
                "xpath=following-sibling::*[1][self::div or self::span or self::div/span or self::span/span]"
            )

            if value_elem.count() == 0:
                value_elem = label.locator(
                    "xpath=following-sibling::*[1]//span[@data-testid='kui-text']"
                )
            if value_elem.count() == 0:
                value_elem = label.locator(
                    "xpath=following-sibling::*[1]//*[self::span or self::div][@data-testid='kui-tooltip-trigger' or @data-testid='kui-badge']"
                )
            if value_elem.count() > 0:
                value = value_elem.first.text_content().strip()
            else:
                value = ""
            data[key] = value

        return data

    def wait_for_status_active(self, timeout=1800, interval=20):
        start_time = time.time()
        while time.time() - start_time < timeout:
            self.page.wait_for_selector("//h4[text()='Basic Details']", state="visible")
            basic_details_data = self.get_all_data_nim()
            status = basic_details_data.get("Status", "").strip().upper()
            remaining_time = int(timeout - (time.time() - start_time))
            logging.info(f"Current status: {status},left time: {remaining_time}s")
            if status == "ACTIVE":
                return True
            elif status == "ERROR" or status == "INACTIVE":
                return False
            time.sleep(interval)
        return False


class FunctionDetailPageRuntimeDetails(CollapsedTable):
    """Elements and related functionalities for the runtime details on Function Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionsDetailPageRuntimeDetailsElements())


class FunctionDetailPageModelDetails(CollapsedTable):
    """Elements and related functionalities for the model details on Function Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionsDetailPageModelDetailsElements())


class FunctionDetailPageEnvironmentVariables(CollapsedTable):
    """Elements and related functionalities for the environment variables on Function Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionsDetailPageEnvironmentVariablesElements())


class FunctionDetailPageConfigurationDetails(CollapsedTable):
    """Elements and related functionalities for the configuration details on Function Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    SECTION_NAME = "Configuration Details"

    def __init__(self, page: Page):
        super().__init__(page, section=FunctionDetailPageConfigurationDetails.SECTION_NAME)

    def get_all_function_configuration(self):
        """Get all the function configuration details.

        Returns:
        --------
        `dict`
            The function configuration details
        """

        section = (
            self.page.locator(
                'h4[data-testid="kui-text"]', has_text="Function Configuration"
            )
            .locator("xpath=..")
            .locator("xpath=..")
        )

        boxes = section.locator('[data-testid="kui-box"]')
        count = boxes.count()
        result = {}

        for i in range(count):
            label = (
                boxes.nth(i).locator('label[data-testid="kui-label"]').inner_text().strip()
            )
            value = (
                boxes.nth(i).locator('span[data-testid="kui-text"]').inner_text().strip()
            )
            result[label] = value

        return result


class FunctionDetailPageInstanceDetails(CollapsedTable):
    """Elements and related functionalities for the configuration details on Function Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    SECTION_NAME = "Instance Details"

    def __init__(self, page: Page):
        super().__init__(page, section=FunctionDetailPageInstanceDetails.SECTION_NAME)
