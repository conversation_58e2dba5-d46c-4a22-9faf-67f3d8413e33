import logging

from playwright.sync_api import Page, Locator

from component.components import Form, PageMisc, DropDownSelector, PaginationTable

from element.deployments_create_page_elements import (
    DeploymentsCreatePageMiscElements,
    DeploymentsCreatePageFunctionNameSelectorElements,
    DeploymentsCreatePageFunctionVersionSelectorElements,
    DeploymentsCreatePageInstanceTypePagePaginationElements,
    DeploymentsCreatePageInstanceRegionsFilterDropDownSelectorElements,
    DeploymentsCreatePageInstanceClustersFilterDropDownSelectorElements,
    DeploymentsCreatePageInstanceAttributesFilterDropDownSelectorElements,
    DeploymentsCreatePageDeploymentSpecificationsFormElements,
)


class DeploymentsCreatePageMisc(PageMisc):
    """Contains all the elements and operations of the side bar on the Functions List Page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, DeploymentsCreatePageMiscElements())

    def deploy_version_btn(self):
        self.page.locator(self.elements["DeployVersionBtn"]).wait_for()
        self.page.wait_for_timeout(1000)
        self.page.locator(self.elements["DeployVersionBtn"]).click()
        logging.info("Click deploy version btn")
        self.page.wait_for_selector(self.elements["AnyBanner"], state="visible")
        if self.page.locator(self.elements["DeployingBanner"]).count() == 1:
            self.page.wait_for_selector(self.elements["DeployingBanner"], state="hidden")
        else:
            logging.error("Not found the deploying banner.")

    def review_deployment_details(
        self, expected_version_id, edit_configuration_in_review=False
    ):
        """Review and check the deployment details.

        parameters:
        -----------
        expected_version_id: The expected version ID.
        edit_configuration_in_review: if to edit configuration
        """
        deploy_button = self.page.get_by_role("button", name="Deploy Version")
        deploy_button.wait_for(state="visible", timeout=30000)
        deploy_button.click()

        # try:
        #     self.page.locator(self.elements["DeployFuncVersBtn"]).wait_for()
        # except Exception as e:
        #     print(e)

        # displayed_version_id = self.page.locator(
        #     self.elements["FunctionVersionTextInReviewPage"]
        # ).text_content()
        # assert (
        #     displayed_version_id == expected_version_id
        # ), f"The function version ID not correct, expected {expected_version_id}, actually {displayed_version_id}"

        # # Edit the configuration if needed
        # if edit_configuration_in_review:
        #     concurrecy_to_edit = str(random.randint(3, 512))
        #     self.page.locator(self.elements["EditConfigurationBtn"]).click()
        #     self.page.locator(self.elements["FirstMaxConcurrencyInput"]).wait_for()
        #     self.page.locator(self.elements["FirstMaxConcurrencyInput"]).fill(
        #         concurrecy_to_edit
        #     )
        #     self.page.locator(self.elements["ReviewDeployBtn"]).wait_for()
        #     self.page.locator(self.elements["ReviewDeployBtn"]).click()
        #     self.page.locator(self.elements["DeployFuncVersBtn"]).wait_for()
        #     assert (
        #         concurrecy_to_edit
        #         in self.page.locator(
        #             self.elements["MaxConcurrencyTextinReviewPage"]
        #         ).text_content()
        #     )
        # self.page.locator(self.elements["DeployFuncVersBtn"]).click()
        # self.page.wait_for_selector(self.elements["AnyBanner"], state="visible")
        # if self.page.locator(self.elements["DeployingBanner"]).count() == 1:
        #     self.page.wait_for_selector(self.elements["DeployingBanner"], state="hidden")
        # else:
        #     pytest.fail("Not found the deploying banner.")

    def check_drop_down_selector_default_displayed_content(self):
        """Check the default displayed content in deployment create page drop down selectors.

        parameters:
        -----------
        None
        """
        self.page.locator(
            self.elements["InstanceTypeRegionsFilterSelectorBeforeChoose"]
        ).wait_for()
        assert (
            self.page.locator(
                self.elements["InstanceTypeRegionsFilterSelectorBeforeChoose"]
            ).count()
            == 1
            and self.page.locator(
                self.elements["InstanceTypeClustersFilterSelectorBeforeChoose"]
            ).count()
            == 1
            and self.page.locator(
                self.elements["InstanceTypeAttributesFilterSelectorBeforeChoose"]
            ).count()
            == 1
            and self.page.locator(
                self.elements["RegionSelectorInDeploymentSpecbyInstanceBeforeChoose"]
            ).count()
            >= 1
            and self.page.locator(
                self.elements["ClusterSelectorInDeploymentSpecbyInstanceBeforeChoose"]
            ).count()
            >= 1
            and self.page.locator(
                self.elements["AttributeSelectorInDeploymentSpecbyInstanceBeforeChoose"]
            ).count()
            >= 1
        ), "The default displayed content in drop down selector is not correct."


class DeploymentsCreatePageFunctionNameSelector(DropDownSelector):
    """Contains all the elements and operations of the function name selector in deployment create page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, DeploymentsCreatePageFunctionNameSelectorElements())


class DeploymentsCreatePageFunctionVersionSelector(DropDownSelector):
    """Contains all the elements and operations of the function version selector in deployment create page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, DeploymentsCreatePageFunctionVersionSelectorElements())


class DeploymentsCreatePageInstanceTypePagePagination(PaginationTable):
    """Contains all the elements and operations of the instance type table selector in deployment create page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, DeploymentsCreatePageInstanceTypePagePaginationElements())

    def choose_instances_items_in_multiple_selector(self, instance_list: list[str]):
        """Choose the instance by multiple checkboxes

        parameters:
        -----------
        instance_list: `list[str]`
            The instance name list need to be checked

        Returns:
        --------
        """
        while True:
            instance_row_items = self._page.locator(self.elements["TableItems"]).all()
            for instance_item in instance_row_items:
                instance_info = self.parse_instances_item(instance_item)
                logging.info(f"Instance INFO is {instance_info}")
                if instance_info["Name"].split(" ")[0] in instance_list:
                    instance_info["CheckboxBtn"].click()
            if not self.page.locator(
                self.elements["InstanceTypeTableRightArrowBtn"]
            ).is_disabled():
                self.page.locator(self.elements["InstanceTypeTableRightArrowBtn"]).click()
                self.page.wait_for_load_state("load")
                self.page.wait_for_selector(self.elements["InstanceTypeTableRightArrowBtn"])
                continue
            else:
                break

    def parse_instances_item(self, instances_item: Locator) -> dict:
        """Parse each function properties from the table row.

        parameters:
        -----------
        vers_item: `Locator`
            The locator of the version item

        Returns:
        --------
        vers_info: `Dict`
            include the version properties
        """
        instance_item = instances_item.locator("//td")
        instances_info = {
            "CheckboxBtn": instance_item.nth(0).locator("//input"),
            "Name": instance_item.nth(1).text_content().strip(),
            "CPUs": instance_item.nth(2).text_content().strip(),
            "RAM": instance_item.nth(3).text_content().strip(),
            "GPUs": instance_item.nth(4).text_content().strip(),
            "VRAM": instance_item.nth(5).text_content().strip(),
            "Storage": instance_item.nth(6).text_content().strip(),
            "GPU Driver": instance_item.nth(7).text_content().strip(),
        }
        return instances_info

    def edit_column_filters(self):
        pass

    def get_current_page_data(self):
        pass

    def get_displayed_rows(self):
        pass

    def get_total_page_data(self):
        pass

    def get_total_pages(self):
        pass

    def go_to_page(self):
        pass

    def next_page(self):
        pass

    def previous_page(self):
        pass

    def set_displayed_rows(self):
        pass


class DeploymentsCreatePageInstanceRegionsFilterDropDownSelector(DropDownSelector):
    """Elements and related functionalities of the regions selector.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(
            page, DeploymentsCreatePageInstanceRegionsFilterDropDownSelectorElements()
        )


class DeploymentsCreatePageInstanceClustersFilterDropDownSelector(DropDownSelector):
    """Elements and related functionalities of the clusters selector.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(
            page, DeploymentsCreatePageInstanceClustersFilterDropDownSelectorElements()
        )


class DeploymentsCreatePageInstanceAttributesFilterDropDownSelector(DropDownSelector):
    """Elements and related functionalities of the regions selector.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(
            page, DeploymentsCreatePageInstanceAttributesFilterDropDownSelectorElements()
        )


class DeploymentsCreatePageDeploymentSpecificationsForm(Form):
    """Elements and related functionalities of the deployment specifications form.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page):
        super().__init__(page, DeploymentsCreatePageDeploymentSpecificationsFormElements())

    def fill_in_deployment_specifications(self, instance_detail_info, time_out=2000):
        """Fill in the deployment specifications form in deployment create page.

        parameters:
        -----------
        instance_detail_info: A dict of deployment details info
            name (Required): the name of instance
            target_region_list (Optional): the target list to deploy regions of this instance
            min_instances (Optional): the min number of instances to deploy
            max_instances (Optional): the max number of instances to deploy
            max_concurrency (Optional): max concurrency of the deployment
            target_attributes_list (Optional): the target list to deploy attributes of this instance
        """
        instance_name = instance_detail_info["name"]

        # Choose target region if needed
        if "target_region_list" in instance_detail_info:
            region_selector_element = self.elements[
                "RegionSelectorInDeploymentSpecbyInstance"
            ].format(instance_name)
            region_selector_in_deployment_spec = DropDownSelector(self.page, self.elements)
            region_selector_in_deployment_spec.multiple_select(
                region_selector_element, instance_detail_info["target_region_list"]
            )

        if "cluster_list" in instance_detail_info:
            cluster_selector_element = self.elements[
                "ClusterSelectorInDeploymentSpecbyInstance"
            ].format(instance_name)
            cluster_selector_in_deployment_spec = DropDownSelector(self.page, self.elements)
            cluster_selector_in_deployment_spec.multiple_select(
                cluster_selector_element, instance_detail_info["cluster_list"]
            )

        # Fill in Min instance if needed
        if "min_instances" in instance_detail_info:
            min_instance_input_locator = f"(//span[contains(text(),'{instance_name}')])[2]/../..//input[contains(@name, 'optionalInstanceConfigs.minInstances')]"
            input_field = self.page.locator(min_instance_input_locator)
            input_field.fill(str(instance_detail_info["min_instances"]))

        # Fill in Max instance if needed
        if "max_instances" in instance_detail_info:
            max_instance_input_locator = f"(//span[contains(text(),'{instance_name}')])[2]/../..//input[contains(@name, 'optionalInstanceConfigs.maxInstances')]"
            input_field = self.page.locator(max_instance_input_locator)
            input_field.fill(str(instance_detail_info["max_instances"]))

        # Fill in Max concurrency if needed
        if "max_concurrency" in instance_detail_info:
            max_concurrency_input_locator = f"(//span[contains(text(),'{instance_name}')])[2]/../..//input[contains(@name, 'optionalInstanceConfigs.maxRequestConcurrency')]"
            input_field = self.page.locator(max_concurrency_input_locator)
            input_field.fill(str(instance_detail_info["max_concurrency"]))

        # Choose target attribute if needed
        if "target_attributes_list" in instance_detail_info:
            attribute_selector_element = self.elements[
                "AttributeSelectorInDeploymentSpecbyInstance"
            ].format(instance_name)
            attribute_selector_in_deployment_spec = DropDownSelector(
                self.page, self.elements
            )
            attribute_selector_in_deployment_spec.multiple_select(
                attribute_selector_element, instance_detail_info["target_attributes_list"]
            )

        self.page.wait_for_timeout(time_out)
