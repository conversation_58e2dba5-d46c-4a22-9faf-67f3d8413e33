import logging
from typing import Literal

from cloudia.ui.base_page import BasePage
from playwright.sync_api import Page, Locator
from component.components import (
    Form,
    PageMisc,
    SearchBar,
    PaginationTable,
    DropDownSelector,
)
from element.deployments_list_page_elements import (
    DeployVersionPageFormElements,
    DeploymentsListPageMiscElements,
    DeploymentsListPageSearchBarElements,
    DeploymentsListPagePaginationTableElements,
    DeploymentsListPageKeyExpirationDropDownSelectorElements,
)


class DeployVersionForm(Form):
    def __init__(self, page):
        super().__init__(page, DeployVersionPageFormElements())

    def deploy_function_in_detail(
        self,
        func_name: str,
        min_instances: int,
        max_instances: int,
        func_version: str = None,
        backend: str = None,
        gpu: str = None,
        instance_type: str = None,
        max_concurrency: int = None,
        **kwargs,
    ):
        """Deploy the function version.

        parameters:
        -----------
        func_name: `str`
            The name of the function
        vers_id: `str`
            The version id of the function
        min_instances: `int`
            The min instances of the function
        max_instances: `int`
            The max instances of the function
        func_version: `str`
            The version of the function
        backend: `str`
            The backend of the function
        gpu: `str`
            The gpu of the function
        instance_type: `str`
            The instance type of the function
        max_cocurrency: `int`
            The max cocurrency of the function

        Returns:
        --------
        None
        """
        logging.info("Deploying function version:")
        logging.info(f"Function Name: {func_name}")
        logging.info(f"Function Version: {func_version}")
        logging.info(f"Backend: {backend}")
        logging.info(f"GPU: {gpu}")
        logging.info(f"Instance Type: {instance_type}")
        logging.info(f"Max Concurrency: {max_concurrency}")
        logging.info(f"Min Instances: {min_instances}")
        logging.info(f"Max Instances: {max_instances}")

        # self.page.locator(self.elements["ReviewDeployBtn"]).wait_for(state="visible")
        self.page.wait_for_load_state()
        self.page.locator(self.elements["BackendSelector"]).click()
        if backend:
            self.page.locator(self.elements.BackendOption.format(backend)).click()
        else:
            self.page.locator(self.elements["BackendEntryGCP"]).click()
        self.page.locator(self.elements["GPUSelector"]).click()
        if gpu:
            self.page.locator(self.elements.GPUOption.format(gpu)).click()
        else:
            self.page.keyboard.press(key="Enter")

        self.page.locator(self.elements["InstanceTypeSelector"]).click()
        if instance_type:
            self.page.locator(
                self.elements.InstanceTypeOption.format(instance_type)
            ).click()
        else:
            self.page.keyboard.press(key="Enter")

        self.page.locator(self.elements["MinInstanceInput"]).fill(str(min_instances))
        self.page.locator(self.elements["MaxInstanceInput"]).fill(str(max_instances))
        self.page.locator(self.elements["ReviewDeployBtn"]).click()
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements["DeployVersionBtn"]).wait_for(state="visible")
        self.page.locator(self.elements["DeployVersionBtn"]).click()
        self.page.wait_for_load_state("load")


class DeploymentsListPageKeyExpirationDropDownSelector(DropDownSelector):
    """Elements and related functionalities of the regions selector.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, DeploymentsListPageKeyExpirationDropDownSelectorElements())


class DeploymentsListPageMisc(PageMisc):
    """Contains all the elements and operations of the miscs on the Deployments List Page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object.
    """

    contextMenu = "//div[@data-testid='kui-logo']/../..//div[@data-testid='kui-avatar'] | //span[text()='Welcome Guest']"
    setup = "//div[@role='menu']//div[text()='Setup']"
    GenerateKeyButton = "//button[contains(text(),'Generate API Key')]"
    ActionButton = (
        "//td[normalize-space()='{0}']/parent::tr//button[@data-testid='kui-menu-trigger']"
    )
    DeleteKeyButton = (
        "//div[@data-testid='kui-menu-item'][contains(., 'Delete Personal Key')]"
    )
    confirmDelete = "//button[normalize-space()='Delete Personal Key']"
    deleteKeySuccess = "//span[contains(text(), 'successfully deleted')]"
    enterServiceKey = "//span[text()='Service Keys']"
    createSerKey = "//button[text()=' Create Service Key']"
    inputSerKey = "//div[text()='Select service']/..//input"
    service_key_scope_fillbox = (
        "//div[text()='Cloud Functions']/../../../../..//label[text()='Scope']/../..//input"
    )
    service_key_entity_type_fillbox = "//div[text()='Select entity type']/..//input"
    service_key_copy_button = "//button/*[@data-icon-name='common-copy-generic']"
    deleteSerKey = "//span[text()='Delete Service Key']"
    deleteSerKeyCon = "//button[normalize-space()='Delete Service Key']"
    closePage = "//button[text()='Close']"
    serActionBtn = (
        "//div[@title='{0}']/following-sibling::div//button[@data-testid='kui-button']"
    )

    def __init__(self, page: Page):
        super().__init__(page, DeploymentsListPageMiscElements())
        self.KeyExpirationDropDownSelector = (
            DeploymentsListPageKeyExpirationDropDownSelector(page)
        )

    def generate_key_in_deployments_list_page(self, key_name: str, expiration: str):
        """Generate a key in the deployments list page.

        Returns:
        --------
        api key: `str`
            The api key
        """
        self.page.locator(self.elements["GenerateKeyButton"]).click()
        self.page.locator(self.elements["KeyNameInput"]).fill(key_name)
        KeyExpirationBox_element = self.elements["KeyExpirationBox"]
        self.KeyExpirationDropDownSelector.select(KeyExpirationBox_element, expiration)
        self.page.locator(self.elements["GenerateKeyConfirmButton"]).click()
        self.page.wait_for_load_state("load")
        self.page.wait_for_timeout(1000)
        self.page.wait_for_load_state("load")
        warning_message = self.page.locator(
            "//span[text()='Actor reached maximum allowed number of keys in service']"
        )
        if warning_message.is_visible():
            raise Exception(
                "Actor reached maximum allowed number of keys in service, please delete some keys first"
            )
        self.page.locator(DeploymentsListPageMisc.service_key_copy_button).click()
        copied_key = self.page.evaluate("navigator.clipboard.readText()")
        self.page.locator(self.elements["CopyKeyAndClose"]).click()
        return copied_key

    def _handle_accept_all_popup(self):
        """Handle Accept All popup if it appears.

        Returns:
        --------
        bool: True if popup was handled, False if no popup found
        """
        try:
            accept_button = self.page.locator("//button[text()='Accept All']")
            if accept_button.is_visible():
                accept_button.click()
                logging.info("Clicked Accept All button")
                return True
            return False
        except Exception as e:
            logging.debug(f"No Accept All popup found: {e}")
            return False

    def delete_key_in_deployments_list_page(self, key_name: str):
        """Delete a key in the deployments list page.

        Returns:
        --------
        None
        """
        self.page.locator(self.contextMenu).click()
        self.page.locator(self.setup).click()
        self.page.wait_for_load_state("load")

        self._handle_accept_all_popup()

        self.page.locator(self.GenerateKeyButton).click()
        self.page.locator(self.ActionButton.format(key_name)).click()
        self.page.locator(self.DeleteKeyButton).click()
        self.page.wait_for_timeout(1000)
        self.page.locator(self.confirmDelete).click()
        self.page.wait_for_load_state("load")
        self.page.locator(self.deleteKeySuccess).wait_for(state="visible")
        if not self.page.locator(self.deleteKeySuccess).is_visible():
            raise Exception("Delete key failed")

    def generate_service_key_in_deployments_list_page(self, key_name: str, expiration: str):
        """Generate a service key in the deployments list page with predefined scopes and settings.

        This function:
        1. Fills key name and selects expiration time
        2. Selects service -> Cloud Functions
        3. Selects scope -> invoke function, list functions and queue details
        4. Selects entity type -> All Functions
        5. Completes the key creation process

        Parameters:
        -----------
        key_name: str
            Name for the service key
        expiration: str
            Expiration period for the key, e.g. "1 hour"

        Returns:
        --------
        api key: `str`
            The generated service API key
        """
        # Step 1: Access Setup menu
        logging.info("Step 1: Accessing Setup menu")
        self.page.locator(self.contextMenu).click()
        self.page.locator(self.setup).click()
        self.page.wait_for_load_state("load")

        if self.page.locator("//button[text()='Accept All']").is_visible():
            self.page.locator("//button[text()='Accept All']").click()

        # Step 2: Navigate to Service Keys
        logging.info("Step 2: Navigating to Service Keys section")
        self.page.locator(DeploymentsListPageMisc.enterServiceKey).click()
        self.page.wait_for_load_state("load")

        # Step 3: Click Create Service Key button
        logging.info("Step 3: Clicking Create Service Key button")
        self.page.locator(DeploymentsListPageMisc.createSerKey).click()
        self.page.wait_for_load_state("load")

        # Step 4: Fill key name
        logging.info(f"Step 4: Filling key name: {key_name}")
        service_key_name_fillbox = "//label[text()='Name']/../..//input"
        self.page.locator(service_key_name_fillbox).fill(key_name)

        # Step 5: Select expiration
        if expiration:
            modal_loactor = self.page.get_by_text("Create Service Key")
            combobox_locator = modal_loactor.locator("..").get_by_role("combobox").first
            base_page = BasePage(self.page)
            base_page.choose_dropdown("", [expiration], dropdown_locater=combobox_locator)

        # Step 6: Select Service -> Cloud Functions
        logging.info("Step 6: Selecting Service -> Cloud Functions")
        self.page.locator(DeploymentsListPageMisc.inputSerKey).click()
        self.page.get_by_role("option", name="Cloud Functions", exact=True).click()

        # Step 7: Select scopes: invoke function, list functions, queue details
        logging.info("Step 7: Selecting scopes")
        scope_list = ["Invoke Function", "List Functions", "Queue Details"]
        for scope_value in scope_list:
            logging.info(f"Choose scope value as {scope_value}")
            self.page.locator(DeploymentsListPageMisc.service_key_scope_fillbox).click()
            self.page.get_by_role("option", name=scope_value, exact=True).click()

        # Step 8: Select Entity Type -> All Functions
        logging.info("Step 8: Selecting Entity Type -> All Functions")
        self.page.locator(DeploymentsListPageMisc.service_key_entity_type_fillbox).click()
        self.page.get_by_role("option", name="All Functions", exact=True).click()

        # Step 9: Click Next Step
        logging.info("Step 9: Clicking Next Step button")
        service_key_next_step_button = "//button[text()='Next Step']"
        self.page.locator(service_key_next_step_button).click()
        self.page.wait_for_load_state("load")

        # Step 10: Click Confirm
        logging.info("Step 10: Clicking Confirm button")
        service_key_confirm_button = "//button[text()='Confirm']"
        self.page.locator(service_key_confirm_button).click()
        self.page.wait_for_load_state("load")

        # Step 11: Copy the generated API key
        logging.info("Step 11: Copying the generated API key")
        self.page.wait_for_timeout(1000)
        self.page.locator(DeploymentsListPageMisc.service_key_copy_button).click()
        copied_service_key_value = self.page.evaluate("navigator.clipboard.readText()")
        self.page.locator(DeploymentsListPageMisc.closePage).click()
        return copied_service_key_value

    def delete_service_key_in_deployments_list_page(self, key_name: str):
        """Delete a key in the deployments list page.

        Returns: a service key
        --------
        None
        """
        # Step 1: Access Setup menu
        logging.info("Step 1: Accessing Setup menu")
        self.page.locator(self.contextMenu).click()
        self.page.locator(self.setup).click()
        self.page.wait_for_load_state("load")

        # Step 2: Navigate to Service Keys
        logging.info("Step 2: Navigating to Service Keys section")
        self.page.locator(DeploymentsListPageMisc.enterServiceKey).click()
        self.page.wait_for_load_state("load")
        self.page.locator(DeploymentsListPageMisc.enterServiceKey).click()
        self.page.wait_for_load_state("load")

        # Step 3: Delete Service Keys
        self.page.locator(self.serActionBtn.format(key_name)).click()
        self.page.locator(DeploymentsListPageMisc.deleteSerKey).click()
        self.page.wait_for_timeout(1000)
        self.page.locator(DeploymentsListPageMisc.deleteSerKeyCon).click()
        self.page.wait_for_load_state("load")
        self.page.locator(self.deleteKeySuccess).wait_for(state="visible")
        if not self.page.locator(self.deleteKeySuccess).is_visible():
            raise Exception("Delete key failed")


class DeploymentsListPageSearchBar(SearchBar):
    """Contains all the elements and operations of the search bar in the Deployments List Page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, DeploymentsListPageSearchBarElements())

    def search(self, input_str):
        """Search an entry.

        Params input_str: The input str to be searched
        Returns:
        --------
        None
        """
        super().search(self.elements["SearchBarInput"], input_str)


class DeploymentsListPagePaginationTable(PaginationTable):
    """Contains all the elements and operations of the pagination table in the Deployments List Page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, DeploymentsListPagePaginationTableElements())

    def get_current_page_data(self):
        page_data_list = []
        deployment_list = self.page.locator(self.elements["DeploymentsItems"]).all()
        for deployment_item in deployment_list:
            page_data_list.append(self.parse_deployments_items(deployment_item))
        return page_data_list

    def parse_deployments_items(self, instances_item: Locator) -> dict:
        """Parse each function properties from the table row.

        parameters:
        -----------
        vers_item: `Locator`
            The locator of the version item

        Returns:
        --------
        vers_info: `Dict`
            include the version properties
        """
        instance_item = instances_item.locator("//td")
        instance_types_str = ",".join(
            item.text_content().strip()
            for item in instance_item.nth(3).locator("xpath=//button").all()
        )
        instances_info = {
            "Status": instance_item.nth(0).locator("xpath=/div").text_content().strip(),
            "FunctionName": instance_item.nth(1)
            .locator("xpath=//a")
            .text_content()
            .strip(),
            "VersionID": instance_item.nth(2).text_content().strip(),
            "InstanceTypes": instance_types_str,
            "CreatedDate": instance_item.nth(4).text_content().strip(),
            "Actions": instance_item.nth(5).locator("xpath=//button"),
        }
        return instances_info

    def get_deployment_by_name(self, name: str) -> dict:
        """Get function item locator by name

        parameters:
        -----------
        name: `str`
            The name of the function

        Returns:
        --------
        deploy_info: `Dict`
            The properties of the deployment
        """
        deploy_items = self._page.locator(self.elements.TableItems).all()
        for deploy_item in deploy_items:
            deploy_info = self.parse_deploy_item(deploy_item)
            if deploy_info["Function Name"] == name:
                return deploy_info
        return dict()

    def get_deployment_by_uuid(self, uuid: str) -> dict:
        """Get function item locator by uuid

        parameters:
        -----------
        uuid: `str`
            The uuid of the function

        Returns:
        --------
        func_info: `Dict`
            The properties of the function
        """
        while True:
            deploy_items = self._page.locator(self.elements.TableItems).all()
            for deploy_item in deploy_items:
                deploy_info = self.parse_deploy_item(deploy_item)
                logging.info(f"deploy_info: {deploy_info}\n")
                if deploy_info["Version ID"] == uuid:
                    return deploy_info
                else:
                    logging.info(
                        f"func name {deploy_info['Function Name']}, version id {deploy_info['Version ID']} != {uuid}"
                    )
            if not self.next_page():
                break
        return dict()

    def parse_deploy_item(self, deploy_item: Locator) -> dict:
        """Parse each deployment properties from the table row.

        parameters:
        -----------
        deploy_item: `Locator`
            The locator of the deployment item

        Returns:
        --------
        deploy_info: `Dict`
            include the deploy properties
        """
        deploy_properties = deploy_item.locator("//td")
        deploy_info = {
            "Status": deploy_properties.locator("nth=0").text_content().strip(),
            "Function Name": deploy_properties.locator("nth=1").text_content().strip(),
            "Version ID": deploy_properties.locator("nth=2").text_content().strip(),
            "Instance Types": deploy_properties.locator("nth=3").text_content().strip(),
            "Created Date": deploy_properties.locator("nth=4").text_content().strip(),
            "Action": deploy_properties.locator("nth=5").locator("//button"),
        }
        return deploy_info

    def action_to(
        self,
        name: str = None,
        uuid: str = None,
        entry: Literal[
            "Edit Deployment",
            "Cancel Deployment",
            "View Function Details",
            "View Version Details",
        ] = "View Function Details",
    ) -> Page:
        """Perform action to the current function.

        parameters:
        -----------
        name: `str`
            The name of the function
        uuid: `str`
            The uuid of the function
        entry: `Literal["Deploy Version", "View Function Details", "View Version Details", "View Vesion Logs", "New Version", "Clone Function", "Delete Version"]`
            The action to be performed

        returns:
        -----------
        page : `Playwright.sync_api.page.Page`
            The page object
        """
        entry_dict = {
            "Edit Deployment": {
                "btn": self.elements.EditDeploymentBtn,
            },
            "Cancel Deployment": {
                "btn": self.elements.CancelDeployBtn,
            },
            "View Function Details": {
                "btn": self.elements.ViewFunctionDetailsBtn,
            },
            "View Version Details": {
                "btn": self.elements.ViewVersionDetailsBtn,
            },
        }
        if name:
            func_dict = self.get_deployment_by_name(name)
        elif uuid:
            func_dict = self.get_deployment_by_uuid(uuid)
            if not func_dict:
                raise ValueError(f"Deployment not found for {uuid}")
        else:
            raise ValueError("Either name or uuid must be provided.")
        func_dict["Action"].click()
        self.page.locator(entry_dict[entry]["btn"]).click()
        self.page.wait_for_load_state("load")
        if entry == "Cancel Deployment":
            AllowTaskCompleteMsg = self.page.locator(
                self.elements.AllowTaskCompleteMsg
            ).text_content()
            logging.info(f"AllowTaskCompleteMsg is: {AllowTaskCompleteMsg}")
            assert (
                AllowTaskCompleteMsg
                == "Allow current tasks to complete before terminating instances"
            )
            self.page.locator(self.elements.DisableVersBtnInDialog).click()
            self.page.locator(self.elements.SucessDisableMsg).wait_for(state="visible")
        return self.page

    def edit_column_filters(self):
        pass

    def get_displayed_rows(self):
        pass

    def get_total_page_data(self):
        pass

    def get_total_pages(self):
        pass

    def go_to_page(self):
        pass

    def next_page(self) -> bool:
        """Go to the next page if available.

        Returns:
        --------
        `bool`
            True if the next page is available, otherwise False.
        """
        if self.page.locator(self.elements.NextPageButton).is_enabled():
            self.page.locator(self.elements.NextPageButton).click()
            self.page.wait_for_load_state()
            return True
        return False

    def previous_page(self):
        pass

    def set_displayed_rows(self):
        pass
