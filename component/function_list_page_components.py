from component.components import (
    <PERSON>B<PERSON>,
    SwitchBar,
    <PERSON>Bar,
    PaginationTable,
)
from element.function_list_page_elements import (
    FunctionListPageSideBarElements,
    FunctionListPageSwitchBarElements,
    FunctionListPageNavigationBarElements,
    FunctionListPageFunctionPaginationElements,
)


from component.deployments_list_page_components import (
    DeployVersionForm,
)

from component.function_detail_page_components import (
    FunctionDetailPageNavigationBar,
)

from component.create_function_page_components import (
    CreateFunctionPageForm,
)


from playwright.sync_api import Page, Locator
from playwright._impl._errors import TimeoutError
import logging
from typing import Optional, Dict, Literal, Union, Tuple
import random
import time


class FunctionListPageSideBar(SideBar):
    """Contains all the elements and operations of the side bar on the Functions List Page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionListPageSideBarElements())


class FunctionListPageSwitchBar(SwitchBar):
    """Allows the user to toggle between different tabs.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionListPageSwitchBarElements())

    def switch_to_specific_tab(self, tab_name: str):
        """Switch to specific tab on function list page

        parameters:
        -----------
        tab_name: `str`
            The name of the tab

        Returns:
        --------
        No
        """
        tab_mapping = {
            "My Functions": self.elements.MyFunctions,
            "Shared Functions": self.elements.SharedFunctions,
            "Created by NVIDIA": self.elements.CreateByNVIDIA,
        }

        if tab_name in tab_mapping:
            self._page.locator(tab_mapping[tab_name]).click()
            self._page.wait_for_load_state("load")
        else:
            raise ValueError(f"Tab name '{tab_name}' is not recognized.")


class FunctionListPageNavigationBar(NavigationBar):
    """Contains all the elements and related operations of the navigation bar on the Functions List Page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionListPageNavigationBarElements())


class FunctionListPagePagination(PaginationTable):
    """Contains all elements and related operations of the function pagination on the Functions List Page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object
    """

    search_table_input = "//input[starts-with(@placeholder, 'Search')]"
    search_table_input_text = (
        "//input[@data-testid='kui-text-input-element' and @type='text']"
    )
    match_one_result_span = "//span[text()='1'][text()='Result']"
    match_two_result_span = "//span[text()='2'][text()='Results']"
    match_multiple_span = "//span[text()='Results']"
    header_actions_id = "#data-view-header-row-actions"
    clone_func_btn = "//div[@role='menuitem' and text()='Clone Version']"
    view_version_logs = "//div[@role='menuitem' and text()='View Logs']"
    status_button = "//button[normalize-space()='Status']"
    SaveSecretBtn = (
        "//button[@data-testid='kui-button' and normalize-space()='Save Secrets']"
    )
    SucessSecretMsg = (
        "//span[@data-testid='kui-text' and contains(text(), 'successfully updated')]"
    )

    def __init__(self, page: Page):
        super().__init__(page, FunctionListPageFunctionPaginationElements())
        self.filter_columns = {
            "func_name": "Version Name",
            "func_id": "Function ID",
            "status": "Status",
            "type": "Type",
            "create_date": "Created Date",
        }

    def get_function_by_func_id(self, func_id: str) -> Dict:
        """Get function item locator by func_id

        parameters:
        -----------
        func_id: `str`
            The uuid of the function

        Returns:
        --------
        func_info: `Dict`
            The properties of the function
        """
        # First ensure table is loaded
        try:
            # Wait for the table to be visible and loaded
            self._page.locator(self.elements.TableItems).first.wait_for(
                state="visible", timeout=30000
            )

            # Wait a bit for content to stabilize (helps with dynamic content)
            self._page.wait_for_load_state("networkidle", timeout=10000)

            # Get all function items
            func_items = self._page.locator(self.elements.TableItems).all()

            if not func_items:
                return {}

            # Iterate through items with better error handling
            for func_item in func_items:
                try:
                    func_info = self.parse_func_item(func_item)
                    if func_info and func_info.get("Function ID") == func_id:
                        return func_info
                except Exception as e:
                    logging.warning(f"Error parsing function item: {str(e)}")
                    continue
            return {}
        except Exception as e:
            logging.error(f"Error finding function by ID {func_id}: {str(e)}")
            return {}

    def check_function_status(self, function_id: str) -> str:
        """Get the status of a function by its ID.

        parameters:
        -----------
        function_id: `str`
            The uuid of the function

        Returns:
        --------
        status: `str`
            The status of the function, or empty string if function not found
        """
        # Retry up to 3 times with refresh in case of table loading issues
        max_attempts = 3
        self.search_function_exact_match(function_id)
        for attempt in range(max_attempts):
            try:
                # Try to refresh the page to ensure we have the latest data
                if attempt > 0:
                    logging.info(
                        f"Retrying function status check for {function_id}, attempt {attempt+1}/{max_attempts}"
                    )
                    # Click refresh button if available
                    try:
                        refresh_button = self._page.locator(self.elements.RefreshButton)
                        if refresh_button.count() > 0:
                            refresh_button.click()
                            self._page.wait_for_load_state("networkidle", timeout=10000)
                    except Exception as e:
                        logging.warning(f"Failed to refresh: {str(e)}")

                # Get function details using the ID
                func_dict = self.get_function_by_func_id(function_id)

                # Check if we got a valid result with status
                if func_dict and "Status" in func_dict:
                    status = func_dict.get("Status", "")
                    logging.info(f"Function status is: {status}")
                    return status

                if attempt == max_attempts - 1:
                    logging.warning(
                        f"Function with ID {function_id} not found or missing status information"
                    )
                    return ""

                # Wait briefly before retrying
                self._page.wait_for_timeout(3000)

            except Exception as e:
                logging.error(
                    f"Error checking function status on attempt {attempt+1}/{max_attempts}: {str(e)}"
                )
                if attempt == max_attempts - 1:
                    return ""
                # Wait before retrying
                self._page.wait_for_timeout(3000)

        return ""

    def get_function_by_name(self, name: str) -> Dict:
        """Get function item locator by name

        parameters:
        -----------
        name: `str`
            The name of the function

        Returns:
        --------
        func_info: `Dict`
            The properties of the function
        """
        func_items = self._page.locator(self.elements.TableItems).all()
        for func_item in func_items:
            func_info = self.parse_func_item(func_item)
            if func_info["Version Name"] == name:
                return func_info
        return dict()

    def get_function_by_uuid(self, uuid: str) -> Dict:
        """Get function item locator by uuid

        parameters:
        -----------
        uuid: `str`
            The uuid of the function

        Returns:
        --------
        func_info: `Dict`
            The properties of the function
        """
        if uuid:
            logging.info("Search function infos in search table based on function ID")
            self._page.locator(self.elements.RefreshButton).click()
            self._page.locator(self.elements.SearchBarInput).fill(uuid)
            self._page.locator(self.elements.RefreshButton).click()
            func_item = self._page.locator(self.elements.TableItemsRow).first
            func_info = self.parse_func_item(func_item)
            if func_info["Function ID"] == uuid:
                return func_info
        return dict()

    def parse_func_item(self, func_item: Locator) -> Dict:
        """Parse each function properties from the table row.

        parameters:
        -----------
        func_item: `Locator`
            The locator of the function item

        Returns:
        --------
        func_info: `Dict`
            include the function properties
        """
        try:
            # Wait for all cells in the row to be visible
            func_properties = func_item.locator("//td")

            # Check if the locator has any cells before proceeding
            count = func_properties.count()
            if count < 6:  # We expect at least 6 columns
                logging.warning(
                    f"Incomplete row found, expected at least 6 columns but found {count}"
                )
                return {}

            func_info = {}

            # Use safe text extraction with fallbacks
            try:
                func_info["Version Name"] = (
                    func_properties.locator("nth=0").text_content().strip()
                )
            except Exception:
                func_info["Version Name"] = ""

            try:
                func_info["Function ID"] = (
                    func_properties.locator("nth=1").text_content().strip()
                )
            except Exception:
                func_info["Function ID"] = ""

            try:
                func_info["Status"] = (
                    func_properties.locator("nth=2").text_content().strip()
                )
            except Exception:
                func_info["Status"] = ""

            try:
                func_info["Type"] = func_properties.locator("nth=3").text_content().strip()
            except Exception:
                func_info["Type"] = ""

            try:
                func_info["Created Date"] = (
                    func_properties.locator("nth=5").text_content().strip()
                )
            except Exception:
                func_info["Created Date"] = ""

            try:
                action_button = func_properties.locator("nth=6").locator("//button")
                if action_button.count() > 0:
                    func_info["Action"] = action_button
                else:
                    func_info["Action"] = None
            except Exception:
                func_info["Action"] = None

            return func_info

        except Exception as e:
            logging.error(f"Error parsing function item: {str(e)}")
            return {}

    def show_first_function_details_panel(self) -> Dict:
        """Show the first function details panel, and return the function info

        Returns:
        --------
        `Dict`
            The first function info included in the function details panel
        """
        if self.is_empty():
            logging.info("No function item found.")
            return dict()
        func_item = self._page.locator(self.elements.TableItems).first
        func_item.click()
        return self.parse_func_item(func_item)

    def parse_func_item_list(self, func_item: Locator) -> list:
        """Parse each function properties from the table row.

        parameters:
        -----------
        func_item: `Locator`
            The locator of the function item

        Returns:
        --------
        func_info: `list`
            include the function properties
        """
        func_properties = func_item.locator("//td")
        list_item = []

        for i in range(func_properties.count()):
            element = func_properties.nth(i)
            text = element.text_content().strip()
            list_item.append(text)
        return list_item

    def show_first_function_details_panel_list(self) -> list:
        """Show the first function details panel, and return the function info

        Returns:
        --------
        `list`
            The first function info included in the function details panel
        """
        if self.is_empty():
            logging.info("No function item found.")
            return list()
        func_item = self._page.locator(self.elements.TableItems).first
        return self.parse_func_item_list(func_item)

    def navigate_to_first_function_detail_page(self):
        """Navigate to the first line of function details page

        Returns:
        --------
        None
        """
        func_item = self._page.locator(self.elements.TableItems).first
        func_item.locator("//td").locator("nth=1").click()
        self.page.wait_for_load_state("load")

    def get_current_sort_function_by_status(self) -> str:
        """Get the current sort method of funciton status.

        Returns:
        --------
        `str`
            The name of data-icon-name, e.g., arrow-up or arrow-down.
        """
        svg_element = self._page.locator(self.elements.CurrentSortStatus)
        icon_name = svg_element.get_attribute("data-icon-name")
        return icon_name

    def set_sort_function_by_status(self):
        """Set the current sort method of funciton status.

        Returns:
        --------
        No
        """

        self._page.locator(self.elements.SortFunctionByStatus).click()
        self.page.wait_for_load_state("load")

    def check_if_function_exists(self, func_name: str) -> bool:
        """Check if the function exists by name or uuid.

        parameters:
        -----------
        func_name: `str`
            The name of the function

        Returns:
        --------
        `bool`
            True if the function exists, otherwise False.
        """
        logging.info(f"Checking if the function {func_name} exists...")
        self.page.wait_for_selector(self.elements.RefreshButton).is_visible()
        while True:
            if (
                self.page.locator(
                    self.elements.FunctionNameByName.format(func_name)
                ).count()
                == 1
            ):
                return True
            if not self.next_page():
                return False

    def check_if_function_exists_by_search(self, func_name: str) -> bool:
        """Check if the function exists by name or uuid by search bar.

        parameters:
        -----------
        func_name: `str`
            The name of the function

        Returns:
        --------
        `bool`
            True if the function exists, otherwise False.
        """
        logging.info(f"Checking if the function {func_name} exists...")
        self.page.locator(self.elements["SearchBox"]).fill(func_name)
        func_name_element = self.elements["SearchedFunctionNamebyName"].format(func_name)
        try:
            self.page.locator(func_name_element).first.wait_for(
                timeout=3000, state="visible"
            )
            return self.page.locator(func_name_element).count() >= 1
        except TimeoutError:
            logging.info(f"Function {func_name} not found after search")
            return False

    def _delete_version(self):
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.ConfirmDeleteBtn).click()
        self.page.locator(self.elements.FunctionDeleteBanner).wait_for(state="visible")
        self.page.locator(self.elements.FunctionDeleteBanner).wait_for(state="hidden")
        self.page.wait_for_load_state("load")

    def delete_function_version(self, func_name: str) -> bool:
        """Delete the function version by name.

        parameters:
        -----------
        func_name: `str`
            The name of the function

        Returns:
        --------
        `bool`
            True if the function version is deleted successfully, otherwise False.
        """

        logging.info(f"Deleting the function version {func_name}...")
        while True:
            if (
                self.page.locator(
                    self.elements.FunctionNameByName.format(func_name)
                ).count()
                == 1
            ):
                self.page.locator(self.elements.ActionBtnByName.format(func_name)).click()
                self.page.locator(self.elements.DelVersBtn).click()
                self._delete_version()
                return True
            if not self.next_page():
                return False

    def deploy_function_version(
        self,
        func_name: str,
        min_instances: int,
        max_instances: int,
        func_id: str = None,
        func_version: str = None,
        backend: str = None,
        gpu: str = None,
        instance_type: str = None,
        max_cocurrency: int = None,
        **kwargs,
    ) -> bool:
        """Deploy the function version.

        parameters:
        -----------
        func_name: `str`
            The name of the function
        min_instances: `int`
            The min instances of the function
        max_instances: `int`
            The max instances of the function
        func_version: `str`
            The version of the function
        backend: `str`
            The backend of the function
        gpu: `str`
            The gpu of the function
        instance_type: `str`
            The instance type of the function
        max_cocurrency: `int`
            The max cocurrency of the function

        Returns:
        --------
        `bool`
            True if the function version is deployed successfully, otherwise False.
        """

        logging.info(f"Search the function {func_name}...")
        while True:
            if (
                self.page.locator(
                    self.elements.FunctionNameByName.format(func_name)
                ).count()
                == 1
            ):
                if (
                    self.page.locator(
                        self.elements.FunctionStatusByName.format(func_name)
                    ).text_content()
                    != "INACTIVE"
                ):
                    logging.error(
                        f"The function {func_name} has wrong status, please check..."
                    )
                    return False
                else:
                    self.page.locator(
                        self.elements.ActionBtnByName.format(func_name)
                    ).click()
                    self.page.locator(self.elements.DeployVersionBtn).click()
                    self.page.wait_for_load_state("load")
                    deploy_form = DeployVersionForm(page=self.page)
                    deploy_form.deploy_function_in_detail(
                        func_name=func_name,
                        min_instances=min_instances,
                        max_instances=max_instances,
                        func_version=func_version,
                        backend=backend,
                        gpu=gpu,
                        instance_type=instance_type,
                        max_concurrency=max_cocurrency,
                    )
                    return True
            if not self.next_page():
                return False

    def deploy_function_version_pre(
        self,
        func_name: str,
        func_id=None,
        func_version=None,
    ):
        logging.info(f"Search the function {func_name}...")
        self.page.locator(self.elements["SearchBox"]).fill(func_name)
        func_name_element = self.elements["SearchedFunctionNamebyName"].format(func_name)
        if self.page.locator(func_name_element).count() == 1:
            if (
                self.page.locator(
                    self.elements.FunctionStatusByName.format(func_name)
                ).text_content()
                != "INACTIVE"
            ):
                logging.error(f"The function {func_name} has wrong status, please check...")
                return False
            else:
                self.page.locator(self.elements.ActionBtnByName.format(func_name)).click()
                logging.info("click the action button")
                timeout = 10000
                while not self.page.locator(self.elements.DeployVersionBtn).is_visible():
                    self.page.locator(
                        self.elements.ActionBtnByName.format(func_name)
                    ).click()
                    timeout -= 1000
                    if timeout > 0:
                        logging.info(
                            f"Waiting for the deploy version button to be visible, time left {timeout}ms..."
                        )
                        continue
                    else:
                        logging.error("Failed to click the deploy version button")
                        return False

                self.page.locator(self.elements.DeployVersionBtn).click()
                self.page.wait_for_timeout(2000)
                logging.info("click the deploy version button")
                return True
        else:
            logging.info(f"Function {func_name} not found after search")
            return False

    def add_secret(
        self,
        func_name: str,
        secret: tuple,
        **kwargs,
    ) -> bool:
        """Add secrets for specified funcition.

        parameters:
        -----------
        func_name: `str`
            The name of the function
        secrets: `dict`
            The secrets to be added for this function
        Returns:
        --------
        `bool`
            True if the new secretes are added successfully, otherwise False.
        """

        logging.info(f"Search the function {func_name}...")
        while True:
            if (
                self.page.locator(
                    self.elements.FunctionNameByName.format(func_name)
                ).count()
                == 1
            ):
                if (
                    self.page.locator(
                        self.elements.FunctionStatusByName.format(func_name)
                    ).text_content()
                    != "INACTIVE"
                ):
                    logging.error(
                        f"The function {func_name} has wrong status, please check..."
                    )
                    return False
                else:
                    self.page.locator(
                        self.elements.ActionBtnByName.format(func_name)
                    ).click()
                    self.page.locator(self.elements.ManageSecBtn).click()
                    self.page.wait_for_load_state("load")
                    self.page.fill('input[placeholder="Enter a key"]', f"{secret[0]}")
                    self.page.fill(
                        'textarea[placeholder="Enter a value as a text string or JSON"]',
                        f"{secret[1]}",
                    )
                    self.page.click('button:has-text("Save Secrets")')
                    return True

            if not self.next_page():
                return False

    def modify_secrets(self, func_name: str, secrets_list: list[dict]) -> bool:
        """Modify the secrets for the task.

        parameters:
        -----------
            secrets_list: `list`
            The list of secrets to manage
        """
        logging.info(f"Search the function {func_name}...")
        while True:
            if (
                self.page.locator(
                    self.elements.FunctionNameByName.format(func_name)
                ).count()
                == 1
            ):
                if (
                    self.page.locator(
                        self.elements.FunctionStatusByName.format(func_name)
                    ).text_content()
                    != "INACTIVE"
                ):
                    logging.error(
                        f"The function {func_name} has wrong status, please check..."
                    )
                    return False
                else:
                    self.page.locator(
                        self.elements.ActionBtnByName.format(func_name)
                    ).click()
                    self.page.locator(self.elements.ManageSecBtn).click()
                    self.page.wait_for_load_state("load")
                    self.page.wait_for_timeout(5000)
                    self.nameinput = (
                        "//div[@data-testid='kui-text-input-root'][.//input[@value='{0}']]"
                    )

                    self.keyinput = "//div[@data-testid='kui-text-input-root'][.//input[@value='{0}']]/following-sibling::div[@data-testid='kui-text-area-root']//textarea[@data-testid='kui-text-area-element']"

                    for secret in secrets_list:
                        for key, value in secret.items():
                            self.nameinputlocate = self.nameinput.format(key)
                            logging.info(f"nameinputlocate: {self.nameinputlocate}")
                            if self.page.locator(self.nameinputlocate).is_visible():
                                logging.info(f"Found Secret Name: {key}")
                                self.keyinputlocate = self.page.locator(
                                    self.keyinput.format(key)
                                )
                                self.keyinputlocate.fill(value)
                                logging.info(f"Filled Secret: {key}")
                            else:
                                logging.info(f"Secret Name: {key} not found")
                                return False
                    self.page.locator(self.SaveSecretBtn).click()
                    logging.info("Clicked Save Secret button")
                    self.page.wait_for_load_state("load")
                    self.page.locator(self.SucessSecretMsg).wait_for(state="visible")
                    if self.page.locator(self.SucessSecretMsg).is_visible():
                        return True
                    else:
                        return False
            if not self.next_page():
                return False

    def manage_secrets_for_deployed_function(self, func_name: str):
        """Manage secrets for deployed function.

        Returns:
        --------
        `bool`
            True if the secrets are managed successfully, otherwise False.
        """
        logging.info(f"Search the function {func_name}...")
        hint_msg = "//p[@data-testid='kui-text' and normalize-space(.)='This selected function has no secrets to manage. To add secrets, first stop this function version’s active deployment.']"
        while True:
            if (
                self.page.locator(
                    self.elements.FunctionNameByName.format(func_name)
                ).count()
                == 1
            ):
                if (
                    self.page.locator(
                        self.elements.FunctionStatusByName.format(func_name)
                    ).text_content()
                    != "ACTIVE"
                ):
                    logging.error(
                        f"The function {func_name} has wrong status, please check..."
                    )
                    return False
                else:
                    self.page.locator(
                        self.elements.ActionBtnByName.format(func_name)
                    ).click()
                    self.page.locator(self.elements.ManageSecBtn).click()
                    self.page.wait_for_load_state("load")
                    self.page.wait_for_timeout(5000)
                    if not self.page.locator(hint_msg).is_visible():
                        logging.error(
                            f"The hint message of function {func_name} is not displayed."
                        )
                        return False
                    else:
                        return True

            if not self.next_page():
                return False

    def check_secret_info_in_manage_secrets_page(
        self,
        func_name: str,
    ) -> bool:
        """Check the secret info in the manage secrets page.

        parameters:
        -----------
        func_name: `str`
            The name of the function

        Returns:
        --------
        `bool`
            True if the secret info is displayed correctly, otherwise False.
        """

        logging.info(f"Search the function {func_name}...")
        while True:
            if (
                self.page.locator(
                    self.elements.FunctionNameByName.format(func_name)
                ).count()
                == 1
            ):
                if (
                    self.page.locator(
                        self.elements.FunctionStatusByName.format(func_name)
                    ).text_content()
                    != "INACTIVE"
                ):
                    logging.error(
                        f"The function {func_name} has wrong status, please check..."
                    )
                    return False
                else:
                    self.page.locator(
                        self.elements.ActionBtnByName.format(func_name)
                    ).click()
                    self.page.locator(self.elements.ManageSecBtn).click()
                    self.page.wait_for_load_state("load")
                    return True
            if not self.next_page():
                return False

    def wait_function_to_expected_status(
        self,
        func_name: str,
        expected_status: str,
        time_out: int,
        **kwargs,
    ) -> bool:
        """Wait the function to expected status in timeout.

        parameters:
        -----------
        func_name: `str`
            The name of the function
        expected_status: `str`
            The expected status of the function
        time_out: `int`
            The timeout in seconds

        Returns:
        --------
        `bool`
            True if the function is in expected status in timeout, otherwise False.
        """

        logging.info(f"Start waiting {func_name} to expected status {expected_status}...")
        while True:
            if (
                self.page.locator(
                    self.elements.FunctionNameByName.format(func_name)
                ).count()
                == 1
            ):
                current_status = self.page.locator(
                    self.elements.FunctionStatusByName.format(func_name)
                ).text_content()
                if current_status == expected_status:
                    logging.info(
                        f"Function {func_name} is in expected status {expected_status} now..."
                    )
                    return True
                else:
                    if expected_status == "ACTIVE" and current_status == "ERROR":
                        logging.error(
                            f"Function {func_name} status changed to error while waiting for active, aborting."
                        )
                        return False
                    logging.info(
                        f"Waiting {func_name} to expected status {expected_status}, current as {current_status}, time left {time_out}s..."
                    )
                    self.page.wait_for_timeout(60000)
                    time_out -= 60
                    if time_out > 0:
                        continue
                    else:
                        return False
            if not self.next_page():
                return False

    def add_function_version(
        self,
        func_id: str,
        secrets: Optional[Dict] = None,
    ) -> str:
        """Add a new version for the given function.

        parameters:
        -----------
        func_id: `str`
            The uuid of the function

        Returns:
        --------
        `str`
            The version id if sucessfully add the new version of given function, otherwise empty string.
        """
        while True:
            func_dict = self.get_function_by_uuid(func_id)
            if func_dict:
                logging.info(f"Function detail dict is {func_dict}...")
                max_retries = 3
                for _ in range(max_retries):
                    func_dict["Action"].click()
                    try:
                        self.page.locator(self.elements.CreateNewVersionBtn).wait_for(
                            state="visible", timeout=2000
                        )
                        self.page.locator(self.elements.CreateNewVersionBtn).click()
                        break
                    except TimeoutError as e:
                        logging.info(f"Error: {e}")
                        if _ == max_retries - 1:
                            raise e
                self.page.wait_for_load_state()
                form = CreateFunctionPageForm(self.page)
                if secrets:
                    return form.add_function_version(secrets)
                else:
                    return form.add_function_version()
            else:
                if not self.next_page():
                    return ""

    def add_function_version_helm_chart(
        self,
        HelmChart: str,
        HelmChartVersion: str,
        HelmChartServiceName: str,
        func_id: str,
    ) -> str:
        """Add a new version for the given helm chart function.

        parameters:
        -----------
        HelmChart : `str`
            The HelmChart in the function.

        HelmChartVersion : `str`
            The tag of Helm Chart in the function.

        HelmChartServiceName : 'str'
            The Helm Chart ServiceName in the function

        func_id: `str`
            The uuid of the function

        Returns:
        --------
        `str`
            The version id if sucessfully add the new version of given function, otherwise empty string.
        """
        while True:
            func_dict = self.get_function_by_uuid(func_id)
            if func_dict:
                logging.info(f"Function detail dict is {func_dict}...")
                func_dict["Action"].click()
                self.page.locator(self.elements.CreateNewVersionBtn).click()
                self.page.wait_for_load_state()
                form = CreateFunctionPageForm(self.page)
                return form.add_function_version_helm_chart(
                    HelmChart,
                    HelmChartVersion,
                    HelmChartServiceName,
                )
            else:
                if not self.next_page():
                    return ""

    def deploy_function_version_helm_chart(
        self,
        func_id: str,
    ) -> str:
        """Add a new version for the given helm chart function.

        parameters:
        -----------
        func_id: `str`
            The uuid of the function

        Returns:
        --------
        `str`
            The function has been disable successfully, otherwise empty string.
        """
        while True:
            func_dict = self.get_function_by_uuid(func_id)
            if func_dict:
                logging.info(f"Function detail dict is {func_dict}...")
                retry_times = 3
                for _ in range(retry_times):
                    try:
                        func_dict["Action"].click()
                        self.page.locator(self.elements.DeployVersionBtn).click()
                        return self.page
                    except Exception as e:
                        logging.error(f"Error: {e}")
                        if _ == retry_times - 1:
                            raise e
            else:
                if not self.next_page():
                    return ""

    def disable_function_version_helm_chart(
        self,
        func_id: str,
    ) -> str:
        """Add a new version for the given helm chart function.

        parameters:
        -----------
        func_id: `str`
            The uuid of the function

        Returns:
        --------
        `str`
            The function has been disable successfully, otherwise empty string.
        """
        while True:
            func_dict = self.get_function_by_uuid(func_id)
            if func_dict:
                logging.info(f"Function detail dict is {func_dict}...")
                func_dict["Action"].click()
                self.page.locator(self.elements.DisableVersionBtn).click()
                self.page.locator(self.elements.DisableVersBtnInDialog).click()
                self.page.wait_for_load_state()
                return "Function has been disabled."
            else:
                if not self.next_page():
                    return ""

    def is_empty(self) -> bool:
        """Check if the function pagination is empty.

        Returns:
        --------
        `bool`
            True if the function pagination is empty, otherwise False.
        """
        return self.get_total_function_count() == 0

    def get_current_page_data(self):
        pass

    def get_total_page_data(self):
        pass

    def get_total_pages(self):
        pass

    def get_total_function_count(self) -> int:
        """Get the total functions count.

        Returns:
        --------
        `int`
            The total function count.
        """
        text = self.page.locator(self.elements.TotalFunctions).inner_text()
        return int(text.split()[0])

    def go_to_page(self, page_number):
        pass

    def next_page(self) -> bool:
        """Go to the next page if available.

        Returns:
        --------
        `bool`
            True if the next page is available, otherwise False.
        """
        if self.page.locator(self.elements.NextPageButton).is_enabled():
            self.page.locator(self.elements.NextPageButton).click()
            self.page.wait_for_load_state()
            return True
        return False

    def previous_page(self) -> bool:
        """Go to the previous page if available.

        Returns:
        --------
        `bool`
            True if the previous page is available, otherwise False.
        """
        if self.page.locator(self.elements.PrevPageButton).is_enabled():
            self.page.locator(self.elements.PrevPageButton).click()
            self.page.wait_for_load_state()
            return True
        return False

    def edit_column_filters(
        self,
        func_name: bool = True,
        func_id: bool = True,
        status: bool = True,
        type: bool = True,
        create_date: bool = True,
    ) -> bool:
        """Perform column filters editing

        parameters:
        -----------
        func_name : `bool`
            if the function name column is displayed
        func_id : `bool`
            if the function id column is displayed
        status : `bool`
            if the status column is displayed
        type : `bool`
            if the type column is displayed
        create_date : `bool`
            if the create date column is displayed

        Returns:
        --------
        status : `bool`
            return True if the editing is successful
        """
        logging.info("Start editing column filters...")
        self.page.locator(self.elements.EditColumnsButton).click()
        for k, v in self.filter_columns.items():
            filter = self.page.locator(f"//span[text()='{v}']/..")
            if filter.is_checked() != locals()[k]:
                filter.click()
        try:
            self.page.locator(self.elements.EditColumnsButton).click(
                force=True, timeout=3000
            )
        except TimeoutError:
            pass
        if (
            self.page.locator(self.elements.EditColumnsButton).get_attribute(
                "aria-expanded"
            )
            == "true"
        ):
            self.page.locator(self.elements.EditColumnsButton).click()
        logging.info("Finish editing column filters")
        return True

    def get_displayed_rows(self):
        """Get the displayed rows based on current page

        Returns:
        --------
        rows : `list`
            The displayed rows
        """
        pass

    def set_displayed_rows(
        self,
        rows: Literal[
            "10",
            "20",
            "50",
            "100",
        ],
    ):
        self.page.locator(self.elements.SwitchDisplayedRows).click()
        self.page.locator(self.elements.SelectPagesNum.format(rows)).click()
        self.page.wait_for_timeout(3000)
        self.page.wait_for_load_state("load")

    def navigate_to_function_details_page(
        self,
        func_id: str = None,
        func_name: str = None,
    ) -> bool:
        """Go to the function details page by function uuid.

        parameters:
        -----------
        func_id: `str`
            The uuid of the function
        func_name: `str`
            The name of the function

        return:
        -----------
        `bool`
            True if the function details page is opened successfully, otherwise False.
        """
        if func_id:
            # self.page.locator(self.search_table_input).fill(func_id)
            self.page.get_by_placeholder("Search name, description, or a string").fill(
                func_id
            )
        elif func_name:
            self.page.locator(self.search_table_input).fill(func_name)
        self.page.locator(self.elements["CancelBtn"]).wait_for(state="visible")
        self.page.wait_for_load_state("load")
        self.navigate_to_first_function_detail_page()
        try:
            self.page.wait_for_selector("text=Function Overview")
            return True
        except Exception as e:
            logging.error(f"Failed to navigate to function details page: {e}")
            return False

    # def navigate_to_function_details_page(
    #     self,
    #     func_id: str,
    #     ver_name: str,
    # ) -> bool:
    #     """Navigate to the function details page by function ID and version name.

    #     Parameters
    #     ----------
    #     func_id : str
    #         The UUID of the function
    #     ver_name : str
    #         The version name of the function

    #     Returns
    #     -------
    #     bool
    #         True if navigation was successful, False otherwise
    #     """
    #     try:
    #         # Step 1: Search for the function by ID
    #         self.search_function_exact_match(func_id)
    #         self.page.wait_for_timeout(2000)  # Wait for search results

    #         # Step 2: Click on the version name
    #         version_link = self.page.get_by_text(ver_name).first
    #         version_link.click()
    #         self.page.wait_for_timeout(2000)  # Wait for page load

    #         # Step 3: Verify navigation was successful
    #         function_overview = self.page.get_by_text("Overview")
    #         return function_overview.is_visible()

    #     except Exception as e:
    #         logging.error(f"Failed to navigate to function details page: {e}")
    #         return False

    def navigate_to_func_details_page_random(
        self, active=True, return_func_id=False
    ) -> Union[bool, Tuple[bool, str]]:
        """Navigate to the function details page randomly.

        Parameters
        ----------
        active : bool, optional
            Filter by function status, by default True
        return_func_id : bool, optional
            If True, returns both navigation success status and function ID, by default False

        Returns
        -------
        Union[bool, Tuple[bool, str]]
            If return_func_id is False:
                bool: True if navigation successful, otherwise False
            If return_func_id is True:
                Tuple[bool, str]: (navigation_success, function_id)
        """
        self.wait_function_list_ready()

        if not active:
            self.page.locator(FunctionListPagePagination.status_button).click()

        all_items = self.page.locator(self.elements["TableItems"]).all()
        if active is None:
            random_item = random.choice(all_items)
        else:
            status = "ACTIVE" if active else "INACTIVE"
            filter_items = [
                item for item in all_items if self.parse_func_item(item)["Status"] == status
            ]
            random_item = random.choice(filter_items)

        func_id = self.parse_func_item(random_item)["Function ID"]
        self.navigate_to_function_details_page(func_id)
        func_overview = self.page.get_by_text("Function Overview")
        success = func_overview is not None

        if return_func_id:
            return success, func_id
        return success

    def navigate_to_vers_detail_page(
        self,
        func_id: str = None,
        func_name: str = None,
    ) -> bool:
        """Go to the vers detail page by function name.

        parameters:
        -----------
        func_id: `str`
            The uuid of the function
        func_name: `str`
            The name of the function

        return:
        -----------
        `bool`
            True if the function version details page is opened successfully, otherwise False.
        """
        while True:
            if func_name:
                func_dict = self.get_function_by_name(func_name)
            elif func_id:
                func_dict = self.get_function_by_uuid(func_id)
            else:
                raise ValueError("Either func name or id must be provided.")
            if func_dict:
                logging.info(f"Function detail dict is {func_dict}...")
                version_xpath = f"//span[text()='{func_dict.get('Version Name')}']"
                self.page.locator(version_xpath).nth(0).click()
                self.page.wait_for_load_state("load")
                return True
            if not self.next_page():
                return False

    def navigate_to_new_version_page(
        self,
        func_id: str,
    ) -> str:
        """Add a new version for the given function.

        parameters:
        -----------
        func_id: `str`
            The uuid of the function

        Returns:
        -----------
        `bool`
            True if the new version page is opened successfully, otherwise False.
        """
        while True:
            func_dict = self.get_function_by_uuid(func_id)
            if func_dict:
                logging.info(f"Function detail dict is {func_dict}...")
                func_dict["Action"].click()
                self.page.locator(self.elements.CreateNewVersionBtn).click()
                self.page.wait_for_load_state("load")
                return True
            if not self.next_page():
                return False

    def action_to(
        self,
        name: str = None,
        uuid: str = None,
        entry: Literal[
            "Deploy Version",
            "Disable Version",
            "Edit Deployment",
            "View Function Details",
            "View Version Details",
            "View Vesion Logs",
            "New Version",
            "Clone Function",
            "Delete Version",
            "Redeploy Version",
        ] = "View Function Details",
    ) -> Page:
        """Perform action to the current function.

        parameters:
        -----------
        name: `str`
            The name of the function
        uuid: `str`
            The uuid of the function
        entry: `Literal["Deploy Version", "View Function Details", "View Version Details", "View Vesion Logs", "New Version", "Clone Function", "Delete Version"]`
            The action to be performed

        returns:
        -----------
        page : `Playwright.sync_api.page.Page`
            The page object
        """
        entry_dict = {
            "Edit Deployment": {
                "btn": self.elements.EditDeployBtn,
            },
            "Disable Version": {"btn": self.elements.DisableVersionBtn},
            "Deploy Version": {
                "btn": self.elements.DeployVersionBtn,
            },
            "View Function Details": {
                "btn": self.elements.ViewFunctionDetailsBtn,
                "sign": FunctionDetailPageNavigationBar(self.page).elements[
                    "CreateNewVersion"
                ],
            },
            "View Version Details": {
                "btn": self.elements.ViewVersionDetailsBtn,
            },
            "View Vesion Logs": {
                "btn": self.elements.ViewVersionLogsBtn,
            },
            "New Version": {
                "btn": self.elements.CreateNewVersionBtn,
            },
            "Clone Function": {
                "btn": self.elements.CloneFuncBtn,
            },
            "Delete Version": {
                "btn": self.elements.DelVersBtn,
            },
            "Manage Secrets": {
                "btn": self.elements.ManageSecBtn,
            },
            "Redeploy Version": {
                "btn": self.elements.RedeployVersBtn,
            },
        }
        if name:
            func_dict = self.get_function_by_name(name)
        elif uuid:
            func_dict = self.get_function_by_uuid(uuid)
            if not func_dict:
                raise ValueError(f"Function not found for {uuid}")
        else:
            raise ValueError("Either name or uuid must be provided.")
        self.page.locator("//button[text()=' Refresh']").click()
        self.page.wait_for_selector("//button[text()=' Refresh']", state="visible")
        max_retries = 3
        for i in range(max_retries):
            func_dict["Action"].click()
            try:
                self.page.locator(entry_dict[entry]["btn"]).wait_for(state="visible")
                self.page.locator(entry_dict[entry]["btn"]).click()
                break
            except Exception as e:
                logging.error(f"Failed to click {entry_dict[entry]['btn']}: {e}")
                if i == max_retries - 1:
                    raise e
        self.page.wait_for_load_state("load")
        if entry == "Disable Version":
            AllowTaskCompleteMsg = self.page.locator(
                self.elements.AllowTaskCompleteMsg
            ).text_content()
            logging.info(f"AllowTaskCompleteMsg is: {AllowTaskCompleteMsg}")
            assert (
                AllowTaskCompleteMsg
                == "Allow current tasks to complete before terminating instances"
            )
            self.page.locator(self.elements.DisableVersBtnInDialog).click()
            self.page.locator(self.elements.SucessDisableMsg).wait_for(state="visible")
        elif entry == "Delete Version":
            self._delete_version()
        elif entry == "Deploy Version":
            self.page.wait_for_url("https://nvcf.*nvidia.com/deployments/create/*")
        elif entry == "View Version Logs":
            raise NotImplementedError("View version logs page is not implemented yet")
        elif entry == "Redeploy Version":
            self.page.locator("//button[text()='Redeploy Version']").wait_for(
                state="visible"
            )
            self.page.locator("//button[text()='Redeploy Version']").click()
        if entry_dict[entry].get("sign"):
            self.page.locator(entry_dict[entry]["sign"]).wait_for(state="visible")
        return self.page

    def wait_function_list_ready(self):
        self.page.locator(FunctionListPagePagination.header_actions_id).wait_for(
            state="visible"
        )

    def wait_for_function_status(
        self,
        function_id: str,
        expected_status: str,
        timeout_seconds: int = 300,
        check_interval: int = 30,
    ) -> str:
        """Wait for a function to reach a specified status or until timeout.

        Parameters:
        -----------
        function_id: `str`
            The UUID of the function to check
        expected_status: `str`
            The expected status to wait for (e.g., 'inactive', 'active', 'deploying')
        timeout_seconds: `int`
            Maximum time to wait in seconds (default: 300 seconds/5 minutes)
        check_interval: `int`
            Time between status checks in seconds (default: 30 seconds)

        Returns:
        --------
        current_status: `str`
            The final status of the function (which should match expected_status if successful)

        Raises:
        -------
        TimeoutError:
            If the function does not reach the expected status within the timeout period
        """
        # Search for the function ID
        self.search_function_exact_match(function_id)

        # Start timer
        start_time = time.time()
        elapsed_time = 0
        current_status = ""

        # Loop until expected status or timeout
        while elapsed_time < timeout_seconds:
            # Get current status
            current_status = self.check_function_status(function_id)
            logging.info(f"Current function status: {current_status}")

            # Check if status is as expected
            if current_status.lower() == expected_status.lower():
                logging.info(f"Function is in {expected_status} state as expected")
                return current_status

            # Not in expected state, wait before checking again
            logging.info(
                f"Function is not in {expected_status} state yet. Waiting {check_interval} seconds..."
            )
            self._page.wait_for_timeout(check_interval * 1000)

            # Refresh the page to get updated status
            self._page.locator(self.elements.RefreshButton).click()

            # Update elapsed time
            elapsed_time = time.time() - start_time

        # Timeout expired without reaching expected status
        raise TimeoutError(
            f"Timeout: Function did not reach {expected_status} state within {timeout_seconds} seconds. "
            f"Last status: {current_status}"
        )

    def search_function_exact_match(self, keyword: str):
        self.page.locator(FunctionListPagePagination.search_table_input).fill(keyword)
        self.page.locator(FunctionListPagePagination.match_one_result_span).wait_for(
            state="visible"
        )
        logging.info(f"Function found: {keyword}.")

    def search_function_multiple_match(self, keyword: str):
        self.page.locator(FunctionListPagePagination.search_table_input).fill(keyword)
        self.page.locator(FunctionListPagePagination.match_multiple_span).wait_for(
            state="visible"
        )
        logging.info(f"Function found: {keyword}.")

    def search_function_exact_match_two_results(self, keyword: str):
        self.page.locator(FunctionListPagePagination.search_table_input).fill(keyword)
        self.page.locator(FunctionListPagePagination.match_two_result_span).wait_for(
            state="visible"
        )
        logging.info(f"Function found: {keyword}.")

    def clone_function(self, func_name: str):
        """Clone the function by name.

        parameters:
        -----------
        func_name: `str`
            The name of the function

        Raises:
        -------
        Exception:
            If the function cannot be found
        """

        logging.info(f"Clone the function from function list page: {func_name}...")
        while True:
            if self.page.locator(f"//span[text()='{func_name}']/parent::a").count() == 1:
                self.page.locator(
                    f"//span[text()='{func_name}']/parent::a/../..//button"
                ).click()
                self.page.locator(FunctionListPagePagination.clone_func_btn).click()
                self.page.wait_for_load_state()
                return
            if not self.next_page():
                raise Exception(f"Unable to find the function to be cloned: {func_name}.")

    def clone_first_function_from_multiple_results(self, func_name: str):
        """Clone the function by name.

        parameters:
        -----------
        func_name: `str`
            The name of the function

        Raises:
        -------
        Exception:
            If the function cannot be found
        """

        logging.info(f"Clone the function from function list page: {func_name}...")
        while True:
            if self.page.locator(f"//span[text()='{func_name}']/parent::a").count() >= 1:
                self.page.locator(
                    f"//span[text()='{func_name}']/parent::a/../..//button"
                ).first.click()
                self.page.locator(FunctionListPagePagination.clone_func_btn).click()
                self.page.wait_for_load_state()
                return
            if not self.next_page():
                raise Exception(f"Unable to find the function to be cloned: {func_name}.")

    def navigate_to_version_logs_page_from_function_list_page(self, func_name: str):
        """navigate to version logs by function name.

        parameters:
        -----------
        func_name: `str`
            The name of the function

        Raises:
        -------
        Exception:
            If the function cannot be found
        """
        logging.info(
            f"navigate to version logs page from function list page: {func_name}..."
        )
        while True:
            if self.page.locator(f"//span[text()='{func_name}']/parent::a").count() == 1:
                self.page.locator(
                    f"//span[text()='{func_name}']/parent::a/../..//button"
                ).click()
                self.page.locator(FunctionListPagePagination.view_version_logs).click()
                return
            if not self.next_page():
                raise Exception(f"Unable to find the function to be cloned: {func_name}.")

    def cancel_deployment(
        self, function_id: str = None, verify_status_change: bool = True
    ) -> bool:
        """Cancel deployment for a function identified by ID or name.

        Parameters:
        -----------
        function_id: `str`
            The UUID of the function (preferred identifier)
        verify_status_change: `bool`
            Whether to verify the status change after cancellation (default: True)

        Returns:
        --------
        `bool`
            True if cancellation was successful, False otherwise

        Raises:
        -------
        ValueError:
            If neither function_id nor function_name is provided
        """
        if function_id:
            self.search_function_exact_match(function_id)
            function_row = self.page.locator(f"tr:has-text('{function_id}')")

        # Capture the initial status for verification
        initial_status_cell = function_row.locator(
            "td >> nth=2"
        )  # Status column is typically the 3rd column
        initial_status = initial_status_cell.inner_text().strip()

        # If status is already INACTIVE, return True directly
        if initial_status.upper() == "INACTIVE":
            logging.info(
                f"Function with ID {function_id} is already in INACTIVE state. No need to cancel deployment."
            )
            return True

        # Click the actions button
        actions_button = function_row.locator("td >> nth=-1 >> button").first
        actions_button.click()

        try:
            # Find and try to click "Cancel Deployment" option
            cancel_option = self.page.locator(
                "div[role='menuitem']:has-text('Cancel Deployment')"
            )
            cancel_option.wait_for(state="visible", timeout=3000)
            cancel_option.click()

            # Confirm the cancellation if a dialog appears
            confirm_button = self.page.locator("button:has-text('Cancel Deployment')").first
            if confirm_button.is_visible():
                confirm_button.click()

            # Wait for the operation to complete
            self.page.wait_for_timeout(5000)  # Wait for UI to update

            # Refresh to see updated status
            self.page.locator(self.elements.RefreshButton).click()
            self.page.wait_for_load_state("networkidle")

            # Verify the status change if requested
            if verify_status_change and initial_status.lower() in ["active", "deploying"]:
                function_row = self.page.locator(f"tr:has-text('{function_id}')")
                updated_status_cell = function_row.locator("td >> nth=2")
                updated_status = updated_status_cell.inner_text().strip()

                if updated_status.lower() == initial_status.lower():
                    logging.warning(
                        f"Status did not change after cancellation. Still: {updated_status}"
                    )
                    return False

                logging.info(
                    f"Function status changed from {initial_status} to {updated_status}"
                )

            return True

        except Exception as e:
            # If the function is not in a state where cancellation is possible
            logging.error(f"Failed to cancel deployment: {str(e)}")
            return False
