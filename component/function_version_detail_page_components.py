import re
from datetime import datetime

from component.components import (
    NavigationBar,
    SwitchBar,
    Collapsed<PERSON>hart,
    Tab,
    CollapsedTable,
    DropDownSelector,
    SearchBar,
    PaginationTable,
    ActionMenu,
)

from element.function_version_detail_page_elements import (
    FunctionVersionDetailPageNavigationBarElements,
    FunctionsVerDetailPageSwitchBarElements,
    FunctionsVerDetailPageInvocActAndQueDpthElements,
    FunctionsVerDetailPageAvgInferTimeElements,
    FunctionsVerDetailPageInstancesElements,
    FunctionsVerDetailPageSuccessRateElements,
    FunctionsVerDetailPageLogsTabElements,
    FunctionsDetailPageDeploymentBasicDetailsElements,
    FunctionsVerDetailPageLogsTimeDropDownSelectorElements,
    FunctionsVerDetailPageLogsSearchBarElements,
    FunctionsVerDetailPageLogsPaginationTableElements,
    FunctionsVerDetailPageLogsCountDropDownSelectorElements,
    FunctionVerDetailPageActionMenuElements,
    FunctionVerDetailOverviewBasicDetailElements,
    FunctionVerDetailPageInstanceTypeElements,
    FunctionVerDetailFunctionDetailsElements,
)

from component.deployments_list_page_components import (
    DeployVersionForm,
)
from playwright.sync_api import Locator, Page
from typing import Literal
import logging
import math


class FunctionVerDetailPageNavigationBar(NavigationBar):
    """Elements and related functionalities for Navigation Bar on Function Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    cloud_functions_link = (
        "//div[@data-testid='kui-breadcrumb-item']/a[text()='Cloud Functions']"
    )
    functions_link = "//div[@data-testid='kui-breadcrumb-item']/a[text()='Functions']"
    function_name_link = "//div[@data-testid='kui-breadcrumb-root']/div[5]"
    overview_model_name_link = (
        "//button[@data-testid='kui-tab-trigger'and text()='Overview']"
    )
    function_detail_model_name_link = (
        "//button[@data-testid='kui-tab-trigger'and text()='Function Details']"
    )
    # instance_type_model_name_link = (
    #     "//button[@data-testid='kui-tab-trigger'and text()='Instance Types']"
    # )
    deployments_details_model_name_link = (
        "//button[@data-testid='kui-tab-trigger'and text()='Deployment Details']"
    )
    metrics_model_name_link = "//button[@data-testid='kui-tab-trigger'and text()='Metrics']"
    logs_model_name_link = "//button[@data-testid='kui-tab-trigger'and text()='Logs']"
    active_instance_in_overview = "//h4[@data-testid='kui-text' and (text()='Active Instances' or text()='Instances')]"
    container_run_command_in_function_detail = (
        "//label[@data-testid='kui-label' and text()='Container Run Command']"
    )
    items_no_exist = "//span[@data-testid='kui-status-message-header']"
    instance_type_exist = "//label[@data-testid='kui-label' and text()='Max Concurrency']"
    metrics_exist = "//span[@data-testid='kui-text' and text()='Total Invocations']"
    clone_func_btn = "//div[@role='menuitem' and text()='Clone Version']"
    action_btn = (
        "//div[@class='ngc-layout-header']//button[@data-testid='kui-menu-trigger']"
    )
    clone_multi_ves_btn = "//button[text()='Continue']"
    clone_multi_ves_btn_ready = "//div[text()='Confirm Version to Clone']"
    function_configuration_title = "//h4[text()='Function Configuration']"
    CancelDeployBtn = "//button[text()='Cancel Deployment']"
    ManageSecretsBtn = "//div[@role='menuitem' and text()='Manage Secrets']"
    SaveSecretBtn = (
        "//button[@data-testid='kui-button' and normalize-space()='Save Secrets']"
    )
    SucessSecretMsg = (
        "//span[@data-testid='kui-text' and contains(text(), 'successfully updated')]"
    )

    def __init__(self, page: Page):
        super().__init__(page, FunctionVersionDetailPageNavigationBarElements())

    def get_function_name(self) -> str:
        self.page.locator(FunctionVerDetailPageNavigationBar.function_name_link).wait_for(
            state="visible"
        )
        return (
            self.page.locator(FunctionVerDetailPageNavigationBar.function_name_link)
            .text_content()
            .split(":")[1]
            .strip()
        )

    def deploy_function_version(
        self,
        func_name: str,
        min_instances: int,
        max_instances: int,
        func_version: str = None,
        backend: str = None,
        gpu: str = None,
        instance_type: str = None,
        max_cocurrency: int = None,
        **kwargs,
    ) -> bool:
        """Deploy the function version.

        parameters:
        -----------
        func_name: `str`
            The name of the function
        min_instances: `int`
            The min instances of the function
        max_instances: `int`
            The max instances of the function
        func_version: `str`
            The version of the function
        backend: `str`
            The backend of the function
        gpu: `str`
            The gpu of the function
        instance_type: `str`
            The instance type of the function
        max_cocurrency: `int`
            The max cocurrency of the function

        Returns:
        --------
        `bool`
            True if the function version is deployed successfully, otherwise False.
        """
        self.page.locator(self.elements.DeployFuncBtn).click()
        self.page.wait_for_load_state("load")
        deploy_form = DeployVersionForm(page=self.page)
        deploy_form.deploy_function_in_detail(
            func_name=func_name,
            min_instances=min_instances,
            max_instances=max_instances,
            func_version=func_version,
            backend=backend,
            gpu=gpu,
            instance_type=instance_type,
            max_concurrency=max_cocurrency,
        )
        return True

    def navigate_to_function_detail_page(self) -> bool:
        """back to the function detail page

        Returns:
        --------
        `bool`
            True if the function detail page is navigated successfully, otherwise False.
        """
        self.page.locator(self.elements["FuncBtninBreadcrumb"]).click()
        self.page.wait_for_load_state("load")
        from pages.Functions.FunctionDetailPage import FunctionDetailPage

        func_dtl_page = FunctionDetailPage(self.page)
        self.page.locator(
            func_dtl_page.NavigationBar.elements["CreateNewVersion"]
        ).wait_for()
        func_overview = func_dtl_page.page.get_by_text("Function Overview")
        return func_overview is not None

    def navigate_to_function_list_page(self):
        """Navigate to the function list page."""
        self.page.locator(FunctionVerDetailPageNavigationBar.functions_link).click()
        self.page.wait_for_load_state("load")
        from component.function_list_page_components import (
            FunctionListPageNavigationBar,
        )

        func_list_page = FunctionListPageNavigationBar(self.page)
        title = self.page.locator(func_list_page.elements.Title).text_content()
        assert title == "Functions", "Functions list page is not displayed!"

    def action_to(
        self,
        entry: Literal[
            "View Invoke Command",
            "New Version",
            "Clone Function",
            "Edit Deployment",
            "Delete Version",
            "Manage Secrets",
        ] = "View Invoke Command",
    ) -> Page:
        """Perform action to the current function.

        parameters:
        -----------
        entry: `Literal["View Invoke Command", "New Version", "Clone Function", "Edit Deployment", "Delete Version"]`
            The action to be performed

        returns:
        -----------
        page : `Playwright.sync_api.page.Page`
            The page object
        """
        entry_dict = {
            "View Invoke Command": {
                "btn": self.elements.ViewInvokCmd,
            },
            "New Version": {
                "btn": self.elements.NewVersionBtn,
            },
            "Clone Function": {
                "btn": self.elements.CloneFuncBtn,
            },
            "Edit Deployment": {
                "btn": self.elements.EditDeployBtn,
            },
            "Delete Version": {
                "btn": self.elements.DelVersBtn,
            },
            "Manage Secrets": {
                "btn": self.ManageSecretsBtn,
            },
        }
        self.page.locator(self.elements.moreOpts).last.click()
        self.page.wait_for_selector(entry_dict[entry]["btn"], state="visible")
        self.page.locator(entry_dict[entry]["btn"]).click()
        self.page.wait_for_load_state("load")
        if entry == "Delete Version":
            self.page.locator(self.elements.DelVersConfirmBtn).click()
            self.page.locator(self.elements.SucessDelMsg).wait_for(state="visible")
        return self.page

    def add_secret(self, secret: tuple):
        """Add a secret to the function.

        parameters:
        -----------
        secret: `tuple`
            The secret to be added

        Returns:
        --------
        `bool`
            True if the secret is added successfully, otherwise False.
        """
        self.page.fill('input[placeholder="Enter a key"]', f"{secret[0]}")
        self.page.fill(
            'textarea[placeholder="Enter a value as a text string or JSON"]',
            f"{secret[1]}",
        )
        self.page.click('button:has-text("Save Secrets")')
        self.page.wait_for_timeout(5000)
        if self.page.locator(
            "//span[@data-testid='kui-text' and normalize-space(text())='Secrets successfully updated.']"
        ).is_visible():
            return True
        else:
            return False

    def modify_secrets(self, secrets_list: list[dict]) -> bool:
        """Modify the secrets for the task.

        parameters:
        -----------
            secrets_list: `list`
            The list of secrets to manage
        """

        self.nameinput = "//div[@data-testid='kui-text-input-root'][.//input[@value='{0}']]"

        self.keyinput = "//div[@data-testid='kui-text-input-root'][.//input[@value='{0}']]/following-sibling::div[@data-testid='kui-text-area-root']//textarea[@data-testid='kui-text-area-element']"

        for secret in secrets_list:
            for key, value in secret.items():
                self.nameinputlocate = self.nameinput.format(key)
                logging.info(f"nameinputlocate: {self.nameinputlocate}")
                if self.page.locator(self.nameinputlocate).is_visible():
                    logging.info(f"Found Secret Name: {key}")
                    self.keyinputlocate = self.page.locator(self.keyinput.format(key))
                    self.keyinputlocate.fill(value)
                    logging.info(f"Filled Secret: {key}")
                else:
                    logging.info(f"Secret Name: {key} not found")
                    return False
        self.page.locator(self.SaveSecretBtn).click()
        logging.info("Clicked Save Secret button")
        self.page.wait_for_load_state("load")
        self.page.locator(self.SucessSecretMsg).wait_for(state="visible")
        if self.page.locator(self.SucessSecretMsg).is_visible():
            return True
        else:
            return False

    def clone_function(self, func_name: str):
        """Clone the function by name.

        parameters:
        -----------
        func_name: `str`
            The name of the function

        Raises:
        -------
        Exception:
            If the function cannot be found
        """

        logging.info(f"Clone the version from function version detail page: {func_name}...")
        self.page.locator(FunctionVerDetailPageNavigationBar.action_btn).click()
        try:
            self.page.locator(FunctionVerDetailPageNavigationBar.clone_func_btn).wait_for()
            self.page.locator(FunctionVerDetailPageNavigationBar.clone_func_btn).click()
        except Exception as e:
            logging.info("Can not find clone version button" + str(e))

    def clone_function_for_multiple_versions(self, func_name: str):
        """Clone the function by name.

        parameters:
        -----------
        func_name: `str`
            The name of the function

        Raises:
        -------
        Exception:
            If the function cannot be found
        """

        logging.info(
            f"Clone the version from function version detail page for multiple versions: {func_name}..."
        )
        self.page.locator(FunctionVerDetailPageNavigationBar.action_btn).click()
        try:
            self.page.locator(FunctionVerDetailPageNavigationBar.clone_func_btn).wait_for()
            self.page.locator(FunctionVerDetailPageNavigationBar.clone_func_btn).click()
            self.page.locator(
                FunctionVerDetailPageNavigationBar.clone_multi_ves_btn_ready
            ).wait_for(state="visible")
            self.page.locator(
                FunctionVerDetailPageNavigationBar.clone_multi_ves_btn
            ).click()
        except Exception as e:
            logging.info("Can not find clone version button" + str(e))

    def cancel_deployment(self):
        """
        Cancel Deployment by clicking the Cancel Deployment button
        Click the Cancel Deployment button in the pop out windows to confirm the canceling of the deployment
        """
        self.page.locator(FunctionVerDetailPageNavigationBar.CancelDeployBtn).click()
        self.page.locator(FunctionVerDetailPageNavigationBar.CancelDeployBtn).nth(1).click()

    def check_function_version_detail_page_title(self, func_id: str, func_name: str):
        """Check the function version detail page title.

        parameters:
        -----------
        func_id: `str`
            The function id
        func_name: `str`
            The function name
        """
        children = self.page.locator('//div[@data-testid="kui-breadcrumb-root"]/*')
        count = children.count()

        result = []
        for i in range(count):
            child = children.nth(i)
            data_testid = child.get_attribute("data-testid")
            if data_testid == "kui-breadcrumb-item":
                if child.locator("a").count() > 0:
                    text = child.locator("a").inner_text().strip()
                elif child.locator("span").count() > 0:
                    text = child.locator("span").inner_text().strip()
                else:
                    text = child.inner_text().strip()
                result.append(text)
            elif data_testid == "kui-breadcrumb-separator":
                svg = child.locator("svg")
                if svg.count() > 0:
                    icon_name = svg.first.get_attribute("data-icon-name")
                    result.append(icon_name)
        logging.info(f"result: {result}")
        check_result_flag = True
        if result[0] != "Functions":
            check_result_flag = False
        elif result[1] != "shapes-chevron-right":
            check_result_flag = False
        elif result[2] != f"Function ID: {func_id}":
            check_result_flag = False
        elif result[3] != "shapes-chevron-right":
            check_result_flag = False
        elif result[4] != f"Version Name: {func_name}":
            check_result_flag = False
        return check_result_flag


class FunctionVerDetailSwitchBar(SwitchBar):
    """Elements and related functionalities for Switch Bar on Function Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    overview_button = "//button[text()='Overview']"

    def __init__(self, page: Page):
        super().__init__(page, FunctionsVerDetailPageSwitchBarElements())


class FunctionVerDetailPageColpsChart(CollapsedChart):
    """Elements and related functionalities for the collapsed chart on Function Version Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    pass


class FunctionVerDetailPageInvocActAndQueDpth(FunctionVerDetailPageColpsChart):
    """Elements and related functionalities for Invocation Activity and Queue Depth on Function Version Detail Page.

    parameters:
    -----------
    Page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionsVerDetailPageInvocActAndQueDpthElements())

    def expand_chart(self):
        self.page.locator(self.elements.ExpandBtn).wait_for(timeout=6000, state="visible")
        if self.page.locator(self.elements.ExpandBtn).is_visible():
            self.page.locator(self.elements.ExpandBtn).click()
            self.page.wait_for_timeout(500)
            return True
        else:
            return False

    def get_first_xaxis_time(self):
        return self.page.locator(self.elements.FirstxAxis).text_content()

    def get_last_xaxis_time(self):
        return self.page.locator(self.elements.LastxAxis).text_content()

    def set_and_verify_time_frame(
        self,
        span: Literal[
            "Past 1 Minute",
            "Past 5 Minutes",
            "Past 15 Minutes",
            "Past 30 Minutes",
            "Past 1 Hour",
            "Past 3 Hours",
            "Past 6 Hours",
            "Past 12 Hours",
            "Past 1 Day",
            "Past 2 Days",
            "Past 7 Days",
        ],
    ):
        """Set the time frame and verify the time frame.

        parameters:
        -----------
        span: `Literal["Past 1 Minute", "Past 5 Minutes", "Past 15 Minutes", "Past 30 Minutes", "Past 1 Hour", "Past 3 Hours", "Past 6 Hours", "Past 12 Hours", "Past 1 Day", "Past 2 Days", "Past 7 Days"]`
            The time frame to be set

        Returns:
        --------
        `bool`
            True if the time frame is set and verified successfully, otherwise False.
        """

        first_time = self.page.locator(self.elements.FirstxAxis).text_content()
        last_time = self.page.locator(self.elements.LastxAxis).text_content()

        current_year = datetime.now().year
        try:
            first_dt = datetime.strptime(f"{current_year} {first_time}", "%Y %m/%d %H:%M")
            last_dt = datetime.strptime(f"{current_year} {last_time}", "%Y %m/%d %H:%M")
        except ValueError:
            return False

        time_diff = last_dt - first_dt
        actual_minutes = time_diff.total_seconds() / 60

        expected_minutes = {
            "Past 1 Minute": 1,
            "Past 5 Minutes": 5,
            "Past 15 Minutes": 15,
            "Past 30 Minutes": 30,
            "Past 1 Hour": 60,
            "Past 3 Hours": 180,
            "Past 6 Hours": 360,
            "Past 12 Hours": 720,
            "Past 1 Day": 1440,
            "Past 2 Days": 2880,
            "Past 7 Days": 10080,
        }

        expected_time = expected_minutes[span]

        tolerance = 10
        min_acceptable = expected_time * (1 - tolerance)
        max_acceptable = expected_time * (1 + tolerance)

        is_valid = min_acceptable <= actual_minutes <= max_acceptable

        return is_valid

    def get_all_invo_data(self, protocol: str = None):
        """Get Invocation Activity and Queue Depth data.

        Returns:
        --------
        data : `dict`
            statistical attrs and related values.
        """
        data = dict()
        self.page.locator(self.elements.invocationsAttr).wait_for(
            timeout=6000, state="visible"
        )
        svg_element = self.page.locator(self.elements.SVGChart)
        svg_element.wait_for(state="visible", timeout=700)
        svg_element.hover(force=True)

        if protocol == "HTTP":
            self.page.wait_for_timeout(500)
            attrs = [
                self.page.locator(self.elements.invocationsAttr),
                self.page.locator(self.elements.QueueDepthAttr),
            ]
            values = [
                self.page.locator(self.elements.invocationsVal),
                self.page.locator(self.elements.QueueDepthVal),
            ]

        elif protocol == "GRPC":
            attrs = [
                self.page.locator(self.elements.invocationsAttr),
                self.page.locator(self.elements.QueueDepthAttr),
            ]
            values = [
                self.page.locator(self.elements.invocationsVal),
                self.page.locator(self.elements.QueueDepthVal),
            ]
        else:
            return dict()
        self.page.locator(self.elements.invocationsVal).wait_for(state="visible")
        self.page.locator(self.elements.QueueDepthVal).wait_for(state="visible")
        for attr, value in zip(attrs, values):
            data[attr.text_content()] = value.text_content()
        data = {key: value.split(": ", 1)[1] for key, value in data.items()}

        return data

    def get_latest_invo_data(self, protocol: str = None, steps: int = 50):
        """Get latest Invocation Activity and Queue Depth data by scanning the SVG chart from left to right.

        Returns:
        --------
        data : `dict`
            'max_queue_depth': highest queue depth value (as float or int)
            'invocations_at_max_queue_depth': corresponding invocations value
        """
        self.page.locator(self.elements.invocationsAttr).wait_for(
            timeout=6000, state="visible"
        )
        svg_element = self.page.locator(self.elements.SVGChart)
        svg_element.wait_for(state="visible", timeout=700)

        content_rect = svg_element.evaluate(
            """(svg) => {
            const rect = svg.querySelector('clipPath rect');
            return {
                x: parseFloat(rect.getAttribute('x')),
                y: parseFloat(rect.getAttribute('y')),
                width: parseFloat(rect.getAttribute('width')),
                height: parseFloat(rect.getAttribute('height')),
            };
        }"""
        )

        # Prepare to scan from left to right
        max_queue_depth = -math.inf
        invocations_at_max = None
        hover_y = content_rect["y"] + content_rect["height"] / 2
        for i in range(steps + 1):
            hover_x = content_rect["x"] + (content_rect["width"] * i / steps)
            svg_element.hover(position={"x": hover_x, "y": hover_y}, force=True)
            self.page.wait_for_timeout(100)  # Allow tooltip to update
            if protocol in ("HTTP", "GRPC"):
                self.page.locator(self.elements.invocationsVal).wait_for(
                    state="visible", timeout=1000
                )
                self.page.locator(self.elements.QueueDepthVal).wait_for(
                    state="visible", timeout=1000
                )
                invocations_val = self.page.locator(
                    self.elements.invocationsVal
                ).text_content()
                queue_depth_val = self.page.locator(
                    self.elements.QueueDepthVal
                ).text_content()
                # Extract the value after ': ' if present
                if ": " in invocations_val:
                    invocations_val = invocations_val.split(": ", 1)[1]
                if ": " in queue_depth_val:
                    queue_depth_val = queue_depth_val.split(": ", 1)[1]
                try:
                    queue_depth_num = float(queue_depth_val.replace(",", ""))
                except Exception:
                    continue
                if queue_depth_num > max_queue_depth:
                    max_queue_depth = queue_depth_num
                    invocations_at_max = invocations_val
        if max_queue_depth == -math.inf:
            return {}
        return {"Queue Depth": int(max_queue_depth), "Invocations": invocations_at_max}


class FunctionVerDetailPageAvgInferTime(FunctionVerDetailPageColpsChart):
    """Elements and related functionalities for Average Inference Time on Function Version Detail Page.

    parameters:
    -----------
    Page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionsVerDetailPageAvgInferTimeElements())

    def get_first_xaxis_time(self):
        return self.page.locator(self.elements.FirstxAxis).text_content()

    def get_last_xaxis_time(self):
        return self.page.locator(self.elements.LastxAxis).text_content()

    def set_and_verify_time_frame(
        self,
        span: Literal[
            "Past 1 Minute",
            "Past 5 Minutes",
            "Past 15 Minutes",
            "Past 30 Minutes",
            "Past 1 Hour",
            "Past 3 Hours",
            "Past 6 Hours",
            "Past 12 Hours",
            "Past 1 Day",
            "Past 2 Days",
            "Past 7 Days",
        ],
    ):
        """Set the time frame and verify the time frame.

        parameters:
        -----------
        span: `Literal["Past 1 Minute", "Past 5 Minutes", "Past 15 Minutes", "Past 30 Minutes", "Past 1 Hour", "Past 3 Hours", "Past 6 Hours", "Past 12 Hours", "Past 1 Day", "Past 2 Days", "Past 7 Days"]`
            The time frame to be set

        Returns:
        --------
        `bool`
            True if the time frame is set and verified successfully, otherwise False.
        """

        first_time = self.page.locator(self.elements.FirstxAxis).text_content()
        last_time = self.page.locator(self.elements.LastxAxis).text_content()

        current_year = datetime.now().year
        try:
            first_dt = datetime.strptime(f"{current_year} {first_time}", "%Y %m/%d %H:%M")
            last_dt = datetime.strptime(f"{current_year} {last_time}", "%Y %m/%d %H:%M")
        except ValueError:
            return False

        time_diff = last_dt - first_dt
        actual_minutes = time_diff.total_seconds() / 60

        expected_minutes = {
            "Past 1 Minute": 1,
            "Past 5 Minutes": 5,
            "Past 15 Minutes": 15,
            "Past 30 Minutes": 30,
            "Past 1 Hour": 60,
            "Past 3 Hours": 180,
            "Past 6 Hours": 360,
            "Past 12 Hours": 720,
            "Past 1 Day": 1440,
            "Past 2 Days": 2880,
            "Past 7 Days": 10080,
        }

        expected_time = expected_minutes[span]

        tolerance = 10
        min_acceptable = expected_time * (1 - tolerance)
        max_acceptable = expected_time * (1 + tolerance)

        is_valid = min_acceptable <= actual_minutes <= max_acceptable

        return is_valid

    def expand_chart(self):
        self.page.locator(self.elements.ExpandBtn).wait_for(timeout=6000, state="visible")
        if self.page.locator(self.elements.ExpandBtn).is_visible():
            self.page.locator(self.elements.ExpandBtn).click()
            self.page.wait_for_timeout(500)
            return True
        else:
            return False

    def get_all_invo_data(self, protocol: str = None):
        """Get Average Inference Time data.

        Returns:
        --------
        data : `dict`
            statistical attrs and related values.
        """
        data = dict()
        self.page.locator(self.elements.ExpandBtn).wait_for(timeout=6000, state="visible")
        self.page.locator(self.elements.ExpandBtn).click()
        svg_element = self.page.locator(self.elements.SVGChart)
        svg_element.wait_for(state="visible", timeout=6000)
        svg_element.hover(force=True)

        if protocol == "HTTP":
            self.page.wait_for_timeout(500)
            attrs = ["Average Inference Time"]
            values = [self.page.locator(self.elements.AvgInferTimeVal)]

        elif protocol == "GRPC":
            attrs = ["Average Inference Time"]
            values = [self.page.locator(self.elements.AvgInferTimeVal)]
        else:
            return dict()
        self.page.locator(self.elements.AvgInferTimeVal).wait_for(state="visible")
        for attr, value in zip(attrs, values):
            data[attr] = value.text_content()
        data = {key: value.split(": ", 1)[1] for key, value in data.items()}

        return data


class FunctionVerDetailPageInstances(FunctionVerDetailPageColpsChart):
    """Elements and related functionalities for Instances on Function Version Detail Page.

    parameters:
    -----------
    Page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionsVerDetailPageInstancesElements())

    def get_first_xaxis_time(self):
        return self.page.locator(self.elements.FirstxAxis).text_content()

    def get_last_xaxis_time(self):
        return self.page.locator(self.elements.LastxAxis).text_content()

    def set_and_verify_time_frame(
        self,
        span: Literal[
            "Past 1 Minute",
            "Past 5 Minutes",
            "Past 15 Minutes",
            "Past 30 Minutes",
            "Past 1 Hour",
            "Past 3 Hours",
            "Past 6 Hours",
            "Past 12 Hours",
            "Past 1 Day",
            "Past 2 Days",
            "Past 7 Days",
        ],
    ):
        """Set the time frame and verify the time frame.

        parameters:
        -----------
        span: `Literal["Past 1 Minute", "Past 5 Minutes", "Past 15 Minutes", "Past 30 Minutes", "Past 1 Hour", "Past 3 Hours", "Past 6 Hours", "Past 12 Hours", "Past 1 Day", "Past 2 Days", "Past 7 Days"]`
            The time frame to be set

        Returns:
        --------
        `bool`
            True if the time frame is set and verified successfully, otherwise False.
        """

        first_time = self.page.locator(self.elements.FirstxAxis).text_content()
        last_time = self.page.locator(self.elements.LastxAxis).text_content()

        current_year = datetime.now().year
        try:
            first_dt = datetime.strptime(f"{current_year} {first_time}", "%Y %m/%d %H:%M")
            last_dt = datetime.strptime(f"{current_year} {last_time}", "%Y %m/%d %H:%M")
        except ValueError:
            return False

        time_diff = last_dt - first_dt
        actual_minutes = time_diff.total_seconds() / 60

        expected_minutes = {
            "Past 1 Minute": 1,
            "Past 5 Minutes": 5,
            "Past 15 Minutes": 15,
            "Past 30 Minutes": 30,
            "Past 1 Hour": 60,
            "Past 3 Hours": 180,
            "Past 6 Hours": 360,
            "Past 12 Hours": 720,
            "Past 1 Day": 1440,
            "Past 2 Days": 2880,
            "Past 7 Days": 10080,
        }

        expected_time = expected_minutes[span]

        tolerance = 10
        min_acceptable = expected_time * (1 - tolerance)
        max_acceptable = expected_time * (1 + tolerance)

        is_valid = min_acceptable <= actual_minutes <= max_acceptable

        return is_valid

    def expand_chart(self):
        self.page.locator(self.elements.ExpandBtn).wait_for(timeout=6000, state="visible")
        if self.page.locator(self.elements.ExpandBtn).is_visible():
            self.page.locator(self.elements.ExpandBtn).click()
            self.page.wait_for_timeout(500)
            return True
        else:
            return False

    def get_all_invo_data(self, protocol: str = None):
        """Get Instances Over Time data.

        Returns:
        --------
        data : `dict`
            statistical attrs and related values.
        """
        data = dict()
        self.page.locator(self.elements.ExpandBtn).wait_for(timeout=6000, state="visible")
        self.page.locator(self.elements.ExpandBtn).click()
        svg_element = self.page.locator(self.elements.SVGChart)
        svg_element.wait_for(state="visible", timeout=6000)
        svg_element.hover(force=True)

        if protocol == "HTTP":
            self.page.wait_for_timeout(500)
            attrs = ["Instances Over Time"]
            values = [self.page.locator(self.elements.InstancesVal)]

        elif protocol == "GRPC":
            attrs = ["Instances Over Time"]
            values = [self.page.locator(self.elements.InstancesVal)]
        else:
            return dict()
        self.page.locator(self.elements.InstancesVal).wait_for(state="visible")
        for attr, value in zip(attrs, values):
            data[attr] = value.text_content()
        data = {key: value.split(": ", 1)[1] for key, value in data.items()}

        return data


class FunctionVerDetailPageSuccessRate(FunctionVerDetailPageColpsChart):
    """Elements and related functionalities for Success Rate on Function Version Detail Page.

    parameters:
    -----------
    Page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionsVerDetailPageSuccessRateElements())

    def get_first_xaxis_time(self):
        return self.page.locator(self.elements.FirstxAxis).text_content()

    def get_last_xaxis_time(self):
        return self.page.locator(self.elements.LastxAxis).text_content()

    def set_and_verify_time_frame(
        self,
        span: Literal[
            "Past 1 Minute",
            "Past 5 Minutes",
            "Past 15 Minutes",
            "Past 30 Minutes",
            "Past 1 Hour",
            "Past 3 Hours",
            "Past 6 Hours",
            "Past 12 Hours",
            "Past 1 Day",
            "Past 2 Days",
            "Past 7 Days",
        ],
    ):
        """Set the time frame and verify the time frame.

        parameters:
        -----------
        span: `Literal["Past 1 Minute", "Past 5 Minutes", "Past 15 Minutes", "Past 30 Minutes", "Past 1 Hour", "Past 3 Hours", "Past 6 Hours", "Past 12 Hours", "Past 1 Day", "Past 2 Days", "Past 7 Days"]`
            The time frame to be set

        Returns:
        --------
        `bool`
            True if the time frame is set and verified successfully, otherwise False.
        """

        first_time = self.page.locator(self.elements.FirstxAxis).text_content()
        last_time = self.page.locator(self.elements.LastxAxis).text_content()

        current_year = datetime.now().year
        try:
            first_dt = datetime.strptime(f"{current_year} {first_time}", "%Y %m/%d %H:%M")
            last_dt = datetime.strptime(f"{current_year} {last_time}", "%Y %m/%d %H:%M")
        except ValueError:
            return False

        time_diff = last_dt - first_dt
        actual_minutes = time_diff.total_seconds() / 60

        expected_minutes = {
            "Past 1 Minute": 1,
            "Past 5 Minutes": 5,
            "Past 15 Minutes": 15,
            "Past 30 Minutes": 30,
            "Past 1 Hour": 60,
            "Past 3 Hours": 180,
            "Past 6 Hours": 360,
            "Past 12 Hours": 720,
            "Past 1 Day": 1440,
            "Past 2 Days": 2880,
            "Past 7 Days": 10080,
        }

        expected_time = expected_minutes[span]

        tolerance = 10
        min_acceptable = expected_time * (1 - tolerance)
        max_acceptable = expected_time * (1 + tolerance)

        is_valid = min_acceptable <= actual_minutes <= max_acceptable

        return is_valid

    def expand_chart(self):
        self.page.locator(self.elements.ExpandBtn).wait_for(timeout=6000, state="visible")
        if self.page.locator(self.elements.ExpandBtn).is_visible():
            self.page.locator(self.elements.ExpandBtn).click()
            self.page.wait_for_timeout(500)
            return True
        else:
            return False

    def get_all_invo_data(self, protocol: str = None):
        """Get Success Rate data.

        Returns:
        --------
        data : `dict`
            statistical attrs and related values.
        """
        data = dict()
        self.page.locator(self.elements.ExpandBtn).wait_for(timeout=6000, state="visible")
        self.page.locator(self.elements.ExpandBtn).click()
        svg_element = self.page.locator(self.elements.SVGChart)
        svg_element.wait_for(state="visible", timeout=6000)
        svg_element.hover(force=True)

        if protocol == "HTTP":
            self.page.wait_for_timeout(500)
            attrs = ["Success Rate"]
            values = [self.page.locator(self.elements.SuccessRateVal)]

        elif protocol == "GRPC":
            attrs = ["Success Rate"]
            values = [self.page.locator(self.elements.SuccessRateVal)]
        else:
            return dict()
        self.page.locator(self.elements.SuccessRateVal).wait_for(state="visible")
        for attr, value in zip(attrs, values):
            data[attr] = value.text_content()
        data = {key: value.split(": ", 1)[1] for key, value in data.items()}

        return data


class FunctionVerDetailPageLogsTab(Tab):
    """Elements and related functionalities in the Logs Tab on Function Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionsVerDetailPageLogsTabElements())

    def get_all_logs_count(self):
        logs_count_text = self.page.locator(
            self.elements["ResultCountText"]
        ).first.text_content()
        logs_counts = re.findall(r"\d+", logs_count_text)
        return int(logs_counts[0])


class FunctionsDetailPageDeploymentBasicDetails(CollapsedTable):
    """All the xpath of basic details elements for Function Deployment Detail Page."""

    def __init__(self, page: Page):
        super().__init__(page, FunctionsDetailPageDeploymentBasicDetailsElements())


class FunctionVerDetailPageLogsTimeDropDownSelector(DropDownSelector):
    """Elements and related functionalities in the Logs Tab on Function Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionsVerDetailPageLogsTimeDropDownSelectorElements())

    def check_expand_icon(self):
        """Check if the expand collapse icon is visible.

        Returns:
        --------
        bool
        """
        self.page.locator(self.elements["RefreshBtn"]).wait_for(state="visible")
        self.page.wait_for_load_state("load")
        expand_icon_visible = self.page.locator(self.elements["ExpandIcon"]).is_visible()
        collapse_icon_visible = self.page.locator(
            self.elements["CollapseIcon"]
        ).is_visible()
        if expand_icon_visible:
            self.page.wait_for_timeout(500)
            self.page.locator(self.elements["ExpandIcon"]).click()
            self.page.wait_for_timeout(500)
            expand_svg_visible = self.page.locator(self.elements["ExpandSVG"]).is_visible()
            return expand_svg_visible
        else:
            if collapse_icon_visible:
                self.page.wait_for_timeout(500)
                self.page.locator(self.elements["CollapseIcon"]).click()
                self.page.wait_for_timeout(500)
                if expand_icon_visible:
                    self.page.locator(self.elements["ExpandIcon"]).click()
                    self.page.wait_for_timeout(500)
                    expand_svg_visible = self.page.locator(
                        self.elements["ExpandSVG"]
                    ).is_visible()
                    return expand_svg_visible
                else:
                    return False
            else:
                return False

    def check_collapse_icon(self):
        """Check if the collapse icon is visible.

        Returns:
        --------
        bool
        """
        self.page.locator(self.elements["RefreshBtn"]).wait_for(state="visible")
        self.page.wait_for_load_state("load")

        collapse_icon_visible = self.page.locator(
            self.elements["CollapseIcon"]
        ).is_visible()
        expand_icon_visible = self.page.locator(self.elements["ExpandIcon"]).is_visible()
        if collapse_icon_visible:
            self.page.locator(self.elements["CollapseIcon"]).click()
            self.page.wait_for_timeout(500)
            collapse_svg_visible = self.page.locator(
                self.elements["CollapseSVG"]
            ).is_visible()
            return collapse_svg_visible
        else:
            if expand_icon_visible:
                self.page.locator(self.elements["ExpandIcon"]).click()
                self.page.wait_for_timeout(500)
                if collapse_icon_visible:
                    self.page.locator(self.elements["CollapseIcon"]).click()
                    self.page.wait_for_timeout(500)
                    collapse_svg_visible = self.page.locator(
                        self.elements["CollapseSVG"]
                    ).is_visible()
                    return collapse_svg_visible
                else:
                    return False
            else:
                return False

    def check_default_as_1_hour(self):
        """Check if default select is 1 hour.

        Returns:
        --------
        bool
        """
        self.page.locator(self.elements["RefreshBtn"]).wait_for(state="visible")
        self.page.wait_for_load_state("load")
        return self.page.locator(self.elements["LastOneHourEntry"]).count() == 1

    def select(self, entry_name):
        """Select an entry.

        Returns:
        --------
        None
        """
        self.page.locator(self.elements["RefreshBtn"]).wait_for(
            state="visible", timeout=1200000
        )
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements["Selector"]).click()
        self.page.locator(self.elements["SelectEntrybyName"].format(entry_name)).click()
        if self.page.locator(self.elements["LoadingBtn"]).is_disabled():
            self.page.locator(self.elements["RefreshBtn"]).wait_for(
                state="visible", timeout=1200000
            )
        self.page.wait_for_load_state("load")
        self.page.wait_for_timeout(3000)


class FunctionVerDetailPageLogsCountDropDownSelector(DropDownSelector):
    """Elements and related functionalities in the Logs Tab on Function Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionsVerDetailPageLogsCountDropDownSelectorElements())

    def select(self, entry_name):
        """Select an entry.

        Returns:
        --------
        None
        """
        self.page.locator(self.elements["RefreshBtn"]).wait_for(state="visible")
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements["Selector"]).click()
        self.page.locator(self.elements["SelectEntrybyName"].format(entry_name)).click()
        self.page.locator(self.elements["SelectedbyName"].format(entry_name)).wait_for()
        self.page.wait_for_load_state("load")
        self.page.wait_for_timeout(2000)


class FunctionVerDetailPageActionMenu(ActionMenu):
    """Elements and related functionalities in the ActionMenu on Function version Detail Page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionVerDetailPageActionMenuElements())

    def choose(self, entry_name):
        action_btn_locator = self.elements["ActionBtn"]
        super().choose(action_btn_locator, entry_name)


class FunctionVerDetailPageLogsSearchBar(SearchBar):
    """Contains all elements and related operations of the Logs Tab search bar on Function Detail Page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionsVerDetailPageLogsSearchBarElements())

    def search(self, input_str):
        """Search an entry.

        params input_str: The str to be searched
        Returns:
        --------
        None
        """
        super().search(self.elements["SearchBarInput"], input_str)

    def check_log_raw_data(self):
        """Check if the log raw data is displayed.

        Returns:
        --------
        bool
        """
        self.page.locator(self.elements["FirstTableRow"]).wait_for(state="visible")
        self.page.locator(self.elements["SwitchBtn"]).wait_for(state="visible")
        self.page.locator(self.elements["SwitchBtn"]).click()
        self.page.wait_for_timeout(1000)
        if self.page.locator(self.elements["FirstRawRow"]).is_visible():
            return True
        else:
            return False

    def wait_for_refresh_btn_visible(self):
        """Wait for the refresh button to be visible.

        Returns:
        --------
        None
        """
        self.page.locator(self.elements["RefreshBtn"]).wait_for(state="visible")

    def wait_for_raw_data_visible(self):
        """Wait for the raw data to be visible.

        Returns:
        --------
        None
        """
        self.page.locator(self.elements["FirstRawRow"]).wait_for(state="visible")


class FunctionVerDetailPageLogsPaginationTable(PaginationTable):
    """Contains all elements and related operations of the Logs Tab pagination on Function Detail Page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionsVerDetailPageLogsPaginationTableElements())

    def parse_log_item(self, log_item: Locator) -> dict:
        """
        Parse each log properties from the table row.
        :param func_item: The table row locator
        """
        log_properties = log_item.locator("//td")
        log_info = {
            "Type": log_properties.locator("nth=0").text_content().strip(),
            "Timestamp": log_properties.locator("nth=1").text_content().strip(),
            "Message": log_properties.locator("nth=2").text_content().strip(),
            "Actions": log_properties.locator("nth=3").text_content().strip(),
        }
        return log_info

    def copy_log_by_row_num(self, row_num):
        """
        Parse each log properties from the table row.
        :param func_item: The table row locator
        """
        self.page.locator("//button[text()=' Refresh']").click()
        self.page.wait_for_selector("//button[text()=' Refresh']", state="visible")
        self.page.locator(self.elements["DisplayedItembyRow"].format(row_num)).locator(
            "nth=3"
        ).click()
        self.page.locator(self.elements["CopyLogBtn"]).click()
        return self.page.evaluate("navigator.clipboard.readText()").strip()

    def edit_column_filters(self):
        pass

    def get_current_page_data(self):
        page_data_list = []
        log_item_list = self.page.locator(self.elements["LogsDisplayedItems"]).all()
        for log_item in log_item_list:
            page_data_list.append(self.parse_log_item(log_item))
        return page_data_list

    def get_displayed_rows(self):
        """
        Get displayed log item item
        """
        return self.page.locator(self.elements["LogsDisplayedItems"]).count()

    def get_total_page_data(self):
        pass

    def get_total_pages(self):
        pass

    def go_to_page(self):
        pass

    def next_page(self):
        pass

    def previous_page(self):
        pass

    def set_displayed_rows(self, displayed_rows):
        self.page.locator("//span[text()='Show']/following-sibling::div").click()
        self.page.locator(f"//div[@role='option']//span[text()='{displayed_rows}']").click()
        self.page.wait_for_load_state("load")

    def check_timestamp_order(self, icon_name):
        """Check if the time is sorted by the given icon name."""
        created_date = []
        for i in range(10):
            element = self.page.locator(
                "//td[@data-testid='kui-table-data-cell' and @headers='data-view-header-timestamp']"
            ).nth(i)
            text = element.text_content()
            created_date.append(text)
            print(f"{i+1}th created date: {text}")
        logging.info(f"created_date: {created_date}")
        if icon_name == "arrow-up":
            return self.check_time_order(created_date) == "ascending"
        elif icon_name == "arrow-down":
            return self.check_time_order(created_date) == "descending"
        else:
            return False

    def check_time_order(self, time_list):
        """Check if the time is sorted by the given icon name."""
        if not time_list or len(time_list) < 2:
            return "equal"

        datetime_list = []
        for time_str in time_list:
            try:
                time_str = time_str.replace(":", ":")
                dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f UTC")
                datetime_list.append(dt)
            except ValueError as e:
                raise ValueError(
                    f"Invalid time format: {time_str}. Expected format: MM/DD/YYYY, HH:MM AM/PM"
                ) from e

        if all(dt == datetime_list[0] for dt in datetime_list):
            return "equal"

        is_ascending = all(
            datetime_list[i] <= datetime_list[i + 1] for i in range(len(datetime_list) - 1)
        )

        is_descending = all(
            datetime_list[i] >= datetime_list[i + 1] for i in range(len(datetime_list) - 1)
        )

        if is_ascending and not is_descending:
            return "ascending"
        elif is_descending and not is_ascending:
            return "descending"
        else:
            return "unsorted"


class FunctionVerDetailPageFunctionDetails(CollapsedTable):
    """Elements and related functionalities for Instances on Function Version Detail Page.

    parameters:
    -----------
    Page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionVerDetailFunctionDetailsElements())

    def get_container(self):
        data = {}
        data[self.page.locator(self.elements.Container).text_content()] = self.page.locator(
            self.elements.ContainerVal
        ).text_content()
        return data

    def get_models(self):
        data = {}
        data["Models"] = self.page.locator(self.elements.ModelsVal).text_content()
        return data

    def get_resources(self):
        data = {}
        data["Resources"] = self.page.locator(self.elements.ResourcesVal).text_content()
        return data

    def get_model_mount_points(self):
        data = {}
        data["Model Mount Points"] = self.page.locator(
            self.elements.ModelMountPointsVal
        ).all_text_contents()
        return data

    def get_resource_mount_points(self):
        data = {}
        data["Resource Mount Points"] = self.page.locator(
            self.elements.ResourceMountPointsVal
        ).all_text_contents()
        return data

    def get_inference_protocol(self):
        data = {}
        data[
            self.page.locator(self.elements.InferenceProtocol).text_content()
        ] = self.page.locator(self.elements.InferenceProtocolVal).text_content()
        return data

    def get_inference_port(self):
        data = {}
        data[
            self.page.locator(self.elements.InferencePort).text_content()
        ] = self.page.locator(self.elements.InferencePortVal).text_content()
        return data

    def get_inference_endpoint(self):
        data = {}
        data[
            self.page.locator(self.elements.InferenceEndpoint).text_content()
        ] = self.page.locator(self.elements.InferenceEndpointVal).text_content()
        return data

    def get_health_port(self):
        data = {}
        data[
            self.page.locator(self.elements.HealthPort).text_content()
        ] = self.page.locator(self.elements.HealthPortVal).text_content()
        return data

    def get_health_endpoint(self):
        data = {}
        data[
            self.page.locator(self.elements.HealthEndpoint).text_content()
        ] = self.page.locator(self.elements.HealthEndpointVal).text_content()
        return data

    def get_run_command_overrides(self):
        data = {}
        data[
            self.page.locator(self.elements.RunCommandOverrides).text_content()
        ] = self.page.locator(self.elements.RunCommandOverridesVal).text_content()
        return data

    def get_environment_variables(self):
        data = {}
        data[self.page.locator(self.elements.EnvVar).text_content()] = self.page.locator(
            self.elements.EnvVarVal
        ).text_content()
        return data

    def get_logs_endpoint(self):
        data = {}
        data[
            self.page.locator(self.elements.LogsEndpoint).text_content()
        ] = self.page.locator(self.elements.LogsEndpointVal).text_content()
        return data

    def get_metrics_endpoint(self):
        data = {}
        data[
            self.page.locator(self.elements.MetricsEndpoint).text_content()
        ] = self.page.locator(self.elements.MetricsEndpointVal).text_content()
        return data

    def get_traces_endpoint(self):
        data = {}
        data[
            self.page.locator(self.elements.TracesEndpoint).text_content()
        ] = self.page.locator(self.elements.TracesEndpointVal).text_content()
        return data

    def get_secrets(self):
        data = {}
        data[
            self.page.locator(self.elements.SecretsEndpoint).text_content()
        ] = self.page.locator(self.elements.SecretsEndpointVal).text_content()
        return data

    def get_low_tatency_streaming(self):
        data = {}
        data[
            self.page.locator(self.elements.LowTatencyStreaming).text_content()
        ] = self.page.locator(self.elements.LowTatencyStreamingVal).text_content()
        return data

    def get_all_data(self):
        data = {}
        for item in [
            self.get_models(),
            self.get_resources(),
            self.get_health_port(),
            self.get_inference_port(),
            self.get_health_endpoint(),
            self.get_inference_endpoint(),
            self.get_inference_protocol(),
            self.get_container(),
            self.get_environment_variables(),
            self.get_run_command_overrides(),
            self.get_logs_endpoint(),
            self.get_metrics_endpoint(),
            self.get_secrets(),
            self.get_model_mount_points(),
            self.get_resource_mount_points(),
            self.get_low_tatency_streaming(),
        ]:
            data.update(item)
        return data


class FunctionVerDetailPageOverviewBasicDetails(CollapsedTable):
    """Elements and related functionalities for Instances on Function Version Detail Page.

    parameters:
    -----------
    Page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionVerDetailOverviewBasicDetailElements())

    def get_function_id_info(self):
        data = {}
        data[
            self.page.locator(self.elements.FunctionID).text_content()
        ] = self.page.locator(self.elements.FunctionIDVal).text_content()
        return data

    def get_version_id_info(self):
        data = {}
        data[self.page.locator(self.elements.VersionID).text_content()] = self.page.locator(
            self.elements.VersionIDVal
        ).text_content()
        return data

    def get_description(self):
        data = {}
        data[
            self.page.locator(self.elements.Descriptions).text_content()
        ] = self.page.locator(self.elements.DescriptionsVal).text_content()
        return data

    def get_tags(self):
        data = {}
        data[self.page.locator(self.elements.Tags).text_content()] = self.page.locator(
            self.elements.TagsVal
        ).text_content()
        return data


class FunctionVerDetailPageInstanceType(CollapsedTable):
    """Elements and related functionalities for Instances on Function Version Detail Page.

    parameters:
    -----------
    Page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, FunctionVerDetailPageInstanceTypeElements())

    def get_instance_type(self):
        data = {}
        data["Instance Type"] = self.page.locator(self.elements.GPU).nth(-1).text_content()
        return data

    def get_GPU(self):
        data = {}
        data[
            self.page.locator(self.elements.InstanceType).text_content()
        ] = self.page.locator(self.elements.InstanceTypeVal).text_content()
        return data

    def get_min_max_instance(self):
        data = {}
        data[
            self.page.locator(self.elements.MinMaxInstance).text_content()
        ] = self.page.locator(self.elements.MinMaxInstanceVal).text_content()
        return data

    def get_max_concurrency(self):
        data = {}
        data[
            self.page.locator(self.elements.MaxConcurrency).text_content()
        ] = self.page.locator(self.elements.MaxConcurrencyVal).text_content()
        return data

    def get_target_regions(self):
        data = {}
        data[
            self.page.locator(self.elements.TargetRegions).text_content()
        ] = self.page.locator(self.elements.TargetRegionsVal).text_content()
        return data
