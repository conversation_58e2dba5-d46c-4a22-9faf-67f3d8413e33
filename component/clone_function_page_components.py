from component.components import NavigationBar
from component.create_function_page_components import CreateFunctionPageForm
from playwright.sync_api import Page
from typing import Optional, Dict
import logging


class CloneFunctionPageNavigationBar(NavigationBar):
    """Elements and related operation for Navigation Bar on Clone Function Page."""

    title = "//h2[starts-with(text(),'Clone Function: ')]"

    def __init__(self, page: Page):
        super().__init__(page)


class CloneFunctionPageForm(CreateFunctionPageForm):
    """Elements and related operation for Form on Clone Function Page."""

    SelectModel = "//button[@aria-label='Select Model(s)']"
    SelectModelOpen = "//button[@aria-label='Select Model(s)' and @data-state='open']"
    SecKeySelect = "//button[@aria-label='Secrets']"
    SecKeySelectOpen = "//button[@aria-label='Secrets' and @data-state='open']"
    SecKeyInputBySeq = "//input[starts-with(@name, 'secrets') and substring(@name, string-length(@name) - string-length('name') + 1) = 'name']"
    SecValueInputBySeq = "//textarea[@data-testid='kui-text-area-element']"
    AddSecButton = "//button[@data-testid='kui-button' and @type='button' and text()=' Add Another Secret']"
    AddAnotherSecBtn = "//button[@data-testid='kui-button' and contains(normalize-space(.), 'Add Another Secret')]"

    def __init__(self, page: Page):
        super().__init__(page)

    def verify_clone_details(self, expected_details):
        """Verify that the cloned function details match the expected values.

        Args:
            expected_details (dict): Expected function details to verify against

        Raises:
            Exception: If an expected key is not found on the clone page
            AssertionError: If any values don't match expectations
        """
        # Get all actual values from the clone page
        actual_details = self._get_actual_details()

        # Convert container_* keys to container before verification
        modified_expected_details = {}
        for key, value in expected_details.items():
            if key.startswith("container_"):
                modified_expected_details["container"] = value
            else:
                modified_expected_details[key] = value
        self._verify_expected_details(modified_expected_details, actual_details)

        # Verify any extra fields are empty
        self._verify_extra_fields_empty(expected_details, actual_details)

        logging.info("Clone details verification completed successfully")

    def verify_clone_details_nim(self, expected_details):
        """Verify that the cloned function details match the expected values.

        Args:
            expected_details (dict): Expected function details to verify against

        Raises:
            Exception: If an expected key is not found on the clone page
            AssertionError: If any values don't match expectations
        """
        # Get all actual values from the clone page
        actual_details = {}
        # self.page.pause()
        # elastic_section = self.page.locator(
        #     'span[data-testid="kui-text"]', has_text="Elastic NIM Details"
        # ).locator('xpath=ancestor::div[@data-testid="kui-grid"][1]')

        # NIM
        # nim_value = (
        #     elastic_section.locator('label:has-text("NIM")')
        #     .locator('xpath=../../..//div[contains(@class, "css-1w36a1o-singleValue")]')
        #     .first.text_content()
        # )
        nim_value = self.page.locator(
            "//label[@data-testid='kui-label' and text()='NIM']/ancestor::div[@data-testid='kui-multiselect']//div[contains(@class, 'singleValue')]"
        ).first.text_content()
        actual_details["NIM"] = nim_value.strip() if nim_value else ""

        # Tag
        # tag_value = (
        #     elastic_section.locator('label:has-text("Tag")')
        #     .locator('xpath=../../..//div[contains(@class, "css-1w36a1o-singleValue")]')
        #     .nth(1)
        #     .text_content()
        # # )
        tag_value = self.page.locator(
            "//label[@data-testid='kui-label' and text()='Tag']/ancestor::div[@data-testid='kui-multiselect']//div[contains(@class, 'singleValue')]"
        ).first.text_content()
        actual_details["Tag"] = tag_value.strip() if tag_value else ""

        # Model Configuration
        model_conf_value = self.page.locator(
            "//label[@data-testid='kui-label' and contains(text(), 'Model Configuration')]/ancestor::div[@data-testid='kui-multiselect']//div[contains(@class, 'singleValue')]"
        ).first.text_content()
        actual_details["Model Configuration"] = (
            model_conf_value.strip() if model_conf_value else ""
        )

        func_section = self.page.locator(
            'span[data-testid="kui-text"]', has_text="Function Details"
        ).locator('xpath=ancestor::div[@data-testid="kui-grid"][1]')

        # Description
        desc = func_section.locator(
            "//label[text()='Description']/../following-sibling::div//textarea"
        ).input_value()
        actual_details["Description"] = desc.strip() if desc else ""

        logging.info(f"Actual details: {actual_details}")

        self._verify_expected_details(expected_details, actual_details)

        logging.info("Clone details verification completed successfully")

    def _get_actual_details(self):
        """Get all field values from the clone page."""
        actual_details = {}
        input_keys = {
            "func_name",
            "func_desc",
            "inference_port",
            "inference_endpoint",
            "health_endpoint",
            "health_port",
        }

        # Wait for page to be ready
        self.page.get_by_placeholder("Enter a name").wait_for(state="visible")

        # Get value for each field
        for key, locator in CreateFunctionPageForm.element_locators.items():
            if key == "func_container":
                self.page.wait_for_timeout(1000)
                actual_details[key] = self.page.locator(locator).is_checked()
            elif key in input_keys:
                actual_details[key] = self.page.locator(locator).input_value()
            elif key == "tags":
                value = ""
                for index in range(self.page.locator(locator).count()):
                    value += self.page.locator(locator).nth(index).text_content()
                actual_details[key] = value
            else:
                actual_details[key] = self.page.locator(locator).text_content()
            logging.info(f"Found {key}: {actual_details[key]}")

        return actual_details

    def _verify_expected_details(self, expected_details, actual_details):
        """Verify expected details match actual details."""
        for key, expected_value in expected_details.items():
            if key not in actual_details:
                raise Exception(f"Expected field {key} not found on clone page")

            actual_value = actual_details[key]

            # Handle special cases
            if key == "func_name":
                expected = f"{expected_value}-clone"
            elif key == "func_desc":
                expected = f"{expected_value} (clone)"
            else:
                expected = expected_value
                if key in ("inference_port", "health_port"):
                    actual_value = int(actual_value)

            assert (
                actual_value == expected
            ), f"Mismatch in {key}: expected '{expected}', got '{actual_value}'"

    def _verify_extra_fields_empty(self, expected_details, actual_details):
        """Verify any extra fields not in expected_details are empty."""
        for key, actual_value in actual_details.items():
            if "container" in key:
                continue
            if key not in expected_details:
                assert (
                    not actual_value
                ), f"Extra field {key} has value '{actual_value}', expected empty"

    def clone_container_function(
        self,
        name: str = None,
        container: str = None,
        tag: str = None,
        inference_protocol: str = None,
        inference_endpoint: str = None,
        health_endpoint: str = None,
        inference_port: Optional[int] = None,
        model: Optional[list] = None,
        env: Optional[Dict] = None,
        command: Optional[str] = None,
        telemetry_logs: Optional[str] = None,
        telemetry_metrics: Optional[str] = None,
        secrets: Optional[list[dict]] = None,
    ):
        """Clone a container function.

        parameters:
        -----------
        name : `str`
            The name of the function.

        container : `str`
            The container in the function.

        tag : `str`
            The tag of container in the function.

        inference_protocol : `str`
            The inference protocol of container in the function.

        inference_endpoint : `str`
            The inference endpoint of container in the function.

        health_endpoint : `str`
            The health endpoint of the container.

        inference_port : `int`, optional
            The inference port of the function, default is None.

        model : `list`, optional
            Model info as a list, each element is a list of model, model_version and model_name, default is None.

        env : `Dict`, optional
            Environments, key value pairs, default is None.

        command : `str`, optional
            The run command of the function, default is None.

        telemetry_logs: `str`, optional
            The logs endpoint of the function, default is None.

        telemetry_metrics: `str`, optional
            The metrics endpoint of the function, default is None.

        Returns:
        --------
        None
        """

        logging.info("Cloning function...")

        # Fill Function Name
        if name is not None:
            self.page.locator(CreateFunctionPageForm.func_name_input).fill(name)

        # Select Container and Tag
        if container is not None:
            self.page.locator(CreateFunctionPageForm.container_input).fill(container)
            self.page.wait_for_load_state("load")
            self.page.locator(self.elements.SelectorChoosebyName.format(container)).click()
            if tag is not None:
                self.page.locator(CreateFunctionPageForm.tag_input).fill(tag)
                self.page.wait_for_load_state("load")
                self.page.locator(self.elements.SelectorChoosebyName.format(tag)).click()
            else:
                raise Exception("Container tag is not specified.")

        # Select Models
        if model is not None:
            model_count = len(model)
            mode_seq = 1
            self.page.locator(self.SelectModel).click()
            try:
                self.page.locator(self.SelectModelOpen).wait_for(state="visible")
            except Exception:
                logging.info("Model has not opened and need to click again to make it open")
                self.page.locator(self.SelectModel).click()
            for model_entry in model:
                self.page.locator(CreateFunctionPageForm.model_input).fill(model_entry[0])
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(model_entry[0])
                ).click()
                self.page.locator(CreateFunctionPageForm.model_ver_input).fill(
                    model_entry[1]
                )
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(model_entry[1])
                ).click()
                self.page.locator(CreateFunctionPageForm.model_name_input).fill(
                    model_entry[2]
                )
                if mode_seq == model_count:
                    break
                else:
                    self.page.locator(CreateFunctionPageForm.add_model_btn).click()
                    mode_seq += 1

        # Inference Path
        if inference_protocol is not None:
            self.page.locator(self.elements.HealthProtInput).fill(inference_protocol)
            self.page.wait_for_load_state("load")
            self.page.locator(
                self.elements.SelectorChoosebyName.format(inference_protocol)
            ).click()
            if inference_port is not None:
                self.page.locator(CreateFunctionPageForm.inference_port_input).fill(
                    inference_port
                )
            if inference_endpoint is not None:
                self.page.locator(CreateFunctionPageForm.inference_url_input).fill(
                    inference_endpoint
                )

        # Health Path
        if health_endpoint is not None:
            self.page.locator(CreateFunctionPageForm.health_uri_input).fill(health_endpoint)

        # Telemetry Logs
        if telemetry_logs:
            self.page.locator(CreateFunctionPageForm.TelemetrySelect).click()
            try:
                self.page.locator(CreateFunctionPageForm.TelemetrySelectOpen).wait_for(
                    state="visible"
                )
            except Exception:
                logging.info("Model has not opened and need to click again to make it open")
                self.page.locator(CreateFunctionPageForm.TelemetrySelect).click()
            self.page.locator(CreateFunctionPageForm.TelemetryLogsInput).fill(
                telemetry_logs
            )
            self.page.wait_for_load_state("load")
            self.page.locator(
                self.elements.SelectorChoosebyName.format(telemetry_logs)
            ).click()
        if telemetry_metrics:
            if not self.page.locator(
                CreateFunctionPageForm.TelemetrySelectOpen
            ).is_visible():
                self.page.locator(CreateFunctionPageForm.TelemetrySelect).click()
            self.page.locator(CreateFunctionPageForm.TelemetryMetricsInput).fill(
                telemetry_metrics
            )
            self.page.wait_for_load_state("load")
            self.page.locator(
                self.elements.SelectorChoosebyName.format(telemetry_metrics)
            ).nth(-1).click()

        if secrets:
            secrets_count = len(secrets)
            secrets_seq = 1
            self.page.locator(self.SecKeySelect).click()
            try:
                self.page.locator(self.SecKeySelectOpen).wait_for(state="visible")
            except Exception:
                logging.info("Model has not opened and need to click again to make it open")
                self.page.locator(self.SecKeySelect).click()
            self.page.locator(self.AddAnotherSecBtn).click()
            for key, value in secrets.items():
                self.page.locator(self.SecKeyInputBySeq).nth(secrets_seq - 1).fill(key)
                self.page.locator(self.SecValueInputBySeq).nth(-1).fill(value)
                if secrets_seq == secrets_count:
                    break
                else:
                    self.page.locator(self.AddSecButton).nth(-1).click()
                    secrets_seq += 1

        # Set Environment
        if env is not None:
            env_count = len(env)
            env_seq = 1
            for key, value in env.items():
                self.page.locator(self.elements.EnvKeyInputBySeq.format(env_seq)).fill(key)
                self.page.locator(self.elements.EnvValueInputBySeq.format(env_seq)).fill(
                    value
                )
                if env_seq == env_count:
                    break
                else:
                    self.page.locator(self.elements.AddEnvButton).click()
                    env_seq += 1

        # Container Run Command
        if command is not None:
            self.page.locator(CreateFunctionPageForm.run_command_text_area).fill(command)

        # Click Create Function Button
        self.page.get_by_text("Create Function Without Deploying").click()
        self.page.wait_for_load_state("load")

        # Wait for navigation to complete and verify we're on the function version detail page
        logging.info(
            "Waiting for navigation to function version detail page to complete..."
        )
        from component.function_version_detail_page_components import (
            FunctionVerDetailSwitchBar,
        )

        # Wait for a specific element that should be present on the function version detail page
        try:
            # Wait for the Overview tab which is present on the function version detail page
            self.page.locator(FunctionVerDetailSwitchBar.overview_button).wait_for(
                timeout=30000
            )
            logging.info("Successfully navigated to function version detail page")
            return True
        except Exception as e:
            logging.error(f"Failed to navigate to function version detail page: {str(e)}")
            return False
