from playwright.sync_api import Page
from component.components import PageMisc
from element.deployments_edit_page_elements import DeploymentsEditPageMiscElements


class DeploymentsEditPageMisc(PageMisc):
    """Contains all the elements and operations of the side bar on the Deployment Edit Page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, DeploymentsEditPageMiscElements())
