from playwright.sync_api import Page
from component.components import PageMisc
from element.tasks_list_page_elements import (
    TasksListPageMiscElements,
)


class TasksListPageMisc(PageMisc):
    """Contains all the elements and operations of the miscs on the Deployments List Page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object.
    """

    def __init__(self, page: Page):
        super().__init__(page, TasksListPageMiscElements())
