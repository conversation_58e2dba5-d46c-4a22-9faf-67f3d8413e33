import logging

from playwright.sync_api import Page
from abc import <PERSON><PERSON><PERSON>, abstractmethod
from element.elements import Elements
from config.consts import CURRENT_ENV


class PageMisc(metaclass=ABCMeta):
    """Base class for MISC elements and functionalities in a page that not proper to be a standalone class.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.

    elements : `Elements`
        The elements of the chart.
    """

    def __init__(self, page: Page, elements: Elements):
        self._page = page
        self.elements = elements

    @property
    def page(self) -> Page:
        return self._page


class SideBar:
    """Base class for Side bar. Contains the elements and functionalities of the side bar on a page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    elements : `Elements`
        The elements of the side bar.
    """

    def __init__(self, page: Page, elements=None):
        self._page = page
        self.elements = elements


class SwitchBar:
    """Base class for switch bar. Contains the elements and functionalities of the switch bar on a page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    elements : `Elements`
        The elements of the switch bar.
    """

    def __init__(self, page: Page, elements: Elements = None):
        self._page = page
        self.elements = elements

    def toggle_tab(self, barName: str, input_bar_ele=None, enable_load=True):
        """Toggle to the tab with the given name.

        parameters:
        -----------
        barName : `str`
            The name of the tab to toggle to.

        Returns:
        --------
        None
        """
        self._page.locator(barName).click()
        if enable_load:
            self._page.wait_for_load_state()

        if input_bar_ele:
            logging.info(f"input_bar_els is {input_bar_ele}")
            self._page.locator(input_bar_ele).wait_for(state="visible")
            self._page.wait_for_load_state("load")


class NavigationBar:
    """Base class for navigation bar. Contains the elements and functionalities of the navigation bar on a page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    elements : `Elements`
        The elements of the navigation bar.
    """

    def __init__(
        self,
        page: Page,
        elements: Elements = None,
    ):
        self._page = page
        self.elements = elements

    @property
    def page(self) -> Page:
        return self._page


class Table(metaclass=ABCMeta):
    """Table base."""

    pass


class Pagination(metaclass=ABCMeta):
    """Base class for pagination. Contains the elements and functionalities of the pagination on a page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.
    elements : `Elements`
        The elements of the pagination.
    """

    def __init__(
        self,
        page: Page,
        elements: Elements = None,
    ):
        self._page = page
        self.elements = elements
        self.current_page = None

    @property
    def page(self) -> Page:
        return self._page

    @abstractmethod
    def get_current_page_data(self):
        """Retrieve and return data for the current page

        Returns:
        --------
        data : `dict`
            The data for the current page
        """
        pass

    @abstractmethod
    def get_total_page_data(self):
        """Retrieve and return data for all pages

        Returns:
        --------
        data : `dict`
            The data of all pages
        """
        pass

    @abstractmethod
    def get_total_pages(self):
        """Get total number of pages

        Returns:
        --------
        count: `int`
            The total number of pages
        """
        pass

    @abstractmethod
    def go_to_page(self, page_number):
        """Navigate to the specified page number

        parameters:
        -----------
        page_number : `int`
            The page number to navigate to

        Returns:
        --------
        status : `bool`
            if the navigation is successful or not
        """
        pass

    @abstractmethod
    def next_page(self):
        """Navigate to the next page

        Returns:
        --------
        status : `bool`
            if the navigation is successful or not
        """
        pass

    @abstractmethod
    def previous_page(self):
        """Navigate to the previous page

        Returns:
        --------
        status : `bool`
            if the navigation is successful or not
        """
        pass

    @abstractmethod
    def edit_column_filters(self):
        """Perform column filters editing

        Returns:
        --------
        status : `bool`
            if the editing is successful or not
        """
        pass

    @abstractmethod
    def get_displayed_rows(self):
        """Get the displayed rows based on current page

        Returns:
        --------
        rows : `list`
            The displayed rows
        """
        pass

    @abstractmethod
    def set_displayed_rows(self, rows):
        """Set the displayed rows based on provided data

        parameters:
        -----------
        rows : `list`
            The rows to be displayed

        Returns:
        --------
        status : `bool`
            if the setting is successful or not
        """
        pass


class PaginationTable(Pagination, Table):
    """Base class for pagination table. Contains the elements and functionalities of the pagination table on a page."""

    def __init__(
        self,
        page: Page,
        elements: Elements = None,
    ):
        super().__init__(page, elements)


class StatisticalTable(Table):
    """Contains the elements and functionalities of the statistical table on a page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.

    elements : `Elements`
        The elements of the statistical table.
    """

    def __init__(
        self,
        page: Page,
        elements: Elements = None,
    ):
        self._page = page
        self.elements = elements

    @property
    def page(self) -> Page:
        return self._page

    @abstractmethod
    def get_all_data(self):
        """Retrieve and return all data from the statistical table.

        Returns:
        --------
        data : `dict`
            The data from the statistical table.
        """
        pass


class Form(metaclass=ABCMeta):
    """Form base.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.

    elements : `Elements`
        The elements of the form.
    """

    def __init__(self, page: Page, elements: Elements = None):
        self._page = page
        self.elements = elements

    @property
    def page(self) -> Page:
        return self._page


class Chart(metaclass=ABCMeta):
    """Chart base.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.

    elements : `Elements`
        The elements of the chart.
    """

    def __init__(self, page: Page, elements: Elements = None):
        self._page = page
        self.elements = elements

    @property
    def page(self) -> Page:
        return self._page


class CollapsedChart(Chart, metaclass=ABCMeta):
    """Contains the elements and functionalities of the collapsed chart on a page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.

    elements : `Elements`
        The elements of the collapsed chart.
    """

    def __init__(self, page: Page, elements: Elements = None):
        super().__init__(page, elements)

    def expand(self):
        """Expand the table.

        Returns:
        --------
        None
        """
        loc = self.page.locator(self.elements["ExpandBtn"])
        if not loc.get_attribute("expanded"):
            loc.click()
        self.page.wait_for_load_state()

    def collapse(self):
        """Collapse the table.

        Returns:
        --------
        None
        """
        loc = self.page.locator(self.elements["ExpandBtn"])
        if loc.get_attribute("expanded") == "true":
            loc.click()
        self.page.wait_for_load_state()


class CollapsedTable(Table, metaclass=ABCMeta):
    """Contains the elements and functionalities of the collapsed table on a page.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.

    elements : `Elements`
        The elements of the collapsed table.
    """

    def __init__(
        self,
        page: Page,
        elements: Elements = None,
        section: str = None,
    ):
        self._page = page
        self.elements = elements
        self.section = section

    @property
    def page(self) -> Page:
        return self._page

    def expand(self):
        """Expand the table.

        Returns:
        --------
        None
        """
        loc = self.page.locator(self.elements["ExpandBtn"])
        if not loc.get_attribute("expanded"):
            loc.click()
        self.page.wait_for_load_state()

    def collapse(self):
        """Collapse the table.

        Returns:
        --------
        None
        """
        loc = self.page.locator(self.elements["ExpandBtn"])
        if loc.get_attribute("expanded") == "true":
            loc.click()
        self.page.wait_for_load_state()

    # def get_all_label_data(self):
    #     import requests
    #     from bs4 import BeautifulSoup

    #     # Get webpage content
    #     url = self.page.url
    #     response = requests.get(url)
    #     html_content = response.text

    #     # Parse HTML
    #     soup = BeautifulSoup(html_content, 'html.parser')

    #     # Extract label: value data
    #     data = {}
    #     for label in soup.find_all('label'):
    #         # Assume value is in the next sibling element of label
    #         value = label.find_next_sibling().get_text(strip=True)
    #         data[label.get_text(strip=True)] = value
    #     return data

    def get_all_data(self):
        """Get all the model details data.

        Returns:
        --------
        data : `dict`
            model details attrs and related values.
        """
        data = dict()

        if self.elements:
            # Use elements file to get attributes and values
            attrs = self._page.locator(self.elements.DetailsAttr).all()
            values = self._page.locator(self.elements.DetailsVal).all()
        else:
            # Get attributes and values without using elements file
            attrs = self._page.locator(
                f"//span[text()='{self.section}']/../../..//div[@data-testid='kui-accordion-content']/div/div/label"
            ).all()
            values = self._page.locator(
                f"//span[text()='{self.section}']/../../..//div[@data-testid='kui-accordion-content']/div/div/span"
            ).all()

        for attr, value in zip(attrs, values):
            data[attr.text_content()] = value.text_content()

        return data


class Tab(metaclass=ABCMeta):
    """Tab Base.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.

    elements : `Elements`
        The elements of the chart.
    """

    def __init__(self, page: Page, elements: Elements):
        self._page = page
        self.elements = elements

    @property
    def page(self) -> Page:
        return self._page


class SearchBar(metaclass=ABCMeta):
    """SearchBar Base.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.

    elements : `Elements`
        The elements of the chart.
    """

    def __init__(self, page: Page, elements: Elements):
        self._page = page
        self.elements = elements

    @property
    def page(self) -> Page:
        return self._page

    def search(self, input_bar_ele, input_str: str, cancel_btn_ele=None):
        """Search an entry.

        params input_bar_ele: The element of input bar
        params input_str: The str to be searched
        cancel_btn_ele: The "x" cancel btn element
        Returns:
        --------
        None
        """
        if cancel_btn_ele is None:
            cancel_btn_ele = (
                "//input/following-sibling::button/*[@data-icon-name='close']/.."
            )
        self.page.locator(input_bar_ele).wait_for(state="visible")
        self.page.wait_for_load_state("load")
        self.page.fill(input_bar_ele, input_str)
        self.page.wait_for_load_state("load")
        self.page.locator(cancel_btn_ele).wait_for(state="visible")
        self.page.wait_for_load_state("load")


class DropDownSelector(metaclass=ABCMeta):
    """Drop Down Selector Base.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.

    elements : `Elements`
        The elements of the chart.
    """

    def __init__(self, page: Page, elements: Elements):
        self._page = page
        self.elements = elements

    @property
    def page(self) -> Page:
        return self._page

    def search_and_select(self, input_element, search_content, time_out=3000):
        """Search in input bar, and then select an entry.

        parameters:
        -----------
        input_element: The locator of search input
        search_content: The content to be searched and chose

        Returns:
        --------
        None
        """
        self.page.locator(input_element).wait_for(state="visible")
        self.page.wait_for_load_state("load")
        self.page.locator(input_element).fill(search_content)
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements["FirstSelectEntry"]).click()
        self.page.wait_for_load_state("load")
        self.page.wait_for_timeout(time_out)

    def multiple_select(self, drop_down_element, select_entries_list, time_out=3000):
        """Multiple choose in select entries.

        parameters:
        -----------
        input_element: The locator of search input
        entries_list: The entries that need to be selected

        Returns:
        --------
        None
        """
        self.page.wait_for_load_state("networkidle")
        self.page.wait_for_load_state("domcontentloaded")
        self.page.locator(drop_down_element).wait_for(state="visible")
        self.page.wait_for_timeout(time_out)
        self.page.locator(drop_down_element).click(force=True)
        self.page.wait_for_timeout(time_out)
        all_entries_list = self.page.locator(
            self.elements["MultiDropDownSelectEntries"]
        ).all_text_contents()
        logging.info(f"All entries in selector are: {all_entries_list}")
        for entry in all_entries_list:
            if CURRENT_ENV == "staging":
                checkbox_locator = f"//span[text()='{entry}']/preceding-sibling::input[@data-testid='kui-checkbox']"
            else:
                checkbox_locator = f"//div[text()='{entry}']/preceding-sibling::input[@data-testid='kui-checkbox']"
            checkbox = self.page.locator(checkbox_locator)
            is_checked = checkbox.is_checked()
            if entry in select_entries_list and not is_checked:
                checkbox.click()
            # elif (
            #     entry_element_locator.get_attribute("aria-disabled") == "false"
            #     and entry_element_locator.get_attribute("aria-selected") == "true"
            #     and entry not in select_entries_list
            # ):
            #     entry_element_locator.click()
        self.page.wait_for_timeout(time_out)
        self.page.wait_for_load_state("load")
        self.page.locator(drop_down_element).click()
        self.page.wait_for_load_state("load")
        self.page.wait_for_timeout(time_out)

    def check_if_select_candidate_exists(
        self, input_element, search_content, time_out=3000
    ):
        """Return if the select candidate exists.

        parameters:
        -----------
        input_element: The locator of search input
        search_content: The content to be searched and chose

        Returns:
        --------
        None
        """
        self.page.locator(input_element).wait_for(state="visible")
        self.page.wait_for_load_state("load")
        self.page.locator(input_element).fill(search_content)
        self.page.wait_for_load_state("load")
        if_found_candidate = (
            self.page.locator(self.elements["FirstSelectEntry"]).count() == 1
        )
        self.page.locator(self.elements["FirstSelectEntry"]).click()
        self.page.wait_for_load_state("load")
        self.page.wait_for_timeout(time_out)
        return if_found_candidate

    def select(self, box_name_element, entry_name, time_out=3000):
        """Select an entry.

        Returns:
        --------
        None
        """
        self.page.locator(box_name_element).wait_for(state="visible")
        self.page.wait_for_load_state("load")
        self.page.locator(box_name_element).click()
        self.page.locator(self.elements["SelectEntrybyName"].format(entry_name)).click()
        # self.page.locator(self.elements["SelectedbyName"].format(entry_name)).wait_for()
        self.page.wait_for_load_state("load")
        self.page.wait_for_timeout(time_out)


class ActionMenu(metaclass=ABCMeta):
    """Action Menu Selector Base.

    parameters:
    -----------
    page : `playwright.sync_api.page.Page`
        The page object.

    elements : `Elements`
        The elements of the chart.
    """

    def __init__(self, page: Page, elements: Elements):
        self._page = page
        self.elements = elements

    @property
    def page(self) -> Page:
        return self._page

    def choose(self, action_btn_ele, entry_name):
        """Choose an entry.

        parameters:
        -----------
        action_btn_ele: The element of action button
        entry_name: The entry to be clicked

        Returns:
        --------
        None
        """
        self._page.locator(action_btn_ele).click()
        self._page.locator(self.elements["ActionEntryByName"].format(entry_name)).click()
        self._page.wait_for_load_state("load")
