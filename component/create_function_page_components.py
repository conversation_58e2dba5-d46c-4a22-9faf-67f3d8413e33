from component.components import NavigationBar, Form
from element.create_function_page_elements import (
    CreateFunctionPageNavigationBarElements,
    CreateFunctionFormElements,
)
from playwright.sync_api import Page
from typing import Optional, Dict
import logging
from config.consts import CURRENT_ENV


class CreateFunctionPageNavigationBar(NavigationBar):
    """Elements and related operation for Navigation Bar on Create Function Page."""

    def __init__(self, page: Page):
        super().__init__(page, CreateFunctionPageNavigationBarElements())


class CreateFunctionPageForm(Form):
    """Elements and related operation for Form on Create Function Page."""

    element_locators = {
        "func_container": "#functionRadio-container-Container",
        "func_name": "//input[@name='name']",
        "func_desc": "//label[text()='Function Description']/../..//textarea",
        "tags": "//button[@data-testid='kui-tag']",
        "container": "//label[text()='Container']/../..//div[contains(@class, 'singleValue')]",
        "tag": "//label[text()='Tag']/../..//div[contains(@class, 'singleValue')]",
        "inference_protocol": "//label[text()='Health Protocol']/../..//div[contains(@class, 'singleValue')]",
        "inference_port": "//input[@placeholder = 'Enter inference port']",
        "health_port": "//input[@placeholder = 'Enter health port']",
        "inference_endpoint": "//label[text()='Inference Endpoint']/../..//input",
        "health_endpoint": "//label[text()='Health Endpoint']/../..//input",
    }

    func_type_container_option = "#functionRadio-container-Container"
    func_type_Helm_Chart_option = "//input[@id='functionRadio-container-Helm Chart']"
    func_type_NIM_option = "//input[@id='functionRadio-container-Elastic NIM']"
    func_name_input = "//input[@name='name']"
    container_input = (
        "//label[text()='Container']/../..//div[text()='Select one']/..//input"
    )
    HelmChart_input = (
        "//label[text()='Helm Chart']/../..//div[text()='Select one']/..//input"
    )
    HelmChartVersion_input = (
        "//label[text()='Helm Chart Version']/../..//div[text()='Select version']/..//input"
    )
    HelmChartServiceName_input = "//input[@name='helmChartServiceName']"

    tag_input = "//label[text()='Tag']/../..//div[text()='Select container tag']/..//input"
    # SelectorChoosebyName = "//div[text()='{0}']"
    model_input = (
        "//label[text()='Model']/../..//div[text()='Select a container first']/..//input"
    )
    model_ver_input = (
        "//label[text()='Model Version']/../..//div[text()='Select version']/..//input"
    )
    model_name_input = "//label[text()='Name']/../..//input[contains(@name, 'models')]"
    add_model_btn = "(//label[text()='Model']/../../../../..)[last()]/..//button"
    inference_protocol_input = "//label[text()='Inference Protocol']/../..//div[text()='Select protocol']/..//input"
    healthpath_protocol_input = (
        "//label[text()='Health Protocol']/../..//div[text()='Select protocol']/..//input"
    )
    inference_port_input = "//input[@name='inferencePort']"
    inference_url_input = "//input[@name='inferenceUrl']"
    health_uri_input = "//input[@name='healthUri']"
    health_port = "//input[@name='healthPort']"
    # EnvKeyInputBySeq = "//input[@name='containerEnvironment[{0}].key']"
    # EnvValueInputBySeq = "//input[@name='containerEnvironment[{0}].value']"
    # AddEnvButton = "(//span[text()='Environment Variables']/..//button)[{0}]"
    AddEnvButton = "//span[text()='Environment Variables']/..//button"
    EnvKeyInputBySeq = "//input[starts-with(@name, 'containerEnvironment') and substring(@name, string-length(@name) - string-length('key') + 1) = 'key']"
    EnvValueInputBySeq = "//input[starts-with(@name, 'containerEnvironment') and substring(@name, string-length(@name) - string-length('value') + 1) = 'value']"
    run_command_text_area = "//textarea[@name='containerArgs']"
    review_function_btn = "//button[text()='Review Function']"
    # create_function_btn = "//button[text()='Create Function']"
    create_function_btn = (
        "//button[text()='Create Function Without Deploying' or text()='Create Function']"
    )
    review_config_title = "//span[text()='Review Configuration']"
    function_create_banner = "//span[contains(text(), 'successfully created')]"
    message_helper = "//input[@name='name']/../following-sibling::span"
    helm_chart_service_name_helper = (
        "//input[@name='helmChartServiceName']/../following-sibling::span"
    )
    TelemetrySelect = "//button[@aria-label='Telemetry Endpoints']"
    TelemetrySelectOpen = (
        "//button[@aria-label='Telemetry Endpoints' and @data-state='open']"
    )
    TelemetryLogsInput = "//div[text()='Select a logs endpoint']/..//input"
    TelemetryMetricsInput = "//label[text()='Metrics']/ancestor::div[contains(@data-testid, 'kui-flex')]/following-sibling::div//div[contains(@class, 'c-cKCGFj') and contains(@class, 'css-19bb58m')]/input[@type='text' and @role='combobox']"
    TelemetryTracesInput = "//label[text()='Traces']/../following-sibling::div//input[@type='text' and @role='combobox']"
    # HealthPathTitle = "//span[text()='Health Path']"
    StreamingModeSwitch = "//div[@data-testid='kui-switch']//button[@role='switch']"

    nim_input = "//label[text()='NIM']/parent::*/following-sibling::*[1]"
    nim_tag_input = "//label[text()='Tag']/parent::*/following-sibling::*[1]"
    if CURRENT_ENV == "production":
        nim_model_configuration_input = (
            "//div[text()='Select a model']/following-sibling::div//input"
        )
    else:
        nim_model_configuration_input = (
            "//div[text()='Select a Model Configuration']/following-sibling::div//input"
        )
    nim_function_name_prefix_input = "//input[@placeholder='Enter a prefix']"
    nim_description_input = "//textarea[@placeholder='Enter a description']"
    clusters_input = "//span[text()='Selected Instance Type Settings']/../following-sibling::div//label[text()='Clusters']/../following-sibling::div//div[contains(@class,'c-WUEIO')]/div"

    def __init__(self, page: Page):
        super().__init__(page, CreateFunctionFormElements())

    def add_function_version(
        self,
        secrets: Optional[Dict] = None,
    ) -> str:
        """Add a new version for the function.

        Returns:
        --------
        uuid : `str`
            The version ID.
        """
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements["FunctionConfigurationTitle"]).wait_for()
        if secrets:
            secrets_count = len(secrets)
            secrets_seq = 1
            self.page.locator(self.elements.SecKeySelect).click()
            try:
                self.page.locator(self.elements.SecKeySelectOpen).wait_for(state="visible")
            except Exception:
                logging.info("Model has not opened and need to click again to make it open")
                self.page.locator(self.elements.SecKeySelect).click()
            self.page.locator(self.elements.AddSecButton).click()
            for key, value in secrets.items():
                self.page.locator(self.elements.SecKeyInputBySeq).nth(secrets_seq - 1).fill(
                    key
                )
                self.page.locator(self.elements.SecValueInputBySeq).nth(-1).fill(value)
                if secrets_seq == secrets_count:
                    break
                else:
                    self.page.locator(self.elements.AddSecButton).nth(-1).click()
                    secrets_seq += 1

        with self.page.expect_navigation():
            self.page.locator(
                "//button[text()='Create Function Without Deploying']"
            ).click()
        self.page.wait_for_load_state("load")
        # self.page.locator(self.elements["ReviewConfigTitle"]).wait_for()
        # self.page.locator(self.elements["CreateFunctionBtn"]).click()
        # self.page.locator(self.elements["DeployVersionBtn"]).wait_for(state="visible")
        # self.page.locator(self.elements["FunctionCreateBanner"]).wait_for(state="hidden")
        # self.page.wait_for_load_state("load")

        from component.function_version_detail_page_components import (
            FunctionVerDetailPageOverviewBasicDetails,
        )

        data = FunctionVerDetailPageOverviewBasicDetails(self.page).get_version_id_info()
        return data["Version ID"]

    def add_function_version_helm_chart(
        self,
        HelmChart: str,
        HelmChartVersion: str,
        HelmChartServiceName: str,
    ) -> str:
        """Add a new version for the helm chart function.

        Returns:
        --------
        uuid : `str`
            The version ID.
        """
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements["FunConfigurationTitle"]).wait_for()
        # Select Helm Chart and  Helm Chart Version
        self.page.locator(self.HelmChart_input).fill(HelmChart)
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(HelmChart)).click()
        self.page.locator(self.HelmChartVersion_input).fill(HelmChartVersion)
        self.page.wait_for_load_state("load")
        self.page.locator(
            self.elements.SelectorChoosebyName.format(HelmChartVersion)
        ).click()
        # Fill Helm Chart Service Name
        self.page.locator(self.HelmChartServiceName_input).fill(HelmChartServiceName)
        self.page.locator(self.elements["CreateFunctionBtn"]).click()
        self.page.locator(self.elements["FunctionCreateBanner"]).wait_for(state="visible")
        self.page.locator(self.elements["FunctionCreateBanner"]).wait_for(state="hidden")
        self.page.wait_for_load_state("load")
        from component.function_detail_page_components import (
            FunctionDetailPageBasicDetails,
        )

        data = FunctionDetailPageBasicDetails(self.page).get_all_data()
        return data["Version ID"]

    def create_container_function(
        self,
        name: str,
        container: str,
        tag: str,
        inference_protocol: str,
        inference_endpoint: str,
        health_endpoint: str,
        inference_port: Optional[int] = None,
        model: Optional[list] = None,
        resource: Optional[list] = None,
        secrets: Optional[Dict] = None,
        env: Optional[Dict] = None,
        command: Optional[str] = None,
        tags: Optional[str] = None,
        health_port: Optional[int] = None,
        telemetry_logs: Optional[str] = None,
        telemetry_metrics: Optional[str] = None,
        telemetry_traces: Optional[str] = None,
        is_deploy: Optional[bool] = False,
        streaming_mode: Optional[bool] = False,
    ):
        """Create a new container function.

        parameters:
        -----------
        name : `str`
            The name of the function.

        container : `str`
            The container in the function.

        tag : `str`
            The tag of container in the function.

        tags: `int`, optional
            The Tags for function version.
        inference_protocol : `str`
            The inference protocol of container in the function.

        inference_endpoint : `str`
            The inference endpoint of container in the function.

        health_endpoint : `str`
            The health endpoint of the container.

        inference_port : `int`, optional
            The inference port of the function, default is None.

        model : `list`, optional
            Model info as a list, each element is a list of model, model_version and model_name, default is None.

        env : `Dict`, optional
            Environments, key value pairs, default is None.

        command : `str`, optional
            The run command of the function, default is None.

        Returns:
        --------
        None
        """
        logging.info(f"Creating function {name}...")

        # Choose container type
        self.page.locator(self.elements.ContainerTypeChoose).click()

        # Fill Function Name
        self.page.locator(self.elements.NameInput).fill(name)

        # Select Container and Tag
        self.page.locator(self.elements.ContainerInput).fill(container)
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(container)).click()
        self.page.locator(self.elements.TagInput).fill(tag)
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(tag)).click()

        # The tags need to be a string and split with ","
        if tags:
            for index, func_tag in enumerate([item.strip() for item in tags.split(",")]):
                self.page.locator(self.elements.TagsInput).first.fill(func_tag)
                self.page.keyboard.press("Enter")
                self.page.locator(self.elements.TagsInfo.format(func_tag)).wait_for(
                    state="visible"
                )
                if (
                    self.page.locator(
                        self.elements.TagsInfo.format(func_tag)
                    ).text_content()
                    != func_tag
                ):
                    logging.info("Output tag is not same with Input one!")

        # Select Models
        if model:
            model_count = len(model)
            mode_seq = 1
            self.page.locator(self.elements.SelectModel).click()
            try:
                self.page.locator(self.elements.SelectModelOpen).wait_for(state="visible")
            except Exception:
                logging.info("Model has not opened and need to click again to make it open")
                self.page.locator(self.elements.SelectModel).click()
            for model_entry in model:
                self.page.locator(self.elements.ModelInput).fill(model_entry[0])
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(model_entry[0])
                ).nth(-1).click()
                self.page.locator(self.elements.ModelVersionInput).fill(model_entry[1])
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(model_entry[1])
                ).nth(-1).click()
                self.page.locator(self.elements.ModelNameInput).nth(mode_seq - 1).fill(
                    model_entry[2]
                )
                if mode_seq == model_count:
                    break
                else:
                    self.page.get_by_role("button", name="Add Another Model").click()
                    mode_seq += 1

        if streaming_mode:
            self.page.locator(self.StreamingModeSwitch).click()

        # Select resource
        if resource:
            resource_count = len(resource)
            resource_seq = 1
            self.page.locator(self.elements.ResourceSelect).click()
            try:
                self.page.locator(self.elements.ResourceSelectOpen).wait_for(
                    state="visible"
                )
            except Exception:
                logging.info("Model has not opened and need to click again to make it open")
                self.page.locator(self.elements.ResourceSelect).click()
            for resource_entry in resource:
                self.page.locator(self.elements.ResourceInput).fill(resource_entry[0])
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(resource_entry[0])
                ).nth(-1).click()
                self.page.locator(self.elements.ResourceVersionInput).fill(
                    resource_entry[1]
                )
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(resource_entry[1])
                ).nth(-1).click()
                self.page.locator(self.elements.ResourceNameInput).nth(
                    resource_seq - 1
                ).fill(resource_entry[2])
                if resource_seq == resource_count:
                    break
                else:
                    self.page.get_by_role("button", name="Add Another Resource").click()
                    resource_seq += 1

        # Inference Path
        self.page.locator(self.elements.HealthProtInput).fill(inference_protocol)
        self.page.wait_for_load_state("load")
        self.page.locator(
            self.elements.SelectorChoosebyName.format(inference_protocol)
        ).click()
        if inference_port:
            self.page.locator(self.elements.PortInput).fill(str(inference_port))
        self.page.locator(self.elements.InferenceEndpointInput).fill(inference_endpoint)

        # Health Path
        self.page.locator(self.elements.HealthEndpointInput).fill(health_endpoint)

        if health_port:
            self.page.locator(self.health_port).fill(str(health_port))

        # Telemetry Logs
        if telemetry_logs:
            self.page.locator(self.elements.TelemetrySelect).click()
            try:
                self.page.locator(self.elements.TelemetrySelectOpen).wait_for(
                    state="visible"
                )
            except Exception:
                logging.info("Model has not opened and need to click again to make it open")
                self.page.locator(self.elements.TelemetrySelect).click()
            self.page.locator(self.elements.TelemetryLogsInput).fill(telemetry_logs)
            self.page.wait_for_load_state("load")
            self.page.locator(
                self.elements.SelectorChoosebyName.format(telemetry_logs)
            ).click()
        if telemetry_metrics:
            if not self.page.locator(self.elements.TelemetrySelectOpen).is_visible():
                self.page.locator(self.elements.TelemetrySelect).click()
            self.page.locator(self.elements.TelemetryMetricsInput).fill(telemetry_metrics)
            self.page.wait_for_load_state("load")
            self.page.locator(
                self.elements.SelectorChoosebyName.format(telemetry_metrics)
            ).nth(-1).click()

        if telemetry_traces:
            if not self.page.locator(self.elements.TelemetrySelectOpen).is_visible():
                self.page.locator(self.elements.TelemetrySelect).click()
            self.page.locator(self.elements.TelemetryTracesInput).fill(telemetry_traces)
            self.page.wait_for_load_state("load")
            self.page.locator(
                self.elements.SelectorChoosebyName.format(telemetry_traces)
            ).nth(-1).click()

        # Set Secrets
        if secrets:
            secrets_count = len(secrets)
            secrets_seq = 1
            self.page.locator(self.elements.SecKeySelect).click()
            try:
                self.page.locator(self.elements.SecKeySelectOpen).wait_for(state="visible")
            except Exception:
                logging.info("Model has not opened and need to click again to make it open")
                self.page.locator(self.elements.SecKeySelect).click()
            for key, value in secrets.items():
                self.page.locator(self.elements.SecKeyInputBySeq).nth(secrets_seq - 1).fill(
                    key
                )
                self.page.locator(self.elements.SecValueInputBySeq).nth(-1).fill(value)
                if secrets_seq == secrets_count:
                    break
                else:
                    self.page.locator(self.elements.AddSecButton).nth(-1).click()
                    secrets_seq += 1
        # Set Environment
        if env:
            env_count = len(env)
            env_seq = 1
            self.page.locator(self.elements.EnvKeySelect).click()
            try:
                self.page.locator(self.elements.EnvKeySelectOpen).wait_for(state="visible")
            except Exception:
                logging.info("Model has not opened and need to click again to make it open")
                self.page.locator(self.elements.EnvKeySelect).click()
            for key, value in env.items():
                self.page.locator(self.elements.EnvKeyInputBySeq).nth(env_seq - 1).fill(key)
                self.page.locator(self.elements.EnvValueInputBySeq).nth(env_seq - 1).fill(
                    value
                )
                if env_seq == env_count:
                    break
                else:
                    self.page.locator(self.elements.AddEnvButton).nth(-1).click()
                    env_seq += 1

        # Container Run Command
        if command:
            self.page.locator(self.elements.RunComandSelect).click()
            try:
                self.page.locator(self.elements.RunComandSelectOpen).wait_for(
                    state="visible"
                )
            except Exception:
                logging.info("Model has not opened and need to click again to make it open")
                self.page.locator(self.elements.RunComandSelect).click()
            self.page.locator(self.elements.RunCommandTextArea).fill(command)

        # Click Create Function Button
        if not is_deploy:
            self.page.locator(self.elements.CreateFunctionBtn).click()
            self.page.wait_for_selector(self.elements.FunctionCreateBanner, state="visible")
            self.page.wait_for_selector(self.elements.FunctionCreateBanner, state="hidden")
            self.page.wait_for_load_state("load")
        else:
            logging.info("Deploying function...")
            self.page.locator(self.elements.CreateDeployFunctionBtn).click()
            self.page.wait_for_load_state("load")
            self.page.wait_for_timeout(5000)

    def check_secret_name(self):
        # Choose container type
        self.page.locator(self.elements.ContainerTypeChoose).click()

        check_secret_name = [
            "awesome-name",
            "_start_with_underscore_hyphen",
            "name_with_unknown_character++",
            "_underscore_and_unknown_character++",
            "underscore_and_unknown_character",
        ]
        error_type1 = "//span[normalize-space(text())='Key text cannot contain hyphens.']"
        error_type2 = "//span[normalize-space(text())='The name may only include uppercase letters (A-Z), lowercase letters (a-z), digits (0-9), and underscore(_).']"
        error_msg = {
            "awesome-name": error_type1,
            "_start_with_underscore_hyphen": error_type2,
            "name_with_unknown_character++": error_type2,
            "_underscore_and_unknown_character++": error_type2,
            "underscore_and_unknown_character": None,
        }
        # Set Secrets
        secrets_seq = 1
        self.page.locator(self.elements.SecKeySelect).click()
        try:
            self.page.locator(self.elements.SecKeySelectOpen).wait_for(state="visible")
        except Exception:
            logging.info("Model has not opened and need to click again to make it open")
            self.page.locator(self.elements.SecKeySelect).click()
        for key in check_secret_name:
            self.page.locator(self.elements.SecKeyInputBySeq).nth(secrets_seq - 1).fill(key)
            self.page.locator(self.elements.SecValueInputBySeq).nth(-1).fill(" ")
            self.page.wait_for_timeout(2000)
            if error_msg[key]:
                if not self.page.locator(error_msg[key]).is_visible():
                    logging.info(f"Secret name {key} is valid")
                    return False
            else:
                if (
                    self.page.locator(error_type1).is_visible()
                    or self.page.locator(error_type2).is_visible()
                ):
                    logging.info(f"Secret name {key} is invalid")
                    return False
            self.page.locator(self.elements.SecKeyInputBySeq).nth(secrets_seq - 1).fill("")
        return True

    def create_gRPC_container_function(
        self,
        name: str,
        container: str,
        tag: str,
        inference_protocol: str,
        inference_port: Optional[int] = None,
        model: Optional[list] = None,
        env: Optional[Dict] = None,
        command: Optional[str] = None,
        telemetry_logs: Optional[str] = None,
        telemetry_metrics: Optional[str] = None,
        telemetry_traces: Optional[str] = None,
        streaming_mode: Optional[bool] = False,
    ):
        """Create a new container function.

        parameters:
        -----------
        name : `str`
            The name of the function.

        container : `str`
            The container in the function.

        tag : `str`
            The tag of container in the function.

        inference_protocol : `str`
            The inference protocol of container in the function.

        inference_port : `int`, optional
            The inference port of the function, default is None.

        model : `list`, optional
            Model info as a list, each element is a list of model, model_version and model_name, default is None.

        env : `Dict`, optional
            Environments, key value pairs, default is None.

        command : `str`, optional
            The run command of the function, default is None.

        Returns:
        --------
        None
        """
        logging.info(f"Creating gRPC function {name}...")

        # Choose container type
        self.page.locator(self.elements.ContainerTypeChoose).click()

        # Fill Function Name
        self.page.locator(self.elements.NameInput).fill(name)

        # Select Container and Tag
        self.page.locator(self.elements.ContainerInput).fill(container)
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(container)).click()
        self.page.locator(self.elements.TagInput).fill(tag)
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(tag)).click()

        # Select Models
        if model:
            model_count = len(model)
            mode_seq = 1
            for model_entry in model:
                self.page.locator(self.elements.ModelInput).fill(model_entry[0])
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(model_entry[0])
                ).click()
                self.page.locator(self.elements.ModelVersionInput).fill(model_entry[1])
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(model_entry[1])
                ).click()
                self.page.locator(self.elements.ModelNameInput).fill(model_entry[2])
                if mode_seq == model_count:
                    break
                else:
                    self.page.locator(self.elements.AddModelButton).click()
                    mode_seq += 1

        if streaming_mode:
            self.page.locator(self.StreamingModeSwitch).click()
        # Inference Path
        self.page.locator(self.elements.HealthProtInput).fill(inference_protocol)
        self.page.wait_for_load_state("load")
        self.page.locator(
            self.elements.SelectorChoosebyName.format(inference_protocol)
        ).click()
        if inference_port:
            self.page.locator(self.elements.PortInput).fill(inference_port)
        # Set Environment
        if env:
            env_count = len(env)
            env_seq = 1
            for key, value in env.items():
                self.page.locator(self.elements.EnvKeyInputBySeq.format(env_seq)).fill(key)
                self.page.locator(self.elements.EnvValueInputBySeq.format(env_seq)).fill(
                    value
                )
                if env_seq == env_count:
                    break
                else:
                    self.page.locator(self.elements.AddEnvButton).click()
                    env_seq += 1

        # Telemetry Logs
        if telemetry_logs:
            self.page.locator(self.elements.TelemetrySelect).click()
            try:
                self.page.locator(self.elements.TelemetrySelectOpen).wait_for(
                    state="visible"
                )
            except Exception:
                logging.info("Model has not opened and need to click again to make it open")
                self.page.locator(self.elements.TelemetrySelect).click()
            self.page.locator(self.elements.TelemetryLogsInput).fill(telemetry_logs)
            self.page.wait_for_load_state("load")
            self.page.locator(
                self.elements.SelectorChoosebyName.format(telemetry_logs)
            ).click()
        if telemetry_metrics:
            if not self.page.locator(self.elements.TelemetrySelectOpen).is_visible():
                self.page.locator(self.elements.TelemetrySelect).click()
            self.page.locator(self.elements.TelemetryMetricsInput).fill(telemetry_metrics)
            self.page.wait_for_load_state("load")
            self.page.locator(
                self.elements.SelectorChoosebyName.format(telemetry_metrics)
            ).nth(-1).click()
        if telemetry_traces:
            if not self.page.locator(self.elements.TelemetrySelectOpen).is_visible():
                self.page.locator(self.elements.TelemetrySelect).click()
            self.page.locator(self.elements.TelemetryTracesInput).fill(telemetry_traces)
            self.page.wait_for_load_state("load")
            self.page.locator(
                self.elements.SelectorChoosebyName.format(telemetry_traces)
            ).nth(-1).click()

        # Container Run Command
        if command:
            self.page.locator(self.elements.RunCommandTextArea).fill(command)

        # Click Create Function Button
        # self.page.locator(self.elements.ReviewFunctionBtn).click()
        # self.page.wait_for_load_state("load")
        # self.page.wait_for_selector(self.elements.ReviewConfigTitle)
        self.page.locator(self.elements.CreateFunctionBtn).click()
        self.page.wait_for_selector(self.elements.FunctionCreateBanner, state="visible")
        self.page.wait_for_selector(self.elements.FunctionCreateBanner, state="hidden")
        self.page.wait_for_load_state("load")

    def create_helm_chart_function(
        self,
        name: str,
        HelmChart: str,
        HelmChartVersion: str,
        HelmChartServiceName: str,
        inference_protocol: str,
        inference_endpoint: Optional[str] = None,
        health_endpoint: Optional[str] = None,
        inference_port: Optional[int] = None,
        model: Optional[list] = None,
        telemetry_logs: Optional[str] = None,
        telemetry_metrics: Optional[str] = None,
        telemetry_traces: Optional[str] = None,
        is_deploy: Optional[bool] = False,
        health_port: Optional[int] = None,
        secrets: Optional[Dict] = None,
        streaming_mode: Optional[bool] = False,
    ):
        """Create a new container function.

        parameters:
        -----------
        name : `str`
            The name of the function.

        HelmChart : `str`
            The HelmChart in the function.

        HelmChartVersion : `str`
            The tag of Helm Chart in the function.

        HelmChartServiceName : 'str'
            The Helm Chart ServiceName in the function

        inference_protocol : `str`
            The inference protocol of Helm Chart in the function.

        inference_endpoint : `str`, required for HTTP, not use for gRPC.
            The inference endpoint of Helm Chart in the function.

        health_endpoint : `str` , required for HTTP, not use for gRPC.
            The health endpoint of the Helm Chart.

        inference_port : `int`, optional
            The inference port of the function, default is None.

        Returns:
        --------
        None
        """
        logging.info(f"Creating Helm Chart function {name}...")

        # Choose container type
        self.page.locator(self.func_type_Helm_Chart_option).click()

        # Fill Function Name
        self.page.locator(self.func_name_input).fill(name)

        # Select Helm Chart and  Helm Chart Version
        self.page.locator(self.HelmChart_input).fill(HelmChart)
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(HelmChart)).click()
        self.page.locator(self.HelmChartVersion_input).fill(HelmChartVersion)
        self.page.wait_for_load_state("load")
        self.page.locator(
            self.elements.SelectorChoosebyName.format(HelmChartVersion)
        ).click()

        # Fill Helm Chart Service Name
        self.page.locator(self.HelmChartServiceName_input).fill(HelmChartServiceName)

        # Select Models
        if model:
            model_count = len(model)
            mode_seq = 1
            self.page.locator(self.elements.SelectModel).click()
            try:
                self.page.locator(self.elements.SelectModelOpen).wait_for(state="visible")
            except Exception:
                logging.info("Model has not opened and need to click again to make it open")
                self.page.locator(self.elements.SelectModel).click()
            for model_entry in model:
                self.page.locator(self.elements.ModelInput).fill(model_entry[0])
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(model_entry[0])
                ).nth(-1).click()
                self.page.locator(self.elements.ModelVersionInput).fill(model_entry[1])
                self.page.wait_for_load_state("load")
                self.page.locator(
                    self.elements.SelectorChoosebyName.format(model_entry[1])
                ).nth(-1).click()
                self.page.locator(self.elements.ModelNameInput).nth(mode_seq - 1).fill(
                    model_entry[2]
                )
                if mode_seq == model_count:
                    break
                else:
                    self.page.get_by_role("button", name="Add Another Model").click()
                    mode_seq += 1

        # Inference Path
        self.page.locator(self.healthpath_protocol_input).fill(inference_protocol)
        self.page.wait_for_load_state("load")
        self.page.locator(
            self.elements.SelectorChoosebyName.format(inference_protocol)
        ).click()
        if streaming_mode:
            self.page.locator(self.StreamingModeSwitch).click()

        if inference_port:
            self.page.locator(self.inference_port_input).fill(str(inference_port))

        if inference_protocol == "HTTP":
            # Inference Endpoint
            self.page.locator(self.inference_url_input).fill(str(inference_endpoint))

        if health_endpoint:
            self.page.locator(self.health_uri_input).fill(str(health_endpoint))

        if health_port:
            self.page.locator(self.health_port).fill(str(health_port))

        if secrets:
            secrets_count = len(secrets)
            secrets_seq = 1
            self.page.locator(self.elements.SecKeySelect).click()
            try:
                self.page.locator(self.elements.SecKeySelectOpen).wait_for(state="visible")
            except Exception:
                logging.info("Model has not opened and need to click again to make it open")
                self.page.locator(self.elements.SecKeySelect).click()
            for key, value in secrets.items():
                self.page.locator(self.elements.SecKeyInputBySeq).nth(secrets_seq - 1).fill(
                    key
                )
                self.page.locator(self.elements.SecValueInputBySeq).nth(-1).fill(value)
                if secrets_seq == secrets_count:
                    break
                else:
                    self.page.locator(self.elements.AddSecButton).nth(-1).click()
                    secrets_seq += 1

        # Telemetry Logs
        if telemetry_logs:
            self.page.locator(self.elements.TelemetrySelect).click()
            try:
                self.page.locator(self.elements.TelemetrySelectOpen).wait_for(
                    state="visible"
                )
            except Exception:
                logging.info("Model has not opened and need to click again to make it open")
                self.page.locator(self.elements.TelemetrySelect).click()
            self.page.locator(self.elements.TelemetryLogsInput).fill(telemetry_logs)
            self.page.wait_for_load_state("load")
            self.page.locator(
                self.elements.SelectorChoosebyName.format(telemetry_logs)
            ).click()
        if telemetry_metrics:
            if not self.page.locator(self.elements.TelemetrySelectOpen).is_visible():
                self.page.locator(self.elements.TelemetrySelect).click()
            self.page.locator(self.elements.TelemetryMetricsInput).fill(telemetry_metrics)
            self.page.wait_for_load_state("load")
            self.page.locator(
                self.elements.SelectorChoosebyName.format(telemetry_metrics)
            ).nth(-1).click()
        if telemetry_traces:
            if not self.page.locator(self.elements.TelemetrySelectOpen).is_visible():
                self.page.locator(self.elements.TelemetrySelect).click()
            self.page.locator(self.elements.TelemetryTracesInput).fill(telemetry_traces)
            self.page.wait_for_load_state("load")
            self.page.locator(
                self.elements.SelectorChoosebyName.format(telemetry_traces)
            ).nth(-1).click()
        # Click Create Function Button
        if not is_deploy:
            self.page.locator(self.elements.CreateFunctionBtn).click()
            self.page.wait_for_selector(self.elements.FunctionCreateBanner, state="visible")
            self.page.wait_for_selector(self.elements.FunctionCreateBanner, state="hidden")
            self.page.wait_for_load_state("load")
        else:
            logging.info("Deploying function...")
            self.page.locator(self.elements.CreateDeployFunctionBtn).click()
            self.page.wait_for_load_state("load")
            self.page.wait_for_timeout(5000)

    def create_nim_function(
        self,
        nim: str,
        tag: str,
        model_configuration: str,
        environment_variables: Optional[list] = None,
        function_name_prefix: Optional[str] = None,
        description: Optional[str] = None,
        tags: Optional[str] = None,
        is_deploy: Optional[bool] = False,
    ):
        """Create a nim container function.

        parameters:
        -----------
        nim : `str`
            The NIM in the function.

        tag : `str`
            The tag of NIM in the function.

        model_configuration : `str`
            The model configuration in the function.

        function_name_prefix : `str`, optional
            The prefix of the function name.

        description : `str`, optional
            The description of the function.

        tags : `str`, optional
            The tags of the function.

        is_deploy : `bool`, optional
            Whether to deploy the function.

        Returns:
        --------
        None
        """
        logging.info(f"Creating NIM container function {function_name_prefix}...")

        # Choose nim type
        self.page.locator(self.func_type_NIM_option).click()

        # Fill nim
        self.page.locator(self.nim_input).click()
        self.page.wait_for_load_state("load")
        self.page.locator("div[role='option']", has_text=nim).click()

        # Fill tag
        self.page.locator(self.nim_tag_input).wait_for(state="visible")
        self.page.locator(self.nim_tag_input).click()
        self.page.wait_for_load_state("load")
        self.page.locator("div[role='option']", has_text=tag).click()

        # Fill model configuration
        self.page.locator(self.nim_model_configuration_input).wait_for(state="visible")
        self.page.locator(self.nim_model_configuration_input).click()
        self.page.wait_for_load_state("load")
        self.page.locator("div[role='option']", has_text=model_configuration).click()

        if environment_variables:
            env_count = len(environment_variables)
            env_seq = 1
            self.page.locator(self.elements.EnvKeySelect).click()
            try:
                self.page.locator(self.elements.EnvKeySelectOpen).wait_for(state="visible")
            except Exception:
                logging.info("Model has not opened and need to click again to make it open")
                self.page.locator(self.elements.EnvKeySelect).click()
            exist_count = self.page.locator(self.elements.EnvKeyInputBySeq).count()
            for key, value in environment_variables.items():
                self.page.locator("//button[contains(text(),'Add Another')]").click()
                self.page.locator(self.elements.EnvKeyInputBySeq).nth(
                    exist_count + env_seq - 1
                ).fill(key)
                self.page.locator(self.elements.EnvValueInputBySeq).nth(
                    exist_count + env_seq - 1
                ).fill(value)
                if env_seq == env_count:
                    break
                else:
                    env_seq += 1

        if function_name_prefix:
            self.page.locator(self.nim_function_name_prefix_input).fill(
                function_name_prefix
            )

        if description:
            self.page.locator(self.nim_description_input).fill(description)

        if tags:
            tags_section = self.page.locator('label:has-text("Tags")').locator(
                "xpath=../../.."
            )
            input_elem = tags_section.locator('input[role="combobox"]')
            input_elem.fill(tags)
            input_elem.press("Enter")

        if not is_deploy:
            self.page.get_by_role(
                "button", name="Create Function Without Deploying"
            ).click()
            self.page.wait_for_selector(self.elements.FunctionCreateBanner, state="visible")
            self.page.wait_for_selector(self.elements.FunctionCreateBanner, state="hidden")
            self.page.wait_for_load_state("load")
        else:
            logging.info("Deploying function...")
            self.page.get_by_role("button", name="Create and Deploy Function").click()
            self.page.wait_for_load_state("load")
            self.page.wait_for_timeout(5000)

    def validate_name_error_flow(
        self,
        validate_data: list,
    ) -> list:
        """validata name error flow.

        parameters:
        -----------
        validate_data : `list`
            The names need to be validated .

        Returns:
        --------
        `list`
            The list of message helper contents.
        """
        message_helper_list = []
        logging.info(f"validata name error flow for {validate_data}...")

        # Choose container type
        self.page.locator(self.func_type_container_option).click()
        message_helper_content0 = self.page.locator(self.message_helper).text_content()
        message_helper_list.append(message_helper_content0)
        # Fill Function Name
        for i in validate_data:
            self.page.locator(self.func_name_input).fill(i)
            message_helper_content = self.page.locator(self.message_helper).text_content()
            message_helper_list.append(message_helper_content)

        self.page.locator(self.func_name_input).fill("")
        message_helper_content_empty = self.page.locator(self.message_helper).text_content()
        message_helper_list.append(message_helper_content_empty)
        return message_helper_list

    def verify_create_helm_chart_function_missing_required_fields(
        self,
        name: str,
        HelmChart: str,
        HelmChartVersion: str,
        HelmChartServiceName: str,
        inference_protocol: str,
        inference_endpoint: Optional[str] = None,
        health_endpoint: Optional[str] = None,
        inference_port: Optional[int] = None,
        missing_required_fields: list = None,
    ):
        """Verify create helm chart function missing required fields.

        parameters:
        -----------
        name : `str`
            The name of the function.

        HelmChart : `str`
            The HelmChart in the function.

        HelmChartVersion : `str`
            The tag of Helm Chart in the function.

        HelmChartServiceName : 'str'
            The Helm Chart ServiceName in the function

        inference_protocol : `str`
            The inference protocol of Helm Chart in the function.

        inference_endpoint : `str`, required for HTTP, not use for gRPC.
            The inference endpoint of Helm Chart in the function.

        health_endpoint : `str` , required for HTTP, not use for gRPC.
            The health endpoint of the Helm Chart.

        inference_port : `int`, optional
            The inference port of the function, default is None.

        missing_required_fields : `list`, optional
            The list of missing required fields, default is None.["func_name, "helm_chart_service_name"]

        Returns:
        --------
        `bool`
            True if the function is verified successfully, False otherwise.
        """
        logging.info(f"Verify create helm chart function missing required fields {name}...")

        # Choose container type
        self.page.locator(self.func_type_Helm_Chart_option).click()

        # Fill Function Name
        self.page.locator(self.func_name_input).fill(name)
        if "func_name" in missing_required_fields:
            self.page.locator(self.func_name_input).fill("")
        if self.page.locator(self.message_helper).is_visible():
            if self.page.locator(self.message_helper).text_content() == "Required":
                logging.info("The function name is required.")
            else:
                return False
        else:
            return False

        # Select Helm Chart and  Helm Chart Version
        self.page.locator(self.HelmChart_input).fill(HelmChart)
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(HelmChart)).click()
        self.page.locator(self.HelmChartVersion_input).fill(HelmChartVersion)
        self.page.wait_for_load_state("load")
        self.page.locator(
            self.elements.SelectorChoosebyName.format(HelmChartVersion)
        ).click()

        # Fill Helm Chart Service Name
        self.page.locator(self.HelmChartServiceName_input).fill(HelmChartServiceName)
        if "helm_chart_service_name" in missing_required_fields:
            self.page.locator(self.HelmChartServiceName_input).fill("")
        if self.page.locator(self.helm_chart_service_name_helper).is_visible():
            if (
                self.page.locator(self.helm_chart_service_name_helper).text_content()
                == "Required"
            ):
                logging.info("The helm chart service name is required.")
            else:
                return False

        # Inference Path
        self.page.locator(self.healthpath_protocol_input).fill(inference_protocol)
        self.page.wait_for_load_state("load")
        self.page.locator(
            self.elements.SelectorChoosebyName.format(inference_protocol)
        ).click()
        if inference_port:
            self.page.locator(self.inference_port_input).fill(str(inference_port))

        if inference_protocol == "HTTP":
            # Inference Endpoint
            self.page.locator(self.inference_url_input).fill(inference_endpoint)
            # Health Path
            self.page.locator(self.health_uri_input).fill(health_endpoint)

        if missing_required_fields:
            create_button = self.page.locator(self.create_function_btn)
            if create_button.is_enabled():
                return False
        else:
            # Click Create Function Button
            self.page.locator(self.create_function_btn).click()
            self.page.wait_for_selector(self.function_create_banner, state="visible")
            self.page.wait_for_selector(self.function_create_banner, state="hidden")
            self.page.wait_for_load_state("load")
        return True

    def verify_models_fields_not_autofill_when_create_container_function(
        self, name: str, container: str, tag: str
    ):
        """Create a new container function.

        parameters:
        -----------
        name : `str`
            The name of the function.

        container : `str`
            The container in the function.

        tag : `str`
            The tag of container in the function.

        model : `list`, optional
            Model info as a list, each element is a list of model, model_version and model_name, default is None.

        Returns:
        --------
        None
        """
        logging.info(f"Creating function {name}...")

        # Choose container type
        self.page.locator(self.elements.ContainerTypeChoose).click()

        # Fill Function Name
        self.page.locator(self.elements.NameInput).fill(name)

        # Select Container and Tag
        self.page.locator(self.elements.ContainerInput).fill(container)
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(container)).click()
        self.page.locator(self.elements.TagInput).fill(tag)
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(tag)).click()

        # Select Models
        self.page.locator(self.elements.SelectModel).click()
        try:
            self.page.locator(self.elements.SelectModelOpen).wait_for(state="visible")
            self.page.locator(self.elements.SelectModelContainer).wait_for(state="visible")
            self.page.locator(self.elements.SelectModelVersion).wait_for(state="visible")
            self.page.locator(self.elements.SelectModelName).wait_for(state="visible")
        except Exception:
            logging.info("Model has not opened and need to click again to make it open")

    def check_tag_input_box_in_create_function_page(
        self,
    ):
        tags_section = self.page.locator('label:has-text("Tags")').locator("xpath=../../..")
        max_tags = 64
        added = 0
        unique_y = set()

        for i in range(1, max_tags + 1):
            tag = f"test{i}"
            input_elem = tags_section.locator('input[role="combobox"]')
            input_elem.fill(tag)
            input_elem.press("Enter")
            added += 1

            tag_elements = tags_section.locator('button[data-testid="kui-tag"]')
            count = tag_elements.count()
            y_positions = []
            for j in range(count):
                box = tag_elements.nth(j).bounding_box()
                y_positions.append(int(box["y"]))
            unique_y = set(y_positions)
            logging.info(f"tags_num:{added}, unique_y: {unique_y}")

            if len(unique_y) > 1:
                logging.info(f"Tags wrapped after adding {added} tags.")
                return True

        return False

    def check_tag_input_description_in_create_function_page(
        self,
    ):
        description_span = self.page.locator(
            'span[data-testid="kui-text"]',
            has_text="Type a tag then press space or enter to confirm. Up to 64 tags can be added per function.",
        )
        if description_span.is_visible():
            return True
        else:
            return False

    def check_the_pre_populate_model_resource_name(
        self,
    ):
        self.page.locator(self.elements.SelectModel).click()
        try:
            self.page.locator(self.elements.SelectModelOpen).wait_for(state="visible")
        except Exception:
            logging.info("Model has not opened and need to click again to make it open")
            self.page.locator(self.elements.SelectModel).click()
        model_name = self.page.locator(self.elements.ModelNameInput).get_attribute(
            "placeholder"
        )
        model_name_disabled = self.page.locator(self.elements.ModelNameInput).is_disabled()
        if model_name != "Select a model version first" and not model_name_disabled:
            return False
        if CURRENT_ENV == "staging":
            model = [
                ["tadiathdfetp/model_nvcf_qa", "0.1", "tadiathdfetp-model_nvcf_qa_0.1"]
            ]
            resource = [
                [
                    "tadiathdfetp/resource_nvcf_qa",
                    "0.1",
                    "tadiathdfetp-resource_nvcf_qa_0.1",
                ]
            ]
        else:
            model = [
                ["rw983xdqtcdp/model_nvcf_qa", "0.1", "rw983xdqtcdp-model_nvcf_qa_0.1"]
            ]
            resource = [
                [
                    "rw983xdqtcdp/resource_nvcf_qa",
                    "0.1",
                    "rw983xdqtcdp-resource_nvcf_qa_0.1",
                ]
            ]
        self.page.locator(self.elements.ModelInput).fill(model[0][0])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(model[0][0])).nth(
            -1
        ).click()
        self.page.locator(self.elements.ModelVersionInput).fill(model[0][1])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(model[0][1])).nth(
            -1
        ).click()
        pre_populate_model_name = self.page.locator(
            self.elements.ModelNameInput
        ).get_attribute("value")
        if pre_populate_model_name != model[0][2]:
            return False

        # Select resource
        self.page.locator(self.elements.ResourceSelect).click()
        try:
            self.page.locator(self.elements.ResourceSelectOpen).wait_for(state="visible")
        except Exception:
            logging.info("Model has not opened and need to click again to make it open")
            self.page.locator(self.elements.ResourceSelect).click()
        resource_name = self.page.locator(self.elements.ResourceNameInput).get_attribute(
            "placeholder"
        )
        resource_name_disabled = self.page.locator(
            self.elements.ResourceNameInput
        ).is_disabled()
        if (
            resource_name != "Select a resource version first"
            and not resource_name_disabled
        ):
            return False
        self.page.locator(self.elements.ResourceInput).fill(resource[0][0])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(resource[0][0])).nth(
            -1
        ).click()
        self.page.locator(self.elements.ResourceVersionInput).fill(resource[0][1])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(resource[0][1])).nth(
            -1
        ).click()
        pre_populate_resource_name = self.page.locator(
            self.elements.ResourceNameInput
        ).get_attribute("value")
        if pre_populate_resource_name != resource[0][2]:
            return False
        return True

    def check_the_inference_endpoint(self):
        inference_endpoint = self.page.locator(
            self.elements.InferenceEndpointInput
        ).get_attribute("value")
        if inference_endpoint == "/":
            return True
        else:
            return False

    def check_the_model_resource_name_content(self):
        self.page.locator(self.elements.SelectModel).click()
        try:
            self.page.locator(self.elements.SelectModelOpen).wait_for(state="visible")
        except Exception:
            logging.info("Model has not opened and need to click again to make it open")
            self.page.locator(self.elements.SelectModel).click()
        model_name = self.page.locator(self.elements.ModelNameInput).get_attribute(
            "placeholder"
        )
        model_name_disabled = self.page.locator(self.elements.ModelNameInput).is_disabled()
        if model_name != "Select a model version first" and not model_name_disabled:
            return False
        if CURRENT_ENV == "staging":
            model = [
                ["tadiathdfetp/model_nvcf_qa", "0.1", "tadiathdfetp-model_nvcf_qa_0.1"]
            ]
            resource = [
                [
                    "tadiathdfetp/resource_nvcf_qa",
                    "0.1",
                    "tadiathdfetp-resource_nvcf_qa_0.1",
                ]
            ]
        else:
            model = [
                ["rw983xdqtcdp/model_nvcf_qa", "0.1", "rw983xdqtcdp-model_nvcf_qa_0.1"]
            ]
            resource = [
                [
                    "rw983xdqtcdp/resource_nvcf_qa",
                    "0.1",
                    "rw983xdqtcdp-resource_nvcf_qa_0.1",
                ]
            ]
        self.page.locator(self.elements.ModelInput).fill(model[0][0])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(model[0][0])).nth(
            -1
        ).click()
        self.page.locator(self.elements.ModelVersionInput).fill(model[0][1])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(model[0][1])).nth(
            -1
        ).click()

        self.page.get_by_role("button", name="Add Another Model").click()
        self.page.locator(self.elements.ModelInput).fill(model[0][0])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(model[0][0])).nth(
            -1
        ).click()
        self.page.locator(self.elements.ModelVersionInput).fill(model[0][1])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(model[0][1])).nth(
            -1
        ).click()
        if not self.page.locator("//span[text()='Model name must be unique']").is_visible():
            logging.info("There is no error message for model name is not unique")
            return False
        self.page.locator(self.elements.ModelNameInput).nth(-1).fill("0.0.2")
        if not self.page.locator(
            "//span[text()='Model name must begin with a lowercase alpha, and following characters must be alphanumeric. Use periods, underscores or hyphens to separate words.']"
        ).is_visible():
            logging.info("There is no error message for model name is not valid")
            return False

        # Select resource
        self.page.locator(self.elements.ResourceSelect).click()
        try:
            self.page.locator(self.elements.ResourceSelectOpen).wait_for(state="visible")
        except Exception:
            logging.info("Model has not opened and need to click again to make it open")
            self.page.locator(self.elements.ResourceSelect).click()
        resource_name = self.page.locator(self.elements.ResourceNameInput).get_attribute(
            "placeholder"
        )
        resource_name_disabled = self.page.locator(
            self.elements.ResourceNameInput
        ).is_disabled()
        if (
            resource_name != "Select a resource version first"
            and not resource_name_disabled
        ):
            return False
        self.page.locator(self.elements.ResourceInput).fill(resource[0][0])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(resource[0][0])).nth(
            -1
        ).click()
        self.page.locator(self.elements.ResourceVersionInput).fill(resource[0][1])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(resource[0][1])).nth(
            -1
        ).click()
        self.page.get_by_role("button", name="Add Another Resource").click()
        self.page.locator(self.elements.ResourceInput).fill(resource[0][0])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(resource[0][0])).nth(
            -1
        ).click()
        self.page.locator(self.elements.ResourceVersionInput).fill(resource[0][1])
        self.page.wait_for_load_state("load")
        self.page.locator(self.elements.SelectorChoosebyName.format(resource[0][1])).nth(
            -1
        ).click()
        if not self.page.locator(
            "//span[text()='Resource name must be unique']"
        ).is_visible():
            logging.info("There is no error message for resource name is not unique")
            return False
        self.page.locator(self.elements.ResourceNameInput).nth(-1).fill("0.0.2")
        if not self.page.locator(
            "//span[text()='Resource name must begin with a lowercase alpha, and following characters must be alphanumeric. Use periods, underscores or hyphens to separate words.']"
        ).is_visible():
            logging.info("There is no error message for resource name is not valid")
            return False
        return True

    def check_the_description_in_create_function_page(self, long_description: str):
        import re

        description_span = self.page.locator(self.nim_description_input).get_attribute(
            "style"
        )
        match = re.search(r"height:\s*(\d+)px", description_span)
        if match:
            initial_height = int(match.group(1))

        logging.info(f"initial_height: {initial_height}")
        self.page.locator(self.nim_description_input).fill(long_description)
        self.page.wait_for_load_state("load")
        self.page.wait_for_timeout(5000)
        description_span = self.page.locator(self.nim_description_input).get_attribute(
            "style"
        )
        match = re.search(r"height:\s*(\d+)px", description_span)
        if match:
            final_height = int(match.group(1))
            logging.info(f"final_height: {final_height}")
            if final_height > initial_height:
                return True
            else:
                return False
