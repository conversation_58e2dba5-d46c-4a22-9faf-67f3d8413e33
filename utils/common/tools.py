import logging
import os
import re
import importlib

from datetime import datetime
from utils.send_mail import send_mail


def convert_duration_to_iso_format(duration_str: str) -> str:
    """Convert human readable duration to ISO 8601 duration format.

    Args:
        duration_str: Human readable duration string (e.g., "1 hour", "10 minutes")

    Returns:
        ISO 8601 duration format string (e.g., "PT1H", "PT10M")
    """
    duration_str = duration_str.strip().lower()

    if duration_str == "none (runs forever)":
        return "NONE"

    parts = duration_str.split()
    if len(parts) != 2:
        raise ValueError(f"Invalid duration format: {duration_str}")

    number = int(parts[0])
    unit = parts[1].rstrip("s")

    unit_map = {"minute": "M", "hour": "H", "day": "D"}

    if unit not in unit_map:
        raise ValueError(f"Unsupported time unit: {unit}")

    return f"PT{number}{unit_map[unit]}"


def add_timestamp(name: str, format="%Y%m%d_%H%M%S"):
    """Add a timestamp to make the name unique"""
    if name is None:
        raise ValueError("The given name is None")
    timestamp = datetime.now().strftime(format)
    return f"{name}_{timestamp}"


def get_cases_by_status(stats, status):
    cases = [
        f"{i.head_line}_{i.when}" for i in stats.get(status, []) if i.when != "teardown"
    ]
    return cases


def send_ci_report(terminalreporter, results):
    GITLAB_CI = os.getenv("GITLAB_CI", False)
    SEND_REPORT = os.getenv("SEND_REPORT", False)
    CI_PIPELINE_SOURCE = os.getenv("CI_PIPELINE_SOURCE", "")
    report_jobs = ["web", "schedule", "parent_pipeline"]
    if GITLAB_CI == "true" and SEND_REPORT == "true" and CI_PIPELINE_SOURCE in report_jobs:
        logging.info("Sending CI/CD report...")
        CI_JOB_NAME = os.getenv("CI_JOB_NAME", "Null")
        CI_PIPELINE_ID = os.getenv("CI_PIPELINE_ID", 0)
        CI_PROJECT_PATH = os.getenv("CI_PROJECT_PATH", "")
        RECIPIENTS = os.getenv("RECIPIENTS", "").split(",")
        if len(RECIPIENTS) > 0:
            logging.info(f"Recipients: {RECIPIENTS}")
        else:
            logging.warning("Empty Recipients. Skip sending mail.")
            return
        CC_LIST = os.getenv("CC_LIST", "").split(",")
        logging.info(f"CC List: {CC_LIST}")
        result = "PASS" if terminalreporter._session.exitstatus == 0 else "FAIL"
        title = f"{result} - {terminalreporter._session.name} {CI_JOB_NAME} result report - {datetime.strftime(datetime.now(), '%Y/%m/%d')}"
        send_mail.send(
            title=title,
            results=results,
            recipients=RECIPIENTS,
            cc_list=CC_LIST,
            project_path=CI_PROJECT_PATH,
            pipeline_id=CI_PIPELINE_ID,
        )
    else:
        logging.info("Skip the send report mail step.")


def execSystemCmd(cmd):
    r = os.popen(cmd)
    text = r.read()
    r.close()
    return text.rstrip()


def parse_uc_entity_url_path(url_path: str):
    """
    Parse the URL path(with out the schema and host) adn output the
      - org
      - team(if have)
      - entity_type
      - entity_name

    the format sample:
      - orgs/rtpxz6khmfun/containers/public_image
      - orgs/rtpxz6khmfun/teams/uc_test/containers/public_image
    """
    url_pattern = re.compile(
        r"/{0,1}orgs/([0-9a-zA-Z_]*)(/teams/([0-9a-zA-Z_]*)){0,1}/(.*)/(.*)"
    )
    res = url_pattern.match(url_path)
    if res:
        groups = res.groups()
        org = groups[0]
        team = groups[2]
        entity_type = groups[3]
        entity_name = groups[4]
        return org, team, entity_type, entity_name
    else:
        logging.error(f"The url_path: {url_path} format is not correct!")
        raise ValueError(f"The url_path: {url_path} format is not correct!")


def module_import(module_path: str, class_name: str):
    """Import the module and class.

    parameters:
    -----------
    module_path: `str`
        The path of the module
    class_name: `str`
        The class name

    Returns:
    --------
    `class`
        The class object
    """
    module = importlib.import_module(module_path)
    return getattr(module, class_name)
