from pathlib import Path

from cloudia.utils.file_handler import file_handler


class ElementsByPath:
    def __init__(self, element_path: Path):
        self.elements_file = element_path
        element_toml = file_handler(self.elements_file)
        self.elements = element_toml.read()

    def get(self, page_name):
        if self.elements.get(page_name, None):
            return self.elements.get(page_name)
        else:
            raise TypeError(f"Elements for {page_name} is not defined")
