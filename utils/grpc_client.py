import grpc
from jsonschema import validate
from jsonschema.exceptions import ValidationError
import data.grpc_data.grpc_echo_sample.echo_pb2 as echo_pb2
import data.grpc_data.grpc_echo_sample.echo_pb2_grpc as echo_pb2_grpc
from grpc_reflection.v1alpha import reflection_pb2
from grpc_reflection.v1alpha import reflection_pb2_grpc


class GRPCClient:
    def __init__(self, url, metadata=None):
        self.url = url
        self.channel = grpc.secure_channel(self.url, grpc.ssl_channel_credentials())
        self.stub = echo_pb2_grpc.EchoStub(self.channel)
        self.metadata = metadata if metadata else []
        self.reflection_stub = reflection_pb2_grpc.ServerReflectionStub(self.channel)

    def make_request(self, method_name, request_args, streaming=False):
        method = getattr(self.stub, method_name)
        if streaming:
            request_iterator = (echo_pb2.EchoRequest(**args) for args in request_args)
            response_iterator = method(request_iterator, metadata=self.metadata)
            return response_iterator
        else:
            response = method(echo_pb2.EchoRequest(**request_args), metadata=self.metadata)
            return response

    def validate_response(self, response, schema):
        try:
            validate(instance=response, schema=schema)
        except ValidationError as e:
            return False, e
        return True, None

    def describe_service(self):
        describe_service_request = reflection_pb2.ServerReflectionRequest(
            file_containing_symbol="Echo"
        )
        describe_response_iterator = self.reflection_stub.ServerReflectionInfo(
            iter([describe_service_request]), metadata=self.metadata
        )
        return describe_response_iterator

    def list_service(self):
        list_services_request = reflection_pb2.ServerReflectionRequest(list_services="")
        list_response_iterator = self.reflection_stub.ServerReflectionInfo(
            iter([list_services_request]), metadata=self.metadata
        )
        return list_response_iterator
