import logging
import os
import re
from datetime import datetime
from pathlib import Path

from jinja2 import Environment, FileSystemLoader


def send_mail(**kwargs):  # noqa: C901
    """
    Send a plain text/html mail, support to attach file(s) or image(s).
    @param:
        from_addr: Email address that send from (string)
        to_addrs: Email address(es) that send to (string or list)
        cc_addrs: Email address(es) that cc to (Optional, string or list)
        subject: Email subject (string)
        content: Email content
        email_type: Email type (plain/html,
            plain will not apply to the mail with image attached)
        attachments: Attach file(s) (Optional)
        images: Attach image(s) (Optional)
        smtp_server: SMTP server address
        smtp_port: SMTP port number (Optional, default: 25)
        verbose: Show verbose (True/False, default: False)
    @example:
        send_mail(
            from_addr='test@localhost', to_addrs=['<EMAIL>'],
            cc_addrs='<EMAIL>', subject='Test Mail',
            content='This is a test email.', email_type='html',
            attachments=['attach1', 'attach2'], images='image1',
            smtp_server='smtp.nvidia.com', smtp_port=25, verbose=True)
    """
    import smtplib
    from email.mime.image import MIMEImage
    from email.mime.multipart import MIMEMultipart
    from email.mime.text import MIMEText

    from_addr = kwargs.get("from_addr")
    to_addrs = kwargs.get("to_addrs")
    cc_addrs = kwargs.get("cc_addrs")
    subject = kwargs.get("subject")
    content = kwargs.get("content")
    email_type = kwargs.get("email_type")
    attachments = kwargs.get("attachments")
    images = kwargs.get("images")
    smtp_server = kwargs.get("smtp_server")
    smtp_port = kwargs.get("smtp_port", 25)
    verbose = kwargs.get("verbose", False)
    for req_param in [
        "from_addr",
        "to_addrs",
        "subject",
        "content",
        "email_type",
        "smtp_server",
        "smtp_port",
    ]:
        if is_empty(eval(req_param)):
            logging.error(f'Requires "{req_param}" for email sending')
            return False
    if not isinstance(email_type, str) or email_type not in ["plain", "html"]:
        logging.error(f"Invalid Email specified: {email_type}")
        logging.debug("Supported types: plain, html")
        return False
    # Validate email address
    if not is_valid_email_address(from_addr):
        logging.error("Invalid From email address format: %s" % from_addr)
        return False
    if isinstance(to_addrs, str) or isinstance(to_addrs, list):
        if isinstance(to_addrs, str):
            to_addrs = [addr for addr in to_addrs.split(";") if addr is not None]
        for to_addr in to_addrs:
            if not is_valid_email_address(to_addr):
                logging.error("Invalid To email address format: %s" % to_addr)
                return False
    else:
        logging.error("To email address should be a string or list")
        logging.debug("Specified address type: %s" % type(to_addrs).__name__)
        return False
    if isinstance(cc_addrs, str) or isinstance(cc_addrs, list):
        if isinstance(cc_addrs, str):
            cc_addrs = [cc_addrs]
        for cc_addr in cc_addrs:
            if not is_valid_email_address(cc_addr):
                logging.error(f"Invalid Cc email address format: {cc_addr}")
                return False
    else:
        cc_addrs = []
    # Validate SMTP port number
    if not is_positive_integer(smtp_port) or int(smtp_port) < 1 or int(smtp_port) > 65535:
        logging.error("Invalid SMTP port number specified: %s" % smtp_port)
        logging.debug("Port number should be 1~65535")
        return False
    smtp_port = int(smtp_port)
    # Validate attachments
    if isinstance(attachments, str) or isinstance(attachments, list):
        if isinstance(attachments, str):
            attachments = [attachments]
        for attachment in attachments:
            if not os.path.isfile(attachment):
                logging.error("Invalid attachment file specified: %s" % attachment)
                return False
    else:
        attachments = []
    # Validate images
    if isinstance(images, str) or isinstance(images, list):
        if isinstance(images, str):
            images = [images]
        for image in images:
            if not os.path.isfile(image):
                logging.error("Invalid image files specified: %s" % image)
                return False
    else:
        images = []
    msg = MIMEMultipart()
    if images:
        ori_text = "<p>%s</p>" % content
        for image in images:
            ori_text += '<br><img src="cid:%s"><br>' % os.path.abspath(image)
        msg.attach(MIMEText(ori_text, "html"))
    else:
        msg.attach(MIMEText(content, _subtype=email_type, _charset="gb2312"))
    # Add each attachment
    for attachment in attachments:
        filename = os.path.split(attachment)[1]
        att = MIMEText(open(attachment, "rb").read(), "base64", "gb2312")
        att["Content-Type"] = "application/octet-stream"
        att["Content-Disposition"] = 'attachment; filename="%s"' % filename
        msg.attach(att)
    # Add each image
    for image in images:
        img = MIMEImage(open(image, "rb").read())
        img.add_header("Content-ID", "<%s>" % image)
        msg.attach(img)
    # Add email header
    msg["Subject"] = subject
    msg["From"] = from_addr
    msg["To"] = ";".join(to_addrs)
    if cc_addrs:
        msg["Cc"] = ";".join(cc_addrs)
    # Send email
    send_succeeded = False
    smtpObj = smtplib.SMTP(smtp_server, smtp_port, timeout=5)
    if verbose:
        smtpObj.set_debuglevel(True)
    try:
        smtpObj.sendmail(from_addr, to_addrs + cc_addrs, msg.as_string())
        logging.info("Succeeded to send the email")
        send_succeeded = True
    except Exception as e:
        logging.error("Failed to send the email")
        logging.debug(e)
    finally:
        smtpObj.quit()
    return send_succeeded


def is_empty(value):
    """
    Check if target is not defined or empty (string, list, dict, tuple).
    """
    if value is None:
        return True

    if isinstance(value, (str, list, dict, tuple)):
        return len(value) == 0

    if isinstance(value, (int, float)):
        return len(str(value).strip()) == 0

    return False


def is_valid_email_address(email_address):
    """
    Validate email address format.
    """
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"

    return bool(re.match(pattern, email_address))


def is_positive_integer(s):
    """
    Validates a string if is positive integer.
    """

    try:
        return str(int(s)) == str(s) and int(s) > 0
    except (TypeError, ValueError):
        return False


def send(title, results, recipients, cc_list, project_path, pipeline_id):
    logging.info("Send the report mail!")

    CURRENT_DIR = Path(__file__).parent.resolve()

    templateLoader = FileSystemLoader(searchpath=Path(CURRENT_DIR))
    env = Environment(loader=templateLoader)
    report_template = env.get_template("template.html")

    recipients = recipients
    cc_list = cc_list
    time_now = datetime.now()
    report_time = datetime.strftime(time_now, "%Y-%m-%d, %H:%M:%S")
    report_content = report_template.render(
        title=title,
        results=results,
        project_path=project_path,
        pipeline_id=pipeline_id,
        report_time=report_time,
    )
    with open("report_content.html", "w+", encoding="utf-8") as f:
        f.write(report_content)
    try:
        send_mail(
            from_addr="<EMAIL>",
            to_addrs=recipients,
            cc_addrs=cc_list,
            subject=title,
            content=report_content,
            email_type="html",
            smtp_server="smtp.nvidia.com",
            smtp_port=25,
            verbose=False,
        )
    except Exception as e:
        print(e)
