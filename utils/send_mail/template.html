<!DOCTYPE html>
<html>
<meta charset="utf-8" />
<title>{{ title }}</title>

<head>
    {% raw %}
    <style type="text/css">
        body {
            font-family: verdana;
            font-size: 12px;
        }

        h2 {
            font-size: 14px;
            font-weight: normal;
        }

        table.gridtable {
            font-family: verdana, arial, sans-serif;
            font-size: 12px;
            color: #333333;
            border-width: 1px;
            border-color: #666666;
            border-collapse: collapse;
        }

        table.gridtable th {
            border-width: 1px;
            padding: 12px;
            border-style: solid;
            border-color: #666666;
            background-color: #84b052;
        }

        table.gridtable td {
            border-width: 1px;
            padding: 12px;
            border-style: solid;
            border-color: #666666;
            background-color: #ffffff;
        }
    </style>
    {% endraw %}
</head>

<body>
    <h2>
        <p>Hi,</p>
        <p>Here comes the test report at {{ report_time }}.</p>
        <p>GitLab CI/CD pipeline: <a
                href="https://gitlab-master.nvidia.com/{{ project_path }}/-/pipelines/{{ pipeline_id }}/test_report"> {{
                project_path }}/pipelines/{{ pipeline_id }}/test_report</a></p>
    </h2>
    <br />
    <table class="gridtable">
        <tr>
            <th>PASS</th>
            <th>FAIL</th>
            <th>ERROR</th>
            <th>SKIPPED</th>
            <th>TOTAL</th>
        </tr>
        <tr>
            <td align="center">{{ results.PASS }}</td>
            <td align="center">{{ results.FAIL.number }}</td>
            <td align="center">{{ results.ERROR.number }}</td>
            <td align="center">{{ results.SKIPPED }}</td>
            <td align="center">{{ results.Total }}</td>
        </tr>
    </table>
    <br />
    {% if results.FAIL.number > 0 %}
    <h2>The failed cases are:</h2>
    <ul>
        {% for failed_case in results.FAIL.cases %}
        <li>{{ failed_case }}</li>
        {% endfor %}
    </ul>
    {% endif %}
    <br />
    {% if results.ERROR.number > 0 %}
    <h2>The error cases are:</h2>
    <ul>
        {% for error_case in results.ERROR.cases %}
        <li>{{ error_case }}</li>
        {% endfor %}
    </ul>
    {% endif %}
    <br />
</body>

</html>
