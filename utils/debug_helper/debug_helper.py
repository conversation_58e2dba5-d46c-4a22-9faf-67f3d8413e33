import os
from datetime import datetime
from pathlib import Path

import pytest

from config.consts import DEFAULT_VIEWPORT_SIZE

CAP_DEBUG = os.environ.get("CAP_DEBUG", False)
CAP_VIDEO = os.environ.get("CAP_VIDEO", True)
CAP_HAR = os.environ.get("CAP_HAR", True)
CURRENT_DIR = Path(__file__).parent.resolve()
WORK_DIR = Path(".").resolve()
LOG_DIR = WORK_DIR / "log"


class DebugHelper:
    @staticmethod
    def generate_debug_config(
        config: dict,
        request: pytest.FixtureRequest,
        log_path: Path = LOG_DIR,
    ) -> dict:
        if CAP_DEBUG or request.config.getoption("CAP_DEBUG"):
            timestamp = datetime.timestamp(datetime.now())
            node_name = getattr(request.node, "name", ".")
            HAR_PATH = log_path / f"{node_name}/har/testing_{timestamp}.har"
            VIDEO_DIR = log_path / f"{node_name}/video/video_{timestamp}"
            if CAP_VIDEO and request.config.getoption("CAP_VIDEO"):
                config.update(
                    {
                        "record_video_dir": VIDEO_DIR,
                        "record_video_size": DEFAULT_VIEWPORT_SIZE,
                    }
                )
            if CAP_HAR and request.config.getoption("CAP_HAR"):
                config.update({"record_har_path": HAR_PATH})
        return config
