import logging

from copy import deepcopy
from cloudia.api.http.api_session import APISession
from cloudia.api.utils.ssa_token_utils import SSAToken
from config.scope_consts import (
    NVCT_SCOPE_ALL,
)


def merge_config(orig_config):
    user_config = deepcopy(orig_config)
    base_config = {}
    if "base_config" in orig_config:
        base_config = orig_config["base_config"]
        del user_config["base_config"]
    merge_config = user_config.copy()
    if base_config:
        for user, config in merge_config.items():
            if not isinstance(config, dict):
                logging.warning(
                    f"Invalid config for user '{user}'. Expected dict, found {type(config)}. Skipping merge."
                )
                continue
            config.update(
                {key: value for key, value in base_config.items() if key not in config}
            )
    return merge_config


def get_session_nvct_ssa_allscope(running_config):
    ssa_token = SSAToken(running_config["nvct_ssa_info"], NVCT_SCOPE_ALL).ssa_token
    return {
        "type": "ssa",
        "nvct": APISession(api_host=running_config["api_host_nvct"], api_key=ssa_token),
    }
