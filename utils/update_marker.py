#!/usr/bin/env python3
import os
import platform
import re
import subprocess

from config.consts import MARKERS_DIR

from cloudia.utils.file_handler import Toml<PERSON><PERSON>ler

TEMPLATE_URL_PREFIX = "https://hqnvwb01/DTResults/showTask.aspx?id=P1072_{}"

if not MARKERS_DIR.parent.exists():
    MARKERS_DIR.parent.mkdir(parents=True)
if not MARKERS_DIR.exists():
    MARKERS_DIR.touch()


def list_templates():
    templates = []
    if os.name == "posix" and platform.system() == "Darwin":
        GET_MARKERS_CMD = (
            'grep -r --include="*.py" "pytest.mark.T" testcases/ | awk \'{print $2}\''
        )
    else:
        GET_MARKERS_CMD = 'grep -r -P -h --include="*.py" "pytest.mark.T" testcases/'
    try:
        completed_process = subprocess.run(
            GET_MARKERS_CMD,
            shell=True,
            check=True,
            capture_output=True,
            text=True,
        )
        templates = re.findall(r"@pytest.mark.(T[0-9]{7,})", completed_process.stdout, re.M)
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        exit(1)
    return templates


def update_markers_file(templates: list):
    markers = TomlHandler(MARKERS_DIR).read()
    for template in templates:
        url = TEMPLATE_URL_PREFIX.format(template)
        if not markers.get(template, None):
            print(f"Add template: {template}: {url}")
            markers.update({template: url})
        if markers.get(template) != url:
            print(f"Update template: {template}: {url}")
            markers.update({template: url})
    try:
        sorted_markers = {k: markers[k] for k in sorted(markers)}
        TomlHandler(MARKERS_DIR).write(sorted_markers)
        print("Markers file has been updated.")
        print(f"There are {len(sorted_markers)} test cases")
    except Exception as e:
        print(e)


if __name__ == "__main__":
    templates = list_templates()
    update_markers_file(templates)
