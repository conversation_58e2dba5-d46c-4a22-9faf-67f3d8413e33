import logging
import pytest
from playwright.sync_api import Page

from pages.GPUCapacity.GpuCapacity import GpuCapacityPage
from cloudia.utils.backend_service.nvcf.nvcf_utils import NVCFUtils
from pages.Functions.FunctionVerDetailPage import FunctionVerDetailPage
from pages.Deployments.DeploymentsCreatePage import DeploymentsCreatePage


class TestGpuCapacity:
    NVCF_ADMIN_USER = ["org_admin-nvcf_admin"]
    NVCF_USER_USESR = ["org_user-nvcf_user"]
    NVCF_VIEWER_USER = ["org_user-nvcf_viewer"]
    NVCF_PROD_ADMIN = ["nvcf_admin"]
    NVCF_VIEWER_USER = ["nvcf_viewer"]
    NVCF_GMAIL_OWNER = ["nvcf_gmail_owner"]

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3952638
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_gpu_allocation_in_chart_view(
        self,
        get_user_page: Page,
    ):
        gpu_capacity_page = GpuCapacityPage(get_user_page)
        gpu_capacity_page.navigate_to_gpu_capacity_page()
        #  check the default view is chart view
        assert gpu_capacity_page.page.locator(
            gpu_capacity_page.TABLE_VIEW_BUTTON
        ).is_visible(), "Chart View button is not visible"
        gpu_allocation_num = gpu_capacity_page.get_gpu_allocation_num()
        logging.info(f"GPU allocation number: {gpu_allocation_num}")
        assert gpu_capacity_page.check_tooltip_on_each_gpu_allocation()

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3952639
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_gpu_allocation_in_table_view(
        self,
        get_user_page: Page,
    ):
        gpu_capacity_page = GpuCapacityPage(get_user_page)
        gpu_capacity_page.navigate_to_gpu_capacity_page()
        #  check the default view is chart view
        assert gpu_capacity_page.page.locator(
            gpu_capacity_page.TABLE_VIEW_BUTTON
        ).is_visible(), "Chart View button is not visible"
        chart_view_gpu_allocation = gpu_capacity_page.get_gpu_allocation_in_table_view()
        gpu_capacity_page.page.locator(gpu_capacity_page.TABLE_VIEW_BUTTON).click()
        table_view_gpu_allocation = gpu_capacity_page.get_gpu_allocation_in_chart_view()
        assert (
            chart_view_gpu_allocation == table_view_gpu_allocation
        ), "GPU allocation in chart view and table view are not the same"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3952640
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_deployment_in_instance_by_function_version(
        self,
        get_user_page: Page,
        session_nvcf_admin_sak,
    ):
        session_dict = session_nvcf_admin_sak
        funcs_info = NVCFUtils(session_dict).get_functions_info(["ACTIVE"])
        func_id = funcs_info["fastapi_echo"]["id"]
        func_ver_id = funcs_info["fastapi_echo"]["versionId"]
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(func_id=func_id, vers_id=func_ver_id)
        func_ver_detail_page.page.wait_for_selector(
            "//button[text()='Overview']", state="visible"
        )
        func_ver_detail_page.switch_tab(
            tab_name="deployment details", func_id=func_id, vers_id=func_ver_id
        )
        instance = func_ver_detail_page.InstancesType.get_instance_type()
        gpu_type = func_ver_detail_page.InstancesType.get_GPU()
        target_regions = func_ver_detail_page.InstancesType.get_target_regions()
        logging.info(
            f"Instance: {instance}, GPU: {gpu_type}, Target Regions: {target_regions}"
        )

        gpu_capacity_page = GpuCapacityPage(get_user_page)
        gpu_capacity_page.navigate_to_gpu_capacity_page()
        certain_func_ver_data = gpu_capacity_page.get_certain_function_version_data(
            func_version=func_ver_id
        )
        assert (
            len(certain_func_ver_data) == 1
        ), "The number of instances in the function version is not correct"
        for data in certain_func_ver_data:
            assert data["GPU Type"] == gpu_type["GPUs"], "The GPU type is not correct"
            assert (
                data["Region"] == target_regions["Target Regions"]
            ), "The region is not correct"
            assert (
                data["Instance Name"] == instance["Instance Type"]
            ), "The instance name is not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3952641
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_filters_in_instance_by_function_version(
        self,
        get_user_page: Page,
    ):
        gpu_capacity_page = GpuCapacityPage(get_user_page)
        gpu_capacity_page.navigate_to_gpu_capacity_page()
        # check the gpu type filter can work
        assert gpu_capacity_page.check_filter(
            type="gpu_type"
        ), "The gpu type filter is not correct"
        #  check the instance name filter can work
        assert gpu_capacity_page.check_filter(
            type="instance_name"
        ), "The instance name filter is not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3952642
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_search_in_instance_by_function_version(
        self,
        get_user_page: Page,
        session_nvcf_admin_sak,
    ):
        session_dict = session_nvcf_admin_sak
        funcs_info = NVCFUtils(session_dict).get_functions_info(["ACTIVE"])
        func_id = funcs_info["fastapi_echo"]["id"]
        func_ver_id = funcs_info["fastapi_echo"]["versionId"]
        func_name = funcs_info["fastapi_echo"]["name"]

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(func_id=func_id, vers_id=func_ver_id)
        func_ver_detail_page.page.wait_for_selector(
            "//button[text()='Overview']", state="visible"
        )
        func_ver_detail_page.switch_tab(
            tab_name="deployment details", func_id=func_id, vers_id=func_ver_id
        )
        instance = func_ver_detail_page.InstancesType.get_instance_type()
        instance_name = instance["Instance Type"]

        gpu_capacity_page = GpuCapacityPage(get_user_page)
        gpu_capacity_page.navigate_to_gpu_capacity_page()

        assert gpu_capacity_page.check_search(
            search_text=func_name, type="function_name"
        ), "The search is not correct"
        assert gpu_capacity_page.check_search(
            search_text=func_ver_id, type="function_version"
        ), "The search is not correct"
        assert gpu_capacity_page.check_search(
            search_text=instance_name, type="instance_name"
        ), "The search is not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3952643
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_sort_in_instance_by_function_version(
        self,
        get_user_page: Page,
    ):
        gpu_capacity_page = GpuCapacityPage(get_user_page)
        gpu_capacity_page.navigate_to_gpu_capacity_page()
        assert gpu_capacity_page.check_sort(
            type="gpu_type"
        ), "The sort for gpu type is not correct"
        assert gpu_capacity_page.check_sort(
            type="instance_name"
        ), "The sort for instance name is not correct"
        assert gpu_capacity_page.check_sort(
            type="function_name"
        ), "The sort for function name is not correct"
        assert gpu_capacity_page.check_sort(
            type="region"
        ), "The sort for region is not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3952644
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_active_instances(
        self,
        get_user_page: Page,
    ):
        gpu_capacity_page = GpuCapacityPage(get_user_page)
        gpu_capacity_page.navigate_to_gpu_capacity_page()
        gpu_capacity_page.page.locator(gpu_capacity_page.TABLE_VIEW_BUTTON).click()
        gpu_allocation_in_table_view = gpu_capacity_page.get_gpu_allocation_in_chart_view()
        active_instances_in_table_view = (
            gpu_capacity_page.get_active_instances_in_table_view()
        )
        logging.info(f"GPU allocation in table view: {gpu_allocation_in_table_view}")
        logging.info(f"Active instances in table view: {active_instances_in_table_view}")
        for gpu in gpu_allocation_in_table_view:
            if gpu["Name"] in active_instances_in_table_view:
                assert (
                    int(gpu["Active"]) == active_instances_in_table_view[gpu["Name"]]
                ), f"The active instances is not correct for gpu type: {gpu['Name']}"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3952649
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_gpu_allocation_in_chart_view_by_regions(
        self,
        get_user_page: Page,
    ):
        gpu_capacity_page = GpuCapacityPage(get_user_page)
        gpu_capacity_page.navigate_to_gpu_capacity_page()
        #  check the default is all regions
        assert gpu_capacity_page.page.locator(
            "//span[text()='All Regions']"
        ).first.is_visible(), "All Regions is not visible"
        assert (
            gpu_capacity_page.check_gpu_allocation_regions()
        ), "The regions are not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3952650
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_gpu_allocation_in_table_view_by_regions(
        self,
        get_user_page: Page,
    ):
        gpu_capacity_page = GpuCapacityPage(get_user_page)
        gpu_capacity_page.navigate_to_gpu_capacity_page()
        #  check the default is all regions
        assert gpu_capacity_page.page.locator(
            "//span[text()='All Regions']"
        ).first.is_visible(), "All Regions is not visible"
        assert gpu_capacity_page.check_gpu_allocation_regions(
            view="table"
        ), "The regions are not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3952651
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_active_instances_by_regions(
        self,
        get_user_page: Page,
    ):
        gpu_capacity_page = GpuCapacityPage(get_user_page)
        gpu_capacity_page.navigate_to_gpu_capacity_page()
        #  check the default is all regions
        assert gpu_capacity_page.page.locator(
            "//span[text()='All Regions']"
        ).last.is_visible(), "All Regions is not visible"
        assert (
            gpu_capacity_page.check_active_instances_regions()
        ), "The regions are not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3952652
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_deploy_instance_number_exceed_limitation(
        self,
        get_user_page: Page,
        new_container_function,
    ):
        func_id, func_ver_id = new_container_function
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.navigate_to_page()
        func_name_content = (
            "//label[text()='Function Name' or text()='Function ID']/following::div[1]"
        )
        func_vers_content = "//label[text()='Function Version' or text()='Function Version ID']/following::div[1]"

        func_name_input_element = deployments_create_page.FunctionNameSelector.elements[
            "FuncNameInput"
        ]
        func_vers_input_element = deployments_create_page.FunctionVersionSelector.elements[
            "FuncVersionInput"
        ]

        if (
            deployments_create_page.page.locator(func_name_content).text_content()
            == "Select a function"
        ):
            deployments_create_page.FunctionNameSelector.search_and_select(
                func_name_input_element, func_id
            )

        deployments_create_page.page.wait_for_timeout(1000)

        if (
            deployments_create_page.page.locator(func_vers_content).text_content()
            == "Select a function version"
        ):
            deployments_create_page.FunctionNameSelector.search_and_select(
                func_vers_input_element, func_ver_id
            )

        gpu_checkbox = deployments_create_page.page.locator(
            "//div[@data-testid='gpu-checkboxes']"
        )
        child_gpu = gpu_checkbox.locator("span")
        gpu_count = child_gpu.count()
        max_instance_input_locator = (
            "//label[text()='Max Instances']/../following-sibling::div/input"
        )
        error_msg = "The maximum instances entered is over the available number by"
        #  traverse all gpus
        for i in range(gpu_count):
            child_gpu.nth(i).click()
            deployments_create_page.page.wait_for_selector(
                "//button[text()=' Refresh']", state="visible"
            )
            avaliable_locator = "//span[text()='Available']/../following-sibling::span"
            avaliable_count = deployments_create_page.page.locator(
                avaliable_locator
            ).text_content()
            logging.info(f"Avaliable count: {avaliable_count}")
            if avaliable_count != "—":
                avaliable_count = int(avaliable_count)
                deployments_create_page.page.locator(max_instance_input_locator).fill(
                    str(avaliable_count + 1)
                )
                deployments_create_page.page.press(max_instance_input_locator, "Enter")
                assert deployments_create_page.page.get_by_text(
                    error_msg
                ).is_visible(), "The error message is not visible"
            child_gpu.nth(i).click()

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3952654
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_export_and_download_gpu_allocation_csv(
        self,
        get_user_page: Page,
    ):
        gpu_capacity_page = GpuCapacityPage(get_user_page)
        gpu_capacity_page.navigate_to_gpu_capacity_page()
        ui_data = gpu_capacity_page.get_gpu_allocation_in_table_view()
        download_data = gpu_capacity_page.get_gpu_allocation_in_download_csv()
        logging.info(f"UI data: {ui_data}")
        logging.info(f"Download data: {download_data}")

        # Convert download_data into a quick lookup dictionary
        download_dict = {item["Name"]: item for item in download_data}

        # Ensure comparison fields
        fields = ["Min", "Max", "Active", "Available", "TotalAllocated"]

        # Start comparison
        all_matched = True
        for ui_item in ui_data:
            name = ui_item["Name"]
            download_item = download_dict.get(name)

            if not download_item:
                logging.error(f"Entry '{name}' is missing in downloaded data.")
                all_matched = False
                continue

            for field in fields:
                ui_val = str(ui_item.get(field)).strip()
                dl_val = str(download_item.get(field)).strip()
                if ui_val != dl_val:
                    logging.warning(
                        f"Mismatch found in '{name}' ? Field '{field}': UI = '{ui_val}', Download = '{dl_val}'"
                    )
                    all_matched = False

        assert all_matched, "The data is not correct"
