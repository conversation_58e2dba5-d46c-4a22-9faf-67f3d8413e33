import logging
import copy
import time
import json
from datetime import datetime, timezone, timedelta

import pytest
from playwright.sync_api import Page
from pages.Settings.TelemetryPage import TelemetryPage
from pages.Settings.SettingsPage import SettingsPage
from pages.Functions.FunctionsListPage import FunctionListPage
from pages.Functions.FunctionVerDetailPage import FunctionVerDetailPage
from config.consts import TEST_DATA_CF, CURRENT_ENV, LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID
from utils.common.tools import add_timestamp
from pages.CreateFunction.CreateFunctionPage import CreateFunctionPage
from pages.Deployments.DeploymentsCreatePage import DeploymentsCreatePage
from cloudia.utils.tools import Tools
from cloudia.api.http.api_session import APISession
from cloudia.api.http.api_validate import APIValidation
from cloudia.utils.backend_service.nvcf.nvcf_const import NVCF_DATA
from pages.CloneFunction.CloneFunctionPage import CloneFunctionPage
from pages.Tasks.TasksCreatePage import TasksCreatePage
from pages.Tasks.TasksDetailsPage import TasksDetailsPage
from utils.test_utils import get_session_nvct_ssa_allscope
from cloudia.utils.backend_service.nvcf.nvct_utils import NVCTUtils
from config.consts import NCA_ID
from cloudia.utils.backend_service.nvcf.byoo_validator.servicenow_traces_validator_utils import (
    ServiceNowTracesValidator,
)

from cloudia.utils.backend_service.nvcf.byoo_validator.datadog_metrics_validator_utils import (
    DatadogMetricsValidator,
)

from cloudia.utils.backend_service.nvcf.byoo_validator.datadog_log_validator_utils import (
    DatadogLogsValidator,
)

from cloudia.utils.backend_service.nvcf.byoo_validator.models_utils import (
    WrapperType,
    WorkloadType,
    CloudProvider,
)
from cloudia.utils.backend_service.nvcf.byoo_validator.grafana_metrics_validator_utils import (
    GrafanaMetricsValidator,
)
from cloudia.utils.backend_service.nvcf.byoo_validator.grafana_logs_validator_utils import (
    GrafanaLogsValidator,
)

from cloudia.utils.backend_service.nvcf.byoo_validator.grafana_traces_validator_utils import (
    GrafanaTracesValidator,
)

from cloudia.utils.backend_service.nvcf.byoo_validator.azure_monitor_traces_validator_utils import (
    AzureMonitorTracesValidator,
)

from cloudia.utils.backend_service.nvcf.byoo_validator.azure_monitor_logs_validator_utils import (
    AzureMonitorLogsValidator,
)

from cloudia.utils.backend_service.nvcf.byoo_validator.azure_monitor_metrics_validator_utils import (
    AzureMonitorMetricsValidator,
)
from cloudia.utils.backend_service.nvcf.byoo_validator.kratos_thanos_validator_utils import (
    KratosThanosMetricsValidator,
)


class TestTelemetry:
    NVCF_ADMIN_USER = ["org_admin-nvcf_admin"]
    NVCF_USER_USESR = ["org_user-nvcf_user"]
    NVCF_VIEWER_USER = ["nvcf_viewer"]
    NVCF_PROD_ADMIN = ["nvcf_admin"]

    # Common test data for filter tests
    FILTER_TEST_PROVIDERS = ["Datadog", "Grafana Cloud"]
    FILTER_TEST_TELEMETRY_TYPES = ["Logs", "Metrics"]

    func_vers_remove_list = list()
    telemetry_remove_list = list()

    def invoke_function_and_get_result(self, api_data, func_id, session_nvcf_admin_sak):
        """
        Invoke the function and handle the normal case where we get a 200 OK response right away.
        """
        api_data_copy = copy.deepcopy(api_data)
        api_data_copy["req"]["path"] += func_id
        session: APISession = session_nvcf_admin_sak["nvcf"]
        resp = session.request(**api_data_copy["req"])
        assert resp.status_code == 200, f"Expected status 200, but got {resp.status_code}"
        APIValidation(resp, api_data["exp"]).validate_response()
        return resp

    def validate_grafana_metrics_telemetry_data(
        self,
        func_id: str,
        running_config: dict,
        workload_type: str = "container",
        cloudprovider: str = "gfn",
        hours_back: int = 1,
        golden: bool = True,
    ) -> bool:
        start_time = (datetime.now(timezone.utc) - timedelta(hours=hours_back)).strftime(
            "%Y-%m-%dT%H:%M:%SZ"
        )
        end_time = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")

        prometheus_url = running_config["grafana_cloud_prometheus"]["url"]
        username = running_config["grafana_metrics"]["email"]
        password = running_config["grafana_metrics"]["password"]

        MetricsValidator = GrafanaMetricsValidator(prometheus_url, username, password)
        wrapper_type = WrapperType("function")
        workload_type = WorkloadType(workload_type)
        cloudprovider = CloudProvider(cloudprovider)

        logging.info(f"Starting metrics validation for func_id: {func_id}")
        logging.info(f"Time range: {start_time} to {end_time}")
        logging.info(f"Workload type: {workload_type}, Cloud provider: {cloudprovider}")

        try:
            validator_result = MetricsValidator.validate(
                wrapper_type,
                workload_type,
                cloudprovider,
                func_id,
                start_time,
                end_time,
                golden=golden,
            )

            logging.info(f"Metrics validation result: {validator_result}")
            return validator_result

        except Exception as e:
            logging.error(f"Error during metrics validation: {e}")
            raise ValueError(f"Metrics validation failed: {e}")

    @pytest.fixture()
    def function_version_teardown(self, session_nvcf_admin_sak):
        yield
        session: APISession = session_nvcf_admin_sak["ngc"]
        while self.func_vers_remove_list:
            func_id, func_version_id = self.func_vers_remove_list.pop()
            logging.info(
                f"Deleting function with function {func_id} and ID {func_version_id}"
            )
            test_data_del = Tools.load_case_data(NVCF_DATA, "DELETE_FUNCTION")
            test_data_del["req"]["path"] += f"/{func_id}/versions/{func_version_id}"
            resp_del = session.request(**test_data_del["req"])
            APIValidation(resp_del, test_data_del["exp"]).validate_response()
            logging.info(
                f"Successfully deleted function with function {func_id} and ID {func_version_id}"
            )

    nvct_remove_list = list()

    @pytest.fixture()
    def nvct_teardown(
        self,
        running_config,
    ):
        yield
        session = get_session_nvct_ssa_allscope(running_config)
        NVCT_Util = NVCTUtils(session)
        while self.nvct_remove_list:
            nvct_dict = self.nvct_remove_list.pop()
            logging.info(f"Deleting task {nvct_dict}")
            NVCT_Util.delete_task(nvct_dict["task_id"])

    @pytest.fixture()
    def telemetry_endpoint_teardown(
        self,
        get_user_page: Page,
    ):
        yield
        while self.telemetry_remove_list:
            telemetry_dict = self.telemetry_remove_list.pop()
            if not telemetry_dict:
                logging.info("No telemetry endpoints to delete")
                continue
            logging.info(f"Deleting telemetry endpoint for {telemetry_dict}")
            key, value = telemetry_dict.popitem()
            if not value or not isinstance(value, str):
                logging.info(f"Invalid telemetry endpoint {key} with ID {value}")
                continue
            try:
                logging.info(f"Delete the telemetry {value}  ...")
                telemetry_page = TelemetryPage(get_user_page)
                telemetry_page.navigate_to_settings()
                telemetry_page.verify_on_settings_page()
                telemetry_page.verify_telemetry_section_visible()
                telemetry_page.delete_endpoint(value)
                logging.info(f"Successfully deleted telemetry endpoint {value}")
            except Exception as e:
                logging.error(f"Error deleting telemetry endpoint {value}: {e}")

    @pytest.fixture
    def prometheus_remote_write_telemetry_endpoint_setup(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        running_config,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"][
            "prometheus_remote_write"
        ]
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate and create
        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()
        telemetry_page.verify_telemetry_section_visible()
        telemetry_page.click_add_endpoint()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=running_config["kratos_thanos_clientkey"]["api_key"],
            client_certificate=running_config["kratos_thanos_clientcert"]["api_key"],
            protocol=telemetry_data["protocol"],
            telemetry_type=telemetry_data["telemetry_types"],
        )
        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"New created prometheus remote write endpoint {endpoint_name} not found in table"

        yield endpoint_name

    @pytest.fixture
    def grafana_cloud_telemetry_endpoint_setup(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        running_config: dict,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["grafana"]
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate and create
        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()
        telemetry_page.verify_telemetry_section_visible()
        telemetry_page.click_add_endpoint()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            instance_id=telemetry_data["instance_id"],
            endpoint_url=telemetry_data["endpoint_url"],
            telemetry_type={"logs": True, "metrics": True, "traces": True},
            key=running_config["grafana_apikey"]["api_key"],
            protocol="HTTP",
        )
        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"New created grafana cloud endpoint {endpoint_name} not found in table"

        yield endpoint_name

    @pytest.fixture
    def datadog_telemetry_endpoint_setup(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        running_config: dict,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["datadog"]
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate and create
        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()
        telemetry_page.verify_telemetry_section_visible()
        telemetry_page.click_add_endpoint()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            telemetry_type={"logs": True, "metrics": True, "traces": True},
            key=running_config["datadog_apikey"]["api_key"],
            protocol="GRPC",
        )
        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"New created datadog endpoint {endpoint_name} not found in table"

        yield endpoint_name

    @pytest.fixture
    def service_now_telemetry_endpoint_setup(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        running_config: dict,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["service_now"]
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate and create
        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()
        telemetry_page.verify_telemetry_section_visible()
        telemetry_page.click_add_endpoint()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            telemetry_type={"traces": True},
            key=running_config["servicenow_key"]["api_key"],
            protocol="GRPC",
        )
        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"New created service now endpoint {endpoint_name} not found in table"

        yield endpoint_name

    @pytest.fixture
    def azure_monitor_telemetry_endpoint_setup(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        running_config: dict,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["azure_monitor"]
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate and create
        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()
        telemetry_page.verify_telemetry_section_visible()
        telemetry_page.click_add_endpoint()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            live_endpoint=telemetry_data["live_endpoint"],
            telemetry_type={"traces": True, "logs": True, "metrics": True},
            instrumentation_key=running_config["azure_monitor_instrumentation_key"][
                "api_key"
            ],
            application_id=running_config["azure_monitor_application_id"]["api_key"],
            protocol="GRPC",
        )
        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"New created service now endpoint {endpoint_name} not found in table"

        yield endpoint_name

    @pytest.fixture
    def kratos_thanos_telemetry_endpoint_setup(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        running_config,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["kratos_thanos"]
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate and create
        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()
        telemetry_page.verify_telemetry_section_visible()
        telemetry_page.click_add_endpoint()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=running_config["kratos_thanos_clientkey"]["api_key"],
            client_certificate=running_config["kratos_thanos_clientcert"]["api_key"],
            protocol=telemetry_data["protocol"],
            telemetry_type=telemetry_data["telemetry_types"],
        )
        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"New created kratos thanos endpoint {endpoint_name} not found in table"

        yield endpoint_name

    @pytest.fixture
    def byoo_container_function_with_prometheus_remote_write_endpoint(
        self,
        request,
        get_user_page: Page,
        test_data_settings: dict,
        prometheus_remote_write_telemetry_endpoint_setup,
        telemetry_endpoint_teardown,
        function_version_teardown,
    ):
        function_type = getattr(request, "param", "container")
        telemetry_name = prometheus_remote_write_telemetry_endpoint_setup
        telemetry_name_with_provider = telemetry_name + " (Prometheus)"
        if function_type == "container":
            # Get function config data
            if CURRENT_ENV == "staging":
                func_info = test_data_settings[
                    "byoo_container_function_telemetry_endpoint_test_stg"
                ]
            else:
                func_info = test_data_settings[
                    "byoo_container_function_telemetry_endpoint_test"
                ]
        elif function_type == "helm":
            if CURRENT_ENV == "staging":
                func_info = test_data_settings[
                    "byoo_helm_chart_function_telemetry_endpoint_test_stg"
                ]
            else:
                func_info = test_data_settings[
                    "byoo_helm_chart_function_telemetry_endpoint_test"
                ]
        else:
            raise ValueError(f"Invalid function type: {function_type}")

        logging.info(
            f"Start creation of byoo {function_type} function with telemetry endpoint"
        )

        # Generate function name
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")

        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        if function_type == "container":
            create_func_page.Form.create_container_function(
                source_func_name,
                func_info["container"],
                func_info["tag"],
                func_info["inference_protocol"],
                func_info["inference_endpoint"],
                func_info["health_endpoint"],
                inference_port=func_info["inference_port"],
                tags=func_info["tags"],
                telemetry_metrics=telemetry_name_with_provider,
            )
        elif function_type == "helm":
            create_func_page.Form.create_helm_chart_function(
                source_func_name,
                func_info["helm_chart"],
                func_info["helm_chart_version"],
                func_info["helm_chart_service_name"],
                func_info["inference_protocol"],
                inference_endpoint=func_info["inference_endpoint"],
                health_endpoint=func_info["health_endpoint"],
                inference_port=func_info["inference_port"],
                health_port=func_info["health_port"],
                telemetry_metrics=telemetry_name_with_provider,
            )

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        assert func_id and func_ver_id, "Function ID or Version ID is not found"
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")

        function_info = {
            "func_id": func_id,
            "func_ver_id": func_ver_id,
            "telemetry_name": telemetry_name,
        }

        yield function_info
        self.telemetry_remove_list.append({"telemetry_endpoint_name": telemetry_name})
        self.func_vers_remove_list.append([func_id, func_ver_id])

    @pytest.fixture
    def byoo_function_with_kratos_thanos_endpoint(
        self,
        request,
        get_user_page: Page,
        test_data_settings: dict,
        kratos_thanos_telemetry_endpoint_setup,
        telemetry_endpoint_teardown,
        function_version_teardown,
    ):
        function_type = getattr(request, "param", "container")
        telemetry_name = kratos_thanos_telemetry_endpoint_setup
        telemetry_name_with_provider = telemetry_name + " (Kratos Thanos)"
        if function_type == "container":
            # Get function config data
            if CURRENT_ENV == "staging":
                func_info = test_data_settings[
                    "byoo_container_function_telemetry_endpoint_test_stg"
                ]
            else:
                func_info = test_data_settings[
                    "byoo_container_function_telemetry_endpoint_test"
                ]
        elif function_type == "helm":
            if CURRENT_ENV == "staging":
                func_info = test_data_settings[
                    "byoo_helm_chart_function_telemetry_endpoint_test_stg"
                ]
            else:
                func_info = test_data_settings[
                    "byoo_helm_chart_function_telemetry_endpoint_test"
                ]
        else:
            raise ValueError(f"Invalid function type: {function_type}")

        logging.info(
            f"Start creation of byoo {function_type} function with telemetry endpoint"
        )

        # Generate function name
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")

        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        if function_type == "container":
            create_func_page.Form.create_container_function(
                source_func_name,
                func_info["container"],
                func_info["tag"],
                func_info["inference_protocol"],
                func_info["inference_endpoint"],
                func_info["health_endpoint"],
                inference_port=func_info["inference_port"],
                tags=func_info["tags"],
                telemetry_metrics=telemetry_name_with_provider,
            )
        elif function_type == "helm":
            create_func_page.Form.create_helm_chart_function(
                source_func_name,
                func_info["helm_chart"],
                func_info["helm_chart_version"],
                func_info["helm_chart_service_name"],
                func_info["inference_protocol"],
                inference_endpoint=func_info["inference_endpoint"],
                health_endpoint=func_info["health_endpoint"],
                inference_port=func_info["inference_port"],
                health_port=func_info["health_port"],
                telemetry_metrics=telemetry_name_with_provider,
            )

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        assert func_id and func_ver_id, "Function ID or Version ID is not found"
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")

        function_info = {
            "func_id": func_id,
            "func_ver_id": func_ver_id,
            "telemetry_name": telemetry_name,
        }

        yield function_info
        self.telemetry_remove_list.append({"telemetry_endpoint_name": telemetry_name})
        self.func_vers_remove_list.append([func_id, func_ver_id])

    @pytest.fixture
    def byoo_container_function_with_grafana_cloud_endpoint(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        grafana_cloud_telemetry_endpoint_setup,
        telemetry_endpoint_teardown,
        function_version_teardown,
    ):
        telemetry_name = grafana_cloud_telemetry_endpoint_setup
        telemetry_name_with_provider = telemetry_name + " (Grafana Cloud)"
        # Get function config data
        if CURRENT_ENV == "staging":
            func_info = test_data_settings[
                "byoo_container_function_telemetry_endpoint_test_stg"
            ]
        else:
            func_info = test_data_settings[
                "byoo_container_function_telemetry_endpoint_test"
            ]

        logging.info("Start creation of byoo container function with telemetry endpoint")

        # Generate function name
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")

        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_metrics=telemetry_name_with_provider,
            telemetry_traces=telemetry_name_with_provider,
            telemetry_logs=telemetry_name_with_provider,
        )

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Basic details data: {basic_details_data}")
        assert func_id and func_ver_id, "Function ID or Version ID is not found"
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")

        function_info = {
            "func_id": func_id,
            "func_ver_id": func_ver_id,
            "telemetry_name": telemetry_name,
            "description": basic_details_data["Description"],
        }

        yield function_info
        self.telemetry_remove_list.append({"telemetry_endpoint_name": telemetry_name})
        self.func_vers_remove_list.append([func_id, func_ver_id])

    @pytest.fixture
    def byoo_helm_chart_function_with_grafana_cloud_endpoint(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        grafana_cloud_telemetry_endpoint_setup,
        telemetry_endpoint_teardown,
        function_version_teardown,
    ):
        telemetry_name = grafana_cloud_telemetry_endpoint_setup
        telemetry_name_with_provider = telemetry_name + " (Grafana Cloud)"
        # Get function config data
        if CURRENT_ENV == "staging":
            func_info = test_data_settings[
                "byoo_helm_chart_function_telemetry_endpoint_test_stg"
            ]
        else:
            func_info = test_data_settings[
                "byoo_helm_chart_function_telemetry_endpoint_test"
            ]

        logging.info("Start creation of byoo helm chart function with telemetry endpoint")

        # Generate function name
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")

        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_helm_chart_function(
            source_func_name,
            func_info["helm_chart"],
            func_info["helm_chart_version"],
            func_info["helm_chart_service_name"],
            func_info["inference_protocol"],
            inference_endpoint=func_info["inference_endpoint"],
            health_endpoint=func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            health_port=func_info["health_port"],
            telemetry_metrics=telemetry_name_with_provider,
            telemetry_traces=telemetry_name_with_provider,
            telemetry_logs=telemetry_name_with_provider,
        )

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Basic details data: {basic_details_data}")
        assert func_id and func_ver_id, "Function ID or Version ID is not found"
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")

        function_info = {
            "func_id": func_id,
            "func_ver_id": func_ver_id,
            "telemetry_name": telemetry_name,
            "description": basic_details_data["Description"],
        }

        yield function_info
        self.telemetry_remove_list.append({"telemetry_endpoint_name": telemetry_name})
        self.func_vers_remove_list.append([func_id, func_ver_id])

    @pytest.fixture
    def byoo_helm_chart_function_with_datadog_telemetry_endpoint(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        datadog_telemetry_endpoint_setup,
        telemetry_endpoint_teardown,
        function_version_teardown,
    ):
        telemetry_name = datadog_telemetry_endpoint_setup
        telemetry_name_with_provider = telemetry_name + " (Datadog)"
        # Get function config data
        if CURRENT_ENV == "staging":
            func_info = test_data_settings[
                "byoo_helm_chart_function_telemetry_endpoint_test_stg"
            ]
        else:
            func_info = test_data_settings[
                "byoo_helm_chart_function_telemetry_endpoint_test"
            ]

        logging.info("Start creation of byoo helm chart function with telemetry endpoint")

        # Generate function name
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")

        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_helm_chart_function(
            source_func_name,
            func_info["helm_chart"],
            func_info["helm_chart_version"],
            func_info["helm_chart_service_name"],
            func_info["inference_protocol"],
            inference_endpoint=func_info["inference_endpoint"],
            health_endpoint=func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            health_port=func_info["health_port"],
            telemetry_metrics=telemetry_name_with_provider,
            telemetry_traces=telemetry_name_with_provider,
            telemetry_logs=telemetry_name_with_provider,
        )

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        assert func_id and func_ver_id, "Function ID or Version ID is not found"
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")

        function_info = {
            "func_id": func_id,
            "func_ver_id": func_ver_id,
            "telemetry_name": telemetry_name,
        }

        yield function_info
        self.telemetry_remove_list.append({"telemetry_endpoint_name": telemetry_name})
        self.func_vers_remove_list.append([func_id, func_ver_id])

    @pytest.fixture
    def byoo_container_function_with_datadog_telemetry_endpoint(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        datadog_telemetry_endpoint_setup,
        telemetry_endpoint_teardown,
        function_version_teardown,
    ):
        telemetry_name = datadog_telemetry_endpoint_setup
        telemetry_name_with_provider = telemetry_name + " (Datadog)"
        # Get function config data
        if CURRENT_ENV == "staging":
            func_info = test_data_settings[
                "byoo_container_function_telemetry_endpoint_test_stg"
            ]
        else:
            func_info = test_data_settings[
                "byoo_container_function_telemetry_endpoint_test"
            ]

        # Generate function name
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")

        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()

        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            inference_endpoint=func_info["inference_endpoint"],
            health_endpoint=func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            health_port=func_info["health_port"],
            telemetry_metrics=telemetry_name_with_provider,
            telemetry_traces=telemetry_name_with_provider,
            telemetry_logs=telemetry_name_with_provider,
        )

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        assert func_id and func_ver_id, "Function ID or Version ID is not found"
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")

        function_info = {
            "func_id": func_id,
            "func_ver_id": func_ver_id,
            "telemetry_name": telemetry_name,
        }

        yield function_info
        self.telemetry_remove_list.append({"telemetry_endpoint_name": telemetry_name})
        self.func_vers_remove_list.append([func_id, func_ver_id])

    @pytest.fixture
    def byoo_helm_chart_function_with_service_now_telemetry_endpoint(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        service_now_telemetry_endpoint_setup,
        telemetry_endpoint_teardown,
        function_version_teardown,
    ):
        telemetry_name = service_now_telemetry_endpoint_setup
        telemetry_name_with_provider = telemetry_name + " (Servicenow)"
        # Get function config data
        if CURRENT_ENV == "staging":
            func_info = test_data_settings[
                "byoo_helm_chart_function_telemetry_endpoint_test_stg"
            ]
        else:
            func_info = test_data_settings[
                "byoo_helm_chart_function_telemetry_endpoint_test"
            ]

        logging.info("Start creation of byoo helm chart function with telemetry endpoint")

        # Generate function name
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")

        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_helm_chart_function(
            source_func_name,
            func_info["helm_chart"],
            func_info["helm_chart_version"],
            func_info["helm_chart_service_name"],
            func_info["inference_protocol"],
            inference_endpoint=func_info["inference_endpoint"],
            health_endpoint=func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            health_port=func_info["health_port"],
            telemetry_traces=telemetry_name_with_provider,
        )

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        assert func_id and func_ver_id, "Function ID or Version ID is not found"
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")

        function_info = {
            "func_id": func_id,
            "func_ver_id": func_ver_id,
            "telemetry_name": telemetry_name,
        }

        yield function_info
        self.telemetry_remove_list.append({"telemetry_endpoint_name": telemetry_name})
        self.func_vers_remove_list.append([func_id, func_ver_id])

    @pytest.fixture
    def byoo_container_function_with_service_now_telemetry_endpoint(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        service_now_telemetry_endpoint_setup,
        telemetry_endpoint_teardown,
        function_version_teardown,
    ):
        telemetry_name = service_now_telemetry_endpoint_setup
        telemetry_name_with_provider = telemetry_name + " (Servicenow)"
        # Get function config data
        if CURRENT_ENV == "staging":
            func_info = test_data_settings[
                "byoo_container_function_telemetry_endpoint_test_stg"
            ]
        else:
            func_info = test_data_settings[
                "byoo_container_function_telemetry_endpoint_test"
            ]

        logging.info("Start creation of byoo container function with telemetry endpoint")

        # Generate function name
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")

        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_traces=telemetry_name_with_provider,
        )

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        assert func_id and func_ver_id, "Function ID or Version ID is not found"
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")

        function_info = {
            "func_id": func_id,
            "func_ver_id": func_ver_id,
            "telemetry_name": telemetry_name,
        }

        yield function_info
        self.telemetry_remove_list.append({"telemetry_endpoint_name": telemetry_name})
        self.func_vers_remove_list.append([func_id, func_ver_id])

    @pytest.fixture
    def byoo_helm_chart_function_with_azure_monitor_telemetry_endpoint(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        azure_monitor_telemetry_endpoint_setup,
        telemetry_endpoint_teardown,
        function_version_teardown,
    ):
        telemetry_name = azure_monitor_telemetry_endpoint_setup
        telemetry_name_with_provider = telemetry_name + " (Azure Monitor)"
        # Get function config data
        if CURRENT_ENV == "staging":
            func_info = test_data_settings[
                "byoo_helm_chart_function_telemetry_endpoint_test_stg"
            ]
        else:
            func_info = test_data_settings[
                "byoo_helm_chart_function_telemetry_endpoint_test"
            ]

        logging.info("Start creation of byoo helm chart function with telemetry endpoint")

        # Generate function name
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")

        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_helm_chart_function(
            source_func_name,
            func_info["helm_chart"],
            func_info["helm_chart_version"],
            func_info["helm_chart_service_name"],
            func_info["inference_protocol"],
            inference_endpoint=func_info["inference_endpoint"],
            health_endpoint=func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            health_port=func_info["health_port"],
            telemetry_traces=telemetry_name_with_provider,
            telemetry_logs=telemetry_name_with_provider,
            telemetry_metrics=telemetry_name_with_provider,
        )

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        assert func_id and func_ver_id, "Function ID or Version ID is not found"
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")

        function_info = {
            "func_id": func_id,
            "func_ver_id": func_ver_id,
            "telemetry_name": telemetry_name,
        }

        yield function_info
        self.telemetry_remove_list.append({"telemetry_endpoint_name": telemetry_name})
        self.func_vers_remove_list.append([func_id, func_ver_id])

    def perform_telemetry_filter_test(self, settings_page):
        """
        Common method to test telemetry endpoint filtering functionality.
        This method tests both Provider and Telemetry Type filters.

        Args:
            settings_page: SettingsPage object with the page already navigated to settings

        Returns:
            bool: True if at least one filter test succeeded
        """
        # Navigate to telemetry section and get initial count
        settings_page.scroll_to_telemetry_endpoints()

        initial_count = settings_page.get_telemetry_endpoints_count()

        # Skip test if no endpoints are available
        if initial_count == 0:
            pytest.skip("No telemetry endpoints found in the table, skipping test")

        # Test for providers
        provider_tested = False
        for provider in self.FILTER_TEST_PROVIDERS:
            provider_result = settings_page.filter_endpoint(
                filter_type="Provider", filter_value=provider, initial_count=initial_count
            )

            if provider_result["success"]:
                provider_tested = True
                break
            else:
                logging.error(
                    f"Provider {provider} not found or filter test failed: {provider_result['error']}"
                )

        # Ensure at least one provider filter was tested
        if not provider_tested:
            logging.warning(
                "Could not test any Provider filters - no matching providers found"
            )

        # Test telemetry types
        telemetry_type_tested = False
        for telemetry_type in self.FILTER_TEST_TELEMETRY_TYPES:
            logging.info(f"Testing Telemetry Type filter with {telemetry_type}")
            telemetry_type_result = settings_page.filter_endpoint(
                filter_type="Telemetry Type",
                filter_value=telemetry_type,
                initial_count=initial_count,
            )

            if telemetry_type_result["success"]:
                logging.info(
                    f"Successfully tested Telemetry Type filter with {telemetry_type}"
                )
                telemetry_type_tested = True
                break
            else:
                logging.error(
                    f"Telemetry Type {telemetry_type} not found or filter test failed: {telemetry_type_result['error']}"
                )

        # Ensure at least one telemetry type filter was tested
        if not telemetry_type_tested:
            logging.warning(
                "Could not test any Telemetry Type filters - no matching types found"
            )

        # Verify at least one filter type was tested
        assert (
            provider_tested or telemetry_type_tested
        ), "Could not test any filters - no valid values were found"

        return True

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T4093793
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_datadog_telemetry_endpoint(
        self, get_user_page: Page, test_data_settings: dict, running_config: dict
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["datadog"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            protocol=telemetry_data["protocol"],
            telemetry_type={"logs": True, "metrics": True},
            key=telemetry_data["key"],
        )

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T4093794
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_grafana_telemetry_endpoint(
        self, get_user_page: Page, test_data_settings: dict, running_config: dict
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["grafana"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            instance_id=telemetry_data["instance_id"],
            endpoint_url=telemetry_data["endpoint_url"],
            telemetry_type={"logs": True, "metrics": True},
            key=running_config["grafana_apikey"]["api_key"],
        )

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093790
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_VIEWER_USER, indirect=True)
    @pytest.mark.skip(reason="Production bug not fixed")
    def test_sort_telemetry_endpoints_viewer_user(
        self, get_user_page: Page, test_data_settings: dict
    ):
        """
        Test ID: 4093790
        Title: Validate sort function in telemetry endpoint list page Org Admin User

        Test steps:
        1. Login NGC with Org Owner + NVCF Admin
        2. Enter Settings Page to go to the telemetry endpoint list page
        3. Click the "Name" tab and verify endpoints are sorted by name
        4. Click the "Provider" tab and verify endpoints are sorted by provider
        5. Click the "Modified On" tab and verify endpoints are sorted by date

        Expected Results:
        - Endpoints are properly sorted by name, provider, and modified date
        """
        # Create SettingsPage instance instead of TelemetryPage
        settings_page = SettingsPage(get_user_page)

        # STEP 1 & 2: Navigate to settings page and scroll to telemetry endpoints section
        settings_page.navigate()
        settings_page.scroll_to_telemetry_endpoints()
        logging.info(
            "Navigated to settings page and scrolled to telemetry endpoints section"
        )

        # Check if endpoints exist
        endpoints_count = settings_page.get_telemetry_endpoints_count()
        if (
            test_data_settings["defaults"].get("skip_if_no_endpoints", True)
            and endpoints_count == 0
        ):
            pytest.skip("No telemetry endpoints found in the table, skipping test")

        logging.info(f"Found {endpoints_count} telemetry endpoints")

        # STEP 3: Test sorting by Name column
        settings_page.test_column_sorting(
            "Name", settings_page.verify_telemetry_endpoints_sorted_by_name
        )

        # STEP 4: Test sorting by Provider column
        settings_page.test_column_sorting(
            "Provider", settings_page.verify_telemetry_endpoints_sorted_by_provider
        )

        # STEP 5: Test sorting by Created On column (Modified On in the template)
        settings_page.test_column_sorting(
            "Created On", settings_page.verify_telemetry_endpoints_sorted_by_date
        )

        logging.info("Telemetry endpoint sorting test completed successfully")

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T4093862
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_telemetry_endpoint_for_deploying_function(
        self,
        get_user_page: Page,
        session_nvcf_admin_sak,
        request,
        test_data_settings: dict,
        byoo_container_function_with_grafana_cloud_endpoint: dict,
    ):
        """
        Test that verifies deleting a telemetry endpoint associated with a deploying function is not allowed.
        Steps:
        1. Login to NGC with Org Owner + NVCF Admin credentials
        2. Navigate to the Cloud Functions Page
        3. Go to the Functions page from the left side navigation bar
        4. Check the function is inactive, deploy it and verify it enters "deploying" state
        5. Attempt to delete the associated telemetry endpoint
        6. Verify that the delete operation is not allowed and a proper error message is displayed
        7. Cancel the deployment
        """

        func_data = byoo_container_function_with_grafana_cloud_endpoint
        function_id = func_data["func_id"]
        version_id = func_data["func_ver_id"]
        # Extract the endpoint name from the expected logs endpoint
        # Example: From "telemetry-ui-automation-deploying (Grafana Cloud)" extract "telemetry-ui-automation-deploying"
        telemetry_endpoint_name = func_data["telemetry_name"]

        # Setup a finalizer to ensure we cancel the deployment regardless of test outcome
        func_list_page = FunctionListPage(get_user_page)

        def cleanup_function():
            logging.info("Running cleanup to cancel deployment if needed")
            try:
                # Navigate back to functions page
                func_list_page.navigate_to_page()

                # Check status and cancel if deploying/active
                status = func_list_page.FunctionPagination.check_function_status(
                    function_id
                )
                if status.lower() in ["deploying", "active"]:
                    logging.info(f"Cleaning up function with status: {status}")
                    func_list_page.FunctionPagination.cancel_deployment(
                        function_id=function_id, verify_status_change=True
                    )
                    logging.info("Cleanup completed successfully")
                else:
                    logging.info(f"No cleanup needed, function status is: {status}")
            except Exception as e:
                logging.error(f"Error during cleanup: {str(e)}")

        # Register the finalizer to run after the test
        request.addfinalizer(cleanup_function)

        # Step 1-3: Login and Navigate (handled by the fixture)
        func_list_page.navigate_to_page()

        # Step 4a: Check if function is inactive
        initial_status = func_list_page.FunctionPagination.check_function_status(
            function_id
        )
        if initial_status.lower() != "inactive":
            logging.info(
                f"Function is not in inactive state, current state: {initial_status}"
            )
            # If function is already deployed, undeploy it first
            if initial_status.lower() in ["active", "deploying", "failed"]:
                func_list_page.FunctionPagination.cancel_deployment(
                    function_id=function_id, verify_status_change=True
                )

        # Step 4b: Deploy the function
        from pages.Deployments.DeploymentsCreatePage import DeploymentsCreatePage

        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            page=get_user_page,
            func_id=function_id,
            func_vers_id=version_id,
            wait_until_deployed=False,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

        # Step 4c: Verify function is in "deploying" state
        func_list_page.FunctionPagination.wait_for_function_status(
            function_id=function_id,
            expected_status="DEPLOYING",
            timeout_seconds=60,
            check_interval=5,
        )

        # Step 5 & 6: Navigate to Settings page and attempt to delete the endpoint
        from pages.Settings.SettingsPage import SettingsPage

        settings_page = SettingsPage(get_user_page)
        settings_page.navigate()

        # Verify the telemetry endpoint exists before attempting to delete
        settings_page.scroll_to_telemetry_endpoints()
        settings_page.wait_for_telemetry_endpoints_loading()
        settings_page.search_for_endpoint(telemetry_endpoint_name)
        settings_page.page.wait_for_selector(
            "//button[text()= ' Refresh']", state="visible"
        )

        assert settings_page.verify_endpoint_exists(
            telemetry_endpoint_name
        ), f"Telemetry endpoint '{telemetry_endpoint_name}' does not exist"

        # Use the common delete flow with confirmation (should_cancel=False)
        delete_result = settings_page.perform_endpoint_delete_flow(
            endpoint_name=telemetry_endpoint_name,
            should_cancel=False,  # Confirm deletion
        )

        # Verify that the delete operation failed as expected (endpoint is in use)
        # The endpoint should still exist (verified=False) and we should have received an error
        assert not delete_result[
            "verified"
        ], "The endpoint was unexpectedly deleted when it should have been protected"
        assert (
            "error" in delete_result
        ), "No error was reported when attempting to delete a protected endpoint"
        # Additional verification: Check that the endpoint still exists
        settings_page.clear_endpoint_search()
        settings_page.search_for_endpoint(telemetry_endpoint_name)
        assert settings_page.verify_endpoint_exists(
            telemetry_endpoint_name
        ), "Endpoint no longer exists after deletion attempt"

        logging.info(
            f"Verified endpoint {telemetry_endpoint_name} still exists as expected"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.********
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_telemetry_endpoint_not_used(
        self, test_data_settings: dict, get_user_page: Page, running_config: dict
    ):
        """
        Test ID: 4093805
        Title: Delete existing NVCF telemetry endpoint with Org Admin

        Test steps:
        1. Login NGC with Org Owner + NVCF Admin
        2. Enter Settings Page from left side navigation bar
        3. Create a test telemetry endpoint to ensure we have one to delete
        4. Delete the created telemetry endpoint
        5. Verify the endpoint was deleted successfully
        """
        # Create page objects
        telemetry_page = TelemetryPage(get_user_page)
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page
        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()

        # Step 3: Create a test telemetry endpoint
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["grafana"]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        telemetry_page.click_add_endpoint()
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            instance_id=telemetry_data["instance_id"],
            endpoint_url=telemetry_data["endpoint_url"],
            telemetry_type={"logs": True, "metrics": True},
            key=running_config["grafana_apikey"]["api_key"],
        )

        # Verify the endpoint was created successfully
        telemetry_page.search_endpoint(endpoint_name)
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} was not created successfully"

        # Wait for endpoint table to fully load before getting initial count
        settings_page.clear_endpoint_search()
        settings_page.wait_for_telemetry_endpoints_loading()

        # Get initial endpoint count for verification later
        # initial_count = settings_page.get_telemetry_endpoints_count()
        result = settings_page.perform_endpoint_delete_flow(
            endpoint_name=endpoint_name,
            should_cancel=False,  # Confirm deletion
        )
        assert result, "Endpoint deletion failed"

        # Step 5: Verify the endpoint was deleted
        settings_page.clear_endpoint_search()

        # Verify endpoint count decreased by 1 (do this before searching for the deleted endpoint)
        # new_count = settings_page.get_telemetry_endpoints_count()
        # assert (
        #     new_count == initial_count - 1
        # ), f"Expected endpoint count to decrease by 1 (from {initial_count} to {initial_count-1}), but got {new_count}"

        # Now search for the endpoint to verify it doesn't exist
        settings_page.search_for_endpoint(endpoint_name)
        assert not settings_page.check_if_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} still exists after deletion"

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093806
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_telemetry_endpoint_but_cancel_operation(
        self, test_data_settings: dict, get_user_page: Page, running_config: dict
    ):
        """
        Test ID: 4093806
        Title: Delete existing NVCF telemetry endpoint with Org Admin but cancel the operation

        Test steps:
        1. Login NGC with Org Owner + NVCF Admin
        2. Enter Settings Page from left side navigation bar
        3. Select one existing nvcf telemetry endpoint for the org
        4. Click the Actions menu(its 3 dots menu) of the matched telemetry endpoint
        5. Select and click the "Delete Endpoint" menu
        6. The delete confirmation window pops out to confirm the delete action
        7. Click "Cancel" button
        8. Check the endpoint is not deleted from the endpoint list page
        """
        # Create page objects
        telemetry_page = TelemetryPage(get_user_page)
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page
        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()

        # Step 3: Find an existing telemetry endpoint to use for the test
        settings_page.wait_for_telemetry_endpoints_loading()
        rows = settings_page.get_telemetry_endpoints_rows()
        assert rows.count() > 0, "No telemetry endpoints found for testing"

        # Get the first endpoint name from the table
        test_endpoint_name = rows.first.locator("td").first.text_content().strip()
        logging.info(f"Using endpoint '{test_endpoint_name}' for cancel deletion test")

        # Steps 4 & 5: Click the actions menu and select Delete Endpoint
        settings_page.click_endpoint_actions_button(test_endpoint_name)
        settings_page.click_delete_endpoint_option()

        # Step 6: Verify delete confirmation dialog appears
        get_user_page.wait_for_timeout(1000)  # Wait for animation

        # Look for dialog or buttons that indicate the confirmation dialog
        dialog_visible = False
        try:
            # Try to find dialog by role and content
            dialog = get_user_page.locator("div[role='dialog']").first
            if dialog.is_visible() and ("delete" in dialog.text_content().lower()):
                dialog_visible = True
        except Exception:
            # Try to find confirmation by buttons if dialog detection fails
            cancel_btn = get_user_page.get_by_role("button", name="Cancel").first
            if cancel_btn.is_visible():
                dialog_visible = True

        assert dialog_visible, "Delete confirmation dialog not visible"
        logging.info("Delete confirmation dialog is displayed")

        # Step 7: Click Cancel button on the confirmation dialog
        settings_page.cancel_endpoint_deletion()

        # Step 8: Verify endpoint still exists after canceling deletion
        settings_page.clear_endpoint_search()
        settings_page.search_for_endpoint(test_endpoint_name)
        assert settings_page.verify_endpoint_exists(
            test_endpoint_name
        ), "Endpoint no longer exists after canceling deletion"

        logging.info(
            f"Endpoint '{test_endpoint_name}' still exists as expected after cancellation"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093861
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_telemetry_endpoint_for_inactive_function(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        session_nvcf_admin_sak,
        byoo_container_function_with_grafana_cloud_endpoint: dict,
    ):
        """
        Test ID: 4093861
        Title: Delete the associated telemetry endpoint for the inactive function

        Test steps:
        1. Login NGC with Org Owner + NVCF Admin
        2. Enter Settings Page from left side navigation bar
        3. Verify the function is inactive by checking its status on the Functions page
        4. Find the telemetry endpoint associated with the inactive function
        5. Attempt to delete the telemetry endpoint
        6. Verify that the deletion is not allowed and an error message is displayed

        Expected Results:
        - The delete operation should not be allowed
        - A proper error message should be displayed, e.g.:
          "Cannot be deleted as it in use by Function id '<function-id>',
           version '<version-id>' - Delete the function first to be able to delete the telemetry."
        """
        # Get the function and endpoint data from test_data_settings.yaml
        if CURRENT_ENV == "staging":
            test_data = test_data_settings[
                "inactive_function_version_with_telemetry_endpoint_stg"
            ]["telemetry_export_test"]
        else:
            test_data = test_data_settings[
                "inactive_function_version_with_telemetry_endpoint"
            ]["telemetry_export_test"]

        func_data = byoo_container_function_with_grafana_cloud_endpoint
        function_id = func_data["func_id"]
        endpoint_name = test_data["expected_logs_endpoint"].split(" (")[0]
        expected_error_message = test_data_settings["telemetry"]["deletion_tests"][
            "expected_error_message"
        ]
        # Step 1-2: Create page objects and navigate
        settings_page = SettingsPage(get_user_page)
        func_list_page = FunctionListPage(get_user_page)

        # Step 3: Verify the function is inactive
        func_list_page.navigate_to_page()

        # Check function status
        function_status = func_list_page.FunctionPagination.check_function_status(
            function_id
        )
        # Ensure the function is inactive before proceeding with the test
        if function_status.lower() != "inactive":
            logging.info(f"Function is not inactive, current state: {function_status}")
            if function_status.lower() in ["active", "deploying", "failed"]:
                logging.info("Attempting to cancel deployment...")
                func_list_page.FunctionPagination.cancel_deployment(
                    function_id=function_id, verify_status_change=True
                )
                # Check status again
                function_status = func_list_page.FunctionPagination.check_function_status(
                    function_id
                )
                if function_status.lower() != "inactive":
                    pytest.skip(
                        f"Could not get function to INACTIVE state. Current state: {function_status}"
                    )
            else:
                pytest.skip(f"Function is in unexpected state: {function_status}")

        # Step 4: Navigate to settings page to find the telemetry endpoint
        settings_page.navigate()
        logging.info("Navigated to settings page")

        # Take screenshot of the settings page for debugging
        debug_screenshot_path = f"screenshots/settings_page_before_search_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        get_user_page.screenshot(path=debug_screenshot_path)
        logging.info(f"Took screenshot of settings page: {debug_screenshot_path}")

        # Make sure the telemetry section is visible
        settings_page.scroll_to_telemetry_endpoints()
        settings_page.wait_for_telemetry_endpoints_loading()
        logging.info("Scrolled to telemetry endpoints section and waited for it to load")

        # Debug: Count all endpoints before searching
        all_endpoints_count = settings_page.get_telemetry_endpoints_count()
        logging.info(f"Found {all_endpoints_count} total endpoints before searching")

        # Get a screenshot of the telemetry table
        debug_screenshot_path = f"screenshots/telemetry_table_before_search_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        get_user_page.screenshot(path=debug_screenshot_path)
        logging.info(f"Took screenshot of telemetry table: {debug_screenshot_path}")

        # Clear any existing search to ensure fresh results
        settings_page.clear_endpoint_search()

        # Search for our specific endpoint
        settings_page.search_for_endpoint(endpoint_name)
        logging.info(f"Searched for endpoint: {endpoint_name}")

        # Check if our endpoint exists
        endpoint_exists = settings_page.verify_endpoint_exists(endpoint_name)

        if not endpoint_exists:
            # Take a screenshot for debugging
            debug_screenshot_path = f"screenshots/endpoint_not_found_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            get_user_page.screenshot(path=debug_screenshot_path)
            logging.info(
                f"Endpoint not found initially, took screenshot: {debug_screenshot_path}"
            )

            # Try refreshing the page and searching again
            logging.info("Refreshing page and trying again...")
            settings_page.navigate()
            settings_page.scroll_to_telemetry_endpoints()
            settings_page.wait_for_telemetry_endpoints_loading()

            # Try using a refresh button if available
            try:
                refresh_btn = get_user_page.get_by_role("button", name="Refresh")
                if refresh_btn.is_visible():
                    refresh_btn.click()
                    logging.info("Clicked refresh button")
                    get_user_page.wait_for_load_state("networkidle", timeout=5000)
            except Exception as e:
                logging.warning(f"Could not find refresh button: {str(e)}")

            # Search again
            settings_page.search_for_endpoint(endpoint_name)
            endpoint_exists = settings_page.verify_endpoint_exists(endpoint_name)

            if not endpoint_exists:
                # This is a test issue, but we can create the expected endpoint for testing
                logging.warning(
                    f"Test endpoint {endpoint_name} not found after retry. This test will be skipped."
                )
                pytest.skip(
                    f"Test endpoint {endpoint_name} not found and needed for the test"
                )

        logging.info(f"Found telemetry endpoint: {endpoint_name}")

        # Step 5 & 6: Attempt to delete the endpoint using the common function
        delete_result = settings_page.perform_endpoint_delete_flow(
            endpoint_name=endpoint_name,
            should_cancel=False,  # Confirm deletion
        )

        logging.info(f"Delete result: {delete_result}")

        # Take screenshot after delete attempt
        debug_screenshot_path = f"screenshots/after_delete_attempt_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        get_user_page.screenshot(path=debug_screenshot_path)
        logging.info(f"Took screenshot after delete attempt: {debug_screenshot_path}")

        # Verify that the delete operation failed as expected (endpoint is in use)
        # The endpoint should still exist (verified=False) and we should have received an error
        assert not delete_result.get(
            "verified", False
        ), "The endpoint was unexpectedly deleted when it should have been protected"
        assert (
            "error" in delete_result
        ), "No error was reported when attempting to delete a protected endpoint"

        # Verify the error message contains the expected text
        if "error" in delete_result:
            if expected_error_message not in delete_result["error"]:
                logging.warning(
                    f"Expected error message '{expected_error_message}' not found in actual error: '{delete_result['error']}'"
                )

                # Check if the endpoint still exists despite error message mismatch
                settings_page.clear_endpoint_search()
                settings_page.search_for_endpoint(endpoint_name)
                endpoint_still_exists = settings_page.verify_endpoint_exists(endpoint_name)

                # If the endpoint still exists, the test intent is fulfilled even if error message differs
                assert (
                    endpoint_still_exists
                ), "Endpoint no longer exists after deletion attempt"
                logging.info(
                    f"Verified endpoint {endpoint_name} still exists as expected, even though error message differed"
                )
            else:
                logging.info(f"Received expected error message: {delete_result['error']}")

        # Additional verification: Check that the endpoint still exists
        settings_page.clear_endpoint_search()
        settings_page.search_for_endpoint(endpoint_name)
        assert settings_page.verify_endpoint_exists(
            endpoint_name
        ), "Endpoint no longer exists after deletion attempt"

        logging.info(f"Verified endpoint {endpoint_name} still exists as expected")

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093799
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_telemetry_endpoint_with_invalid_name(self, get_user_page: Page):
        """
        Test ID: 4093799
        Title: Create NVCF telemetry endpoints with invalid name format - Negative

        Test steps:
        1. Login NGC with Org Owner + NVCF Admin
        2. Enter Settings Page from left side navigation bar
        3. Click "Add Endpoint" button to setup an endpoint
        4. Fill the name fields with the name length exceed 48 characters
        5. Fill the name fields with invalid name format (e.g., -1example, .example, -.-)

        Expected results:
        1. The secret name field must be unique for a given organization
        2. The name length should not exceed 48 characters and follow ^[a-z0-9A-Z][a-z0-9A-Z\\_\\.\\-]*$]
        3. Proper error msg should be prompted for invalid name format
        """
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Define invalid names to test
        invalid_names = ["-example", ".example", "-.-"]

        # Use the common validation method to test endpoint name requirements
        logging.info("Testing telemetry endpoint name validation requirements")
        validation_results = telemetry_page.validate_endpoint_name_requirements(
            invalid_names
        )

        # Verify length validation
        assert validation_results["length_validation"], "Length validation failed"
        logging.info("Successfully verified error message for long name")

        # Verify format validation for each invalid name
        for name, result in validation_results["format_validation"].items():
            assert result, f"Format validation failed for invalid name: {name}"
            logging.info(f"Successfully verified error message for invalid name: {name}")

        logging.info(
            "Successfully verified all name validation rules for telemetry endpoints"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093821
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_associated_endpoint_active_function(
        self, get_user_page: Page, test_data_settings: dict, session_nvcf_admin_sak=None
    ):
        """
        Test ID: 4093821
        Title: Delete the associated telemetry endpoint for the active function

        Test steps:
        1. Login NGC with Org Owner + NVCF Admin
        2. Enter Settings Page from left side navigation bar
        3. Verify the function is active by checking its status on the Functions page
        4. Find the telemetry endpoint associated with the active function
        5. Attempt to delete the telemetry endpoint
        6. Verify that the deletion is not allowed and an error message is displayed

        Expected Results:
        - The delete operation should not be allowed
        - A proper error message should be displayed, e.g.:
          "Cannot be deleted as it in use by Function id '<function-id>',
           version '<version-id>' - Delete the function first to be able to delete the telemetry."
        """
        # Get the function and endpoint data from test_data_settings.yaml
        if CURRENT_ENV == "staging":
            test_data = test_data_settings[
                "active_function_version_with_telemetry_endpoint_stg"
            ]["telemetry_export_test"]
        else:
            test_data = test_data_settings[
                "active_function_version_with_telemetry_endpoint"
            ]["telemetry_export_test"]
        function_id = test_data["function_id"]
        function_name = test_data["function_name"]

        # Extract the endpoint name from the expected logs endpoint
        # Example: From "telemetry-ui-automation (Grafana Cloud)" extract "telemetry-ui-automation"
        endpoint_name = test_data["expected_logs_endpoint"].split(" (")[0]
        expected_error_message = test_data_settings["telemetry"]["deletion_tests"][
            "expected_error_message"
        ]

        logging.info(
            f"Using function: {function_name} (ID: {function_id}) and endpoint: {endpoint_name}"
        )

        # Step 1-2: Create page objects and navigate
        settings_page = SettingsPage(get_user_page)
        func_list_page = FunctionListPage(get_user_page)

        # Step 3: Verify the function is active
        func_list_page.navigate_to_page()

        # Check function status
        function_status = func_list_page.FunctionPagination.check_function_status(
            function_id
        )
        logging.info(f"Function {function_name} current status: {function_status}")

        # Ensure the function is active before proceeding with the test
        if function_status.lower() != "active":
            logging.info(f"Function is not active, current state: {function_status}")
            if function_status.lower() == "inactive":
                logging.info("Function is inactive. Need to deploy it to make it active")
                from pages.Deployments.DeploymentsCreatePage import DeploymentsCreatePage

                # Navigate to deploy function version page via function list page
                deploy_page = func_list_page.FunctionPagination.action_to(
                    uuid=function_id, entry="Deploy Version"
                )

                deployments_create_page = DeploymentsCreatePage(deploy_page)
                deployments_create_page.deploy_function_version(
                    page=get_user_page,
                    func_id=function_id,
                    func_vers_id=test_data["version_id"],
                    wait_until_deployed=True,
                    **TEST_DATA_CF["deployment_min"],
                    session_nvcf_admin_sak=session_nvcf_admin_sak,
                )

                # Check status again
                function_status = func_list_page.FunctionPagination.check_function_status(
                    function_id
                )
                if function_status.lower() != "active":
                    pytest.skip(
                        f"Could not activate function. Current state: {function_status}"
                    )
            else:
                pytest.skip(f"Function is in unexpected state: {function_status}")

        # Step 4: Navigate to settings page to find the telemetry endpoint
        settings_page.navigate()

        # Verify the endpoint exists before attempting to delete
        settings_page.scroll_to_telemetry_endpoints()
        settings_page.wait_for_telemetry_endpoints_loading()
        settings_page.search_for_endpoint(endpoint_name)

        assert settings_page.verify_endpoint_exists(
            endpoint_name
        ), f"Test endpoint {endpoint_name} not found"
        logging.info(f"Found telemetry endpoint: {endpoint_name}")

        # Step 5 & 6: Attempt to delete the endpoint using the common function
        delete_result = settings_page.perform_endpoint_delete_flow(
            endpoint_name=endpoint_name,
            should_cancel=False,  # Confirm deletion
        )

        # Verify that the delete operation failed as expected (endpoint is in use)
        # The endpoint should still exist (verified=False) and we should have received an error
        assert not delete_result.get(
            "verified", False
        ), "The endpoint was unexpectedly deleted when it should have been protected"
        assert (
            "error" in delete_result
        ), "No error was reported when attempting to delete a protected endpoint"

        # Verify the error message contains the expected text
        if "error" in delete_result:
            assert (
                expected_error_message in delete_result["error"]
            ), f"Error message '{delete_result['error']}' does not contain expected text '{expected_error_message}'"
            logging.info(f"Received expected error message: {delete_result['error']}")

        # Additional verification: Check that the endpoint still exists
        settings_page.clear_endpoint_search()
        settings_page.search_for_endpoint(endpoint_name)
        assert settings_page.verify_endpoint_exists(
            endpoint_name
        ), "Endpoint no longer exists after deletion attempt"

        logging.info(f"Verified endpoint {endpoint_name} still exists as expected")

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093788
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", ["nvcf_admin"], indirect=True)
    def test_validate_endpoint_filter_function_nvcf_admin(self, get_user_page: Page):
        """
        Test ID: 4093788
        Title: Validate filter function in telemetry endpoint list page Org Admin User

        Test steps:
        Pre-request: with several existing NVCF telemetry endpoints in this org
        1. Login NGC with Org Owner + NVCF Admin
        2. Enter Settings Page to go to the telemetry endpoint list page
        3. Click "Filter" and select "Provider" as Filtering condition, check the endpoint you want to filter from the checkbox
        4. Validating the filter function correctly filters out the telemetry endpoint as set, and verify the endpoint count
        5. Click the "Clear Filters" button, check the filter condition is cleared
        6. Click "Filter" and select "Telemetry Type" as Filtering condition, check the type from the checkbox
        7. Validating the filter function correctly filters out the telemetry endpoint as set, and verify the endpoint count
        8. Click the "Clear Filters" button, check the filter condition is cleared

        Expected Results:
        - Filter functionality works correctly for Provider selection
        - Filter functionality works correctly for Telemetry Type selection
        - Endpoint count display is accurate after filtering
        - Filters can be successfully cleared
        """
        # Create page objects
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page
        settings_page.navigate()
        logging.info("Navigated to settings page")

        # Use the common filter test method
        self.perform_telemetry_filter_test(settings_page)

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093800
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_duplicate_telemetry_endpoint_name(
        self, get_user_page: Page, telemetry_endpoint_teardown
    ):
        """
        Test ID: 4093800
        Title: Create multi NVCF telemetry endpoints with same endpoint name for the same provider - Negative

        Test steps:
        1. Login NGC with Org Owner + NVCF Admin
        2. Enter Settings Page from left side navigation bar
        3. Click "Add Endpoint" button to setup an endpoint
        4. Fill the required fields, select the endpoint "Provider" from dropdown list, e.g. select Datadog
        5. Select supported "Telemetry Type", e.g., "Logs"
        6. Select the "Communication Protocol" for this endpoint, e.g., "HTTP"
        7. Save the configuration
        8. Check the endpoint is listed correctly from Settings page
        9. Repeat steps to create another endpoint with same name as above
        10. Check the telemetry endpoint should not be created and a proper error message should be displayed

        Expected Results:
        - NVCF endpoint with same name should not be created
        - Error message should indicate that telemetry names must be unique
        """
        # Create page objects
        telemetry_page = TelemetryPage(get_user_page)
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page
        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()
        logging.info("Navigated to settings page")

        # Define test data
        provider = "Datadog"  # Using Datadog as example provider
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = (
            f"byoo_auto_duplicate-test-{timestamp}"  # Create unique name for this test run
        )
        api_key = "api_key_test_12345"

        # Step 3-7: Create first telemetry endpoint
        telemetry_page.click_add_endpoint()
        logging.info(f"Creating first endpoint with name: {endpoint_name}")
        first_created_name = telemetry_page.create_telemetry_endpoint(
            name=endpoint_name,
            endpoint_url="https://api.datadoghq.com",
            provider=provider,
            key=api_key,
        )
        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Step 8: Verify first endpoint was created successfully
        telemetry_page.search_endpoint(first_created_name)
        assert telemetry_page.verify_endpoint_exists(
            first_created_name
        ), f"First endpoint {first_created_name} was not created successfully"
        logging.info(f"Successfully created first endpoint: {first_created_name}")

        # Clear search before attempting to create duplicate
        settings_page.clear_endpoint_search()

        # Step 9: Attempt to create second endpoint with the same name
        telemetry_page.click_add_endpoint()
        logging.info(f"Attempting to create duplicate endpoint with name: {endpoint_name}")

        # Use the enhanced create_telemetry_endpoint method with expect_error=True
        # Use a different API key to ensure only the name is the duplicate factor
        name, error_found, error_message = telemetry_page.create_telemetry_endpoint(
            name=endpoint_name,
            provider=provider,
            endpoint_url="https://api.datadoghq.com",
            key="different_api_key_67890",
            expect_error=True,
        )

        # Step 10: Verify error was detected
        assert error_found, "No error was detected when creating a duplicate endpoint"
        logging.info(f"Error detected when creating duplicate endpoint: {error_message}")

        # Verify error message indicates name already exists/must be unique
        if error_message:
            has_error_content = any(
                keyword in error_message.lower()
                for keyword in ["exist", "already", "unique", "duplicate"]
            )
            assert (
                has_error_content
            ), f"Error message does not indicate endpoint already exists: {error_message}"

        # Cancel the current form to clean up
        try:
            cancel_button = get_user_page.get_by_role("button", name="Cancel")
            cancel_button.click()
        except Exception as e:
            # If Cancel button is not found, try just navigating back
            logging.warning(f"Could not find Cancel button: {str(e)}")

        # Navigate back to settings page if not already there
        if "settings" not in get_user_page.url.lower():
            settings_page.navigate()

        # Search for the endpoint and verify only one instance exists
        settings_page.search_for_endpoint(endpoint_name)
        endpoint_rows = get_user_page.locator("tr").filter(has_text=endpoint_name).all()
        assert (
            len(endpoint_rows) == 1
        ), f"Expected exactly 1 endpoint with name {endpoint_name}, found {len(endpoint_rows)}"
        logging.info("Verified only one endpoint with the duplicate name exists")

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093813
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_view_telemetry_export_information_inactive(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        byoo_container_function_with_grafana_cloud_endpoint: dict,
    ):
        """
        Test ID: 4093813
        Title: View "Telemetry Export" information from inactive function version details page

        Test steps:
        1. Login NGC with NVCF Admin
        2. In the Function List Page, select one inactive function which has telemetry endpoint configuration
        3. Navigate to the version details page of this function and click the "Function Details" tab
        4. Check the "Telemetry Export" information is correct and displayed normally
        5. Check that secrets information used for telemetries should not be displayed in UI

        Expected Results:
        - "Telemetry Export" information is correct for each function
        - No secrets or sensitive information should be displayed
        """
        # Create page objects
        function_list_page = FunctionListPage(get_user_page)

        if CURRENT_ENV == "staging":
            test_data = test_data_settings[
                "inactive_function_version_with_telemetry_endpoint_stg"
            ]["telemetry_export_test"]
        else:
            test_data = test_data_settings[
                "inactive_function_version_with_telemetry_endpoint"
            ]["telemetry_export_test"]

        func_data = byoo_container_function_with_grafana_cloud_endpoint
        function_id = func_data["func_id"]
        version_id = func_data["func_ver_id"]
        version_name = func_data["description"]
        telemetry_endpoint_name = func_data["telemetry_name"] + " (Grafana Cloud)"

        # Step 1 & 2: Navigate to function list page and search for the function
        function_list_page.navigate_to_page()

        # First check if the function exists
        function_list_page.FunctionPagination.wait_function_list_ready()
        function_info = function_list_page.FunctionPagination.get_function_by_func_id(
            function_id
        )
        if not function_info:
            function_list_page.FunctionPagination.search_function_exact_match(function_id)
            # Wait for search results
            get_user_page.wait_for_timeout(3000)

        # Check function status to ensure it's inactive
        function_status = function_list_page.FunctionPagination.check_function_status(
            function_id
        )
        # from pages.Functions.FunctionVerDetailPage import FunctionVerDetailPage

        function_ver_detail_page = FunctionVerDetailPage(
            get_user_page,
            func_id=function_id,
            vers_id=version_id,
        )
        # If the function is active/deployed or deploying, we need to cancel/undeploy it first
        if function_status.lower() != "inactive":
            # Navigate directly to the function version details page first
            function_list_page.navigate_to_function_version_detail(
                function_id=function_id,
                version_name=version_name,
                tab_name=test_data["filter_tab"],
            )

            # Create FunctionVerDetailPage instance for canceling deployment if needed

            if function_status.lower() == "deploying":
                # Try to cancel deployment if in deploying state
                if function_ver_detail_page.has_cancel_deployment_button():
                    logging.info(
                        "Function is in DEPLOYING state, attempting to cancel deployment"
                    )
                    cancel_result = function_ver_detail_page.cancel_deployment()
                    if cancel_result:
                        logging.info("Successfully cancelled the deployment")
                    else:
                        logging.warning("Failed to cancel deployment, but continuing test")
                else:
                    logging.warning(
                        "Cancel Deployment button not found, may already be in different state"
                    )
            else:
                # Try to undeploy if in active state
                try:
                    undeploy_btn = get_user_page.get_by_role("button", name="Undeploy")
                    if undeploy_btn.is_visible():
                        undeploy_btn.click()
                        logging.info("Clicked Undeploy button")

                        # Confirm undeploy in dialog
                        confirm_btn = get_user_page.get_by_role("button", name="Undeploy")
                        if confirm_btn.is_visible():
                            confirm_btn.click()
                            logging.info("Confirmed undeploy operation")
                        else:
                            logging.warning("Couldn't find confirm button for undeploy")
                    else:
                        logging.warning(
                            "Undeploy button not found, may already be inactive"
                        )
                except Exception as e:
                    logging.error(f"Error while undeploying function: {str(e)}")

            # Wait for the function to become inactive
            function_status_updated = (
                function_list_page.FunctionPagination.wait_for_function_status(
                    function_id=function_id,
                    expected_status="Inactive",
                    timeout_seconds=120,
                    check_interval=10,
                )
            )
            logging.info(
                f"Function status after undeploy/cancel: {function_status_updated}"
            )

            # Navigate back to function list to refresh status
            function_list_page.navigate_to_page()

            # Verify function is now inactive
            function_status = function_list_page.FunctionPagination.check_function_status(
                function_id
            )

            if function_status.lower() != "inactive":
                pytest.xfail(
                    f"Could not get function to inactive state, current status: {function_status}"
                )

        # Step 3: Navigate to the function version detail page and click Function Details tab
        function_list_page.navigate_to_function_version_detail(
            function_id=function_id,
            version_name=version_name,
            tab_name=test_data["filter_tab"],
        )

        # Step 4 & 5: Verify Telemetry Export information
        # Verify Logs Endpoint information is displayed correctly if configured
        function_ver_detail_page.verify_endpoint_value(
            endpoint_type="Logs Endpoint",
            expected_value=telemetry_endpoint_name,
        )

        function_ver_detail_page.verify_endpoint_value(
            endpoint_type="Metrics Endpoint",
            expected_value=telemetry_endpoint_name,
        )

        function_ver_detail_page.verify_endpoint_value(
            endpoint_type="Traces Endpoint",
            expected_value=telemetry_endpoint_name,
        )

        # Verify no sensitive information is displayed
        page_content = get_user_page.content()
        sensitive_patterns = ['key="', 'secret="', 'password="', 'token="']
        for pattern in sensitive_patterns:
            assert pattern not in page_content, f"Sensitive information found: {pattern}"

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093816
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    # @pytest.mark.skip(reason="Production bug not fixed")
    def test_update_telemetry_endpoint_credential(
        self,
        get_user_page: Page,
        datadog_telemetry_endpoint_setup,
        telemetry_endpoint_teardown,
        test_data_settings: dict,
        running_config: dict,
    ):
        """
        Test ID: 4093816
        Title: Update existing NVCF Datadog telemetry endpoint credential with Org Admin

        Test steps:
        1. Login NGC with Org Owner + NVCF Admin
        2. Enter Settings Page from left side navigation bar
        3. Select one existing nvcf telemetry endpoint for the org
        4. Use the common update_endpoint_key function to update the credential
        5. Check the update is successful

        Expected Results:
        - NVCF endpoint secrets can be updated successfully
        """
        # Get the created endpoint name from the fixture
        target_endpoint_name = datadog_telemetry_endpoint_setup
        logging.info(f"Using created Datadog telemetry endpoint: {target_endpoint_name}")

        # Create page objects
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page
        settings_page.navigate()
        settings_page.verify_on_settings_page()
        logging.info("Successfully navigated to the Settings page")

        # Scroll to telemetry endpoints section to ensure it's visible
        settings_page.scroll_to_telemetry_endpoints()

        # Wait for the telemetry endpoints table to load

        # Generate new credentials for update
        new_key = f"updated-api-key-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        new_instance_id = f"updated-instance-id-{datetime.now().strftime('%Y%m%d%H%M%S')}"

        update_success = settings_page.update_endpoint_key(
            endpoint_name=target_endpoint_name,
            new_key=new_key,
            new_instance_id=new_instance_id,
        )

        # Step 5: Verify update was successful
        assert update_success, f"Failed to update key for endpoint {target_endpoint_name}"

        # Final verification - endpoint still exists after update
        settings_page.clear_endpoint_search()
        settings_page.search_for_endpoint(target_endpoint_name)
        assert settings_page.check_if_endpoint_exists(
            target_endpoint_name
        ), f"Endpoint {target_endpoint_name} not found after credential update"

        logging.info(
            f"Successfully updated credentials for endpoint {target_endpoint_name}"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093814
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_view_telemetry_export_information_deploying(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        session_nvcf_admin_sak,
        byoo_container_function_with_grafana_cloud_endpoint: dict,
    ):
        """
        Test ID: 4093814
        Title: View "Telemetry Export" information from deploying function version details page

        Test steps:
        1. Login NGC with NVCF Admin
        2. In the Function List Page, search for the deploying function
        3. Check the function status and deploy it if inactive
        4. Navigate to the version details page and click the "Function Details" tab
        5. Check the "Telemetry Export" information is correct and displayed normally
        6. Check that secrets information used for telemetries should not be displayed in UI

        Expected Results:
        - "Telemetry Export" information is correct for each function
        - No secrets or sensitive information should be displayed
        """
        # Create page objects
        function_list_page = FunctionListPage(get_user_page)

        # Get function data from fixture
        func_data = byoo_container_function_with_grafana_cloud_endpoint
        function_id = func_data["func_id"]
        version_id = func_data["func_ver_id"]
        version_name = func_data["description"]
        telemetry_endpoint_name = func_data["telemetry_name"] + " (Grafana Cloud)"

        # Step 1 & 2: Navigate to function list page and search for the function
        function_list_page.navigate_to_page()

        # First check if the function exists
        function_list_page.FunctionPagination.wait_function_list_ready()
        function_info = function_list_page.FunctionPagination.get_function_by_func_id(
            function_id
        )
        if not function_info:
            logging.info(
                f"Function {function_id} not found, searching by function ID directly"
            )
            function_list_page.FunctionPagination.search_function_exact_match(function_id)
            # Wait for search results
            get_user_page.wait_for_timeout(3000)

        # Step 3: Check function status and deploy if needed
        function_status = function_list_page.FunctionPagination.check_function_status(
            function_id
        )
        logging.info(f"Function {function_id} current status: {function_status}")

        # Handle both cases - when function is already deploying and when it's inactive
        if function_status.lower() == "inactive":
            logging.info(f"Function {function_id} is inactive, proceeding to deploy it")

            from pages.Deployments.DeploymentsCreatePage import DeploymentsCreatePage
            from config.consts import TEST_DATA_CF

            # Navigate to deploy function version page via function list page
            deploy_page = function_list_page.FunctionPagination.action_to(
                uuid=function_id, entry="Deploy Version"
            )

            # Use DeploymentsCreatePage to deploy the function using the pattern from test_deployments.py
            if CURRENT_ENV == "staging":
                deploy_data = TEST_DATA_CF["deployment_min_stg"]
            else:
                deploy_data = TEST_DATA_CF["deployment_min"]
            deployments_create_page = DeploymentsCreatePage(deploy_page)
            deployments_create_page.deploy_function_version(
                get_user_page,
                function_id,
                version_id,
                wait_until_deployed=False,  # Don't wait for deployment to complete
                **deploy_data,  # Use the standard deployment config
                session_nvcf_admin_sak=session_nvcf_admin_sak,
            )
        elif function_status.lower() == "deploying":
            logging.info(
                f"Function {function_id} is already in deploying state, proceeding with test"
            )
        else:
            logging.info(
                f"Function {function_id} is in unexpected state: {function_status}, attempting to continue"
            )

        # Step 4: Navigate to the function version detail page and click Function Details tab
        function_ver_detail_page = FunctionVerDetailPage(
            get_user_page,
            func_id=function_id,
            vers_id=version_id,
        )

        # Navigate to function version details page
        function_list_page.navigate_to_function_version_detail(
            function_id=function_id,
            version_name=version_name,
            tab_name="Function Details",
        )

        # Step 5: Verify Telemetry Export information
        # Verify Logs Endpoint information is displayed correctly if configured
        function_ver_detail_page.verify_endpoint_value(
            endpoint_type="Logs Endpoint",
            expected_value=telemetry_endpoint_name,
        )

        function_ver_detail_page.verify_endpoint_value(
            endpoint_type="Metrics Endpoint",
            expected_value=telemetry_endpoint_name,
        )

        function_ver_detail_page.verify_endpoint_value(
            endpoint_type="Traces Endpoint",
            expected_value=telemetry_endpoint_name,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093798
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", ["nvcf_admin"], indirect=True)
    def test_create_multiple_telemetry_endpoints_same_provider(self, get_user_page: Page):
        """
        Test ID: 4093798
        Title: Create multi NVCF telemetry endpoints with different endpoint name for the same provider

        Test steps:
        1. Login NGC with Org Owner + NVCF Admin
        2. Enter Settings Page from left side navigation bar
        3. Click "Add Endpoint" button to setup an endpoint
        4. Fill the required fields, select the endpoint "Provider" from dropdown list
        5. Select one supported "Telemetry Type"
        6. Select the "Communication Protocol" for this endpoint
        7. Save the configuration
        8. Check the endpoint is listed correctly from Settings page
        9. Repeat steps to create another NVCF telemetry endpoint with different name for the same provider
        10. Verify all created endpoints are listed correctly

        Expected Results:
        - NVCF endpoints can be created successfully
        - New created endpoints are listed correctly
        """
        # Create page objects
        telemetry_page = TelemetryPage(get_user_page)
        settings_page = SettingsPage(get_user_page)

        # Define test data for endpoints
        provider = "Datadog"  # Using Datadog as example provider
        endpoints = []  # Store created endpoint names

        # Generate unique names for endpoints with timestamp to avoid conflicts
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        first_endpoint_name = f"byoo_auto_endpoint1-{timestamp}"
        second_endpoint_name = f"byoo_auto_endpoint2-{timestamp}"

        # Define different parameters for each endpoint
        first_api_key = "api_key_123456"
        second_api_key = "api_key_789012"

        # Step 1 & 2: Navigate to settings page
        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()
        logging.info("Navigated to settings page")

        # Get initial count of endpoints for verification later
        initial_count = settings_page.get_telemetry_endpoints_count()
        logging.info(f"Initial count of endpoints: {initial_count}")

        # Step 3-8: Create first telemetry endpoint
        telemetry_page.click_add_endpoint()

        # Create first endpoint with Datadog provider and specific parameters
        first_created_name = telemetry_page.create_telemetry_endpoint(
            name=first_endpoint_name,
            endpoint_url="https://api.datadoghq.com",
            provider=provider,
            key=first_api_key,  # First API key for Datadog
        )
        endpoints.append(first_created_name)

        # Search and verify first endpoint was created successfully
        telemetry_page.search_endpoint(first_created_name)
        assert telemetry_page.verify_endpoint_exists(
            first_created_name
        ), f"First endpoint {first_created_name} was not created successfully"
        logging.info(f"Successfully created first endpoint: {first_created_name}")

        # Step 9: Create second telemetry endpoint with the same provider but different key
        telemetry_page.click_add_endpoint()

        # Create second endpoint with the same provider but different parameters
        second_created_name = telemetry_page.create_telemetry_endpoint(
            name=second_endpoint_name,
            provider=provider,
            endpoint_url="https://api.datadoghq.com",
            key=second_api_key,  # Different API key for Datadog
        )
        endpoints.append(second_created_name)

        # Search and verify second endpoint was created successfully
        telemetry_page.search_endpoint(second_created_name)
        assert telemetry_page.verify_endpoint_exists(
            second_created_name
        ), f"Second endpoint {second_created_name} was not created successfully"
        logging.info(f"Successfully created second endpoint: {second_created_name}")

        # Step 10: Verify both endpoints exist - first clear search
        settings_page.clear_endpoint_search()

        # Verify endpoint count has increased by 2
        new_count = settings_page.get_telemetry_endpoints_count()
        assert new_count >= initial_count + 2, (
            f"Expected endpoint count to increase by at least 2 (from {initial_count} to at least "
            f"{initial_count + 2}), but got {new_count}"
        )

        # Verify both created endpoints exist and have the same provider
        for endpoint_name in endpoints:
            # Search for the endpoint
            settings_page.search_for_endpoint(endpoint_name)

            # Verify endpoint exists
            assert settings_page.verify_endpoint_exists(
                endpoint_name
            ), f"Endpoint {endpoint_name} not found after creation"

            # Verify provider is correct
            provider_cell = settings_page.page.locator(
                f"tr:has-text('{endpoint_name}') td:nth-child(2)"
            )
            assert provider_cell.text_content().strip() == provider, (
                f"Expected provider '{provider}' for endpoint {endpoint_name}, "
                f"but got '{provider_cell.text_content().strip()}'"
            )

            logging.info(
                f"Successfully verified endpoint {endpoint_name} with provider {provider}"
            )

            # Clear search before next iteration
            settings_page.clear_endpoint_search()

        logging.info(
            "Successfully created and verified multiple endpoints for the same provider"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093812
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", ["nvcf_admin"], indirect=True)
    def test_view_telemetry_export_information(
        self, get_user_page: Page, test_data_settings: dict
    ):
        """
        Test ID: 4093812
        Title: View "Telemetry Export" information from active function version details page

        Test steps:
        1. Login NGC with NVCF Admin
        2. In the Function List Page, select one active function which has telemetry endpoint configuration
        3. Navigate to the version details page of this function and click the "Function Details" tab
        4. Check the "Telemetry Export" information is correct and displayed normally
        5. Check that secrets information used for telemetries should not be displayed in UI

        Expected Results:
        - "Telemetry Export" information is correct for each function
        - No secrets or sensitive information should be displayed
        """
        # Create FunctionDetailPage instance
        function_list_page = FunctionListPage(get_user_page)
        function_ver_detail_page = FunctionVerDetailPage(get_user_page)

        # Get test data from YAML
        if CURRENT_ENV == "staging":
            test_data = test_data_settings[
                "active_function_version_with_telemetry_endpoint_stg"
            ]["telemetry_export_test"]
        else:
            test_data = test_data_settings[
                "active_function_version_with_telemetry_endpoint"
            ]["telemetry_export_test"]

        # Step 1 & 2: Navigate to the function version detail page with known telemetry configuration
        function_list_page.navigate_to_function_version_detail(
            function_id=test_data["function_id"],
            version_name=test_data["version_name"],
            tab_name=test_data["filter_tab"],
        )
        logging.info(
            f"Navigated to function version details page for {test_data['function_name']}"
        )

        # Step 3 & 4: Verify Logs Endpoint information is displayed correctly
        function_ver_detail_page.verify_endpoint_value(
            endpoint_type="Logs Endpoint",
            expected_value=test_data["expected_logs_endpoint"],
        )
        logging.info(
            f"Verified Logs Endpoint is set to '{test_data['expected_logs_endpoint']}'"
        )

        # Verify Metrics Endpoint information is displayed correctly
        function_ver_detail_page.verify_endpoint_value(
            endpoint_type="Metrics Endpoint",
            expected_value=test_data["expected_metrics_endpoint"],
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093787
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", ["nvcf_admin"], indirect=True)
    def test_validate_endpoint_filter_function(self, get_user_page: Page):
        """
        Test ID: 4093787
        Title: Validate filter function in telemetry endpoint list page NVCF Admin User

        Test steps:
        1. Login NGC with NVCF Admin User
        2. Enter Settings Page from left side navigation bar to go to the telemetry endpoint list page
        3. Test Provider filter functionality
        4. Test Telemetry Type filter functionality
        """
        # Create page objects
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page
        settings_page.navigate()
        logging.info("Navigated to settings page")

        # Scroll to telemetry endpoints section
        settings_page.scroll_to_telemetry_endpoints()
        logging.info("Scrolled to telemetry endpoints section")

        # Get initial count of endpoints for validation
        initial_count = settings_page.get_telemetry_endpoints_count()
        logging.info(f"Initial count of endpoints: {initial_count}")

        # Skip test if no endpoints are available
        if initial_count == 0:
            pytest.skip("No telemetry endpoints found in the table, skipping test")

        # Step 3: Test Provider filter functionality
        logging.info("Testing Provider filter functionality")
        provider_result = settings_page.filter_endpoint(
            filter_type="Provider", filter_value="Datadog", initial_count=initial_count
        )

        # Verify Provider filter test was successful
        assert provider_result[
            "success"
        ], f"Provider filter test failed: {provider_result['error']}"
        logging.info("Provider filter test completed successfully")

        # Step 4: Test Telemetry Type filter functionality
        logging.info("Testing Telemetry Type filter functionality")
        telemetry_type_result = settings_page.filter_endpoint(
            filter_type="Telemetry Type", filter_value="Logs", initial_count=initial_count
        )

        # Verify Telemetry Type filter test was successful
        assert telemetry_type_result[
            "success"
        ], f"Telemetry Type filter test failed: {telemetry_type_result['error']}"
        logging.info("Telemetry Type filter test completed successfully")

        logging.info(
            "Successfully verified filter functionality in telemetry endpoint list page"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093791
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_VIEWER_USER, indirect=True)
    def test_telemetry_page_items_validation_with_viewer_user(self, get_user_page: Page):
        """
        Test ID: 4093791
        Title: Page items validation in telemetry endpoint list page NVCF Admin User

        Test steps:
        1. Login NGC with Org Owner + NVCF Admin
        2. Enter Settings Page from left side navigation bar
        3. Check the "View Documentation" button works well and can link to correct external page
        4. Try to search telemetry endpoint from search bar, it should work well in the list page

        Expected Results:
        - View Documentation button should open correct external documentation page
        - Search functionality should work correctly with fuzzy search capability
        """
        # Create page objects
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page
        settings_page.navigate()
        logging.info("Navigated to settings page")

        settings_page.scroll_to_telemetry_endpoints()
        logging.info("Scrolled to telemetry endpoints section")

        # Step 3: Verify View Documentation button works
        with get_user_page.context.expect_page() as page_info:
            settings_page.page.get_by_role("link", name="View Documentation").click()
            logging.info("Clicked on 'View Documentation' link")

        # Get the newly opened page
        docs_page = page_info.value

        # Make sure new page is loaded
        docs_page.wait_for_load_state("domcontentloaded", timeout=30000)

        # Check page title to verify it loaded successfully
        page_title = docs_page.title()
        assert (
            "NVIDIA Cloud Functions" in page_title
        ), f"Expected page title containing 'NVIDIA Cloud Functions' but got '{page_title}'"

        logging.info("Successfully verified documentation URL and page loaded")

        # Close the docs page and return to the settings page
        docs_page.close()

        # Step 4: Test search functionality
        # Define a test endpoint name to search for - using an endpoint that is likely to exist
        test_endpoint = "byoo_auto_telemetry_dailyrun"

        # Search for the endpoint
        settings_page.search_for_endpoint(test_endpoint)
        logging.info(f"Searched for endpoint: {test_endpoint}")

        # Verify search results
        assert settings_page.verify_endpoint_exists(
            test_endpoint
        ), f"Endpoint {test_endpoint} not found after search"
        logging.info(f"Successfully found endpoint {test_endpoint} using search")

        # Test partial/fuzzy search capability
        # Use only part of the endpoint name
        partial_search = test_endpoint[:-2]  # Take first 5 characters
        settings_page.clear_endpoint_search()
        settings_page.search_for_endpoint(partial_search)
        logging.info(f"Performed partial search with: {partial_search}")

        # Verify partial search finds the endpoint
        assert settings_page.verify_endpoint_exists(
            test_endpoint
        ), f"Endpoint {test_endpoint} not found with partial search '{partial_search}'"
        logging.info(
            f"Successfully verified fuzzy search capability with partial term: {partial_search}"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093792
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", ["nvcf_admin"], indirect=True)
    def test_telemetry_page_items_validation(self, get_user_page: Page):
        """
        Test ID: 4093792
        Title: Page items validation in telemetry endpoint list page NVCF Admin User

        Test steps:
        1. Login NGC with Org Owner + NVCF Admin
        2. Enter Settings Page from left side navigation bar
        3. Check the "View Documentation" button works well and can link to correct external page
        4. Try to search telemetry endpoint from search bar, it should work well in the list page

        Expected Results:
        - View Documentation button should open correct external documentation page
        - Search functionality should work correctly with fuzzy search capability
        """
        # Create page objects
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page
        settings_page.navigate()
        logging.info("Navigated to settings page")

        settings_page.scroll_to_telemetry_endpoints()
        logging.info("Scrolled to telemetry endpoints section")

        # Step 3: Verify View Documentation button works
        with get_user_page.context.expect_page() as page_info:
            settings_page.page.locator(
                "//h4[normalize-space(text())='Telemetry Endpoints']/following-sibling::div/a"
            ).nth(0).click()
            logging.info("Clicked on 'View Documentation' link")

        # Get the newly opened page
        docs_page = page_info.value

        # Make sure new page is loaded
        docs_page.wait_for_load_state("domcontentloaded", timeout=30000)

        # Check page title to verify it loaded successfully
        page_title = docs_page.title()
        assert (
            "NVIDIA Cloud Functions" in page_title
        ), f"Expected page title containing 'NVIDIA Cloud Functions' but got '{page_title}'"

        logging.info("Successfully verified documentation URL and page loaded")

        # Close the docs page and return to the settings page
        docs_page.close()

        # Step 4: Test search functionality
        # Define a test endpoint name to search for - using an endpoint that is likely to exist
        test_endpoint = "byoo_auto_telemetry_dailyrun"

        # Search for the endpoint
        settings_page.search_for_endpoint(test_endpoint)
        logging.info(f"Searched for endpoint: {test_endpoint}")

        # Verify search results
        assert settings_page.verify_endpoint_exists(
            test_endpoint
        ), f"Endpoint {test_endpoint} not found after search"
        logging.info(f"Successfully found endpoint {test_endpoint} using search")

        # Test partial/fuzzy search capability
        # Use only part of the endpoint name
        partial_search = test_endpoint[:-2]  # Take first 5 characters
        settings_page.clear_endpoint_search()
        settings_page.search_for_endpoint(partial_search)
        logging.info(f"Performed partial search with: {partial_search}")

        # Verify partial search finds the endpoint
        assert settings_page.verify_endpoint_exists(
            test_endpoint
        ), f"Endpoint {test_endpoint} not found with partial search '{partial_search}'"
        logging.info(
            f"Successfully verified fuzzy search capability with partial term: {partial_search}"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093785
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", ["nvcf_admin"], indirect=True)
    def test_telemetry_endpoint_list_admin_permissions(self, get_user_page: Page):
        """
        Test ID: 4093785
        Title: Telemetry endpoint list page for NVCF Admin User

        Test steps:
        1. Login NGC with NVCF Admin
        2. Enter Settings Page from left side navigation bar to go to the telemetry endpoint list page
        3. Check "Add Endpoint" button should be displayed for NVCF Admin User View
        4. Make sure the NVCF Admin User have the permission to add and edit for the telemetry endpoint
        """
        # Create page objects
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page
        settings_page.navigate()

        # Scroll to telemetry endpoints section
        settings_page.scroll_to_telemetry_endpoints()

        # Step 3: Check if "Add Endpoint" button exists
        add_button = settings_page.page.get_by_role("link", name="Add Endpoint")
        assert (
            add_button.is_visible()
        ), "Add Endpoint button is not visible for NVCF Admin user"

        # Step 4: Verify Actions column is present in the table using the robust method
        expected_columns = [
            "Name",
            "Provider",
            "Telemetry Type",
            "Protocol",
            "Created On",
            "Actions",
        ]
        verified = settings_page.verify_telemetry_table_columns_robust(expected_columns)
        assert (
            verified
        ), "Failed to verify all expected columns in the telemetry endpoints table"

        # Also check that we can click the actions button on at least one endpoint if there are endpoints
        endpoints_count = settings_page.get_telemetry_endpoints_count()
        if endpoints_count > 0:
            # Click the actions button on the first endpoint
            settings_page.click_endpoint_actions_button()
            # Verify actions menu appears
            assert settings_page.page.locator(
                "text=Update Key"
            ), "Actions menu did not appear"
            # Press Escape to dismiss menu instead of clicking elsewhere
            get_user_page.keyboard.press("Escape")

        logging.info(
            "Successfully verified NVCF Admin has permissions to manage telemetry endpoints"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093786
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_VIEWER_USER, indirect=True)
    def test_telemetry_endpoint_list_with_viewer_user(self, get_user_page: Page):
        """
        Test ID: 4093786
        Title: Telemetry endpoint list page for NVCF Viewer User

        Test steps:
        1. Login NGC with NVCF Viewer
        2. Enter Settings Page from left side navigation bar to go to the telemetry endpoint list page
        3. Check "Add Endpoint" button should NOT be displayed for NVCF Viewer User
        4. Verify the NVCF Viewer User does NOT have permission to add or edit telemetry endpoints
        """
        # Create page objects
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page
        settings_page.navigate()

        # Scroll to telemetry endpoints section
        settings_page.scroll_to_telemetry_endpoints()

        # Step 3: Verify "Add Endpoint" button does NOT exist for viewer
        add_button = settings_page.page.get_by_role("link", name="Add Endpoint")
        assert (
            not add_button.is_visible()
        ), "Add Endpoint button should not be visible for NVCF Viewer user"
        logging.info("Verified Add Endpoint button is not visible for NVCF Viewer user")

        # Step 4: Verify Actions column is NOT present in the table for viewer user
        expected_columns = [
            "Name",
            "Provider",
            "Telemetry Type",
            "Protocol",
            "Created On",
        ]
        # Note: "Actions" column should NOT be in the expected columns for viewer
        verified = settings_page.verify_telemetry_table_columns_robust(expected_columns)
        assert (
            verified
        ), "Failed to verify expected columns in the telemetry endpoints table"

        # # Also verify that there is no Actions column
        actions_header = settings_page.page.locator("th").filter(has_text="Actions")
        assert (
            not actions_header.is_visible()
        ), "Actions column should not be visible for NVCF Viewer user"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T4093823
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_select_telemetry_endpoint_for_creating_container_function_without_deployment(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
    ):
        """
        Test selecting telemetry endpoint for create container function without deployment
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_logs=func_info["telemetry_logs"],
            telemetry_metrics=func_info["telemetry_metrics"],
        )

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]

        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == [func_info["telemetry_metrics"]]

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T4093824
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_select_telemetry_endpoint_for_creating_helm_chart_function_without_deployment(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
    ):
        """
        Test selecting telemetry endpoint for create container function without deployment
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings[
                "helm_chart_function_telemetry_endpoint_test_stg"
            ]
        else:
            func_info = test_data_settings["helm_chart_function_telemetry_endpoint_test"]
        logging.info("Start creation of source helm chart function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_helm_chart_function(
            source_func_name,
            func_info["Helm_Chart"],
            func_info["Helm_Chart_Version"],
            func_info["Helm_Chart_Service_Name"],
            func_info["inference_protocol"],
            func_info["inference_port"],
            telemetry_logs=func_info["telemetry_logs"],
            telemetry_metrics=func_info["telemetry_metrics"],
            health_port=func_info["health_port"],
        )

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]

        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == [func_info["telemetry_metrics"]]

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.T4093825
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_select_telemetry_endpoint_for_creating_container_function_and_deploy_to_GFN_backend(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
        session_nvcf_admin_sak,
    ):
        """
        Test selecting telemetry endpoint for create container function and deploy to GFN backend
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_logs=func_info["telemetry_logs"],
            telemetry_metrics=func_info["telemetry_metrics"],
            is_deploy=True,
        )
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        func_id, func_ver_id = deployments_create_page.deploy_function_version(
            get_user_page,
            None,
            None,
            wait_until_deployed=True,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(
            func_name=source_func_name, func_id=func_id, vers_id=func_ver_id
        )

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == [func_info["telemetry_metrics"]]

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.T4093827
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_select_telemetry_endpoint_for_creating_helm_chart_function_and_deploy_to_GFN_backend(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
        session_nvcf_admin_sak,
    ):
        """
        Test selecting telemetry endpoint for create helm chart function and deploy to GFN backend
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings[
                "helm_chart_function_telemetry_endpoint_test_stg"
            ]
        else:
            func_info = test_data_settings["helm_chart_function_telemetry_endpoint_test"]
        logging.info("Start creation of source helm chart function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_helm_chart_function(
            source_func_name,
            func_info["Helm_Chart"],
            func_info["Helm_Chart_Version"],
            func_info["Helm_Chart_Service_Name"],
            func_info["inference_protocol"],
            inference_endpoint=func_info["inference_endpoint"],
            health_endpoint=func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            health_port=func_info["health_port"],
            telemetry_logs=func_info["telemetry_logs"],
            telemetry_metrics=func_info["telemetry_metrics"],
            is_deploy=True,
        )
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        func_id, func_ver_id = deployments_create_page.deploy_function_version(
            get_user_page,
            None,
            None,
            wait_until_deployed=True,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(
            func_name=source_func_name, func_id=func_id, vers_id=func_ver_id
        )

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == [func_info["telemetry_metrics"]]

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T4111493
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_select_only_one_telemetry_endpoint_for_creating_container_function_without_deployment(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
    ):
        """
        Test selecting only one telemetry endpoint for create container function without deployment
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_logs=func_info["telemetry_logs"],
        )

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")

        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == ["—"]

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T4111494
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_select_only_one_telemetry_endpoint_for_creating_helm_chart_function_without_deployment(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
    ):
        """
        Test selecting only one telemetry endpoint for create helm chart function without deployment
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings[
                "helm_chart_function_telemetry_endpoint_test_stg"
            ]
        else:
            func_info = test_data_settings["helm_chart_function_telemetry_endpoint_test"]
        logging.info("Start creation of source helm chart function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_helm_chart_function(
            source_func_name,
            func_info["Helm_Chart"],
            func_info["Helm_Chart_Version"],
            func_info["Helm_Chart_Service_Name"],
            func_info["inference_protocol"],
            func_info["inference_port"],
            health_port=func_info["health_port"],
            telemetry_logs=func_info["telemetry_logs"],
        )

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        logging.info(f"basic_details_data: {basic_details_data}")
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]

        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == ["—"]

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.T4111499
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_select_only_one_telemetry_endpoint_for_creating_container_function_and_deploy_to_GFN_backend(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
        session_nvcf_admin_sak,
    ):
        """
        Test selecting only one telemetry endpoint for create container function and deploy to GFN backend
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_logs=func_info["telemetry_logs"],
            is_deploy=True,
        )
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        func_id, func_ver_id = deployments_create_page.deploy_function_version(
            get_user_page,
            None,
            None,
            wait_until_deployed=True,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(
            func_name=source_func_name, func_id=func_id, vers_id=func_ver_id
        )

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == ["—"]

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.T4111500
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_select_only_one_telemetry_endpoint_for_creating_helm_chart_function_and_deploy_to_GFN_backend(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
        session_nvcf_admin_sak,
    ):
        """
        Test selecting only one telemetry endpoint for create helm chart function and deploy to GFN backend
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings[
                "helm_chart_function_telemetry_endpoint_test_stg"
            ]
        else:
            func_info = test_data_settings["helm_chart_function_telemetry_endpoint_test"]
        logging.info("Start creation of source helm chart function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        logging.info(f"func_info: {func_info}")
        create_func_page.Form.create_helm_chart_function(
            source_func_name,
            func_info["Helm_Chart"],
            func_info["Helm_Chart_Version"],
            func_info["Helm_Chart_Service_Name"],
            func_info["inference_protocol"],
            inference_endpoint=func_info["inference_endpoint"],
            health_endpoint=func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            health_port=func_info["health_port"],
            telemetry_logs=func_info["telemetry_logs"],
            is_deploy=True,
        )
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        func_id, func_ver_id = deployments_create_page.deploy_function_version(
            get_user_page,
            None,
            None,
            wait_until_deployed=True,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(
            func_name=source_func_name, func_id=func_id, vers_id=func_ver_id
        )

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == ["—"]

    @pytest.mark.T4108256
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_view_telemetry_export_information_no_telemetry(
        self, get_user_page: Page, test_data_settings: dict
    ):
        """
        Test ID: 4108256
        Title: View "Telemetry Export" information from function version without telemetry configuration

        Test steps:
        1. Login NGC with NVCF Admin
        2. In the Function List Page, select function without telemetry endpoint configuration
        3. Navigate to the version details page and click the "Function Details" tab
        4. Check the "Telemetry Export" information is shown as "—"

        Expected Results:
        - Telemetry Export information should show "—" for both Logs and Metrics endpoints
        """
        # Create FunctionDetailPage instance
        function_list_page = FunctionListPage(get_user_page)

        # Get test data from YAML
        if CURRENT_ENV == "staging":
            test_data = test_data_settings["function_version_without_telemetry_stg"][
                "telemetry_export_test"
            ]
        else:
            test_data = test_data_settings["function_version_without_telemetry"][
                "telemetry_export_test"
            ]

        # Initialize function_ver_detail_page with required parameters
        function_ver_detail_page = FunctionVerDetailPage(
            get_user_page,
            func_name=test_data["function_name"],
            func_id=test_data["function_id"],
            vers_id=test_data["version_id"],
        )

        # Step 1 & 2: Navigate to the function version detail page
        function_list_page.navigate_to_function_version_detail(
            function_id=test_data["function_id"],
            version_name=test_data["version_name"],
            tab_name=test_data["filter_tab"],
        )

        # Step 3: Click on Function Details tab
        function_ver_detail_page.page.get_by_text(test_data["filter_tab"]).click()
        logging.info("Clicked on Function Details tab")

        # Step 4: Verify both Logs and Metrics endpoints show "—"
        function_ver_detail_page.verify_endpoint_value(
            endpoint_type="Logs Endpoint",
            expected_value=test_data["expected_logs_endpoint"],
        )
        logging.info("Verified Logs Endpoint shows no configuration")

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T4093822
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_telemetry_endpoints_active_function(
        self, get_user_page: Page, test_data_settings: dict
    ):
        """
        Test ID: 4093822
        Title: Delete one of the associated telemetry endpoints for the active function

        Test steps:
        1. Login NGC with Org Owner + NVCF Admin
        2. Enter Functions Page from left side navigation bar
        3. Select one existing ACTIVE cloud function which has multi BYO Observability configuration
        4. Delete one of the associated telemetry endpoints, such as "Logs" telemetry endpoint
        5. The delete operation should not be allowed, and proper error messages should be displayed

        Expected Results:
        - The delete operation should not be allowed
        - A proper error message should be displayed, e.g.:
          "Cannot be deleted as it in use by Function id '<function-id>',
           version '<version-id>' - Delete the function first to be able to delete the telemetry."
        """
        # Create page objects
        settings_page = SettingsPage(get_user_page)
        func_list_page = FunctionListPage(get_user_page)

        # Get test data from YAML
        if CURRENT_ENV == "staging":
            test_data = test_data_settings[
                "active_function_version_with_telemetry_endpoint_stg"
            ]["telemetry_export_test"]
        else:
            test_data = test_data_settings[
                "active_function_version_with_telemetry_endpoint"
            ]["telemetry_export_test"]
        function_id = test_data["function_id"]
        function_name = test_data["function_name"]

        # Extract the endpoint name from the expected logs endpoint
        # Example: From "telemetry-ui-automation (Grafana Cloud)" extract "telemetry-ui-automation"
        endpoint_name = test_data["expected_logs_endpoint"].split(" (")[0]
        expected_error_message = test_data_settings["telemetry"]["deletion_tests"][
            "expected_error_message"
        ]

        logging.info(
            f"Using function: {function_name} (ID: {function_id}) and endpoint: {endpoint_name}"
        )

        # Step 1-2: Navigate to functions page and verify function status
        func_list_page.navigate_to_page()

        # Check function status to ensure it's active
        function_status = func_list_page.FunctionPagination.check_function_status(
            function_id
        )
        logging.info(f"Function {function_name} current status: {function_status}")

        # Verify the function is active before proceeding
        assert function_status.lower() == "active", (
            f"Function {function_name} is not in active state. "
            f"Current state: {function_status}"
        )

        # Step 3-4: Navigate to settings page and attempt to delete the endpoint
        settings_page.navigate()

        # Verify the endpoint exists before attempting to delete
        settings_page.scroll_to_telemetry_endpoints()
        settings_page.wait_for_telemetry_endpoints_loading()
        settings_page.search_for_endpoint(endpoint_name)

        assert settings_page.verify_endpoint_exists(
            endpoint_name
        ), f"Test endpoint {endpoint_name} not found"
        logging.info(f"Found telemetry endpoint: {endpoint_name}")

        # Step 5: Attempt to delete the endpoint using the common function
        delete_result = settings_page.perform_endpoint_delete_flow(
            endpoint_name=endpoint_name,
            should_cancel=False,  # Confirm deletion
        )

        # Verify that the delete operation failed as expected (endpoint is in use)
        # The endpoint should still exist (verified=False) and we should have received an error
        assert not delete_result.get(
            "verified", False
        ), "The endpoint was unexpectedly deleted when it should have been protected"
        assert (
            "error" in delete_result
        ), "No error was reported when attempting to delete a protected endpoint"

        # Verify the error message contains the expected text
        if "error" in delete_result:
            assert (
                expected_error_message in delete_result["error"]
            ), f"Error message '{delete_result['error']}' does not contain expected text '{expected_error_message}'"
            logging.info(f"Received expected error message: {delete_result['error']}")

        # Additional verification: Check that the endpoint still exists
        settings_page.clear_endpoint_search()
        settings_page.search_for_endpoint(endpoint_name)
        assert settings_page.verify_endpoint_exists(
            endpoint_name
        ), "Endpoint no longer exists after deletion attempt"

        logging.info(f"Verified endpoint {endpoint_name} still exists as expected")

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093820
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_telemetry_endpoint_credential_cancel(
        self,
        get_user_page: Page,
        datadog_telemetry_endpoint_setup,
        telemetry_endpoint_teardown,
        test_data_settings: dict,
        running_config: dict,
    ):
        """
        Test ID: 4093820
        Title: Update existing NVCF telemetry endpoint credential with Org Admin - Cancel changes

        Test steps:
        1. Login NGC with Org Owner + NVCF Admin
        2. Enter Settings Page from left side navigation bar
        3. Select one existing nvcf telemetry endpoint for the org
        4. Click the Actions menu (3 dots menu) of the matched telemetry endpoint
        5. Select and click the "Update Credential" menu
        6. The "Update Key" window pops out
        7. Enter the new credential/secret value for the telemetry endpoint, and click "Cancel"
        8. Check the update is not changed

        Expected Results:
        - Update operation should be cancelled
        - Original endpoint credentials should remain unchanged
        """
        # Get the created endpoint name from the fixture
        target_endpoint_name = datadog_telemetry_endpoint_setup
        logging.info(f"Using created Datadog telemetry endpoint: {target_endpoint_name}")

        # Create page objects
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page
        settings_page.navigate()
        settings_page.verify_on_settings_page()
        logging.info("Successfully navigated to the Settings page")

        # Scroll to telemetry endpoints section to ensure it's visible
        settings_page.scroll_to_telemetry_endpoints()
        settings_page.wait_for_telemetry_endpoints_loading()

        # Verify the endpoint exists before attempting update
        settings_page.search_for_endpoint(target_endpoint_name)
        assert settings_page.verify_endpoint_exists(
            target_endpoint_name
        ), f"Test endpoint {target_endpoint_name} not found"
        logging.info(f"Found telemetry endpoint: {target_endpoint_name}")

        # Generate new test values for the update attempt
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        new_key = f"cancelled-api-key-{timestamp}"
        new_instance_id = f"cancelled-instance-id-{timestamp}"

        # Step 3-7: Attempt to update the endpoint key but cancel the operation
        update_result = settings_page.update_endpoint_key(
            endpoint_name=target_endpoint_name,
            new_key=new_key,
            new_instance_id=new_instance_id,
            cancel=True,  # Cancel the update operation
        )

        # Step 8: Verify update was cancelled
        assert (
            update_result
        ), f"Fail to cancel updating key for endpoint {target_endpoint_name}"

        logging.info(
            f"Successfully cancelled credential update for endpoint {target_endpoint_name}"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093803
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", ["nvcf_admin"], indirect=True)
    def test_telemetry_pagination_function_nvcf_admin(self, get_user_page: Page):
        """
        Test the functionality of setting the number of items displayed per page in a table.

        Test steps:
        1. Login NGC to Cloud Functions Page
        2. Enter [Settings] Page from left side navigation bar, go to the Telemetry Endpoints list page
        3. Check the default show rows to list endpoint is 5, update the max size to other numbers and page shows correctly
        4. Navigate to different page by click the page number
        5. Navigate to different page by enter the page number at "Go to" bar
        """
        # Create SettingsPage instance
        settings_page = SettingsPage(get_user_page)

        # Step 1: Navigate to settings page
        settings_page.navigate()

        # Step 2: Scroll to the Telemetry Endpoints section
        settings_page.scroll_to_telemetry_endpoints()

        # Wait for the page to load completely
        get_user_page.wait_for_load_state("networkidle")

        # Step 3-5: Verify pagination functionality
        result = settings_page.verify_page_pagination()

        # If not enough endpoints, skip the test
        if result["total_endpoints"] < 6:
            pytest.skip(
                f"Not enough endpoints to test pagination (only {result['total_endpoints']} endpoints)"
            )

        # Assert the test result
        assert result["success"], result.get(
            "error", "Pagination functionality verification failed"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.********
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_grafana_telemetry_endpoint_credential(
        self,
        get_user_page: Page,
        grafana_cloud_telemetry_endpoint_setup,
        telemetry_endpoint_teardown,
        test_data_settings: dict,
        running_config: dict,
    ):
        """
        Test ID: 4093815
        Title: Update existing NVCF Grafana Cloud telemetry endpoint credential with Org Admin

        Test steps:
        1. Login NGC with Org Owner + NVCF Admin
        2. Enter Settings Page from left side navigation bar
        3. Select one existing nvcf telemetry endpoint for the org
        4. Click the Actions menu(its 3 dots menu) of the matched telemetry endpoint
        5. Select and click the "Update Credential" menu
        6. The "Update Key" window pops out, should be able to update "Instance ID" and "Key" from this form page
        7. Enter the new "Instance ID" and "Key" value for the telemetry endpoint, and click "Save Changes"
        8. Check the update is succeed

        Expected Results:
        - NVCF endpoint secrets can be updated successfully
        """
        # Get the created endpoint name from fixture
        target_endpoint_name = grafana_cloud_telemetry_endpoint_setup
        logging.info(f"Using pre-created Grafana endpoint: {target_endpoint_name}")

        # Create page objects
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page
        settings_page.navigate()
        settings_page.verify_on_settings_page()
        logging.info("Successfully navigated to the Settings page")

        # Scroll to telemetry endpoints section to ensure it's visible
        settings_page.scroll_to_telemetry_endpoints()

        # Generate new credentials for update
        new_key = f"updated-api-key-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        new_instance_id = f"updated-instance-id-{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # Perform the credential update
        update_success = settings_page.update_endpoint_key(
            endpoint_name=target_endpoint_name,
            new_key=new_key,
            new_instance_id=new_instance_id,
        )

        # Step 5: Verify update was successful
        assert update_success, f"Failed to update key for endpoint {target_endpoint_name}"

        # Final verification - endpoint still exists after update
        settings_page.clear_endpoint_search()
        settings_page.search_for_endpoint(target_endpoint_name)
        assert settings_page.check_if_endpoint_exists(
            target_endpoint_name
        ), f"Endpoint {target_endpoint_name} not found after credential update"

        logging.info(
            f"Successfully updated credentials for endpoint {target_endpoint_name}"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093803
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skip(reason="duplicate test")
    def test_Check_the_page_navigation_in_telemetry_endpoint_list_page_with_admin(
        self, get_user_page: Page
    ):
        """
        Test ID: 4093803
        Title: Check pagination functionality in Telemetry Endpoints list with admin user

        Test steps:
        1. Login NGC to Cloud Functions Page
        2. Enter Settings Page from left side navigation bar, go to the Telemetry Endpoints list page
        3. Check the default show rows to list endpoint is 5, update the max size to other numbers and page shows correctly
        4. Navigate to different page by click the page number
        5. Navigate to different page by enter the page number at "Go to" bar, it will go to the specific page

        Expected Results:
        - Page navigation in the list page works well
        """
        # Create page objects
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page with telemetry endpoints
        settings_page.navigate()
        settings_page.verify_on_settings_page()
        logging.info("Navigated to settings page")

        # Scroll to telemetry endpoints section to ensure it's in view
        settings_page.scroll_to_telemetry_endpoints()

        # Wait for the telemetry endpoints to load
        settings_page.wait_for_telemetry_endpoints_loading()

        # Step 3: Check default page size and change it

        # First, get the total count of endpoints
        total_endpoints = settings_page.get_telemetry_endpoints_count()
        logging.info(f"Total telemetry endpoints: {total_endpoints}")

        # Need at least 6 endpoints to properly test pagination
        if total_endpoints < 6:
            pytest.skip(
                "Not enough telemetry endpoints to test pagination (need at least 6)"
            )

        combobox = settings_page.page.get_by_role("combobox").filter(has_text="5")
        value_element = combobox.get_by_text("5")
        logging.info(f"Value element: {value_element.count()}")
        assert value_element.is_visible(), "Value element not found"

        combobox.click()
        logging.info("Clicked combobox to open dropdown")

        get_user_page.wait_for_timeout(500)

        option_10 = get_user_page.get_by_text("10", exact=True)
        option_10.click()
        logging.info("Selected 10 items per page")

        get_user_page.wait_for_timeout(1000)

        # # Verify the rows have updated
        updated_rows = settings_page.get_telemetry_endpoints_rows()
        updated_rows_count = updated_rows.count()
        assert (
            updated_rows_count <= 10
        ), f"Expected at most 10 rows, found {updated_rows_count}"
        logging.info(
            f"After changing to 10 items per page, rows shown: {updated_rows_count}"
        )

        input_element = get_user_page.locator(
            "//h4[text()='Telemetry Endpoints']/../../div//input[@type='number' and @min='1' and @data-testid='kui-text-input-element']"
        )
        logging.info(f"Input element: {input_element.count()}")
        # assert go_to_input.is_visible(), "Go to input not found"
        # # Clear and enter page 3
        input_element.fill("")
        input_element.fill("2")
        input_element.press("Tab")
        get_user_page.wait_for_timeout(5000)
        active_button = get_user_page.locator("button.c-PJLV-blrJqR-isActive-true").filter(
            has_text="2"
        )
        logging.info(f"Active button: {active_button.count()}")
        assert active_button.is_visible(), "Active button not found"

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093804
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_VIEWER_USER, indirect=True)
    def test_Check_the_page_navigation_in_telemetry_endpoint_list_page_with_viewer_user(
        self, get_user_page: Page
    ):
        """
        Test ID: 4093804
        Title: Check pagination functionality in Telemetry Endpoints list with viewer user

        Test steps:
        1. Login NGC to Cloud Functions Page
        2. Enter Settings Page from left side navigation bar, go to the Telemetry Endpoints list page
        3. Check the default show rows to list endpoint is 5, update the max size to other numbers and page shows correctly
        4. Navigate to different page by click the page number
        5. Navigate to different page by enter the page number at "Go to" bar, it will go to the specific page

        Expected Results:
        - Page navigation in the list page works well
        """
        # Create page objects
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page with telemetry endpoints
        settings_page.navigate()
        settings_page.verify_on_settings_page()
        logging.info("Navigated to settings page")

        # Scroll to telemetry endpoints section to ensure it's in view
        settings_page.scroll_to_telemetry_endpoints()

        # Wait for the telemetry endpoints to load
        settings_page.wait_for_telemetry_endpoints_loading()

        # Step 3: Check default page size and change it

        # First, get the total count of endpoints
        total_endpoints = settings_page.get_telemetry_endpoints_count()
        logging.info(f"Total telemetry endpoints: {total_endpoints}")

        # Need at least 6 endpoints to properly test pagination
        if total_endpoints < 6:
            pytest.skip(
                "Not enough telemetry endpoints to test pagination (need at least 6)"
            )

        combobox = settings_page.page.get_by_role("combobox").filter(has_text="5")
        value_element = combobox.get_by_text("5")
        logging.info(f"Value element: {value_element.count()}")
        assert value_element.is_visible(), "Value element not found"

        combobox.click()
        logging.info("Clicked combobox to open dropdown")

        get_user_page.wait_for_timeout(500)

        option_10 = get_user_page.get_by_text("10", exact=True)
        option_10.click()
        logging.info("Selected 10 items per page")

        get_user_page.wait_for_timeout(1000)

        # # Verify the rows have updated
        updated_rows = settings_page.get_telemetry_endpoints_rows()
        updated_rows_count = updated_rows.count()
        assert (
            updated_rows_count <= 10
        ), f"Expected at most 10 rows, found {updated_rows_count}"
        logging.info(
            f"After changing to 10 items per page, rows shown: {updated_rows_count}"
        )

        input_element = get_user_page.locator(
            "//h4[text()='Telemetry Endpoints']/../../div//input[@type='number' and @min='1' and @data-testid='kui-text-input-element']"
        )
        logging.info(f"Input element: {input_element.count()}")
        # assert go_to_input.is_visible(), "Go to input not found"
        # # Clear and enter page 3
        input_element.fill("")
        input_element.fill("2")
        input_element.press("Tab")
        get_user_page.wait_for_timeout(5000)
        active_button = get_user_page.locator("button.c-PJLV-blrJqR-isActive-true").filter(
            has_text="2"
        )
        logging.info(f"Active button: {active_button.count()}")
        assert active_button.is_visible(), "Active button not found"

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093801
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_telemetry_endpoint_list_page_with_admin_user(self, get_user_page: Page):
        """
        Test ID: 4093801
        Title: Verify telemetry endpoint list display for admin user

        Test steps:
        1. Login NGC with viewer user to Cloud Functions Page
        2. Enter Settings Page from left side navigation bar
        3. Go to the Telemetry Endpoints list page and check the "Telemetry Endpoints" title is shown
        4. Check the total endpoints count (nearby the "Filter" menu) is correct in the list page
        5. Check the search bar function works well in the list page
        6. Check the contents for Name, Provider, Telemetry Type, Protocol, Modified on fields are shown correctly
        7. Check the Refresh fields can work well

        Expected results:
        - Telemetry endpoints list should be displayed correctly
        - Search functionality should work as expected
        - All required columns should be visible
        - Refresh functionality should update the displayed data
        """
        # Create page objects
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page
        settings_page.navigate()
        settings_page.verify_on_settings_page()
        logging.info("Successfully navigated to Settings page")

        # Step 3: Scroll to telemetry endpoints section and verify title
        settings_page.scroll_to_telemetry_endpoints()

        # Verify "Telemetry Endpoints" title is visible
        telemetry_title = get_user_page.get_by_role("heading", name="Telemetry Endpoints")
        assert telemetry_title.is_visible(), "Telemetry Endpoints title not found"
        logging.info("Verified Telemetry Endpoints title is visible")

        # Wait for telemetry table to load
        settings_page.wait_for_telemetry_endpoints_loading()
        # Step 4: Check the total endpoints count
        total_endpoints = settings_page.get_telemetry_endpoints_count()
        logging.info(f"Total telemetry endpoints: {total_endpoints}")
        assert total_endpoints >= 0, "Failed to get telemetry endpoints count"

        test_endpoint = "byoo_auto_telemetry_dailyrun"
        # Search for the endpoint
        settings_page.search_for_endpoint(test_endpoint)
        logging.info(f"Searched for endpoint: {test_endpoint}")

        # Wait for search results
        settings_page.wait_for_telemetry_endpoints_loading()

        # Get the filtered count
        filtered_count = settings_page.get_telemetry_endpoints_count()
        logging.info(f"Search results count: {filtered_count}")

        # Verify search functionality worked correctly
        if filtered_count > 0:
            # Verify search found the right endpoint
            assert settings_page.verify_endpoint_exists(
                test_endpoint
            ), f"Endpoint {test_endpoint} not found in search results"
            logging.info(f"Successfully found endpoint {test_endpoint} using search")
        else:
            assert False, f"No results found for search term '{test_endpoint}', trying a partial search"
        settings_page.clear_endpoint_search()

        # Step 6: Verify column headers are present and correct
        expected_columns = [
            "Name",
            "Provider",
            "Telemetry Type",
            "Protocol",
            "Created On",
            "Actions",
        ]
        verified = settings_page.verify_telemetry_table_columns_robust(expected_columns)
        assert (
            verified
        ), "Failed to verify all expected columns in the telemetry endpoints table"

        endpoints_count = settings_page.get_telemetry_endpoints_count()
        if endpoints_count > 0:
            # Click the actions button on the first endpoint
            settings_page.click_endpoint_actions_button()
            # Verify actions menu appears
            assert settings_page.page.locator(
                "text=Update Key"
            ).is_visible(), "Actions menu did not appear"
            # Press Escape to dismiss menu instead of clicking elsewhere
            get_user_page.keyboard.press("Escape")

        logging.info(
            "Successfully verified NVCF Admin has permissions to manage telemetry endpoints"
        )

        # Step 7: Test refresh functionality
        refresh_button = get_user_page.locator(
            "//h4[text()='Telemetry Endpoints']/../..//button[@aria-label='Refresh']"
        )
        assert refresh_button.is_visible(), "Refresh button not found"

        # Get current count before refresh
        pre_refresh_count = settings_page.get_telemetry_endpoints_count()

        # Click refresh
        refresh_button.click()
        logging.info("Clicked refresh button")

        # Wait for refresh to complete
        settings_page.wait_for_telemetry_endpoints_loading()

        # Get count after refresh
        post_refresh_count = settings_page.get_telemetry_endpoints_count()

        # Verify refresh worked - at minimum, the table should reload without errors
        logging.info(
            f"Endpoint count before refresh: {pre_refresh_count}, after refresh: {post_refresh_count}"
        )
        assert post_refresh_count >= 0, "Failed to get endpoints count after refresh"

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093802
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_VIEWER_USER, indirect=True)
    def test_telemetry_endpoint_list_page_with_viewer_user(self, get_user_page: Page):
        """
        Test ID: 4093802
        Title: Verify telemetry endpoint list display for viewer user

        Test steps:
        1. Login NGC with viewer user to Cloud Functions Page
        2. Enter Settings Page from left side navigation bar
        3. Go to the Telemetry Endpoints list page and check the "Telemetry Endpoints" title is shown
        4. Check the total endpoints count (nearby the "Filter" menu) is correct in the list page
        5. Check the search bar function works well in the list page
        6. Check the contents for Name, Provider, Telemetry Type, Protocol, Modified on fields are shown correctly
        7. Check the Refresh fields can work well

        Expected results:
        - Telemetry endpoints list should be displayed correctly
        - Search functionality should work as expected
        - All required columns should be visible
        - Refresh functionality should update the displayed data
        """
        # Create page objects
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page
        settings_page.navigate()
        settings_page.verify_on_settings_page()
        logging.info("Successfully navigated to Settings page")

        # Step 3: Scroll to telemetry endpoints section and verify title
        settings_page.scroll_to_telemetry_endpoints()

        # Verify "Telemetry Endpoints" title is visible
        telemetry_title = get_user_page.get_by_role("heading", name="Telemetry Endpoints")
        assert telemetry_title.is_visible(), "Telemetry Endpoints title not found"
        logging.info("Verified Telemetry Endpoints title is visible")

        # Wait for telemetry table to load
        settings_page.wait_for_telemetry_endpoints_loading()
        # Step 4: Check the total endpoints count
        total_endpoints = settings_page.get_telemetry_endpoints_count()
        logging.info(f"Total telemetry endpoints: {total_endpoints}")
        assert total_endpoints >= 0, "Failed to get telemetry endpoints count"

        test_endpoint = "byoo_auto_telemetry_dailyrun"
        # Search for the endpoint
        settings_page.search_for_endpoint(test_endpoint)
        logging.info(f"Searched for endpoint: {test_endpoint}")

        # Wait for search results
        settings_page.wait_for_telemetry_endpoints_loading()

        # Get the filtered count
        filtered_count = settings_page.get_telemetry_endpoints_count()
        logging.info(f"Search results count: {filtered_count}")

        # Verify search functionality worked correctly
        if filtered_count > 0:
            # Verify search found the right endpoint
            assert settings_page.verify_endpoint_exists(
                test_endpoint
            ), f"Endpoint {test_endpoint} not found in search results"
            logging.info(f"Successfully found endpoint {test_endpoint} using search")
        else:
            assert False, f"No results found for search term '{test_endpoint}', trying a partial search"
        settings_page.clear_endpoint_search()

        # Step 6: Verify column headers are present and correct
        expected_columns = [
            "Name",
            "Provider",
            "Telemetry Type",
            "Protocol",
            "Created On",
        ]
        # Note: "Actions" column should NOT be in the expected columns for viewer
        verified = settings_page.verify_telemetry_table_columns_robust(expected_columns)
        assert (
            verified
        ), "Failed to verify expected columns in the telemetry endpoints table"

        # # Also verify that there is no Actions column
        actions_header = settings_page.page.locator("th").filter(has_text="Actions")
        assert (
            not actions_header.is_visible()
        ), "Actions column should not be visible for NVCF Viewer user"

        # Step 7: Test refresh functionality
        refresh_button = get_user_page.get_by_role("button", name="Refresh")
        assert refresh_button.is_visible(), "Refresh button not found"

        # Get current count before refresh
        pre_refresh_count = settings_page.get_telemetry_endpoints_count()

        # Click refresh
        refresh_button.click()
        logging.info("Clicked refresh button")

        # Wait for refresh to complete
        settings_page.wait_for_telemetry_endpoints_loading()

        # Get count after refresh
        post_refresh_count = settings_page.get_telemetry_endpoints_count()

        # Verify refresh worked - at minimum, the table should reload without errors
        logging.info(
            f"Endpoint count before refresh: {pre_refresh_count}, after refresh: {post_refresh_count}"
        )
        assert post_refresh_count >= 0, "Failed to get endpoints count after refresh"

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093789
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", ["nvcf_admin"], indirect=True)
    def test_validate_telemetry_sort_function(self, get_user_page: Page):
        """
        Test ID: 4093789
        Title: Validate sort function in telemetry endpoint list page NVCF Admin User

        Test steps:
        1. Login NGC with NVCF Admin User
        2. Enter Settings Page from left side navigation bar to go to the telemetry endpoint list page
        3. Click the "Name" tab of telemetry endpoints table, check the endpoints should be sorted by name
        4. Click the "Provider" tab of telemetry endpoints table, check the endpoints should be sorted by provider
        5. Click the "Modified On" tab of telemetry endpoints table, check the endpoints should be sorted by modified time
        """
        # Create page objects
        settings_page = SettingsPage(get_user_page)

        # Step 1 & 2: Navigate to settings page
        settings_page.navigate()

        # Scroll to telemetry endpoints section
        settings_page.scroll_to_telemetry_endpoints()

        # Get initial count for validation
        endpoints_count = settings_page.get_telemetry_endpoints_count()
        logging.info(f"Found {endpoints_count} telemetry endpoints")

        # Skip test if no endpoints are available
        if endpoints_count == 0:
            pytest.skip("No telemetry endpoints found in the table, skipping test")

        # Step 3: Test sorting by Name column
        name_result = settings_page.sort_telemetry_column("Name")
        assert name_result[
            "success"
        ], f"Name column sorting test failed: {name_result['error']}"
        assert name_result["asc_verified"], "Name column ascending sort verification failed"
        assert name_result[
            "desc_verified"
        ], "Name column descending sort verification failed"

        # Step 4: Test sorting by Provider column
        provider_result = settings_page.sort_telemetry_column("Provider")
        assert provider_result[
            "success"
        ], f"Provider column sorting test failed: {provider_result['error']}"
        assert provider_result[
            "asc_verified"
        ], "Provider column ascending sort verification failed"
        assert provider_result[
            "desc_verified"
        ], "Provider column descending sort verification failed"

        # Step 5: Test sorting by Created On column (Modified On in the template)
        date_result = settings_page.sort_telemetry_column("Created On")
        assert date_result[
            "success"
        ], f"Created On column sorting test failed: {date_result['error']}"
        assert date_result[
            "asc_verified"
        ], "Created On column ascending sort verification failed"
        assert date_result[
            "desc_verified"
        ], "Created On column descending sort verification failed"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.T4990995
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_select_telemetry_endpoint_for_creating_container_function_and_deploy_from_function_list_page(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
        session_nvcf_admin_sak,
    ):
        """
        Test selecting telemetry endpoint for create container function and deploy from function list page
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info(
            "Start creation of source container function for deploying from function list page."
        )

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_logs=func_info["telemetry_logs"],
            telemetry_metrics=func_info["telemetry_metrics"],
        )

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        function_list_page = FunctionListPage(get_user_page)
        function_list_page.navigate_to_page()
        if_pre_deployed = function_list_page.FunctionPagination.deploy_function_version_pre(
            func_name=source_func_name,
            func_id=func_id,
            func_version=func_ver_id,
        )
        assert if_pre_deployed, "Failed to deploy function version"
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            wait_until_deployed=True,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(
            func_name=source_func_name, func_id=func_id, vers_id=func_ver_id
        )

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == [func_info["telemetry_metrics"]]

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.T4990998
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_select_telemetry_endpoint_for_creating_helm_chart_function_and_deploy_from_function_version_details_page(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
        session_nvcf_admin_sak,
    ):
        """
        Test selecting telemetry endpoint for create helm chart function and deploy from function version details page
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings[
                "helm_chart_function_telemetry_endpoint_test_stg"
            ]
        else:
            func_info = test_data_settings["helm_chart_function_telemetry_endpoint_test"]
        logging.info(
            "Start creation of source helm chart function for deploying from function version details page."
        )

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_helm_chart_function(
            source_func_name,
            func_info["Helm_Chart"],
            func_info["Helm_Chart_Version"],
            func_info["Helm_Chart_Service_Name"],
            func_info["inference_protocol"],
            inference_endpoint=func_info["inference_endpoint"],
            health_endpoint=func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            health_port=func_info["health_port"],
            telemetry_logs=func_info["telemetry_logs"],
            telemetry_metrics=func_info["telemetry_metrics"],
        )

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        DeployVersionBtn = "//button[text()='Deploy Version']"
        func_ver_detail_page.page.locator(DeployVersionBtn).click()
        func_ver_detail_page.page.wait_for_timeout(2000)
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            wait_until_deployed=True,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(
            func_name=source_func_name, func_id=func_id, vers_id=func_ver_id
        )

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == [func_info["telemetry_metrics"]]

    @pytest.mark.CloudFunctions
    @pytest.mark.T4093807
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_verify_no_telemetry_info_in_manage_secrets_for_byoo_function(
        self, get_user_page: Page, test_data_settings: dict
    ):
        """
        Test ID: 4093807
        Title: Verify no telemetry information is displayed in Manage Secrets for byoo function

        Test steps:
        1. Login NGC with NVCF Admin to Cloud Functions Page
        2. Enter function list page
        3. Select one existing cloud function which has BYO Observability configuration
        4. Click the Actions menu (3 dots menu) of the matched cloud function
        5. Select and click the "Manage Secrets" menu
        6. In the "Manage Secrets" window, verify that no telemetry relevant info is displayed

        Expected Results:
        - No telemetry information should be displayed in the Manage Secrets window for inactive function
        """
        logging.info(
            "Starting test to verify no telemetry info in Manage Secrets for inactive function"
        )
        # Step 1 & 2: Navigate to function list page
        # Step 3: Find the target function in the list
        if CURRENT_ENV == "staging":
            test_data = test_data_settings[
                "inactive_function_version_with_telemetry_endpoint_stg"
            ]["telemetry_export_test"]
        else:
            test_data = test_data_settings[
                "inactive_function_version_with_telemetry_endpoint"
            ]["telemetry_export_test"]
        function_name = test_data["function_name"]
        # Step 4 &5 Navigate to Function List page and check the secret info of the manage secrets
        func_page = FunctionListPage(get_user_page)
        func_page.navigate_to_page()
        func_page.FunctionPagination.search_function_exact_match(function_name)
        func_page.FunctionPagination.check_secret_info_in_manage_secrets_page(function_name)

        # Step 6: Check "Manage Secrets" menu option
        manage_secrets_dialog = get_user_page.get_by_role("dialog").filter(
            has_text="Manage Secrets"
        )
        manage_secrets_dialog.wait_for(state="visible", timeout=5000)
        assert manage_secrets_dialog.is_visible(), "Manage Secrets dialog did not appear"

        telemetry_elements = manage_secrets_dialog.get_by_text(
            "Telemetry", exact=False
        ).all()
        endpoints_elements = manage_secrets_dialog.get_by_text(
            "Endpoint", exact=False
        ).all()

        if len(telemetry_elements) > 0 or len(endpoints_elements) > 0:
            logging.warning(
                f"Found {len(telemetry_elements)} telemetry elements and {len(endpoints_elements)} endpoint elements"
            )
            telemetry_info_found = True
        else:
            telemetry_info_found = False

        # Assert that no telemetry info was found
        assert (
            not telemetry_info_found
        ), "Telemetry information was found in Manage Secrets dialog for inactive function"
        logging.info(
            "Verified no telemetry information is displayed in Manage Secrets dialog"
        )

        # Close the dialog
        close_button = manage_secrets_dialog.get_by_role("button", name="Cancel")
        close_button.click()
        logging.info("Closed Manage Secrets dialog")

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.T4093809
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_with_invalid_telemetry_endpoint_for_creating_function_then_deploy(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
        session_nvcf_admin_sak,
    ):
        """
        Test selecting telemetry endpoint for create container function and deploy to GFN backend
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings[
                "container_function_with_invalid_telemetry_endpoint_test_stg"
            ]
        else:
            func_info = test_data_settings[
                "container_function_with_invalid_telemetry_endpoint_test"
            ]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_logs=func_info["invalid_telemetry_logs"],
            telemetry_metrics=func_info["invalid_telemetry_metrics"],
            is_deploy=True,
        )
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        func_id, func_ver_id = deployments_create_page.deploy_function_version(
            get_user_page,
            None,
            None,
            wait_until_deployed=True,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(
            func_name=source_func_name, func_id=func_id, vers_id=func_ver_id
        )

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["invalid_telemetry_logs"]]
        assert list(metrics_endpoint.values()) == [func_info["invalid_telemetry_metrics"]]

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.T4093810
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_clone_container_function_with_telemetry_endpoint_configuration(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
        session_nvcf_admin_sak,
    ):
        """
        Test cloning container function with telemetry endpoint configuration
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_logs=func_info["telemetry_logs"],
            telemetry_metrics=func_info["telemetry_metrics"],
        )
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == [func_info["telemetry_metrics"]]

        # Clone function
        func_page = FunctionListPage(get_user_page)
        func_page.navigate_to_page()
        func_page.FunctionPagination.search_function_exact_match(source_func_name)
        func_page.FunctionPagination.clone_function(source_func_name)

        # Fill in clone details and create clone
        base_func_info = {
            "func_name": source_func_name,
            "func_desc": source_func_name,
            "tags": func_info["tags"],
            "container": func_info["container"],
            "tag": "latest",
            "inference_protocol": func_info["inference_protocol"],
            "inference_port": func_info["inference_port"],
            "inference_endpoint": func_info["inference_endpoint"],
            "health_endpoint": func_info["health_endpoint"],
            "health_port": func_info["health_port"],
        }

        clone_function_page = CloneFunctionPage(get_user_page)
        clone_function_page.Form.verify_clone_details(base_func_info)
        clone_function_page.Form.clone_container_function()

        # In Function Version Detail page, verify the cloned function name
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        get_user_page.wait_for_timeout(2000)
        actual_func_name = func_ver_detail_page.NavigationBar.get_function_name()
        logging.info(f"Actual function name: {actual_func_name}")
        logging.info(f"source_func_name: {source_func_name}")

        assert (
            actual_func_name == source_func_name + "-clone"
        ), f"Actual function name '{actual_func_name}' is not as expected."

        # Gather Function ID and Function Version ID for teardown
        get_user_page.wait_for_timeout(2000)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]

        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # Verify the cloned function has the same telemetry endpoint configuration
        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == [func_info["telemetry_metrics"]]

        # deploy the cloned function
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            wait_until_deployed=True,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.T4093811
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_clone_container_function_and_reeidt_the_telemetry_configuration(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
        session_nvcf_admin_sak,
    ):
        """
        Test cloning container function and reedit the telemetry configuration
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_logs=func_info["telemetry_logs"],
            telemetry_metrics=func_info["telemetry_metrics"],
        )
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == [func_info["telemetry_metrics"]]

        # Clone function
        func_page = FunctionListPage(get_user_page)
        func_page.navigate_to_page()
        func_page.FunctionPagination.search_function_exact_match(source_func_name)
        func_page.FunctionPagination.clone_function(source_func_name)

        # Fill in clone details and create clone
        base_func_info = {
            "func_name": source_func_name,
            "func_desc": source_func_name,
            "tags": func_info["tags"],
            "container": func_info["container"],
            "tag": "latest",
            "inference_protocol": func_info["inference_protocol"],
            "inference_port": func_info["inference_port"],
            "inference_endpoint": func_info["inference_endpoint"],
            "health_endpoint": func_info["health_endpoint"],
            "health_port": func_info["health_port"],
        }

        clone_function_page = CloneFunctionPage(get_user_page)
        clone_function_page.Form.verify_clone_details(base_func_info)
        clone_function_page.Form.clone_container_function(
            telemetry_logs="byoo_auto_datadog_alter_dailyrun (Datadog)",
            telemetry_metrics="byoo_auto_datadog_alter_dailyrun (Datadog)",
        )

        # In Function Version Detail page, verify the cloned function name
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        get_user_page.wait_for_timeout(2000)
        actual_func_name = func_ver_detail_page.NavigationBar.get_function_name()
        logging.info(f"Actual function name: {actual_func_name}")
        logging.info(f"source_func_name: {source_func_name}")

        assert (
            actual_func_name == source_func_name + "-clone"
        ), f"Actual function name '{actual_func_name}' is not as expected."

        # Gather Function ID and Function Version ID for teardown
        get_user_page.wait_for_timeout(2000)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]

        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # Verify the cloned function has the same telemetry endpoint configuration
        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [
            "byoo_auto_datadog_alter_dailyrun (Datadog)"
        ]
        assert list(metrics_endpoint.values()) == [
            "byoo_auto_datadog_alter_dailyrun (Datadog)"
        ]

        # deploy the cloned function
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            wait_until_deployed=True,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T4093796
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_kratos_thanos_telemetry_endpoint_org_admin(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["kratos_thanos"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["private_key"],
            client_certificate=telemetry_data["client_certificate"],
            protocol=telemetry_data["protocol"],
            telemetry_type={"metrics": True},
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T4093819
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_existing_kratos_thanos_nvcf_telemetry_endpoint_credential_with_org_admin(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["kratos_thanos"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["private_key"],
            client_certificate=telemetry_data["client_certificate"],
            protocol=telemetry_data["protocol"],
            telemetry_type={"metrics": True},
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

        update_data = {
            "private_key": "update_key",
            "client_certificate": "update_client_certificate",
        }
        telemetry_page.update_endpoint(endpoint_name, update_data)

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138282
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_documentation_link_for_setting_up_telemetry_endpoints(
        self,
        get_user_page: Page,
    ):
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()

        # Click the documentation link
        with telemetry_page.page.context.expect_page() as new_page_info:
            telemetry_page.page.locator("//a[text()='View documentation']").click()
        new_page = new_page_info.value
        new_page.wait_for_selector("//h1[text()='Observability Guide']", state="visible")
        new_page.bring_to_front()

        new_url = new_page.url
        logging.info(f"New page URL: {new_url}")
        expected_path = "cloud-function/observability.html"
        assert (
            expected_path in new_url
        ), f"Expected URL path '{expected_path}' not found in '{new_url}'"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138283
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_anchor_link_of_telemetry_endpoint_from_function_creation_page(
        self,
        get_user_page: Page,
    ):
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.page.locator(
            create_func_page.Form.func_type_container_option
        ).click()
        create_func_page.page.wait_for_selector(
            "//span[text()='Basic Details']", state="visible"
        )

        # check the anchor link of telemetry endpoint
        # Click the documentation link
        with create_func_page.page.context.expect_page() as new_page_info:
            create_func_page.page.locator("//a[text()='Telemetry Endpoints']").click()
        new_page = new_page_info.value
        new_page.wait_for_load_state("networkidle")
        new_page.wait_for_load_state("domcontentloaded")
        new_page.bring_to_front()

        new_url = new_page.url
        logging.info(f"New page URL: {new_url}")
        expected_path = "settings#telemetry-section"
        assert (
            expected_path in new_url
        ), f"Expected URL path '{expected_path}' not found in '{new_url}'"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.********
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_table_show_5_row_items_by_default_in_telemetry_endpoint_list_page(
        self,
        get_user_page: Page,
    ):
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        default_row_locator = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//div[@data-testid='kui-select-root']"
        default_row = telemetry_page.page.locator(default_row_locator).text_content()
        assert int(default_row) == 5, f"Expected 5 rows, but got {int(default_row)}"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138286
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_telemetry_configuration_information_for_logs_and_metrics_from_active_function_version_details_page(
        self,
        get_user_page: Page,
        test_data_settings,
    ):
        # Get the function and endpoint data from test_data_settings.yaml
        if CURRENT_ENV == "staging":
            test_data = test_data_settings[
                "active_function_version_with_telemetry_endpoint_stg"
            ]["telemetry_export_test"]
        else:
            test_data = test_data_settings[
                "active_function_version_with_telemetry_endpoint"
            ]["telemetry_export_test"]
        function_id = test_data["function_id"]
        function_name = test_data["function_name"]
        function_ver_id = test_data["version_id"]

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.switch_tab(
            "function details",
            func_name=function_name,
            func_id=function_id,
            vers_id=function_ver_id,
        )
        assert func_ver_detail_page.page.locator(
            "//h4[text()='Telemetry Configuration']"
        ).is_visible(), "Telemetry Configuration section is not visible"

        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [test_data["expected_logs_endpoint"]]
        assert list(metrics_endpoint.values()) == [test_data["expected_metrics_endpoint"]]

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138287
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_telemetry_configuration_information_for_logs_and_metrics_from_inactive_function_version_details_page(
        self,
        get_user_page: Page,
        test_data_settings,
    ):
        # Get the function and endpoint data from test_data_settings.yaml
        if CURRENT_ENV == "staging":
            test_data = test_data_settings[
                "inactive_function_version_with_telemetry_endpoint_stg"
            ]["telemetry_export_test"]
        else:
            test_data = test_data_settings[
                "inactive_function_version_with_telemetry_endpoint"
            ]["telemetry_export_test"]
        function_id = test_data["function_id"]
        function_name = test_data["function_name"]
        function_ver_id = test_data["version_id"]

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.switch_tab(
            "function details",
            func_name=function_name,
            func_id=function_id,
            vers_id=function_ver_id,
        )
        assert func_ver_detail_page.page.locator(
            "//h4[text()='Telemetry Configuration']"
        ).is_visible(), "Telemetry Configuration section is not visible"

        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [test_data["expected_logs_endpoint"]]
        assert list(metrics_endpoint.values()) == [test_data["expected_metrics_endpoint"]]

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138288
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_telemetry_configuration_information_for_function_using_logs_only_telemetry_type(
        self,
        get_user_page: Page,
        test_data_settings,
    ):
        # Get the function and endpoint data from test_data_settings.yaml
        if CURRENT_ENV == "staging":
            test_data = test_data_settings[
                "active_function_version_with_only_logs_telemetry_endpoint_stg"
            ]["telemetry_export_test"]
        else:
            test_data = test_data_settings[
                "active_function_version_with_only_logs_telemetry_endpoint"
            ]["telemetry_export_test"]
        function_id = test_data["function_id"]
        function_name = test_data["function_name"]
        function_ver_id = test_data["version_id"]

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.switch_tab(
            "function details",
            func_name=function_name,
            func_id=function_id,
            vers_id=function_ver_id,
        )
        assert func_ver_detail_page.page.locator(
            "//h4[text()='Telemetry Configuration']"
        ).is_visible(), "Telemetry Configuration section is not visible"

        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [test_data["expected_logs_endpoint"]]
        assert list(metrics_endpoint.values()) == ["—"]

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138289
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_telemetry_configuration_information_for_function_using_metrics_only_telemetry_type(
        self,
        get_user_page: Page,
        test_data_settings,
    ):
        # Get the function and endpoint data from test_data_settings.yaml
        if CURRENT_ENV == "staging":
            test_data = test_data_settings[
                "active_function_version_with_only_metrics_telemetry_endpoint_stg"
            ]["telemetry_export_test"]
        else:
            test_data = test_data_settings[
                "active_function_version_with_only_metrics_telemetry_endpoint"
            ]["telemetry_export_test"]
        function_id = test_data["function_id"]
        function_name = test_data["function_name"]
        function_ver_id = test_data["version_id"]

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.switch_tab(
            "function details",
            func_name=function_name,
            func_id=function_id,
            vers_id=function_ver_id,
        )
        assert func_ver_detail_page.page.locator(
            "//h4[text()='Telemetry Configuration']"
        ).is_visible(), "Telemetry Configuration section is not visible"

        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == ["—"]
        assert list(metrics_endpoint.values()) == [test_data["expected_metrics_endpoint"]]

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138293
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_terminology_for_key_field_when_creating_datadog_telemetry_endpoint(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["datadog"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()

        # check the terminology for key field
        telemetry_page.page.wait_for_selector(
            "//h2[text()='Add Endpoint']", state="visible"
        )
        provider_dropdown = telemetry_page.page.get_by_role("combobox").filter(
            has_text="Select a provider"
        )
        provider_dropdown.click()
        provider_option = telemetry_page.page.get_by_role(
            "option", name=telemetry_data["provider"]
        )
        provider_option.click()
        telemetry_page.page.wait_for_selector("//label[text()='Key']", state="visible")
        assert telemetry_page.page.locator(
            "//span[text()='Datadog refers to this as \"API Key\"']"
        ).is_visible(), "Key field placeholder is not correct"

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["key"],
            protocol=telemetry_data["protocol"],
            telemetry_type=telemetry_data["telemetry_types"],
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138294
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_terminology_for_key_field_when_creating_grafana_telemetry_endpoint(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
        running_config,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["grafana"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()

        # check the terminology for key field
        telemetry_page.page.wait_for_selector(
            "//h2[text()='Add Endpoint']", state="visible"
        )
        provider_dropdown = telemetry_page.page.get_by_role("combobox").filter(
            has_text="Select a provider"
        )
        provider_dropdown.click()
        provider_option = telemetry_page.page.get_by_role(
            "option", name=telemetry_data["provider"]
        )
        provider_option.click()
        telemetry_page.page.wait_for_selector("//label[text()='Key']", state="visible")
        assert telemetry_page.page.locator(
            "//span[text()='Grafana Cloud refers to this as \"API Token\"']"
        ).is_visible(), "Key field placeholder is not correct"

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            instance_id=telemetry_data["instance_id"],
            endpoint_url=telemetry_data["endpoint_url"],
            telemetry_type={"logs": True, "metrics": True},
            key=running_config["grafana_apikey"]["api_key"],
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138295
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_functions_number_associated_with_specific_telemetry_endpoint(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
        function_version_teardown,
        running_config,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["grafana"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            instance_id=telemetry_data["instance_id"],
            endpoint_url=telemetry_data["endpoint_url"],
            telemetry_type={"logs": True, "metrics": True},
            key=running_config["grafana_apikey"]["api_key"],
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"
        created_endpoint_name = endpoint_name + " (Grafana Cloud)"

        # create a new function with the same telemetry endpoint
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_logs=created_endpoint_name,
            telemetry_metrics=created_endpoint_name,
        )
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # check the functions number associated with the telemetry endpoint
        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()
        telemetry_page.verify_telemetry_section_visible()
        telemetry_page.search_endpoint(endpoint_name)
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"
        functions_number = telemetry_page.page.locator(
            "//td[@headers='data-view-header-associatedEntities']"
        ).text_content()
        assert functions_number == "1", f"Expected 1 function, but got {functions_number}"

        refresh_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//button[text()=' Refresh']"
        telemetry_page.page.locator(refresh_button).click()
        telemetry_page.page.wait_for_selector(refresh_button, state="visible")
        telemetry_page.page.locator(
            "//td[@headers='data-view-header-associatedEntities']//span"
        ).click()
        telemetry_page.page.wait_for_selector(
            "//div[contains(text(), 'Associated Functions')]", state="visible"
        )

        real_funcid = telemetry_page.page.locator(
            "//td[@headers='data-view-header-id']"
        ).text_content()
        assert (
            real_funcid == func_ver_id
        ), f"Expected function ID {func_ver_id}, but got {real_funcid}"
        telemetry_page.page.locator("//button[text()='Close']").click()

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138297
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_associated_functions_list_of_specific_telemetry_endpoint_can_be_downloaded_as_csv(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
        function_version_teardown,
        running_config,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["grafana"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            instance_id=telemetry_data["instance_id"],
            endpoint_url=telemetry_data["endpoint_url"],
            telemetry_type={"logs": True, "metrics": True},
            key=running_config["grafana_apikey"]["api_key"],
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"
        created_endpoint_name = endpoint_name + " (Grafana Cloud)"

        # create a new function with the same telemetry endpoint
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_logs=created_endpoint_name,
            telemetry_metrics=created_endpoint_name,
        )
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # check the functions number associated with the telemetry endpoint
        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()
        telemetry_page.verify_telemetry_section_visible()
        telemetry_page.search_endpoint(endpoint_name)
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"
        functions_number = telemetry_page.page.locator(
            "//td[@headers='data-view-header-associatedEntities']"
        ).text_content()
        assert functions_number == "1", f"Expected 1 function, but got {functions_number}"

        refresh_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//button[text()=' Refresh']"
        telemetry_page.page.locator(refresh_button).click()
        telemetry_page.page.wait_for_selector(refresh_button, state="visible")
        telemetry_page.page.locator(
            "//td[@headers='data-view-header-associatedEntities']//span"
        ).click()
        telemetry_page.page.wait_for_selector(
            "//div[contains(text(), 'Associated Functions')]", state="visible"
        )

        with telemetry_page.page.expect_download() as download_info:
            telemetry_page.page.locator("//button[text()='Save as CSV']").click()
            download = download_info.value
            logging.info(f"download.suggested_filename: {download.suggested_filename}")
            assert download.suggested_filename == f"{endpoint_name}-associated-entities.csv"

        telemetry_page.page.locator("//button[text()='Close']").click()

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138298
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_associated_function_task_table_list_page_modal(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
        function_version_teardown,
        running_config,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["grafana"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            instance_id=telemetry_data["instance_id"],
            endpoint_url=telemetry_data["endpoint_url"],
            telemetry_type={"logs": True, "metrics": True},
            key=running_config["grafana_apikey"]["api_key"],
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"
        created_endpoint_name = endpoint_name + " (Grafana Cloud)"

        # create a new function with the same telemetry endpoint
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_logs=created_endpoint_name,
            telemetry_metrics=created_endpoint_name,
        )
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # check the functions number associated with the telemetry endpoint
        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()
        telemetry_page.verify_telemetry_section_visible()
        telemetry_page.search_endpoint(endpoint_name)
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"
        functions_number = telemetry_page.page.locator(
            "//td[@headers='data-view-header-associatedEntities']"
        ).text_content()
        assert functions_number == "1", f"Expected 1 function, but got {functions_number}"

        refresh_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//button[text()=' Refresh']"
        telemetry_page.page.locator(refresh_button).click()
        telemetry_page.page.wait_for_selector(refresh_button, state="visible")
        telemetry_page.page.locator(
            "//td[@headers='data-view-header-associatedEntities']//span"
        ).click()
        telemetry_page.page.wait_for_selector(
            "//div[contains(text(), 'Associated Functions')]", state="visible"
        )

        # check the endpoint name and provider
        real_endpoint_name = telemetry_page.page.locator(
            "//span[text()='Endpoint Name']/following-sibling::span"
        ).text_content()
        assert (
            real_endpoint_name == endpoint_name
        ), f"Expected endpoint name {created_endpoint_name}, but got {endpoint_name}"
        real_provider = telemetry_page.page.locator(
            "//span[text()='Provider']/following-sibling::span"
        ).text_content()
        assert (
            real_provider == telemetry_data["provider"]
        ), f"Expected provider {telemetry_data['provider']}, but got {real_provider}"

        # check tabs
        assert telemetry_page.page.locator(
            "//div[@role='dialog']//button[text()='Name']"
        ).is_visible(), "Name tab is not visible"
        assert telemetry_page.page.locator(
            "//div[@role='dialog']//button[text()='Entity Type']"
        ).is_visible(), "Entity tab is not visible"
        assert telemetry_page.page.locator(
            "//div[@role='dialog']//th[text()='Version/Task ID']"
        ).is_visible(), "Version/Task ID tab is not visible"
        assert telemetry_page.page.locator(
            "//div[@role='dialog']//th[text()='Telemetry Type']"
        ).is_visible(), "Telemetry Type tab is not visible"

        telemetry_page.page.locator("//button[text()='Close']").click()

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138299
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_associated_function_task_table_list_pagination(
        self,
        get_user_page: Page,
    ):
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        check_telemetry = "byoo_auto_telemetry_dailyrun"
        # Search for the endpoint
        telemetry_page.search_endpoint(check_telemetry)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            check_telemetry
        ), f"Endpoint {check_telemetry} not found in table"

        refresh_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//button[text()=' Refresh']"
        telemetry_page.page.locator(refresh_button).click()
        telemetry_page.page.wait_for_selector(refresh_button, state="visible")
        telemetry_page.page.locator(
            f"//td[@headers='data-view-header-name'][text()='{check_telemetry}']/following-sibling::td[@headers='data-view-header-associatedEntities']//span"
        ).nth(0).click()
        telemetry_page.page.wait_for_selector(
            "//div[contains(text(), 'Associated Functions')]", state="visible"
        )

        default_row_locator = "//div[@role='dialog']//span[text()='Show']/following-sibling::div[@data-testid='kui-select-root']"
        default_row = telemetry_page.page.locator(default_row_locator).text_content()
        assert int(default_row) == 5, f"Expected 5 rows, but got {int(default_row)}"

        # check can be sorted by name
        telemetry_page.page.locator("//div[@role='dialog']//button[text()='Name']").click()
        sorted_status = telemetry_page.page.locator(
            "//div[@role='dialog']//button[text()='Name']/*[name()='svg']"
        ).get_attribute("data-icon-name")
        name_list = []
        func_num = telemetry_page.page.locator(
            "//div[@role='dialog']//td[@data-testid='kui-table-data-cell' and @headers='data-view-header-name']"
        ).count()
        for i in range(func_num):
            element = telemetry_page.page.locator(
                "//div[@role='dialog']//td[@data-testid='kui-table-data-cell' and @headers='data-view-header-name']"
            ).nth(i)
            text = element.text_content()
            name_list.append(text)
            logging.info(f"{i+1}th name: {text}")
        logging.info(f"name_list: {name_list}")
        if sorted_status == "arrow-up":
            assert all(name_list[i] <= name_list[i + 1] for i in range(len(name_list) - 1))
        elif sorted_status == "arrow-down":
            assert all(name_list[i] >= name_list[i + 1] for i in range(len(name_list) - 1))
        else:
            assert False, "Name column is not sorted"

        # update the default row
        telemetry_page.page.locator(default_row_locator).click()
        telemetry_page.page.locator("//div[@role='option']//span[text()='10']").click()
        assert (
            telemetry_page.page.locator("//div[@role='dialog']//tbody//tr").count() <= 10
        ), "update the default row error"
        if telemetry_page.page.locator("//div[@role='dialog']//tbody//tr").count() == 10:
            # navigate to page by click the page number
            telemetry_page.page.locator("//div[@role='dialog']//button[text()='2']").click()
            telemetry_page.page.wait_for_timeout(1000)
            assert (
                telemetry_page.page.locator(
                    "//div[@role='dialog']//input[@data-testid='kui-text-input-element']"
                ).get_attribute("value")
                == "2"
            ), "navigate to page by click the page number error"

            # navigate to page by go to bar
            telemetry_page.page.locator(
                "//div[@role='dialog']//input[@data-testid='kui-text-input-element']"
            ).fill("1")
            telemetry_page.page.locator(
                "//div[contains(text(), 'Associated Functions')]"
            ).click()
            telemetry_page.page.wait_for_timeout(1000)
            assert (
                telemetry_page.page.locator(
                    "//div[@role='dialog']//input[@data-testid='kui-text-input-element']"
                ).get_attribute("value")
                == "1"
            ), "navigate to page by go to bar error"

        telemetry_page.page.locator("//button[text()='Close']").click()

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138300
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_scroll_area_of_associated_function_task_table_list_pagination(
        self,
        get_user_page: Page,
    ):
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        check_telemetry = "byoo_auto_telemetry_dailyrun"
        # Search for the endpoint
        telemetry_page.search_endpoint(check_telemetry)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            check_telemetry
        ), f"Endpoint {check_telemetry} not found in table"

        refresh_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//button[text()=' Refresh']"
        telemetry_page.page.locator(refresh_button).click()
        telemetry_page.page.wait_for_selector(refresh_button, state="visible")
        telemetry_page.page.locator(
            f"//td[@headers='data-view-header-name'][text()='{check_telemetry}']/following-sibling::td[@headers='data-view-header-associatedEntities']//span"
        ).nth(0).click()
        telemetry_page.page.wait_for_selector(
            "//div[contains(text(), 'Associated Functions')]", state="visible"
        )

        default_row_locator = "//div[@role='dialog']//span[text()='Show']/following-sibling::div[@data-testid='kui-select-root']"

        # update the default row
        telemetry_page.page.locator(default_row_locator).click()
        telemetry_page.page.locator("//div[@role='option']//span[text()='15']").click()
        assert (
            telemetry_page.page.locator("//div[@role='dialog']//tbody//tr").count() <= 15
        ), "update the default row error"

        # check the scroll area
        scroll_area = telemetry_page.page.locator(
            "//div[@role='dialog']//div[contains(@class, 'isolate')]"
        )
        is_scrollable = scroll_area.evaluate("(el) => el.scrollHeight >= el.clientHeight")
        logging.info(f"is_scrollable is {is_scrollable}")
        assert is_scrollable, "Scroll area is not scrollable"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138308
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_verify_all_shown_user_input_fields_must_be_completed_to_update_datadog_telemetry_endpoint_credential(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["datadog"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            protocol=telemetry_data["protocol"],
            telemetry_type={"logs": True, "metrics": True},
            key=telemetry_data["key"],
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

        update_data = {
            "key": "update_key",
        }
        telemetry_page.update_endpoint(endpoint_name, update_data)

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138309
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_verify_all_shown_user_input_fields_must_be_completed_to_update_grafana_cloud_telemetry_endpoint_credential(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
        running_config,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["grafana"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            instance_id=telemetry_data["instance_id"],
            endpoint_url=telemetry_data["endpoint_url"],
            telemetry_type={"logs": True, "metrics": True},
            key=running_config["grafana_apikey"]["api_key"],
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

        update_data = {
            "instance_id": "update_instance_id",
            "api_key": "update_key",
        }
        telemetry_page.update_endpoint(endpoint_name, update_data)

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138310
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_verify_all_shown_user_input_fields_must_be_completed_to_update_kratos_thanos_telemetry_endpoint_credential(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
        running_config,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["kratos_thanos"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["private_key"],
            client_certificate=telemetry_data["client_certificate"],
            protocol=telemetry_data["protocol"],
            telemetry_type={"metrics": True},
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

        update_data = {
            "private_key": "update_key",
            "client_certificate": "update_client_certificate",
        }
        telemetry_page.update_endpoint(endpoint_name, update_data)

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138311
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_verify_input_part_of_user_input_fields_then_update_telemetry_endpoint_credentials_negative(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
        running_config,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["grafana"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            instance_id=telemetry_data["instance_id"],
            endpoint_url=telemetry_data["endpoint_url"],
            telemetry_type={"logs": True, "metrics": True},
            key=running_config["grafana_apikey"]["api_key"],
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

        update_data = {
            "instance_id": "update_instance_id",
            "api_key": "update_key",
        }
        telemetry_page.wait_for_table_finished_loading()
        telemetry_page.search_for_endpoint(endpoint_name)
        refresh_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//button[text()=' Refresh']"
        telemetry_page.page.locator(refresh_button).click()
        telemetry_page.page.wait_for_selector(refresh_button, state="visible")
        action_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//td[@headers='data-view-header-row-actions']"
        telemetry_page.page.locator(action_button).click()
        update_button = "//div[@role='menuitem' and text()='Update Key']"
        telemetry_page.page.locator(update_button).click()
        telemetry_page.page.wait_for_selector(
            "//div[contains(normalize-space(), 'Update Key') and @role='dialog']",
            state="visible",
        )
        telemetry_page.page.locator(
            "//p[text()='Update the existing key for this telemetry endpoint configuration. Ensure all fields are completed to save your changes.']"
        ).is_visible()

        instance_id_input = telemetry_page.page.locator(
            "//input[@name='secret.value.instanceId']"
        )
        instance_id_input.fill(update_data["instance_id"])

        save_button = "//button[text()='Save Changes']"
        assert telemetry_page.page.locator(
            save_button
        ).is_disabled(), "Save button is not disabled"
        telemetry_page.page.locator("//button[text()='Cancel']").click()

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5148431
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_verify_the_content_after_hover_on_the_function_task_name_in_associated_functions_tasks_page(
        self,
        get_user_page: Page,
    ):
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        check_telemetry = "byoo_auto_telemetry_dailyrun"
        # Search for the endpoint
        telemetry_page.search_endpoint(check_telemetry)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            check_telemetry
        ), f"Endpoint {check_telemetry} not found in table"

        refresh_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//button[text()=' Refresh']"
        telemetry_page.page.locator(refresh_button).click()
        telemetry_page.page.wait_for_selector(refresh_button, state="visible")
        telemetry_page.page.locator(
            f"//td[@headers='data-view-header-name'][text()='{check_telemetry}']/following-sibling::td[@headers='data-view-header-associatedEntities']//span"
        ).nth(0).click()
        telemetry_page.page.wait_for_selector(
            "//div[contains(text(), 'Associated Functions')]", state="visible"
        )
        real_funcid = (
            telemetry_page.page.locator("//td[@headers='data-view-header-id']")
            .nth(0)
            .text_content()
        )
        telemetry_page.page.locator("//td[@headers='data-view-header-id']").nth(0).hover()
        tooltip_content = (
            telemetry_page.page.locator("//td[@headers='data-view-header-id']")
            .nth(0)
            .get_attribute("title")
        )
        logging.info(f"real_funcid: {real_funcid}")
        logging.info(f"tooltip_content: {tooltip_content}")
        assert real_funcid in tooltip_content, "Tooltip content is not correct"
        telemetry_page.page.locator("//button[text()='Close']").click()

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5106667
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_kratos_thanos_telemetry_endpoint_with_http_protocol(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["kratos_thanos"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["private_key"],
            client_certificate=telemetry_data["client_certificate"],
            protocol=telemetry_data["protocol"],
            telemetry_type={"metrics": True},
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5106668
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_kratos_thanos_telemetry_endpoint_with_grpc_protocol(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["kratos_thanos"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["private_key"],
            client_certificate=telemetry_data["client_certificate"],
            protocol="GRPC",
            telemetry_type={"metrics": True},
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5106669
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_kratos_thanos_telemetry_endpoint_from_telemetry_list_page(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["kratos_thanos"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["private_key"],
            client_certificate=telemetry_data["client_certificate"],
            protocol=telemetry_data["protocol"],
            telemetry_type={"metrics": True},
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

        assert telemetry_page.page.locator(
            "//h4[normalize-space(text())='Telemetry Endpoints']"
        ).is_visible()
        real_provider = (
            telemetry_page.page.locator("//td[@headers='data-view-header-provider']")
            .nth(0)
            .text_content()
        )
        assert real_provider == telemetry_data["provider"], "Provider is not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5106670
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_existing_kratos_thanos_telemetry_endpoint(
        self,
        get_user_page: Page,
        test_data_settings: dict,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["kratos_thanos"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["private_key"],
            client_certificate=telemetry_data["client_certificate"],
            protocol=telemetry_data["protocol"],
            telemetry_type={"metrics": True},
        )

        logging.info(f"Delete the telemetry {endpoint_name}  ...")
        telemetry_page.delete_endpoint(endpoint_name)

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5106671
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_existing_kratos_thanos_nvcf_telemetry_endpoint_credential(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["kratos_thanos"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["private_key"],
            client_certificate=telemetry_data["client_certificate"],
            protocol=telemetry_data["protocol"],
            telemetry_type={"metrics": True},
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

        update_data = {
            "private_key": "update_key",
            "client_certificate": "update_client_certificate",
        }
        telemetry_page.update_endpoint(endpoint_name, update_data)

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5106672
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_the_associated_kratos_thanos_telemetry_endpoint_of_the_cloud_function(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
        function_version_teardown,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["kratos_thanos"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["private_key"],
            client_certificate=telemetry_data["client_certificate"],
            protocol=telemetry_data["protocol"],
            telemetry_type={"metrics": True},
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"
        created_endpoint_name = endpoint_name + " (Kratos Thanos)"

        # create a new function with the same telemetry endpoint
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_metrics=created_endpoint_name,
        )
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # check the functions number associated with the telemetry endpoint
        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()
        telemetry_page.verify_telemetry_section_visible()
        telemetry_page.search_endpoint(endpoint_name)
        refresh_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//button[text()=' Refresh']"
        telemetry_page.page.locator(refresh_button).click()
        telemetry_page.page.wait_for_selector(refresh_button, state="visible")
        action_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//td[@headers='data-view-header-row-actions']"
        telemetry_page.page.locator(action_button).click()
        delete_button = "//div[@role='menuitem' and text()='Delete Endpoint']"
        telemetry_page.page.locator(delete_button).click()
        telemetry_page.page.wait_for_selector(
            "//div[contains(normalize-space(), 'Delete Endpoint') and @role='dialog']",
            state="visible",
        )
        confirm_button = "//button[contains(normalize-space(), 'Delete Endpoint') and @data-testid='kui-button']"
        telemetry_page.page.locator(confirm_button).click()
        warning_msg = "//span[contains(normalize-space(), 'Cannot be deleted as it is in use by Function id')]"
        telemetry_page.page.wait_for_selector(warning_msg, state="visible")

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5106673
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_select_kratos_thanos_telemetry_endpoint_for_creating_container_function_and_deploy(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
        function_version_teardown,
        session_nvcf_admin_sak,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["kratos_thanos"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["private_key"],
            client_certificate=telemetry_data["client_certificate"],
            protocol=telemetry_data["protocol"],
            telemetry_type={"metrics": True},
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"
        created_endpoint_name = endpoint_name + " (Kratos Thanos)"

        # create a new function with the same telemetry endpoint
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_metrics=created_endpoint_name,
        )
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            **deploy_data,
            wait_until_deployed=True,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5106674
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_clone_container_function_with_kratos_thanos_telemetry_endpoint_configuration(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
        function_version_teardown,
        session_nvcf_admin_sak,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["kratos_thanos"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["private_key"],
            client_certificate=telemetry_data["client_certificate"],
            protocol=telemetry_data["protocol"],
            telemetry_type={"metrics": True},
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"
        created_endpoint_name = endpoint_name + " (Kratos Thanos)"

        # create a new function with the same telemetry endpoint
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_metrics=created_endpoint_name,
        )
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # Clone function
        func_page = FunctionListPage(get_user_page)
        func_page.navigate_to_page()
        func_page.FunctionPagination.search_function_exact_match(source_func_name)
        func_page.FunctionPagination.clone_function(source_func_name)

        # Fill in clone details and create clone
        base_func_info = {
            "func_name": source_func_name,
            "func_desc": source_func_name,
            "tags": func_info["tags"],
            "container": func_info["container"],
            "tag": "latest",
            "inference_protocol": func_info["inference_protocol"],
            "inference_port": func_info["inference_port"],
            "inference_endpoint": func_info["inference_endpoint"],
            "health_endpoint": func_info["health_endpoint"],
            "health_port": func_info["health_port"],
        }

        clone_function_page = CloneFunctionPage(get_user_page)
        clone_function_page.Form.verify_clone_details(base_func_info)
        clone_function_page.Form.clone_container_function()

        # In Function Version Detail page, verify the cloned function name
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        get_user_page.wait_for_timeout(2000)
        actual_func_name = func_ver_detail_page.NavigationBar.get_function_name()
        logging.info(f"Actual function name: {actual_func_name}")
        logging.info(f"source_func_name: {source_func_name}")

        assert (
            actual_func_name == source_func_name + "-clone"
        ), f"Actual function name '{actual_func_name}' is not as expected."

        # Gather Function ID and Function Version ID for teardown
        get_user_page.wait_for_timeout(2000)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]

        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # Verify the cloned function has the same telemetry endpoint configuration
        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == ["—"]
        assert list(metrics_endpoint.values()) == [created_endpoint_name]

        # deploy the cloned function
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            wait_until_deployed=True,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5106675
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_telemetry_export_information_of_kratos_thanos_telemetry_endpoint_from_version_details_page(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
        running_config,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["kratos_thanos"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["private_key"],
            client_certificate=telemetry_data["client_certificate"],
            protocol=telemetry_data["protocol"],
            telemetry_type={"metrics": True},
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"
        created_endpoint_name = endpoint_name + " (Kratos Thanos)"

        # create a new function with the same telemetry endpoint
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_metrics=created_endpoint_name,
        )
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == ["—"]
        assert list(metrics_endpoint.values()) == [created_endpoint_name]

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T4093808
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_the_api_key_cert_of_associated_telemetry_endpoint_to_invalid_value_for_the_active_container_function_negative(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
        function_version_teardown,
        session_nvcf_admin_sak,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["kratos_thanos"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["private_key"],
            client_certificate=telemetry_data["client_certificate"],
            protocol=telemetry_data["protocol"],
            telemetry_type={"metrics": True},
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"
        created_endpoint_name = endpoint_name + " (Kratos Thanos)"

        # create a new function with the same telemetry endpoint
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_metrics=created_endpoint_name,
        )
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            **deploy_data,
            wait_until_deployed=True,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

        update_data = {
            "private_key": "update_key",
            "client_certificate": "update_client_certificate",
        }
        telemetry_page.update_endpoint(endpoint_name, update_data)
        function_list_page = FunctionListPage(get_user_page)
        function_list_page.navigate_to_page()
        function_list_page.page.wait_for_selector(
            "//h2[text()='Functions']", state="visible"
        )
        function_list_page.FunctionPagination.search_function_exact_match(func_id)
        function_details_panel_list = (
            function_list_page.FunctionPagination.show_first_function_details_panel_list()
        )
        logging.info(f"function_details_panel_list: {function_details_panel_list}")
        func_status = function_details_panel_list[2]
        assert func_status == "ACTIVE", f"Function status is not ACTIVE, but {func_status}"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5166908
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_azure_monitor_telemetry_endpoint_with_http_protocol(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        # running_config: dict,
        telemetry_endpoint_teardown,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["azure_monitor"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            instrumentation_key=telemetry_data["instrumentation_key"],
            live_endpoint=telemetry_data["live_endpoint"],
            application_id=telemetry_data["application_id"],
            protocol=telemetry_data["protocol"],
            telemetry_type=telemetry_data["telemetry_types"],
        )

        added_telemetry_info = {
            "telemetry_endpoint_name": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5166909
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_azure_monitor_telemetry_endpoint_with_grpc_protocol(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["azure_monitor"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            instrumentation_key=telemetry_data["instrumentation_key"],
            live_endpoint=telemetry_data["live_endpoint"],
            application_id=telemetry_data["application_id"],
            protocol="GRPC",
            telemetry_type=telemetry_data["telemetry_types"],
        )

        added_telemetry_info = {
            "telemetry_endpoint_name": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5166910
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_azure_monitor_telemetry_endpoint_from_telemetry_list_page(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["azure_monitor"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            instrumentation_key=telemetry_data["instrumentation_key"],
            live_endpoint=telemetry_data["live_endpoint"],
            application_id=telemetry_data["application_id"],
            protocol=telemetry_data["protocol"],
            telemetry_type=telemetry_data["telemetry_types"],
        )

        added_telemetry_info = {
            "telemetry_endpoint_name": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

        assert telemetry_page.page.locator(
            "//h4[normalize-space(text())='Telemetry Endpoints']"
        ).is_visible()
        real_provider = (
            telemetry_page.page.locator("//td[@headers='data-view-header-provider']")
            .nth(0)
            .text_content()
        )
        assert real_provider == telemetry_data["provider"], "Provider is not correct"

        assert telemetry_page.page.locator(
            "//th[normalize-space(text())='Telemetry Type']"
        ).is_visible(), "Telemetry type column is not visible"
        real_telemetry_type = (
            telemetry_page.page.locator("//td[@headers='data-view-header-Telemetry Type']")
            .nth(0)
            .text_content()
        )
        logging.info(f"real_telemetry_type: {real_telemetry_type}")
        expected_types = ["Traces", "Metrics", "Logs"]
        assert all(
            type_name in real_telemetry_type for type_name in expected_types
        ), f"Telemetry type is not correct. Expected all of {expected_types}, but got: {real_telemetry_type}"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5166911
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_existing_azure_monitor_telemetry_endpoint(
        self,
        get_user_page: Page,
        test_data_settings: dict,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["azure_monitor"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            instrumentation_key=telemetry_data["instrumentation_key"],
            live_endpoint=telemetry_data["live_endpoint"],
            application_id=telemetry_data["application_id"],
            protocol=telemetry_data["protocol"],
            telemetry_type=telemetry_data["telemetry_types"],
        )

        logging.info(f"Delete the telemetry {endpoint_name}  ...")
        telemetry_page.delete_endpoint(endpoint_name)
        assert not telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} still exists in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5166912
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_existing_azure_monitor_nvcf_telemetry_endpoint_credential(
        self,
        get_user_page: Page,
        test_data_settings: dict,
    ):
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        endpoint_name = "byoo_auto_azure_dailyrun"
        # Search for the endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

        update_data = {
            "instrumentation_key": "update_instrumentation_key",
            "live_endpoint": "update_live_endpoint",
            "application_id": "update_application_id",
        }
        telemetry_page.update_endpoint(endpoint_name, update_data)

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5166913
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_the_associated_azure_monitor_telemetry_endpoint_of_the_cloud_function(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
        function_version_teardown,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["azure_monitor"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            instrumentation_key=telemetry_data["instrumentation_key"],
            live_endpoint=telemetry_data["live_endpoint"],
            application_id=telemetry_data["application_id"],
            protocol=telemetry_data["protocol"],
            telemetry_type=telemetry_data["telemetry_types"],
        )

        added_telemetry_info = {
            "telemetry_endpoint_name": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"
        created_endpoint_name = endpoint_name + " (Azure Monitor)"

        # create a new function with the same telemetry endpoint
        if CURRENT_ENV == "staging":
            func_info = test_data_settings[
                "byoo_container_function_telemetry_endpoint_test_stg"
            ]
        else:
            func_info = test_data_settings[
                "byoo_container_function_telemetry_endpoint_test"
            ]
        logging.info("Start creation of byoo container function with telemetry endpoint")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_metrics=created_endpoint_name,
            telemetry_logs=created_endpoint_name,
            telemetry_traces=created_endpoint_name,
        )
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # check the functions number associated with the telemetry endpoint
        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()
        telemetry_page.verify_telemetry_section_visible()
        telemetry_page.search_endpoint(endpoint_name)
        refresh_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//button[text()=' Refresh']"
        telemetry_page.page.locator(refresh_button).click()
        telemetry_page.page.wait_for_selector(refresh_button, state="visible")
        action_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//td[@headers='data-view-header-row-actions']"
        telemetry_page.page.locator(action_button).click()
        delete_button = "//div[@role='menuitem' and text()='Delete Endpoint']"
        telemetry_page.page.locator(delete_button).click()
        telemetry_page.page.wait_for_selector(
            "//div[contains(normalize-space(), 'Delete Endpoint') and @role='dialog']",
            state="visible",
        )
        confirm_button = "//button[contains(normalize-space(), 'Delete Endpoint') and @data-testid='kui-button']"
        telemetry_page.page.locator(confirm_button).click()
        telemetry_page.page.wait_for_timeout(5000)
        warning_msg = "//span[contains(normalize-space(), 'Cannot be deleted as it is in use by Function id')]"
        warning_element = telemetry_page.page.locator(warning_msg)
        warning_element.wait_for(state="visible", timeout=10000)
        assert (
            warning_element.is_visible()
        ), "Warning message about function in use should be displayed"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5166914
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_select_azure_monitor_telemetry_endpoint_to_create_container_function(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
    ):
        """
        Test selecting telemetry endpoint for create container function without deployment
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")
        existing_telemetry = "byoo_auto_azure_dailyrun (Azure Monitor)"

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_metrics=existing_telemetry,
            telemetry_logs=existing_telemetry,
            telemetry_traces=existing_telemetry,
        )

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]

        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        traces_endpoint = func_ver_detail_page.FunctionDetails.get_traces_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        logging.info(f"traces_endpoint: {traces_endpoint}")
        assert list(logs_endpoint.values()) == [existing_telemetry]
        assert list(metrics_endpoint.values()) == [existing_telemetry]
        assert list(traces_endpoint.values()) == [existing_telemetry]

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5166916
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_telemetry_export_information_of_azure_monitor_telemetry_endpoint_from_version_details_page(
        self,
        get_user_page: Page,
        test_data_settings: dict,
    ):
        existing_telemetry = "byoo_auto_azure_dailyrun (Azure Monitor)"
        # create a new function with the same telemetry endpoint
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_metrics=existing_telemetry,
            telemetry_logs=existing_telemetry,
            telemetry_traces=existing_telemetry,
        )
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        traces_endpoint = func_ver_detail_page.FunctionDetails.get_traces_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        logging.info(f"traces_endpoint: {traces_endpoint}")
        assert list(logs_endpoint.values()) == [existing_telemetry]
        assert list(metrics_endpoint.values()) == [existing_telemetry]
        assert list(traces_endpoint.values()) == [existing_telemetry]

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.T4093826
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV != "staging",
        reason="Skipping this test as byoo only support staging deployment",
    )
    def test_select_telemetry_endpoint_for_creating_container_function_and_deploy_to_byoc_cluster_backend(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
        session_nvcf_admin_sak,
    ):
        """
        Test selecting telemetry endpoint for create container function and deploy to BYOC cluster backend
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_logs=func_info["telemetry_logs"],
            telemetry_metrics=func_info["telemetry_metrics"],
            is_deploy=True,
        )

        deployments_create_page = DeploymentsCreatePage(get_user_page)
        if CURRENT_ENV == "staging":
            func_id, func_ver_id = deployments_create_page.deploy_function_version(
                get_user_page,
                None,
                None,
                wait_until_deployed=True,
                gpu_list=TEST_DATA_CF["deployment_byoc_stg"]["gpu_list"],
                instance_clusters_filter_list=TEST_DATA_CF["deployment_byoc_stg"][
                    "cluster_list"
                ],
                session_nvcf_admin_sak=session_nvcf_admin_sak,
            )
        else:
            func_id, func_ver_id = deployments_create_page.deploy_function_version(
                get_user_page,
                None,
                None,
                wait_until_deployed=True,
                gpu_list=TEST_DATA_CF["deployment_byoc"]["gpu_list"],
                instance_clusters_filter_list=TEST_DATA_CF["deployment_byoc"][
                    "cluster_list"
                ],
                session_nvcf_admin_sak=session_nvcf_admin_sak,
            )
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(
            func_name=source_func_name, func_id=func_id, vers_id=func_ver_id
        )

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == [func_info["telemetry_metrics"]]

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T4093828
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "production" or CURRENT_ENV == "canary",
        reason="Skip this test case because it is not support",
    )
    def test_select_telemetry_endpoint_for_creating_helm_chart_function_and_deploy_to_dgxc_backend(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
        func_invoc_data,
        session_nvcf_admin_sak,
    ):
        """
        Test selecting telemetry endpoint for create helm chart function and deploy to DGXC backend
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings[
                "helm_chart_function_telemetry_endpoint_test_stg"
            ]
        else:
            func_info = test_data_settings["helm_chart_function_telemetry_endpoint_test"]
        logging.info("Start creation of source helm chart function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_helm_chart_function(
            source_func_name,
            func_info["Helm_Chart"],
            func_info["Helm_Chart_Version"],
            func_info["Helm_Chart_Service_Name"],
            func_info["inference_protocol"],
            func_info["inference_port"],
            health_port=func_info["health_port"],
            telemetry_logs=func_info["telemetry_logs"],
            telemetry_metrics=func_info["telemetry_metrics"],
        )

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]

        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == [func_info["telemetry_metrics"]]

        # deploy to DGXC backend use api
        if CURRENT_ENV == "staging":
            deploy_data = func_invoc_data["DEPLOY_FUNCTION_H100_NON_GFN_STG"]
        else:
            deploy_data = func_invoc_data["DEPLOY_FUNCTION_H100_NON_GFN"]
        deploy_data["req"]["path"] += f"{func_id}/versions/{func_ver_id}"
        session: APISession = session_nvcf_admin_sak["ngc"]
        resp = session.request(**deploy_data["req"])
        APIValidation(resp, deploy_data["exp"]).validate_response()
        # wait the function to active
        function_list_page = FunctionListPage(get_user_page)
        function_list_page.navigate_to_page()
        get_user_page.wait_for_timeout(1000)
        function_list_page.FunctionPagination.search_function_exact_match(func_id)
        function_details_panel_list = (
            function_list_page.FunctionPagination.show_first_function_details_panel_list()
        )
        logging.info(f"function_details_panel_list: {function_details_panel_list}")
        func_name = function_details_panel_list[0]

        function_list_page.FunctionPagination.wait_function_to_expected_status(
            func_name, "ACTIVE", 1800
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T4519406
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "production" or CURRENT_ENV == "canary",
        reason="Skip this test case because it is not support",
    )
    def test_select_one_telemetry_endpoint_for_creating_helm_chart_function_and_deploy_to_byoc_backend(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
        func_invoc_data,
        session_nvcf_admin_sak,
    ):
        """
        Test selecting one telemetry endpoint for create helm chart function and deploy to BYOC backend
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings[
                "helm_chart_function_telemetry_endpoint_test_stg"
            ]
        else:
            func_info = test_data_settings["helm_chart_function_telemetry_endpoint_test"]
        logging.info("Start creation of source helm chart function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_helm_chart_function(
            source_func_name,
            func_info["Helm_Chart"],
            func_info["Helm_Chart_Version"],
            func_info["Helm_Chart_Service_Name"],
            func_info["inference_protocol"],
            func_info["inference_port"],
            health_port=func_info["health_port"],
            telemetry_logs=func_info["telemetry_logs"],
        )

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]

        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == ["—"]

        # deploy to BYOC backend use api
        if CURRENT_ENV == "staging":
            deploy_data = func_invoc_data["DEPLOY_FUNCTION_H100_NON_GFN_STG"]
        else:
            deploy_data = func_invoc_data["DEPLOY_FUNCTION_A10_BYOC"]
        deploy_data["req"]["path"] += f"{func_id}/versions/{func_ver_id}"
        session: APISession = session_nvcf_admin_sak["ngc"]
        resp = session.request(**deploy_data["req"])
        APIValidation(resp, deploy_data["exp"]).validate_response()
        # wait the function to active
        function_list_page = FunctionListPage(get_user_page)
        function_list_page.navigate_to_page()
        get_user_page.wait_for_timeout(1000)
        function_list_page.FunctionPagination.search_function_exact_match(func_id)
        function_details_panel_list = (
            function_list_page.FunctionPagination.show_first_function_details_panel_list()
        )
        logging.info(f"function_details_panel_list: {function_details_panel_list}")
        func_name = function_details_panel_list[0]

        function_list_page.FunctionPagination.wait_function_to_expected_status(
            func_name, "ACTIVE", 1800
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5192800
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_prometheus_remote_write_telemetry_endpoint_with_http_protocol(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"][
            "prometheus_remote_write"
        ]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["private_key"],
            client_certificate=telemetry_data["client_certificate"],
            cafile=telemetry_data["cafile"],
            protocol=telemetry_data["protocol"],
            telemetry_type=telemetry_data["telemetry_types"],
        )

        added_telemetry_info = {
            "telemetry_endpoint_name": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5192801
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_prometheus_remote_write_telemetry_endpoint_with_grpc_protocol(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"][
            "prometheus_remote_write"
        ]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["private_key"],
            client_certificate=telemetry_data["client_certificate"],
            cafile=telemetry_data["cafile"],
            protocol="GRPC",
            telemetry_type=telemetry_data["telemetry_types"],
        )

        added_telemetry_info = {
            "telemetry_endpoint_name": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5192802
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_prometheus_remote_write_telemetry_endpoint_from_telemetry_list_page(
        self,
        get_user_page: Page,
        prometheus_remote_write_telemetry_endpoint_setup,
        telemetry_endpoint_teardown,
    ):
        telemetry_page = TelemetryPage(get_user_page)

        endpoint_name = prometheus_remote_write_telemetry_endpoint_setup
        added_telemetry_info = {
            "telemetry_endpoint_name": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        assert telemetry_page.page.locator(
            "//h4[normalize-space(text())='Telemetry Endpoints']"
        ).is_visible()
        real_provider = (
            telemetry_page.page.locator("//td[@headers='data-view-header-provider']")
            .nth(0)
            .text_content()
        )
        assert real_provider == "Prometheus Remote Write", "Provider is not correct"

        assert telemetry_page.page.locator(
            "//th[normalize-space(text())='Telemetry Type']"
        ).is_visible(), "Telemetry type column is not visible"
        real_telemetry_type = (
            telemetry_page.page.locator("//td[@headers='data-view-header-Telemetry Type']")
            .nth(0)
            .text_content()
        )
        logging.info(f"real_telemetry_type: {real_telemetry_type}")
        expected_types = ["Metrics"]
        assert all(
            type_name in real_telemetry_type for type_name in expected_types
        ), f"Telemetry type is not correct. Expected all of {expected_types}, but got: {real_telemetry_type}"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5192803
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_existing_prometheus_remote_write_telemetry_endpoint(
        self,
        get_user_page: Page,
        prometheus_remote_write_telemetry_endpoint_setup,
    ):
        telemetry_page = TelemetryPage(get_user_page)
        endpoint_name = prometheus_remote_write_telemetry_endpoint_setup
        added_telemetry_info = {
            "telemetry_endpoint_name": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        logging.info(f"Delete the telemetry {endpoint_name}  ...")
        telemetry_page.delete_endpoint(endpoint_name)
        assert not telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} still exists in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5192804
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_existing_prometheus_remote_write_nvcf_telemetry_endpoint_credential(
        self,
        get_user_page: Page,
    ):
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        endpoint_name = "byoo_auto_prometheus_remote_write_dailyrun"
        # Search for the endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

        update_data = {
            "private_key": "update_private_key",
            "client_certificate": "update_client_certificate",
            "cafile": "update_cafile",
        }
        ret = telemetry_page.update_endpoint(endpoint_name, update_data)
        assert ret, "Failed to update the endpoint"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5192805
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_the_associated_prometheus_remote_write_telemetry_endpoint_of_the_cloud_function(
        self,
        get_user_page: Page,
        byoo_container_function_with_prometheus_remote_write_endpoint,
    ):
        telemetry_page = TelemetryPage(get_user_page)
        function_info = byoo_container_function_with_prometheus_remote_write_endpoint
        telemetry_name = function_info["telemetry_name"]
        logging.info(f"telemetry_name: {telemetry_name}")

        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()
        telemetry_page.verify_telemetry_section_visible()
        telemetry_page.search_endpoint(telemetry_name)
        refresh_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//button[text()=' Refresh']"
        telemetry_page.page.locator(refresh_button).click()
        telemetry_page.page.wait_for_selector(refresh_button, state="visible")
        action_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//td[@headers='data-view-header-row-actions']"
        telemetry_page.page.locator(action_button).click()
        delete_button = "//div[@role='menuitem' and text()='Delete Endpoint']"
        telemetry_page.page.locator(delete_button).click()
        telemetry_page.page.wait_for_selector(
            "//div[contains(normalize-space(), 'Delete Endpoint') and @role='dialog']",
            state="visible",
        )
        confirm_button = "//button[contains(normalize-space(), 'Delete Endpoint') and @data-testid='kui-button']"
        telemetry_page.page.locator(confirm_button).click()
        telemetry_page.page.wait_for_timeout(5000)
        warning_msg = "//span[contains(normalize-space(), 'Cannot be deleted as it is in use by Function id')]"
        warning_element = telemetry_page.page.locator(warning_msg)
        warning_element.wait_for(state="visible", timeout=10000)
        assert (
            warning_element.is_visible()
        ), "Warning message about function in use should be displayed"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5192806
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_select_prometheus_remote_write_telemetry_endpoint_to_create_container_function_and_deploy(
        self,
        get_user_page: Page,
        byoo_container_function_with_prometheus_remote_write_endpoint,
        session_nvcf_admin_sak,
    ):
        """
        Test selecting telemetry endpoint for create container function without deployment
        """
        # Get test data for container function
        func_info = byoo_container_function_with_prometheus_remote_write_endpoint
        func_id = func_info["func_id"]
        func_ver_id = func_info["func_ver_id"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]

        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            **deploy_data,
            wait_until_deployed=True,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5192808
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_telemetry_export_information_of_prometheus_remote_write_telemetry_endpoint_from_version_details_page(
        self,
        get_user_page: Page,
        byoo_container_function_with_prometheus_remote_write_endpoint,
    ):
        func_info = byoo_container_function_with_prometheus_remote_write_endpoint
        logging.info(f"func_info: {func_info}")
        telemetry_name = func_info["telemetry_name"]
        func_id = func_info["func_id"]
        func_ver_id = func_info["func_ver_id"]

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.switch_tab(
            "function details",
            func_id=func_id,
            vers_id=func_ver_id,
        )
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert metrics_endpoint["Metrics Endpoint"] == telemetry_name + " (Prometheus)"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5185443
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_grafana_cloud_telemetry_endpoint_with_traces_type_http_protocol(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        running_config: dict,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["grafana"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            instance_id=telemetry_data["instance_id"],
            endpoint_url=telemetry_data["endpoint_url"],
            telemetry_type={"logs": True, "metrics": True, "traces": True},
            key=running_config["grafana_apikey"]["api_key"],
            protocol="HTTP",
        )

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5185444
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_grafana_cloud_telemetry_endpoint_with_traces_type_grpc_protocol(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        running_config: dict,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["grafana"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            instance_id=telemetry_data["instance_id"],
            endpoint_url=telemetry_data["endpoint_url"],
            telemetry_type={"logs": True, "metrics": True, "traces": True},
            key=running_config["grafana_apikey"]["api_key"],
            protocol="GRPC",
        )

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5185445
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_datadog_telemetry_endpoint_with_traces_type_http_protocol(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        running_config: dict,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["datadog"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=running_config["datadog_apikey"]["api_key"],
            telemetry_type={"logs": True, "metrics": True, "traces": True},
            protocol="HTTP",
        )

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5185446
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_datadog_telemetry_endpoint_with_traces_type_grpc_protocol(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        running_config: dict,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["datadog"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=running_config["datadog_apikey"]["api_key"],
            telemetry_type={"logs": True, "metrics": True, "traces": True},
            protocol="GRPC",
        )

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5185447
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_service_now_telemetry_endpoint_with_traces_type_http_protocol(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        running_config: dict,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["service_now"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=running_config["servicenow_key"]["api_key"],
            telemetry_type={"traces": True},
            protocol="HTTP",
        )

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5185448
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_service_now_telemetry_endpoint_with_traces_type_grpc_protocol(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        running_config: dict,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["service_now"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()
        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=running_config["servicenow_key"]["api_key"],
            telemetry_type={"traces": True},
            protocol="GRPC",
        )

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5185449
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_existing_grafana_cloud_telemetry_endpoint_with_traces_type(
        self,
        get_user_page: Page,
        grafana_cloud_telemetry_endpoint_setup,
    ):
        telemetry_page = TelemetryPage(get_user_page)
        endpoint_name = grafana_cloud_telemetry_endpoint_setup
        added_telemetry_info = {
            "telemetry_endpoint_name": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        logging.info(f"Delete the telemetry {endpoint_name}  ...")
        telemetry_page.delete_endpoint(endpoint_name)
        assert not telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} still exists in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5185450
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_existing_datadog_telemetry_endpoint_credential_with_traces_type(
        self,
        get_user_page: Page,
        datadog_telemetry_endpoint_setup,
        telemetry_endpoint_teardown,
    ):
        telemetry_page = TelemetryPage(get_user_page)
        endpoint_name = datadog_telemetry_endpoint_setup

        added_telemetry_info = {
            "telemetry_endpoint_name": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        update_data = {
            "key": "updated_datadog_test_key",
        }
        result = telemetry_page.update_endpoint(endpoint_name, update_data)
        assert result, f"Failed to update datadog telemetry endpoint {endpoint_name}"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.T5185451
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_helm_chart_function_with_grafana_cloud_telemetry_endpoint_and_deploy_to_gfn_backend(
        self,
        get_user_page: Page,
        byoo_helm_chart_function_with_grafana_cloud_endpoint,
        func_invoc_data,
        session_nvcf_admin_sak,
        running_config,
    ):
        # Get test data for container function
        func_info = byoo_helm_chart_function_with_grafana_cloud_endpoint
        func_id = func_info["func_id"]
        func_ver_id = func_info["func_ver_id"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")

        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]

        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            **deploy_data,
            wait_until_deployed=True,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )
        logging.info("Waiting for 10 seconds for function ready for invocation")
        time.sleep(10)

        api_data = Tools.load_case_data(func_invoc_data, "INVOKE_BYOO_FUNCTION")
        count = 5
        for _ in range(count):
            self.invoke_function_and_get_result(api_data, func_id, session_nvcf_admin_sak)
            time.sleep(1)

        logging.info("Waiting for 60 seconds to send telemetry data")
        time.sleep(60)

        validator_result = self.validate_grafana_metrics_telemetry_data(
            func_id, running_config, workload_type="helm", cloudprovider="gfn"
        )
        logging.info(f"Metrics validator_result: {validator_result}")
        metrics_valication = validator_result

        loki_url = running_config["grafana_cloud_loki"]["url"]
        loki_username = running_config["grafana_logs"]["email"]
        loki_password = running_config["grafana_logs"]["password"]
        logs_validator = GrafanaLogsValidator(loki_url, loki_username, loki_password)

        result = logs_validator.validate_nvcf_grafana_logs_telemetry_data(
            func_id,
            func_ver_id,
            hours_back=0.25,
        )

        logging.info(f"Logs query result is : {result}")
        instance_id_from_endpoint = result.pop("instance_id")
        logging.info(f"function instance_id_from_endpoint: {instance_id_from_endpoint}")
        # get the instance id
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(func_id=func_id, vers_id=func_ver_id)
        func_ver_detail_page.page.wait_for_selector(
            "//button[text()='Overview']", state="visible", timeout=10000
        )

        instance_id_from_nvcf = func_ver_detail_page.page.locator(
            "//td[@headers='data-view-header-instanceId']"
        ).text_content()
        logging.info(f"instance_id_from_nvcf: {instance_id_from_nvcf}")

        logs_valication = True
        for k, v in result.items():
            logging.info(f"{k} is : {v}")
            if v != count:
                logging.error(f"The count number is not as expected. {v} != {count}")
                logs_valication = False
                break
        logging.info(f"logs_valication is : {logs_valication}")
        if instance_id_from_endpoint not in instance_id_from_nvcf:
            logging.error(
                f"The instance ID from logs is not as expected. {instance_id_from_endpoint} not in {instance_id_from_nvcf}"
            )
            logs_valication = False

        tempo_url = running_config["grafana_cloud_tempo"]["url"]
        tempo_username = running_config["grafana_traces"]["email"]
        tempo_password = running_config["grafana_traces"]["password"]
        logging.info(f"tempo_url: {tempo_url}")
        logging.info(f"tempo_username: {tempo_username}")
        logging.info(f"tempo_password: {tempo_password}")
        traces_validator = GrafanaTracesValidator(tempo_url, tempo_username, tempo_password)

        traces_results = traces_validator.validate_nvcf_grafana_traces_telemetry_data(
            func_id, hours_back=0.25
        )
        logging.info(f"traces_results is : {traces_results}")

        traces_valication = True
        if traces_results["traces_length"] < count:
            logging.error(
                f"Traces length is not as expected: {traces_results['traces_length']} < {count}"
            )
            traces_valication = False

        if traces_results["instance_id"] not in instance_id_from_nvcf:
            logging.error(
                f"Instance ID mismatch: '{traces_results['instance_id']}' not in '{instance_id_from_nvcf}'"
            )
            traces_valication = False

        if NCA_ID not in traces_results["nca_id"]:
            logging.error(
                f"NCA ID mismatch: '{traces_results['nca_id']}' not in '{NCA_ID}'"
            )
            traces_valication = False

        assert (
            logs_valication is True
        ), "The validation for grafana logs is not as expected."
        assert (
            traces_valication is True
        ), "The validation for grafana traces is not as expected."
        assert (
            metrics_valication is True
        ), "The validation for grafana metrics is not as expected."

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.T5185452
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "staging",
        reason="Skip this test case temporarily because it is not supported on staging",
    )
    def test_create_helm_chart_function_with_datadog_telemetry_endpoint_with_traces_type_and_deploy(
        self,
        get_user_page: Page,
        byoo_helm_chart_function_with_datadog_telemetry_endpoint,
        session_nvcf_admin_sak,
    ):
        # Get test data for container function
        func_info = byoo_helm_chart_function_with_datadog_telemetry_endpoint
        func_id = func_info["func_id"]
        func_ver_id = func_info["func_ver_id"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")

        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            **TEST_DATA_CF["deployment_min"],
            wait_until_deployed=True,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.T5185453
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "staging",
        reason="Skip this test case temporarily because it is not supported on staging",
    )
    def test_create_helm_chart_function_with_service_now_telemetry_endpoint_with_traces_type_and_deploy(
        self,
        get_user_page: Page,
        byoo_helm_chart_function_with_service_now_telemetry_endpoint,
        session_nvcf_admin_sak,
    ):
        # Get test data for container function
        func_info = byoo_helm_chart_function_with_service_now_telemetry_endpoint
        func_id = func_info["func_id"]
        func_ver_id = func_info["func_ver_id"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")

        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            **TEST_DATA_CF["deployment_min"],
            wait_until_deployed=True,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5185454
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_telemetry_config_info_of_byoo_helm_chart_function_using_datadog_telemetry_endpoint_with_traces_type(
        self,
        get_user_page: Page,
        byoo_helm_chart_function_with_datadog_telemetry_endpoint,
    ):
        func_info = byoo_helm_chart_function_with_datadog_telemetry_endpoint
        logging.info(f"func_info: {func_info}")
        telemetry_name = func_info["telemetry_name"]
        func_id = func_info["func_id"]
        func_ver_id = func_info["func_ver_id"]

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.switch_tab(
            "function details",
            func_id=func_id,
            vers_id=func_ver_id,
        )
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        traces_endpoint = func_ver_detail_page.FunctionDetails.get_traces_endpoint()
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        logging.info(f"traces_endpoint: {traces_endpoint}")
        logging.info(f"logs_endpoint: {logs_endpoint}")
        assert metrics_endpoint["Metrics Endpoint"] == telemetry_name + " (Datadog)"
        assert traces_endpoint["Traces Endpoint"] == telemetry_name + " (Datadog)"
        assert logs_endpoint["Logs Endpoint"] == telemetry_name + " (Datadog)"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138284
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_anchor_link_of_telemetry_endpoint_from_task_creation_page(
        self,
        get_user_page: Page,
    ):
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.page.wait_for_selector(
            "//span[text()='Basic Details']", state="visible"
        )

        # check the anchor link of telemetry endpoint
        # Click the documentation link
        with create_task_page.page.context.expect_page() as new_page_info:
            create_task_page.page.locator("//a[text()='Telemetry Endpoints']").click()
        new_page = new_page_info.value
        new_page.wait_for_load_state("networkidle")
        new_page.wait_for_load_state("domcontentloaded")
        new_page.bring_to_front()

        new_url = new_page.url
        logging.info(f"New page URL: {new_url}")
        expected_path = "settings#telemetry-section"
        assert (
            expected_path in new_url
        ), f"Expected URL path '{expected_path}' not found in '{new_url}'"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.********
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_telemetry_configuration_information_for_logs_and_metrics_from_task_details_page(
        self,
        get_user_page: Page,
        nvct_teardown,
        test_data_tasks,
        running_config,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_gfn_upload_result"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_gfn_upload_result"]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            telemetry_logs=LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID,
            telemetry_metrics=LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID,
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)

        telemetry_info = task_details_page.get_telemetry_configuration_information()
        logging.info(f"telemetry_info: {telemetry_info}")
        assert LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID in telemetry_info["logs_endpoint"]
        assert LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID in telemetry_info["metrics_endpoint"]

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138292
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_telemetry_configuration_information_for_task_using_metrics_only_telemetry_type(
        self,
        get_user_page: Page,
        nvct_teardown,
        test_data_tasks,
        running_config,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_gfn_upload_result"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_gfn_upload_result"]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            telemetry_metrics=LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID,
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)

        telemetry_info = task_details_page.get_telemetry_configuration_information()
        logging.info(f"telemetry_info: {telemetry_info}")
        assert telemetry_info["logs_endpoint"] == "—"
        assert LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID in telemetry_info["metrics_endpoint"]

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138296
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_tasks_number_associated_with_specific_telemetry_endpoint(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        test_data_tasks,
        telemetry_endpoint_teardown,
        nvct_teardown,
        running_config,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"]["grafana"]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            instance_id=telemetry_data["instance_id"],
            endpoint_url=telemetry_data["endpoint_url"],
            telemetry_type={"logs": True, "metrics": True},
            key=running_config["grafana_apikey"]["api_key"],
        )

        added_telemetry_info = {
            "telemetry_id": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_gfn_upload_result"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_gfn_upload_result"]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            telemetry_metrics=endpoint_name,
            telemetry_logs=endpoint_name,
        )
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)

        # check the functions number associated with the telemetry endpoint
        telemetry_page.navigate_to_settings()
        telemetry_page.verify_on_settings_page()
        telemetry_page.verify_telemetry_section_visible()
        telemetry_page.search_endpoint(endpoint_name)
        tasks_number = telemetry_page.page.locator(
            "//td[@headers='data-view-header-associatedEntities']"
        ).text_content()
        assert tasks_number == "1", f"Expected 1 task, but got {tasks_number}"

        refresh_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//button[text()=' Refresh']"
        telemetry_page.page.locator(refresh_button).click()
        telemetry_page.page.wait_for_selector(refresh_button, state="visible")
        telemetry_page.page.locator(
            "//td[@headers='data-view-header-associatedEntities']//span"
        ).click()
        telemetry_page.page.wait_for_selector(
            "//div[contains(text(), 'Associated Functions')]", state="visible"
        )

        real_taskid = telemetry_page.page.locator(
            "//td[@headers='data-view-header-id']"
        ).text_content()
        assert real_taskid == task_id, f"Expected task ID {task_id}, but got {real_taskid}"
        telemetry_page.page.locator("//button[text()='Close']").click()

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T4093797
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_prometheus_remote_write_telemetry_endpoint_org_admin(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        telemetry_endpoint_teardown,
    ):
        telemetry_data = test_data_settings["telemetry"]["endpoints"][
            "prometheus_remote_write"
        ]
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        # Create a new telemetry endpoint
        telemetry_page.click_add_endpoint()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        endpoint_name = telemetry_page.create_telemetry_endpoint(
            name=f"{telemetry_data['name']}_{timestamp}",
            provider=telemetry_data["provider"],
            endpoint_url=telemetry_data["endpoint_url"],
            key=telemetry_data["private_key"],
            client_certificate=telemetry_data["client_certificate"],
            cafile=telemetry_data["cafile"],
            protocol=telemetry_data["protocol"],
            telemetry_type=telemetry_data["telemetry_types"],
        )

        added_telemetry_info = {
            "telemetry_endpoint_name": endpoint_name,
        }
        self.telemetry_remove_list.append(added_telemetry_info)

        # Search for the newly created endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T4093818
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_existing_nvcf_prometheus_remote_write_telemetry_endpoint_credential_with_org_admin(
        self,
        get_user_page: Page,
    ):
        # Create TelemetryPage instance
        telemetry_page = TelemetryPage(get_user_page)

        # Navigate to settings page
        telemetry_page.navigate_to_settings()

        # Verify we're on the settings page
        telemetry_page.verify_on_settings_page()

        # Verify the Telemetry Endpoints section is present
        telemetry_page.verify_telemetry_section_visible()

        endpoint_name = "byoo_auto_prometheus_remote_write_dailyrun"
        # Search for the endpoint
        telemetry_page.search_endpoint(endpoint_name)

        # Verify the endpoint appears in the table
        assert telemetry_page.verify_endpoint_exists(
            endpoint_name
        ), f"Endpoint {endpoint_name} not found in table"

        update_data = {
            "private_key": "update_private_key",
            "client_certificate": "update_client_certificate",
            "cafile": "update_cafile",
        }
        ret = telemetry_page.update_endpoint(endpoint_name, update_data)
        assert ret, "Failed to update the endpoint"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.sanity
    @pytest.mark.T5138291
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_telemetry_configuration_information_for_task_using_logs_only_telemetry_type(
        self,
        get_user_page: Page,
        nvct_teardown,
        test_data_tasks,
        running_config,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_gfn_upload_result"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_gfn_upload_result"]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            telemetry_logs=LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID,
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)

        telemetry_info = task_details_page.get_telemetry_configuration_information()
        logging.info(f"telemetry_info: {telemetry_info}")
        assert LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID in telemetry_info["logs_endpoint"]
        assert telemetry_info["metrics_endpoint"] == "—"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.T4519405
    @pytest.mark.skipif(
        CURRENT_ENV == "production" or CURRENT_ENV == "canary",
        reason="Skip this test case because production and canary environment is not supported dgxc",
    )
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_select_only_one_telemetry_endpoint_for_creating_container_function_and_deploy_to_dgxc_backend(
        self,
        test_data_settings,
        get_user_page: Page,
        function_version_teardown,
        session_nvcf_admin_sak,
    ):
        """
        Test selecting only one telemetry endpoint for create container function and deploy to DGXC backend
        """
        # Get test data for container function
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_logs=func_info["telemetry_logs"],
            is_deploy=True,
        )

        get_user_page.wait_for_selector("//h2[text()='Deploy Version']", state="visible")
        current_url = get_user_page.url
        logging.info(f"current_url: {current_url}")
        import re

        match = re.search(r"create/([a-f0-9\-]+):([a-f0-9\-]+)", current_url)
        if match:
            func_id = match.group(1)
            func_ver_id = match.group(2)
            logging.info(f"func_id: {func_id}, func_ver_id: {func_ver_id}")
        self.func_vers_remove_list.append([func_id, func_ver_id])
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_with_dgxc_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_with_H100"]
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        func_id, func_ver_id = deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            **deploy_data,
            wait_until_deployed=True,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(
            func_name=source_func_name, func_id=func_id, vers_id=func_ver_id
        )

        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        assert list(logs_endpoint.values()) == [func_info["telemetry_logs"]]
        assert list(metrics_endpoint.values()) == ["—"]

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5166915
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "production" or CURRENT_ENV == "canary",
        reason="Skip this test case because production and canary environment is not supported dgxc",
    )
    def test_clone_container_function_with_azure_monitor_telemetry_endpoint_configuration_and_deploy(
        self,
        get_user_page: Page,
        test_data_settings: dict,
        function_version_teardown,
        session_nvcf_admin_sak,
    ):
        # create a new function with the same telemetry endpoint
        if CURRENT_ENV == "staging":
            func_info = test_data_settings["container_function_telemetry_endpoint_test_stg"]
        else:
            func_info = test_data_settings["container_function_telemetry_endpoint_test"]
        logging.info("Start creation of source container function for cloning.")
        existing_telemetry = "byoo_auto_azure_dailyrun (Azure Monitor)"

        # Create source function using CreateFunctionPage
        source_func_name = add_timestamp(f"source_{func_info['func_name']}", "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_container_function(
            source_func_name,
            func_info["container"],
            func_info["tag"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            inference_port=func_info["inference_port"],
            tags=func_info["tags"],
            telemetry_metrics=existing_telemetry,
            telemetry_logs=existing_telemetry,
            telemetry_traces=existing_telemetry,
        )
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # Clone function
        func_page = FunctionListPage(get_user_page)
        func_page.navigate_to_page()
        func_page.FunctionPagination.search_function_exact_match(source_func_name)
        func_page.FunctionPagination.clone_function(source_func_name)

        # Fill in clone details and create clone
        base_func_info = {
            "func_name": source_func_name,
            "func_desc": source_func_name,
            "tags": func_info["tags"],
            "container": func_info["container"],
            "tag": "latest",
            "inference_protocol": func_info["inference_protocol"],
            "inference_port": func_info["inference_port"],
            "inference_endpoint": func_info["inference_endpoint"],
            "health_endpoint": func_info["health_endpoint"],
            "health_port": func_info["health_port"],
        }

        clone_function_page = CloneFunctionPage(get_user_page)
        clone_function_page.Form.verify_clone_details(base_func_info)
        clone_function_page.Form.clone_container_function()

        # In Function Version Detail page, verify the cloned function name
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        get_user_page.wait_for_timeout(2000)
        actual_func_name = func_ver_detail_page.NavigationBar.get_function_name()
        logging.info(f"Actual function name: {actual_func_name}")
        logging.info(f"source_func_name: {source_func_name}")

        assert (
            actual_func_name == source_func_name + "-clone"
        ), f"Actual function name '{actual_func_name}' is not as expected."

        # Gather Function ID and Function Version ID for teardown
        get_user_page.wait_for_timeout(2000)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]

        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        # Add function version to clean up list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # Verify the cloned function has the same telemetry endpoint configuration
        func_ver_detail_page.switch_tab(
            "function details",
            func_name=source_func_name,
            func_id=func_id,
            vers_id=func_ver_id,
        )
        logs_endpoint = func_ver_detail_page.FunctionDetails.get_logs_endpoint()
        metrics_endpoint = func_ver_detail_page.FunctionDetails.get_metrics_endpoint()
        traces_endpoint = func_ver_detail_page.FunctionDetails.get_traces_endpoint()
        logging.info(f"logs_endpoint: {logs_endpoint}")
        logging.info(f"metrics_endpoint: {metrics_endpoint}")
        logging.info(f"traces_endpoint: {traces_endpoint}")
        assert list(logs_endpoint.values()) == [existing_telemetry]
        assert list(metrics_endpoint.values()) == [existing_telemetry]
        assert list(traces_endpoint.values()) == [existing_telemetry]

        # deploy the cloned function
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            wait_until_deployed=True,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.T5222925
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV != "staging",
        reason="Skip this test case because az60 only supported on staging",
    )
    @pytest.mark.skip(reason="Skip this test case because of existing bug: 5386140")
    def test_e2e_create_container_function_with_service_now_telemetry_endpoint_and_deploy_to_dgxc_forge_cluster(
        self,
        get_user_page: Page,
        byoo_container_function_with_service_now_telemetry_endpoint,
        session_nvcf_admin_sak,
        func_invoc_data,
        running_config,
    ):
        # Get test data for container function
        func_info = byoo_container_function_with_service_now_telemetry_endpoint
        func_id = func_info["func_id"]
        func_ver_id = func_info["func_ver_id"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")

        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            **TEST_DATA_CF["deployment_with_dgxc_forge_stg"],
            wait_until_deployed=True,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )
        # get the instance id
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(func_id=func_id, vers_id=func_ver_id)
        func_ver_detail_page.page.wait_for_selector(
            "//button[text()='Overview']", state="visible", timeout=10000
        )
        switch_button = func_ver_detail_page.page.locator(
            "//button[contains(text(), 'Switch To ')]"
        )
        switch_button.click()
        instance_id = func_ver_detail_page.page.locator(
            "//td[@headers='data-view-header-instanceId']"
        ).text_content()
        logging.info(f"instance_id: {instance_id}")

        logging.info("Step 3. Invoke the function and validate")
        invoke_function_data = Tools.load_case_data(func_invoc_data, "INVOKE_BYOO_FUNCTION")
        start = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
        logging.info(f"start is {start}")
        invoke_count = 2
        for _ in range(invoke_count):
            self.invoke_function_and_get_result(
                invoke_function_data, func_id, session_nvcf_admin_sak
            )
        time.sleep(120)
        end = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
        logging.info(f"end is {end}")
        check_dict = {
            "data_num": 2 * invoke_count,
            "function_version_id": func_ver_id,
            "instance_id": instance_id,
            "cloud_provider": "forge",
        }

        servicenow_traces_validator = ServiceNowTracesValidator(
            base_url=running_config["servicenow_traces"]["url"],
            api_key=running_config["lightstep_apikey"]["api_key"],
        )
        invalid_flag, error_msg = servicenow_traces_validator.validate(
            WrapperType.FUNCTION, func_id, start, end, check_dict
        )
        logging.info(f"invalid_flag is {invalid_flag}")
        logging.info(f"error_msg is {error_msg}")
        assert invalid_flag, f"{error_msg}"

    @pytest.mark.CloudFunctions
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.deployment_test
    @pytest.mark.T4099173
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_e2e_create_container_function_with_datadog_telemetry_endpoint_and_deploy_to_gfn(
        self,
        get_user_page: Page,
        byoo_container_function_with_datadog_telemetry_endpoint,
        session_nvcf_admin_sak,
        func_invoc_data,
        running_config,
    ):
        # Get test data for container function
        func_info = byoo_container_function_with_datadog_telemetry_endpoint
        func_id = func_info["func_id"]
        func_ver_id = func_info["func_ver_id"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")

        deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            wait_until_deployed=True,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )
        # get the instance id
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(func_id=func_id, vers_id=func_ver_id)

        instance_id = func_ver_detail_page.get_instance_id_with_retry()

        logging.info(f"instance_id: {instance_id}")

        logging.info("Step 3. Invoke the function and validate")
        invoke_function_data = Tools.load_case_data(func_invoc_data, "INVOKE_BYOO_FUNCTION")
        start = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
        logging.info(f"start is {start}")
        invoke_count = 2
        for _ in range(invoke_count):
            self.invoke_function_and_get_result(
                invoke_function_data, func_id, session_nvcf_admin_sak
            )
        time.sleep(120)
        end = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
        logging.info(f"end is {end}")

        datadog_metrics_validator = DatadogMetricsValidator(
            api_key=running_config["datadog_apikey"]["api_key"],
            app_key=running_config["datadog_appkey"]["api_key"],
        )
        result = datadog_metrics_validator.validate(
            entity_id=func_id,
            function_version_id=func_ver_id,
            back_seconds=300,
            wrapper_type=WrapperType.FUNCTION,
        )
        logging.info(f"result is {result}")
        assert result.success, "The metrics are not found"

        datadog_dogs_validator = DatadogLogsValidator(
            api_key=running_config["datadog_apikey"]["api_key"],
            app_key=running_config["datadog_appkey"]["api_key"],
        )

        result = datadog_dogs_validator.validate(
            entity_id=func_id,
            function_version_id=func_ver_id,
            expected_log_count=invoke_count,
            back_seconds=300,
            backend_type=CloudProvider.GFN,
            nca_id=NCA_ID,
            cloud_provider="GFN",
            instance_id=instance_id,
            host_id=instance_id,
            wrapper_type=WrapperType.FUNCTION,
        )
        assert result.success, "The logs are not found"

    @pytest.mark.T5166930
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_e2e_create_container_function_with_azure_monitor_telemetry_endpoint_and_deploy_to_gfn(
        self,
        get_user_page: Page,
        byoo_helm_chart_function_with_azure_monitor_telemetry_endpoint,
        session_nvcf_admin_sak,
        func_invoc_data,
        running_config,
    ):
        api_key = ""
        # Get test data for container function
        func_info = byoo_helm_chart_function_with_azure_monitor_telemetry_endpoint
        func_id = func_info["func_id"]
        func_ver_id = func_info["func_ver_id"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")

        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            **TEST_DATA_CF["deployment_min"],
            wait_until_deployed=True,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )
        # get the instance id
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(func_id=func_id, vers_id=func_ver_id)
        func_ver_detail_page.page.wait_for_selector(
            "//button[text()='Overview']", state="visible", timeout=10000
        )
        # switch_button = func_ver_detail_page.page.locator(
        #     "//button[contains(text(), 'Switch To ')]"
        # )
        # switch_button.click()
        instance_id = func_ver_detail_page.page.locator(
            "//td[@headers='data-view-header-instanceId']"
        ).text_content()
        logging.info(f"instance_id: {instance_id}")

        logging.info("Step 3. Invoke the function and validate")
        invoke_function_data = Tools.load_case_data(func_invoc_data, "INVOKE_BYOO_FUNCTION")
        start = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
        logging.info(f"start is {start}")
        invoke_count = 2
        for _ in range(invoke_count):
            self.invoke_function_and_get_result(
                invoke_function_data, func_id, session_nvcf_admin_sak
            )
        time.sleep(120)
        end = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
        logging.info(f"end is {end}")

        # Use Azure Monitor Logs Validator with default values
        azure_monitor_traces_validator = AzureMonitorTracesValidator(
            base_url="api.loganalytics.io/v1",
            api_key=api_key,
        )

        # Validate traces
        validation_traces = azure_monitor_traces_validator.validate_traces(
            func_id=func_id,
            func_ver_id=func_ver_id,
            nca_id="taLfaNPSpq7LV7vM1ueLJ2526IsX3be1MAjZ58GCQoc",
            cloud_provider="gfn",
            instance_id=instance_id,
            invocation_number=invoke_count,
        )

        assert (
            validation_traces is True
        ), "Azure Monitor traces validation result is not expected"

        # Validate logs
        azure_monitor_logs_validator = AzureMonitorLogsValidator(
            base_url="api.loganalytics.io/v1",
            api_key=api_key,
        )
        validation_logs = azure_monitor_logs_validator.validate_logs(
            func_id=func_id,
            func_ver_id=func_ver_id,
            nca_id="taLfaNPSpq7LV7vM1ueLJ2526IsX3be1MAjZ58GCQoc",
            cloud_provider="gfn",
            instance_id=instance_id,
            invocation_number=invoke_count,
        )
        assert (
            validation_logs is True
        ), "Azure Monitor logs validation result is not expected"

        # Load metrics data from the JSON file
        with open("data/telemetry_metrics.json", "r") as f:
            metrics_data = json.load(f)

        # Use helm_metrics for helm chart function, container_metrics for container function
        # Since this test case can be used for both, we'll validate both sets of metrics
        azure_monitor_metrics_validator = AzureMonitorMetricsValidator(
            base_url="management.azure.com",
            api_key=api_key,
        )

        # Validate container metrics
        validation_helm_metrics = azure_monitor_metrics_validator.validate_single_metric(
            func_id=func_id,
            func_ver_id=func_ver_id,
            nca_id="taLfaNPSpq7LV7vM1ueLJ2526IsX3be1MAjZ58GCQoc",
            cloud_provider="gfn",
            instance_id=instance_id,
            host_id=instance_id,
            metrics_list=metrics_data["helm_metrics"],
            invoke_count=2,
        )
        assert (
            validation_helm_metrics is True
        ), "Azure Monitor helm metrics validation result is not expected"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5192791
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.parametrize(
        "byoo_container_function_with_prometheus_remote_write_endpoint",
        ["helm"],
        indirect=True,
    )
    def test_e2e_verify_the_nvcf_telemetry_data_sent_from_nvcf_to_prometheus_remote_write_observability_provider_helm_chart_function_gfn_cluster(
        self,
        get_user_page: Page,
        byoo_container_function_with_prometheus_remote_write_endpoint,
        session_nvcf_admin_sak,
        func_invoc_data,
        running_config,
        kratos_thanos_ssa_token,
    ):
        """
        Test selecting telemetry endpoint for create helm chart function without deployment
        """
        # Get test data for helm chart function
        func_info = byoo_container_function_with_prometheus_remote_write_endpoint
        func_id = func_info["func_id"]
        func_ver_id = func_info["func_ver_id"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]

        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            **deploy_data,
            wait_until_deployed=True,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

        # get the instance id
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(func_id=func_id, vers_id=func_ver_id)
        func_ver_detail_page.page.wait_for_selector(
            "//button[text()='Overview']", state="visible", timeout=10000
        )

        instance_id = func_ver_detail_page.page.locator(
            "//td[@headers='data-view-header-instanceId']"
        ).text_content()
        logging.info(f"instance_id: {instance_id}")

        api_data = Tools.load_case_data(func_invoc_data, "INVOKE_BYOO_FUNCTION")
        count = 5
        for _ in range(count):
            self.invoke_function_and_get_result(api_data, func_id, session_nvcf_admin_sak)
            time.sleep(1)

        logging.info("Waiting for 90 seconds to send telemetry data")
        time.sleep(90)

        ssa_token = kratos_thanos_ssa_token
        base_url = f"{running_config['kratos_thanos_ssa_info']['endpoint_url']}"

        start_time = (datetime.now(timezone.utc) - timedelta(hours=0.25)).strftime(
            "%Y-%m-%dT%H:%M:%SZ"
        )
        end_time = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")

        kratos_thanos_metrics_validator = KratosThanosMetricsValidator(base_url, ssa_token)
        result = kratos_thanos_metrics_validator.validate(
            wrapper_type=WrapperType.FUNCTION,
            workload_type=WorkloadType.HELM,
            cloud_provider=CloudProvider.GFN,
            id=func_id,
            start=start_time,
            end=end_time,
        )
        logging.info(f"result is : {result}")

        assert (
            result["validation_result"] is True
        ), "The validation for kratos thanos metrics is not as expected."

        if result["instance_id"] != instance_id:
            logging.error(
                f"The instance ID {result['instance_id']} is not as expected. {instance_id}"
            )
        assert (
            NCA_ID in result["nca_id"]
        ), f"The NCA ID {result['nca_id']} not in expected {NCA_ID}"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.deployment_test
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5106688
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.parametrize(
        "byoo_function_with_kratos_thanos_endpoint",
        ["helm"],
        indirect=True,
    )
    def test_e2e_verify_the_nvcf_telemetry_data_sent_from_nvcf_to_kratos_thanos_observability_provider_helm_chart_function_gfn_cluster(
        self,
        get_user_page: Page,
        byoo_function_with_kratos_thanos_endpoint,
        session_nvcf_admin_sak,
        func_invoc_data,
        running_config,
        kratos_thanos_ssa_token,
    ):
        """
        Test selecting telemetry endpoint for create helm chart function without deployment
        """
        # Get test data for helm chart function
        func_info = byoo_function_with_kratos_thanos_endpoint
        func_id = func_info["func_id"]
        func_ver_id = func_info["func_ver_id"]
        logging.info(f"Function ID: {func_id}, Version ID: {func_ver_id}")
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]

        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            **deploy_data,
            wait_until_deployed=True,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

        # get the instance id
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.navigate_to_page(func_id=func_id, vers_id=func_ver_id)
        func_ver_detail_page.page.wait_for_selector(
            "//button[text()='Overview']", state="visible", timeout=10000
        )

        instance_id = func_ver_detail_page.page.locator(
            "//td[@headers='data-view-header-instanceId']"
        ).text_content()
        logging.info(f"instance_id: {instance_id}")

        api_data = Tools.load_case_data(func_invoc_data, "INVOKE_BYOO_FUNCTION")
        count = 5
        for _ in range(count):
            self.invoke_function_and_get_result(api_data, func_id, session_nvcf_admin_sak)
            time.sleep(1)

        logging.info("Waiting for 90 seconds to send telemetry data")
        time.sleep(90)

        ssa_token = kratos_thanos_ssa_token
        base_url = f"{running_config['kratos_thanos_ssa_info']['endpoint_url']}"

        start_time = (datetime.now(timezone.utc) - timedelta(hours=0.25)).strftime(
            "%Y-%m-%dT%H:%M:%SZ"
        )
        end_time = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")

        kratos_thanos_metrics_validator = KratosThanosMetricsValidator(base_url, ssa_token)
        result = kratos_thanos_metrics_validator.validate(
            wrapper_type=WrapperType.FUNCTION,
            workload_type=WorkloadType.HELM,
            cloud_provider=CloudProvider.GFN,
            id=func_id,
            start=start_time,
            end=end_time,
        )
        logging.info(f"result is : {result}")

        assert (
            result["validation_result"] is True
        ), "The validation for kratos thanos metrics is not as expected."

        if result["instance_id"] != instance_id:
            logging.error(
                f"The instance ID {result['instance_id']} is not as expected. {instance_id}"
            )
        assert (
            NCA_ID in result["nca_id"]
        ), f"The NCA ID {result['nca_id']} not in expected {NCA_ID}"
