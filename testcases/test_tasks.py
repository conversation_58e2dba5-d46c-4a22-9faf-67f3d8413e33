import logging
import pytest
from datetime import timedelta, timezone, datetime
import time
from playwright.sync_api import Page
from pages.Tasks.TasksCreatePage import TasksCreatePage
from utils.common.tools import add_timestamp
from element.tasks_create_page_elements import TaskCreateOptionENUM
from cloudia.utils.tools import Tools
from cloudia.api.http.api_session import APISession
from cloudia.utils.backend_service.nvcf.nvct_utils import NVCTUtils
from cloudia.utils.backend_service.nvcf.nvcf_const import NVCT_DATA
from pages.Tasks.TasksDetailsPage import TasksDetailsPage
from pages.Tasks.TasksListPage import TasksListPage
from pages.Tasks.TaskClonePage import TasksClonePage
from utils.test_utils import get_session_nvct_ssa_allscope
from cloudia.utils.env_service import get_current_env
import copy
from config.consts import (
    LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID,
    LOGS_METRICS_GRPC_GRAFANA_TELEMETRY_ID,
    METRICS_GRPC_THANOS_TELEMETRY_ID,
    METRICS_HTTP_THANOS_TELEMETRY_ID,
    LOGS_METRICS_GRPC_DATADOG_TELEMETRY_ID,
    LOGS_METRICS_HTTP_DATADOG_TELEMETRY_ID,
    NCA_ID,
)

from cloudia.utils.backend_service.nvcf.byoo_validator.models_utils import (
    WrapperType,
    WorkloadType,
    CloudProvider,
)
from cloudia.utils.backend_service.nvcf.byoo_validator.grafana_metrics_validator_utils import (
    GrafanaMetricsValidator,
)
from cloudia.utils.backend_service.nvcf.byoo_validator.grafana_logs_validator_utils import (
    GrafanaLogsValidator,
)
from cloudia.utils.backend_service.nvcf.byoo_validator.kratos_thanos_validator_utils import (
    KratosThanosMetricsValidator,
)

CURRENT_ENV = get_current_env()
EXPECTED_RESULTS_NUMBER_WITH_HELM_CHART = 5


class TestTasks:
    NVCF_ADMIN_USER = ["org_admin-nvcf_admin"]
    NVCF_USER_USESR = ["org_user-nvcf_user"]
    NVCF_VIEWER_USER = ["org_user-nvcf_viewer"]
    NVCF_PROD_ADMIN = ["nvcf_admin"]

    api_keys_remove_list = list()

    # Fixtures
    @pytest.fixture()
    def nvct_setup_and_teardown_with_upload(
        self,
        session_nvct_ssa_allscope: dict[str, APISession],
        running_config,
        test_data_tasks,
    ):
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        ngc_api_key = running_config["nvcf_admin"]["api_key"]
        if CURRENT_ENV == "staging":
            task_id = NVCT_Util.create_task(
                test_data_tasks["CTEATE_TASK_AND_UPLOAD_L40S_STG"], ngc_api_key
            )
        else:
            task_id = NVCT_Util.create_task(
                Tools.load_case_data(NVCT_DATA, "CTEATE_TASK_AND_UPLOAD_T10"), ngc_api_key
            )
        yield task_id
        session = get_session_nvct_ssa_allscope(running_config)
        NVCT_Util = NVCTUtils(session)
        logging.info(f"Deleting task {task_id}")
        NVCT_Util.delete_task(task_id)

    @pytest.fixture()
    def nvct_setup_and_teardown_with_upload_helm(
        self,
        session_nvct_ssa_allscope: dict[str, APISession],
        running_config,
        test_data_tasks,
    ):
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        ngc_api_key = running_config["nvcf_admin"]["api_key"]
        if CURRENT_ENV == "staging":
            task_id = NVCT_Util.create_task(
                test_data_tasks["CREATE_HELM_TASK_AND_UPLOAD_RESULT_STG"], ngc_api_key
            )
        else:
            task_id = NVCT_Util.create_task(
                test_data_tasks["CREATE_HELM_TASK_AND_UPLOAD_RESULT"], ngc_api_key
            )
        yield task_id
        logging.info(f"Deleting task {task_id}")
        NVCT_Util.delete_task(task_id)

    @pytest.fixture()
    def nvct_setup_without_teardown_helmchart(
        self,
        session_nvct_ssa_allscope: dict[str, APISession],
        running_config,
        test_data_tasks,
    ):
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        ngc_api_key = running_config["nvcf_admin"]["api_key"]
        if CURRENT_ENV == "staging":
            task_id = NVCT_Util.create_task(
                test_data_tasks["CREATE_HELM_TASK_WITHOUT_UPLOAD_RESULT_STG"], ngc_api_key
            )
        else:
            task_id = NVCT_Util.create_task(
                test_data_tasks["CREATE_HELM_TASK_WITHOUT_UPLOAD_RESULT"], ngc_api_key
            )
        yield task_id

    @pytest.fixture()
    def nvct_setup_without_teardown_error(
        self,
        session_nvct_ssa_allscope: dict[str, APISession],
        running_config,
        test_data_tasks,
    ):
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        ngc_api_key = running_config["nvcf_admin"]["api_key"]
        if CURRENT_ENV == "staging":
            task_id = NVCT_Util.create_task(
                test_data_tasks["CTEATE_TASK_WITHOUT_UPLOAD_H100_ERROR_STG"], ngc_api_key
            )
        else:
            task_id = NVCT_Util.create_task(
                test_data_tasks["CTEATE_TASK_WITHOUT_UPLOAD_T10_ERROR"], ngc_api_key
            )
        yield task_id

    @pytest.fixture()
    def nvct_setup_and_teardown_error(
        self,
        session_nvct_ssa_allscope: dict[str, APISession],
        running_config,
        test_data_tasks,
    ):
        start_time = time.time()
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        ngc_api_key = running_config["nvcf_admin"]["api_key"]
        if CURRENT_ENV == "staging":
            task_id = NVCT_Util.create_task(
                test_data_tasks["CTEATE_TASK_WITHOUT_UPLOAD_H100_ERROR_STG"], ngc_api_key
            )
        else:
            task_id = NVCT_Util.create_task(
                test_data_tasks["CTEATE_TASK_WITHOUT_UPLOAD_T10_ERROR"], ngc_api_key
            )
        yield task_id
        logging.info(f"Deleting task {task_id}")
        end_time = time.time()
        if end_time - start_time >= 900:
            logging.info(
                "Time taken to create task is greater than 900 seconds, so we need to re-generate SSA token"
            )
            from cloudia.api.utils.ssa_token_utils import SSAToken
            from config.scope_consts import NVCT_SCOPE_ALL

            ssa_token = SSAToken(running_config["nvct_ssa_info"], NVCT_SCOPE_ALL).ssa_token
            NVCT_Util = NVCTUtils(
                {
                    "type": "ssa",
                    "nvct": APISession(
                        api_host=running_config["api_host_nvct"], api_key=ssa_token
                    ),
                }
            )
        NVCT_Util.delete_task(task_id)

    @pytest.fixture()
    def nvct_setup_without_teardown_error_non_gfn(
        self,
        session_nvct_ssa_allscope: dict[str, APISession],
        running_config,
        test_data_tasks,
    ):
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        ngc_api_key = running_config["nvcf_admin"]["api_key"]
        if CURRENT_ENV == "staging":
            task_id = NVCT_Util.create_task(
                test_data_tasks["CTEATE_TASK_WITHOUT_UPLOAD_NON_GFN_ERROR_STG"], ngc_api_key
            )
        else:
            task_id = NVCT_Util.create_task(
                test_data_tasks["CTEATE_TASK_WITHOUT_UPLOAD_NON_GFN_ERROR"], ngc_api_key
            )
        yield task_id

    @pytest.fixture()
    def nvct_setup_and_teardown_without_upload_helmchart(
        self,
        session_nvct_ssa_allscope: dict[str, APISession],
        running_config,
        test_data_tasks,
    ):
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        ngc_api_key = running_config["nvcf_admin"]["api_key"]
        if CURRENT_ENV == "staging":
            task_id = NVCT_Util.create_task(
                test_data_tasks["CREATE_HELM_TASK_WITHOUT_UPLOAD_RESULT_STG"], ngc_api_key
            )
        else:
            task_id = NVCT_Util.create_task(
                test_data_tasks["CREATE_HELM_TASK_WITHOUT_UPLOAD_RESULT"], ngc_api_key
            )
        yield task_id
        logging.info(f"Deleting task {task_id}")
        NVCT_Util.delete_task(task_id)

    @pytest.fixture()
    def nvct_setup_and_teardown_without_upload_helmchart_dgxc(
        self,
        session_nvct_ssa_allscope: dict[str, APISession],
        running_config,
        test_data_tasks,
    ):
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        ngc_api_key = running_config["nvcf_admin"]["api_key"]
        if CURRENT_ENV == "staging":
            task_id = NVCT_Util.create_task(
                test_data_tasks["CREATE_HELM_TASK_WITHOUT_UPLOAD_RESULT_DGXC_STG"],
                ngc_api_key,
            )
        else:
            task_id = NVCT_Util.create_task(
                test_data_tasks["CREATE_HELM_TASK_WITHOUT_UPLOAD_RESULT_DGXC"], ngc_api_key
            )
        yield task_id
        logging.info(f"Deleting task {task_id}")
        NVCT_Util.delete_task(task_id)

    @pytest.fixture()
    def nvct_setup_and_teardown_without_upload_helmchart_non_dgxc(
        self,
        session_nvct_ssa_allscope: dict[str, APISession],
        running_config,
        test_data_tasks,
    ):
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        ngc_api_key = running_config["nvcf_admin"]["api_key"]
        if CURRENT_ENV == "staging":
            task_id = NVCT_Util.create_task(
                test_data_tasks["CREATE_HELM_TASK_WITHOUT_UPLOAD_RESULT_NON_DGXC_STG"],
                ngc_api_key,
            )
        else:
            task_id = NVCT_Util.create_task(
                test_data_tasks["CREATE_HELM_TASK_WITHOUT_UPLOAD_RESULT_NON_DGXC"],
                ngc_api_key,
            )
        yield task_id
        logging.info(f"Deleting task {task_id}")
        NVCT_Util.delete_task(task_id)

    @pytest.fixture()
    def nvct_setup_without_teardown_without_upload_helmchart_non_dgxc(
        self,
        session_nvct_ssa_allscope: dict[str, APISession],
        running_config,
        test_data_tasks,
    ):
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        ngc_api_key = running_config["nvcf_admin"]["api_key"]
        if CURRENT_ENV == "staging":
            task_id = NVCT_Util.create_task(
                test_data_tasks["CREATE_HELM_TASK_WITHOUT_UPLOAD_RESULT_NON_DGXC_STG"],
                ngc_api_key,
            )
        else:
            task_id = NVCT_Util.create_task(
                test_data_tasks["CREATE_HELM_TASK_WITHOUT_UPLOAD_RESULT_NON_DGXC"],
                ngc_api_key,
            )
        yield task_id

    @pytest.fixture()
    def nvct_setup_and_without_teardown(
        self,
        session_nvct_ssa_allscope: dict[str, APISession],
        running_config,
        test_data_tasks,
    ):
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        ngc_api_key = running_config["nvcf_admin"]["api_key"]
        if CURRENT_ENV == "staging":
            task_id = NVCT_Util.create_task(
                test_data_tasks["CTEATE_TASK_AND_UPLOAD_L40S_STG"], ngc_api_key
            )
        else:
            task_id = NVCT_Util.create_task(
                Tools.load_case_data(NVCT_DATA, "CTEATE_TASK_AND_UPLOAD_T10"), ngc_api_key
            )
        yield task_id

    # Fixtures
    @pytest.fixture()
    def nvct_setup_and_teardown_without_upload(
        self,
        session_nvct_ssa_allscope: dict[str, APISession],
        running_config,
        test_data_tasks,
    ):
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        ngc_api_key = running_config["nvcf_admin"]["api_key"]
        task_id = NVCT_Util.create_task(
            test_data_tasks["CTEATE_TASK_WITHOUT_UPLOAD_T10"], ngc_api_key
        )
        yield task_id
        session = get_session_nvct_ssa_allscope(running_config)
        NVCT_Util = NVCTUtils(session)
        logging.info(f"Deleting task {task_id}")
        NVCT_Util.delete_task(task_id)

    # Fixtures
    @pytest.fixture()
    def nvct_setup_and_teardown_on_A10(
        self,
        session_nvct_ssa_allscope: dict[str, APISession],
        running_config,
        test_data_tasks,
    ):
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        ngc_api_key = running_config["nvcf_admin"]["api_key"]
        if CURRENT_ENV == "staging":
            task_id = NVCT_Util.create_task(
                test_data_tasks["CTEATE_TASK_AND_UPLOAD_H100_STG"], ngc_api_key
            )
        else:
            task_id = NVCT_Util.create_task(
                test_data_tasks["CTEATE_TASK_AND_UPLOAD_A10"], ngc_api_key
            )
        yield task_id
        session = get_session_nvct_ssa_allscope(running_config)
        NVCT_Util = NVCTUtils(session)
        logging.info(f"Deleting task {task_id}")
        NVCT_Util.delete_task(task_id)

    # Fixtures
    @pytest.fixture()
    def nvct_setup_and_without_teardown_on_A10(
        self,
        session_nvct_ssa_allscope: dict[str, APISession],
        running_config,
        test_data_tasks,
    ):
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        ngc_api_key = running_config["nvcf_admin"]["api_key"]
        if CURRENT_ENV == "staging":
            task_id = NVCT_Util.create_task(
                test_data_tasks["CTEATE_TASK_AND_UPLOAD_H100_STG"], ngc_api_key
            )
        else:
            task_id = NVCT_Util.create_task(
                test_data_tasks["CTEATE_TASK_AND_UPLOAD_A10"], ngc_api_key
            )
        yield task_id

    nvct_remove_list = list()

    @pytest.fixture()
    def nvct_teardown(
        self,
        running_config,
    ):
        yield
        session = get_session_nvct_ssa_allscope(running_config)
        NVCT_Util = NVCTUtils(session)
        while self.nvct_remove_list:
            nvct_dict = self.nvct_remove_list.pop()
            logging.info(f"Deleting task {nvct_dict}")
            NVCT_Util.delete_task(nvct_dict["task_id"])

    @pytest.fixture()
    def personal_key_teardown(
        self,
        delete_personal_keys_with_cookies,
    ):
        """
        Function-scoped fixture that returns a callable function to delete service keys.
        Can be called multiple times in the same test to delete different keys.
        """
        yield
        keys_to_remove = self.api_keys_remove_list.copy()
        self.api_keys_remove_list.clear()

        for key_id in keys_to_remove:
            delete_personal_keys_with_cookies(key_id=key_id)

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904864
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_choose_none_for_maxruntimeduration_when_deploying_task_on_GFN_negative(
        self,
        test_data_tasks,
        get_user_page: Page,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_task_sample_NONE_runs_forever_GFN"]
        else:
            task_info = test_data_tasks["prod_task_sample_NONE_runs_forever_GFN"]
        logging.info("Start a creation of a task container function.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            upload_results=False,
            taskCreateOption=TaskCreateOptionENUM.NONEFORMAXRUNTIMEDURATIONGFN,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904865
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_choose_large_than_8_hours_for_maxRuntimeDuration_when_deploying_on_GFN_negative(
        self,
        test_data_tasks,
        get_user_page: Page,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_task_sample_maxRuntimeDuration_larger_than_8_GFN"
            ]
        else:
            task_info = test_data_tasks[
                "prod_task_sample_maxRuntimeDuration_larger_than_8_GFN"
            ]
        logging.info("Start a creation of a task container function.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            upload_results=False,
            taskCreateOption=TaskCreateOptionENUM.RUNTIMELARGERTHAN8,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904866
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_terminationGracePeriodDuration_larger_than_maxRuntimeDuration_when_deploying_on_GFN_negative(
        self,
        test_data_tasks,
        get_user_page: Page,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_task_sample_terminationGracePeriodDuration_large_than_maxRuntimeDuration_GFN"
            ]
        else:
            task_info = test_data_tasks[
                "prod_task_sample_terminationGracePeriodDuration_large_than_maxRuntimeDuration_GFN"
            ]
        logging.info("Start a creation of a task container function.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            upload_results=False,
            taskCreateOption=TaskCreateOptionENUM.GRACELARGERTHANRUNTIME,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904867
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_container_task_with_personal_key_out_of_PR_scope_GFN_negative(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_gfn_full_config"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_gfn_full_config"]
        logging.info("Start a creation of a task container function.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin_without_pr"]["api_key"],
            taskCreateOption=TaskCreateOptionENUM.WITHOUTPR_OR_EXPIRED,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904868
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_container_task_with_expired_personal_key_GFN_negative(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_gfn_full_config"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_gfn_full_config"]
        logging.info("Start a creation of a task container function.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin_expired"]["api_key"],
            taskCreateOption=TaskCreateOptionENUM.WITHOUTPR_OR_EXPIRED,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904858
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_with_required_fields_and_result_upload_deploying_on_GFN(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        """
        Test case to verify E2E flow of creating a container-based task with result upload on GFN
        Steps:
        1. Create task with required fields
        2. Monitor task status until completion
        3. Verify results in NGC registry
        """
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_gfn_upload_result"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_gfn_upload_result"]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )
        # Step 3: navigate to results tab and verify results
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        # Set expected_results_number to 1 if environment_variables doesn't exist
        if "environment_variables" in task_info and task_info["environment_variables"].get(
            "NUM_OF_RESULTS"
        ):
            expected_results_number = task_info["environment_variables"]["NUM_OF_RESULTS"]
        else:
            expected_results_number = 1
        task_details_page.verify_results_number(expected_results_number)
        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        task_details_page.verify_ngc_registry_results(
            task_info["ResultsUpload_model_name"], "output_result"
        )
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904859
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_with_required_fields_and_without_result_upload_deploying_on_GFN(
        self, test_data_tasks, running_config, get_user_page: Page, nvct_teardown
    ):
        """
        Test case to verify E2E flow of creating a container-based task without result upload on GFN
        Steps:
        1. Create task with required fields
        2. Monitor task status until completion
        """
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_container_task_sample_gfn_without_upload_result"
            ]
        else:
            task_info = test_data_tasks[
                "prod_container_task_sample_gfn_without_upload_result"
            ]
        logging.info("Start a creation of a task container function without result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            upload_results=False,  # Explicitly set to False for this test case
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)

        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )
        # Step 3: Verify task completed successfully
        logging.info("Test completed successfully - Task completed without result upload")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904861
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_with_model_resource_and_result_upload_deploying_on_GFN(
        self, test_data_tasks, running_config, get_user_page: Page, nvct_teardown
    ):
        """
        Test case to verify E2E flow of creating a container-based task with model, resource and result upload on GFN
        Steps:
        1. Create task with model, resource and result upload
        2. Monitor task status until completion
        3. Verify results in NGC registry
        """
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_gfn_model_resource"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_gfn_model_resource"]
        logging.info(
            "Start a creation of a task container function with model, resource and result upload."
        )
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")
        model = task_info["model"][0][0].split("/")
        model_version = task_info["model"][0][1]
        expected_match_model = f"/v2/org/{model[0]}/models/{model[1]}/{model_version}/files"
        logging.info(f"Expected match model: {expected_match_model}")
        resource = task_info["resource"][0][0].split("/")
        resource_version = task_info["resource"][0][1]
        expected_match_resource = (
            f"/v2/org/{resource[0]}/resources/{resource[1]}/{resource_version}/files"
        )
        logging.info(f"Expected match resource: {expected_match_resource}")
        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            model=task_info["model"],
            resource=task_info["resource"],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        config_details = task_details_page.get_task_config_details()
        logging.info(f"Task configuration details: {config_details}")
        model_list = config_details["Models"]
        resource_list = config_details["Resources"]
        logging.info(f"Model list: {model_list}")
        logging.info(f"Resource list: {resource_list}")
        assert any(
            expected_match_model in url for url in model_list
        ), f"Expected match model: {expected_match_model} not found in model list: {model_list}"
        assert any(
            expected_match_resource in url for url in resource_list
        ), f"Expected match resource: {expected_match_resource} not found in resource list: {resource_list}"
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )

        # Step 3: navigate to results tab and verify results
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        # Set expected_results_number to 1 if environment_variables doesn't exist
        if "environment_variables" in task_info and task_info["environment_variables"].get(
            "NUM_OF_RESULTS"
        ):
            expected_results_number = task_info["environment_variables"]["NUM_OF_RESULTS"]
        else:
            expected_results_number = 1
        task_details_page.verify_results_number(expected_results_number)

        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        task_details_page.verify_ngc_registry_results(
            task_info["ResultsUpload_model_name"], "output_result"
        )
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108892
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_helm_based_task_with_required_fields_and_result_upload_deploying_on_GFN(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_helm_task_sample_gfn_upload_result"]
        else:
            task_info = test_data_tasks["prod_helm_task_sample_gfn_upload_result"]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )
        # Step 3: navigate to results tab and verify results
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        expected_results_number = EXPECTED_RESULTS_NUMBER_WITH_HELM_CHART
        task_details_page.verify_results_number(expected_results_number)
        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        task_details_page.verify_ngc_registry_results(
            task_info["ResultsUpload_model_name"], "output_result"
        )
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108893
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_helm_based_task_with_required_fields_and_without_result_upload_deploying_on_GFN(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_helm_task_sample_gfn_upload_result"]
        else:
            task_info = test_data_tasks["prod_helm_task_sample_gfn_upload_result"]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            upload_results=False,
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )
        logging.info("Test completed successfully - Task completed without result upload")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108896
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_helm_based_task_with_helm_chart_overrides_and_result_upload_deploying_on_GFN(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_helm_task_sample_gfn_upload_result"]
        else:
            task_info = test_data_tasks["prod_helm_task_sample_gfn_upload_result"]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            helm_chart_overrides_data=task_info["helm_chart_overrides_data"],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )
        # Step 3: navigate to results tab and verify results
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        expected_results_number = 2
        task_details_page.verify_results_number(expected_results_number)
        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        task_details_page.verify_ngc_registry_results(
            task_info["ResultsUpload_model_name"], "output_result"
        )
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904873
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_set_terminationgraceperiodduration_larger_than_maxruntimeduration_deploying_on_BYOC_negative(
        self, test_data_tasks, running_config, get_user_page: Page, nvct_teardown
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_task_sample_terminationGracePeriodDuration_larger_than_maxRuntimeDuration_BYOC_nagative"
            ]
        else:
            task_info = test_data_tasks[
                "prod_task_sample_terminationGracePeriodDuration_larger_than_maxRuntimeDuration_BYOC_nagative"
            ]
        logging.info(
            "Start a creation of a task container function with termination grace period duration larger than max runtime duration."
        )
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")
        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            clusters=task_info["cluster_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            upload_results=False,
            taskCreateOption=TaskCreateOptionENUM.GRACELARGERTHANRUNTIME,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904876
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_task_list(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
    ):
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        get_user_page.wait_for_timeout(1000)
        # check sort by status
        task_list_page.set_sort_task_by_status()
        sort_method = task_list_page.get_current_sort_task_by_status()
        assert sort_method == "arrow-down" or sort_method == "arrow-up"

        # check sort by task name
        task_list_page.set_sort_task_by_task_name()
        sort_method = task_list_page.get_current_sort_task_by_task_name()
        assert sort_method == "arrow-down" or sort_method == "arrow-up"

        # check sort by task id
        task_list_page.set_sort_task_by_task_id()
        sort_method = task_list_page.get_current_sort_task_by_task_id()
        assert sort_method == "arrow-down" or sort_method == "arrow-up"

        # check sort by task type
        task_list_page.set_sort_task_by_type()
        sort_method = task_list_page.get_current_sort_task_by_type()
        assert sort_method == "arrow-down" or sort_method == "arrow-up"

        # check sort by created date
        task_list_page.set_sort_task_by_created_date()
        sort_method = task_list_page.get_current_sort_task_by_created_date()
        assert sort_method == "arrow-down" or sort_method == "arrow-up"
        assert task_list_page.check_created_date_sorted(sort_method)

        # check sort by last update
        task_list_page.set_sort_task_by_last_update()
        sort_method = task_list_page.get_current_sort_task_by_last_update()
        assert sort_method == "arrow-down" or sort_method == "arrow-up"
        assert task_list_page.check_last_update_sorted(sort_method)

        # check sort by cleanup date
        task_list_page.set_sort_task_by_cleanup_date()
        sort_method = task_list_page.get_current_sort_task_by_cleanup_date()
        assert sort_method == "arrow-down" or sort_method == "arrow-up"

        # check action exists
        assert task_list_page.check_action_exists()

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904877
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_task_list_is_filterable_by_status(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
    ):
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        get_user_page.wait_for_timeout(1000)

        total_count = task_list_page.get_total_task_count()
        status_list = task_list_page.status_list
        real_total_count = 0
        for status in status_list:
            (
                filtered_count,
                is_filtered,
            ) = task_list_page.set_filter_by_status(status)
            assert is_filtered
            real_total_count += filtered_count
            logging.info(f"Filtered count for status {status}: {filtered_count}")
            assert (
                filtered_count <= total_count and filtered_count >= 0
            ), f"Filtered count is not correct for status: {status}"
        assert real_total_count == total_count, "Total count is not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108904
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_task_list_is_filterable_by_type(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
    ):
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        get_user_page.wait_for_timeout(1000)

        total_count = task_list_page.get_total_task_count()
        type_list = task_list_page.type_list
        real_total_count = 0
        for type in type_list:
            (
                filtered_count,
                is_filtered,
            ) = task_list_page.set_filter_by_type(type)
            assert is_filtered
            real_total_count += filtered_count
            logging.info(f"Filtered count for type {type}: {filtered_count}")
            assert (
                filtered_count <= total_count and filtered_count >= 0
            ), f"Filtered count is not correct for type: {type}"
        assert real_total_count == total_count, "Total count is not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904878
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_task_details_for_tasks_with_result_upload(
        self,
        conf,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_with_upload,
        running_config,
    ):
        task_id = nvct_setup_and_teardown_with_upload
        logging.info(f"Task ID: {task_id}")
        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)
        # check basic details

        label_list = [
            "Name",
            "TaskID",
            "Status",
            "Type",
            "Description",
            "Tags",
            "Created",
            "LastUpdated",
            "CleanupDate",
        ]

        assert task_details_page.check_label_exists(label_list)

        check_val_list = ["TaskID"]
        real_val = task_details_page.get_need_data(check_val_list)
        assert real_val["TaskID"] == task_id, "TaskID is not correct"

        # check configuration details
        task_config_details = task_details_page.get_task_config_details()
        logging.info(f"Task config details: {task_config_details}")

        label_list = [
            "Container",
            "Models",
            "Resources",
            "Environment Variables",
            "Secrets",
            "Run Command Overrides",
            "GPU Type",
            "Max Runtime Duration",
            "Max Queued Duration",
            "Termination Grace Period",
            "Result Handling",
            "Result Path",
        ]
        for label in label_list:
            assert label in task_config_details, f"{label} is not in task config details"

        assert (
            task_config_details["Result Handling"]
            == "Upload tasks results to NGC Private Registry"
        ), "ResultHandling is not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.********
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_helm_based_task_details_for_tasks_with_results_upload(
        self,
        conf,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_with_upload_helm,
        running_config,
    ):
        task_id = nvct_setup_and_teardown_with_upload_helm
        logging.info(f"Task ID: {task_id}")
        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)
        # check basic details

        label_list = [
            "Name",
            "TaskID",
            "Status",
            "Type",
            "Description",
            "Tags",
            "Created",
            "LastUpdated",
            "CleanupDate",
        ]

        assert task_details_page.check_label_exists(label_list)

        check_val_list = ["TaskID"]
        real_val = task_details_page.get_need_data(check_val_list)
        assert real_val["TaskID"] == task_id, "TaskID is not correct"

        # check configuration details
        task_config_details = task_details_page.get_task_config_details_helmchart()
        logging.info(f"Task config details: {task_config_details}")

        label_list = [
            "Helm Chart",
            "Models",
            "Resources",
            "Secrets",
            "GPU Type",
            "Instance Type",
            "Helm Chart Overrides",
            "Max Runtime Duration",
            "Max Queued Duration",
            "Termination Grace Period",
            "Result Handling",
            "Result Path",
        ]
        for label in label_list:
            assert label in task_config_details, f"{label} is not in task config details"

        assert (
            task_config_details["Result Handling"]
            == "Upload tasks results to NGC Private Registry"
        ), "ResultHandling is not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.********
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_helm_based_task_details_for_tasks_without_results_upload(
        self,
        conf,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_without_upload_helmchart,
        running_config,
    ):
        task_id = nvct_setup_and_teardown_without_upload_helmchart
        logging.info(f"Task ID: {task_id}")
        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)
        # check basic details

        label_list = [
            "Name",
            "TaskID",
            "Status",
            "Type",
            "Description",
            "Tags",
            "Created",
            "LastUpdated",
            "CleanupDate",
        ]

        assert task_details_page.check_label_exists(label_list)

        check_val_list = ["TaskID"]
        real_val = task_details_page.get_need_data(check_val_list)
        assert real_val["TaskID"] == task_id, "TaskID is not correct"

        # check configuration details
        task_config_details = task_details_page.get_task_config_details_helmchart()
        logging.info(f"Task config details: {task_config_details}")

        label_list = [
            "Helm Chart",
            "Models",
            "Resources",
            "Secrets",
            "GPU Type",
            "Instance Type",
            "Helm Chart Overrides",
            "Max Runtime Duration",
            "Max Queued Duration",
            "Termination Grace Period",
            "Result Handling",
            "Result Path",
        ]
        for label in label_list:
            assert label in task_config_details, f"{label} is not in task config details"

        assert (
            task_config_details["Result Handling"] == "None"
        ), "ResultHandling is not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.********
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_logs_for_helm_based_task(
        self,
        conf,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_without_upload_helmchart,
    ):
        task_id = nvct_setup_and_teardown_without_upload_helmchart
        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)
        task_details_page.navigate_to_logs_tab()

        # check result count
        assert task_details_page.get_result_count() >= 0, "Result count is not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.********
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_task_results_for_completed_helm_based_tasks_with_results_upload(
        self,
        conf,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_with_upload_helm,
    ):
        task_id = nvct_setup_and_teardown_with_upload_helm
        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)

        task_info = task_details_page.get_task_config_details_helmchart()
        logging.info(f"Task info: {task_info}")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=1800,
            check_interval_seconds=30,
        )

        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        expected_results_number = EXPECTED_RESULTS_NUMBER_WITH_HELM_CHART
        task_details_page.verify_results_number(expected_results_number)
        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        model_name = task_info["Result Path"].split("/")[-1]
        task_details_page.verify_ngc_registry_results(model_name, "output_result")
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108909
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_task_results_not_exist_without_results_upload_for_helm_based_task(
        self,
        conf,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_without_upload_helmchart,
        running_config,
    ):
        task_id = nvct_setup_and_teardown_without_upload_helmchart
        logging.info(f"Task ID: {task_id}")
        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)

        assert not task_details_page.check_results_tab_visible(), "Results tab is visible"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108910
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_task_results_for_queued_helm_based_tasks_with_results_upload(
        self,
        conf,
        test_data_tasks,
        get_user_page: Page,
        running_config,
        nvct_setup_and_teardown_with_upload_helm,
        session_nvct_ssa_allscope,
    ):
        task_id = nvct_setup_and_teardown_with_upload_helm
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] == "QUEUED", "Task is not queued"

        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)

        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        assert (
            task_details_page.check_no_results_for_queued_tasks()
        ), "Results are present for queued tasks"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108914
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_cancel_helm_based_task_deploying_on_GFN_from_task_list_page(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_with_upload_helm,
    ):
        task_id = nvct_setup_and_teardown_with_upload_helm
        logging.info(f"Task ID: {task_id}")

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        task_list_page.cancel_first_row_task()
        get_user_page.wait_for_timeout(1000)

        # check task is cancelled
        assert task_list_page.wait_task_to_expected_status(
            task_id, "CANCELED", 1000
        ), "Task is not cancelled"

        # check the cleanup date is update
        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)
        check_val_list = ["CleanupDate"]
        real_val = task_details_page.get_need_data(check_val_list)
        logging.info(f"CleanupDate: {real_val['CleanupDate']}")
        assert real_val["CleanupDate"] != "-", "Cleanup date is not updated"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108915
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_cancel_helm_based_task_deploying_on_GFN_from_task_details_page_when_task_is_queued(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_with_upload_helm,
        running_config,
        session_nvct_ssa_allscope,
    ):
        task_id = nvct_setup_and_teardown_with_upload_helm
        logging.info(f"Task ID: {task_id}")

        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] == "QUEUED", "Task is not queued"

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)

        assert task_details_page.cancel_task(), "Task is not cancelled"
        get_user_page.wait_for_timeout(1000)

        # check the cleanup date is update
        check_val_list = ["Status", "CleanupDate"]
        real_val = task_details_page.get_need_data(check_val_list)
        logging.info(f"Status: {real_val['Status']}")
        logging.info(f"CleanupDate: {real_val['CleanupDate']}")
        assert real_val["Status"] == "CANCELED", "Status is not correct"
        assert real_val["CleanupDate"] != "-", "Cleanup date is not updated"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108916
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_cancel_helm_based_task_deploying_on_GFN_from_task_details_page_when_task_is_running(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_with_upload_helm,
    ):
        task_id = nvct_setup_and_teardown_with_upload_helm
        logging.info(f"Task ID: {task_id}")

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)

        task_details_page.wait_for_task_status(
            expected_status="RUNNING",
            timeout_seconds=1800,
            check_interval_seconds=30,
        )

        task_details_page.cancel_task()
        get_user_page.wait_for_timeout(1000)

        # check task is cancelled
        check_val_list = ["Status", "CleanupDate"]
        real_val = task_details_page.get_need_data(check_val_list)
        logging.info(f"Status: {real_val['Status']}")
        logging.info(f"CleanupDate: {real_val['CleanupDate']}")
        assert real_val["Status"] == "CANCELED", "Status is not correct"
        assert real_val["CleanupDate"] != "-", "Cleanup date is not updated"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904879
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_task_details_for_tasks_without_result_upload(
        self,
        conf,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_without_upload,
    ):
        task_id = nvct_setup_and_teardown_without_upload
        logging.info(f"Task ID: {task_id}")
        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)
        # check basic details

        label_list = [
            "Name",
            "TaskID",
            "Status",
            "Type",
            "Description",
            "Tags",
            "Created",
            "LastUpdated",
            "CleanupDate",
        ]

        assert task_details_page.check_label_exists(label_list)

        check_val_list = ["TaskID"]
        real_val = task_details_page.get_need_data(check_val_list)
        assert real_val["TaskID"] == task_id, "TaskID is not correct"

        # check configuration details
        task_config_details = task_details_page.get_task_config_details()
        logging.info(f"Task config details: {task_config_details}")

        label_list = [
            "Container",
            "Models",
            "Resources",
            "Environment Variables",
            "Secrets",
            "Run Command Overrides",
            "GPU Type",
            "Max Runtime Duration",
            "Max Queued Duration",
            "Termination Grace Period",
            "Result Handling",
            "Result Path",
        ]
        for label in label_list:
            assert label in task_config_details, f"{label} is not in task config details"

        assert (
            task_config_details["Result Handling"] == "None"
        ), "ResultHandling is not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.********
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_logs(
        self,
        conf,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_without_upload,
    ):
        task_id = nvct_setup_and_teardown_without_upload
        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)
        task_details_page.navigate_to_logs_tab()

        # check result count
        assert task_details_page.get_result_count() >= 0, "Result count is not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.********
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_task_results_for_completed_tasks_with_result_upload(
        self,
        conf,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_with_upload,
    ):
        task_id = nvct_setup_and_teardown_with_upload
        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)

        task_info = task_details_page.get_task_config_details()
        logging.info(f"Task info: {task_info}")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=1800,
            check_interval_seconds=30,
        )

        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        # Set expected_results_number to 1 if environment_variables doesn't exist
        if "Environment Variables" in task_info and task_info["Environment Variables"].get(
            "NUM_OF_RESULTS"
        ):
            expected_results_number = int(
                task_info["Environment Variables"]["NUM_OF_RESULTS"]
            )

        else:
            expected_results_number = 1
        logging.info(f"Expected results number type: {type(expected_results_number)}")
        task_details_page.verify_results_number(expected_results_number)
        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        model_name = task_info["Result Path"].split("/")[-1]
        task_details_page.verify_ngc_registry_results(model_name, "output_result")
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904882
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_task_results_not_exist_without_results_upload(
        self,
        conf,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_without_upload,
    ):
        task_id = nvct_setup_and_teardown_without_upload
        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)
        assert not task_details_page.check_results_tab_visible(), "Results tab is visible"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904883
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_task_results_for_queued_tasks_with_result_upload(
        self,
        conf,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_with_upload,
        session_nvct_ssa_allscope,
    ):
        task_id = nvct_setup_and_teardown_with_upload

        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] == "QUEUED", "Task is not queued"

        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)

        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        assert (
            task_details_page.check_no_results_for_queued_tasks()
        ), "Results are present for queued tasks"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904887
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_cancel_task_deploying_on_GFN_from_task_list_page(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_with_upload,
    ):
        task_id = nvct_setup_and_teardown_with_upload
        logging.info(f"Task ID: {task_id}")

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        task_list_page.cancel_first_row_task()
        get_user_page.wait_for_timeout(1000)

        # check task is cancelled
        assert task_list_page.wait_task_to_expected_status(
            task_id, "CANCELED", 1000
        ), "Task is not cancelled"

        # check the cleanup date is update
        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)
        check_val_list = ["CleanupDate"]
        real_val = task_details_page.get_need_data(check_val_list)
        logging.info(f"CleanupDate: {real_val['CleanupDate']}")
        assert real_val["CleanupDate"] != "-", "Cleanup date is not updated"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904888
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_cancel_task_deploying_on_GFN_from_task_details_page_when_task_is_queued(
        self,
        test_data_tasks,
        get_user_page: Page,
        session_nvct_ssa_allscope,
        nvct_setup_and_teardown_with_upload,
    ):
        task_id = nvct_setup_and_teardown_with_upload
        logging.info(f"Task ID: {task_id}")

        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] == "QUEUED", "Task is not queued"

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)

        assert task_details_page.cancel_task(), "Task is not cancelled"
        get_user_page.wait_for_timeout(1000)

        # check the cleanup date is update
        check_val_list = ["Status", "CleanupDate"]
        real_val = task_details_page.get_need_data(check_val_list)
        logging.info(f"Status: {real_val['Status']}")
        logging.info(f"CleanupDate: {real_val['CleanupDate']}")
        assert real_val["Status"] == "CANCELED", "Status is not correct"
        assert real_val["CleanupDate"] != "-", "Cleanup date is not updated"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904892
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_cancel_task_deploying_on_NON_GFN_from_task_list_page(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_on_A10,
    ):
        task_id = nvct_setup_and_teardown_on_A10
        logging.info(f"Task ID: {task_id}")
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        task_list_page.cancel_first_row_task()
        get_user_page.wait_for_timeout(1000)

        # check task is cancelled
        assert task_list_page.wait_task_to_expected_status(
            task_id, "CANCELED", 1000
        ), "Task is not cancelled"

        # check the cleanup date is update
        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)
        check_val_list = ["CleanupDate"]
        real_val = task_details_page.get_need_data(check_val_list)
        logging.info(f"CleanupDate: {real_val['CleanupDate']}")
        assert real_val["CleanupDate"] != "-", "Cleanup date is not updated"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904893
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_cancel_task_deploying_on_NON_GFN_from_task_details_page_when_task_is_queued(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_on_A10,
        session_nvct_ssa_allscope,
    ):
        task_id = nvct_setup_and_teardown_on_A10
        logging.info(f"Task ID: {task_id}")

        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] == "QUEUED", "Task is not queued"

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)

        assert task_details_page.cancel_task(), "Task is not cancelled"
        get_user_page.wait_for_timeout(1000)

        # check the cleanup date is update
        check_val_list = ["Status", "CleanupDate"]
        real_val = task_details_page.get_need_data(check_val_list)
        logging.info(f"Status: {real_val['Status']}")
        logging.info(f"CleanupDate: {real_val['CleanupDate']}")
        assert real_val["Status"] == "CANCELED", "Status is not correct"
        assert real_val["CleanupDate"] != "-", "Cleanup date is not updated"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904895
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_cancel_task_deploying_on_NON_GFN_from_task_details_page_when_task_is_running(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_on_A10,
    ):
        task_id = nvct_setup_and_teardown_on_A10
        logging.info(f"Task ID: {task_id}")
        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)

        # check task is running
        task_details_page.wait_for_task_status(
            expected_status="RUNNING",
            timeout_seconds=1800,
            check_interval_seconds=30,
        )

        assert task_details_page.cancel_task(), "Task is not cancelled"
        get_user_page.wait_for_timeout(1000)

        # check the cleanup date is update
        check_val_list = ["Status", "CleanupDate"]
        real_val = task_details_page.get_need_data(check_val_list)
        logging.info(f"Status: {real_val['Status']}")
        logging.info(f"CleanupDate: {real_val['CleanupDate']}")
        assert real_val["Status"] == "CANCELED", "Status is not correct"
        assert real_val["CleanupDate"] != "-", "Cleanup date is not updated"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904897
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_task_deploying_on_GFN_from_task_list_page(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_without_teardown,
    ):
        """
        Test Steps:
        1. In the [Tasks] page, choose 1 container based task and click the 3-dots Actions button and click "Delete Task"
        2. Click "Delete Task" of the Cancel Task modal
        3. Check the task is deleted from the task list
        """
        task_id = nvct_setup_and_without_teardown
        logging.info(f"Task ID: {task_id}")

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        task_list_page.click_delete_task()
        task_list_page.confirm_delete_task()
        get_user_page.wait_for_timeout(5000)

        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904898
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_task_deploying_on_GFN_from_task_details_page_when_task_is_queued(
        self,
        test_data_tasks,
        get_user_page: Page,
        session_nvct_ssa_allscope,
        nvct_setup_and_without_teardown,
    ):
        task_id = nvct_setup_and_without_teardown
        logging.info(f"Task ID: {task_id}")

        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] == "QUEUED", "Task is not queued"

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)

        # delete task
        assert task_details_page.delete_task(), "Task is not deleted"
        get_user_page.wait_for_timeout(1000)

        # verify the task is deleted
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904900
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_task_deploying_on_GFN_from_task_details_page_when_task_is_running(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_without_teardown,
    ):
        # Step 1: Create a task to delete and navigate to Tasks page
        task_id = nvct_setup_and_without_teardown
        logging.info(f"Task ID: {task_id}")
        # go to task details page
        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)
        # check task is running
        task_details_page.wait_for_task_status(
            expected_status="RUNNING",
            timeout_seconds=1800,
            check_interval_seconds=30,
        )

        # delete task
        assert task_details_page.delete_task(), "Task is not deleted"
        get_user_page.wait_for_timeout(1000)

        # verify the task is deleted
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904902
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_task_deploying_on_GFN_from_task_details_page_when_task_is_completed(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_without_teardown,
    ):
        # Step 1: Create a task to delete and navigate to Tasks page
        task_id = nvct_setup_and_without_teardown
        logging.info(f"Task ID: {task_id}")
        # go to task details page
        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)
        # check task is completed
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=1800,
            check_interval_seconds=30,
        )

        # delete task
        assert task_details_page.delete_task(), "Task is not deleted"
        get_user_page.wait_for_timeout(1000)

        # verify the task is deleted
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904903
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_task_deploying_on_GFN_from_task_details_page_when_task_is_canceled(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_without_teardown,
    ):
        # Step 1: Create a task to delete and navigate to Tasks page
        task_id = nvct_setup_and_without_teardown
        logging.info(f"Task ID: {task_id}")

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        task_list_page.cancel_first_row_task()
        get_user_page.wait_for_timeout(1000)

        # check task is cancelled
        assert task_list_page.wait_task_to_expected_status(
            task_id, "CANCELED", 1000
        ), "Task is not cancelled"

        # go to task details page
        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)

        # delete task
        assert task_details_page.delete_task(), "Task is not deleted"
        get_user_page.wait_for_timeout(1000)

        # verify the task is deleted
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904904
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_task_deploying_on_NON_GFN_from_task_list_page(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_without_teardown_on_A10,
    ):
        task_id = nvct_setup_and_without_teardown_on_A10
        logging.info(f"Task ID: {task_id}")
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        task_list_page.click_delete_task()
        task_list_page.confirm_delete_task()
        get_user_page.wait_for_timeout(5000)

        # verify the task is deleted
        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904905
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_task_deploying_on_NON_GFN_from_task_details_page_when_task_is_queued(
        self,
        test_data_tasks,
        get_user_page: Page,
        session_nvct_ssa_allscope,
        nvct_setup_and_without_teardown_on_A10,
    ):
        task_id = nvct_setup_and_without_teardown_on_A10
        logging.info(f"Task ID: {task_id}")

        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] == "QUEUED", "Task is not queued"

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)

        # delete task
        assert task_details_page.delete_task(), "Task is not deleted"
        get_user_page.wait_for_timeout(1000)

        # verify the task is deleted
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904907
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_task_deploying_on_NON_GFN_from_task_details_page_when_task_is_running(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_without_teardown_on_A10,
    ):
        task_id = nvct_setup_and_without_teardown_on_A10
        logging.info(f"Task ID: {task_id}")

        # go to task details page
        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)
        # check task is running
        task_details_page.wait_for_task_status(
            expected_status="RUNNING",
            timeout_seconds=1800,
            check_interval_seconds=30,
        )

        # delete task
        assert task_details_page.delete_task(), "Task is not deleted"
        get_user_page.wait_for_timeout(1000)

        # verify the task is deleted
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904909
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_task_deploying_on_NON_GFN_from_task_details_page_when_task_is_completed(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_without_teardown_on_A10,
    ):
        task_id = nvct_setup_and_without_teardown_on_A10
        logging.info(f"Task ID: {task_id}")

        # go to task details page
        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)
        # check task is completed
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=1800,
            check_interval_seconds=30,
        )

        # delete task
        assert task_details_page.delete_task(), "Task is not deleted"
        get_user_page.wait_for_timeout(1000)

        # verify the task is deleted
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904910
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_task_deploying_on_NON_GFN_from_task_details_page_when_task_is_canceled(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_without_teardown_on_A10,
    ):
        task_id = nvct_setup_and_without_teardown_on_A10
        logging.info(f"Task ID: {task_id}")

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        task_list_page.cancel_first_row_task()
        get_user_page.wait_for_timeout(1000)

        # check task is cancelled
        assert task_list_page.wait_task_to_expected_status(
            task_id, "CANCELED", 1000
        ), "Task is not cancelled"

        # go to task details page
        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)
        # delete task
        assert task_details_page.delete_task(), "Task is not deleted"
        get_user_page.wait_for_timeout(1000)

        # verify the task is deleted
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904911
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_personal_api_key_from_task_list_page(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
        nvct_setup_and_teardown_with_upload,
    ):
        task_id = nvct_setup_and_teardown_with_upload
        logging.info(f"Task ID: {task_id}")

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        logging.info(f"Searching for task: {task_id}")
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        logging.info(f"Updating API key: {running_config}")
        assert task_list_page.update_api_key(
            running_config["nvcf_admin_alternative"]["api_key"]
        ), "API key is not updated successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904912
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_personal_api_key_from_task_details_page(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
        nvct_setup_and_teardown_with_upload,
    ):
        task_id = nvct_setup_and_teardown_with_upload
        logging.info(f"Task ID: {task_id}")

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)
        logging.info(f"Updating API key: {running_config}")
        assert task_details_page.update_api_key(
            running_config["nvcf_admin_alternative"]["api_key"]
        ), "API key is not updated successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904913
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_personal_api_key_from_task_list_page_using_invalid_key_negative(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
        nvct_setup_and_teardown_with_upload,
    ):
        task_id = nvct_setup_and_teardown_with_upload
        logging.info(f"Task ID: {task_id}")

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        logging.info(f"Searching for task: {task_id}")
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        logging.info(f"Updating API key: {running_config}")
        assert task_list_page.update_api_key(
            "test11",
            is_valid_key=False,
            fail_msg="Authentication Failed",
        ), "API key is not updated successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904914
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_personal_api_key_from_task_list_page_with_personal_api_key_out_of_private_registry_scope_negative(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
        nvct_setup_and_teardown_with_upload,
    ):
        task_id = nvct_setup_and_teardown_with_upload
        logging.info(f"Task ID: {task_id}")

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        logging.info(f"Searching for task: {task_id}")
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        assert task_list_page.update_api_key(
            # running_config["nvcf_without_private_registry_scope"]["api_key"],
            running_config["nvcf_admin_without_pr"]["api_key"],
            is_valid_key=False,
            fail_msg="Access Denied",
        ), "API key is not updated successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108901
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_helm_based_task_with_expired_personal_key_GFN_negative(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_helm_task_sample_gfn_upload_result"]
        else:
            task_info = test_data_tasks["prod_helm_task_sample_gfn_upload_result"]
        logging.info("Start a creation of a helm-based task.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin_expired"]["api_key"],
            taskCreateOption=TaskCreateOptionENUM.WITHOUTPR_OR_EXPIRED,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108900
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_helm_based_task_with_personal_key_out_of_PR_scope_GFN_negative(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_helm_task_sample_gfn_upload_result"]
        else:
            task_info = test_data_tasks["prod_helm_task_sample_gfn_upload_result"]
        logging.info("Start a creation of a helm-based task.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin_without_pr"]["api_key"],
            taskCreateOption=TaskCreateOptionENUM.WITHOUTPR_OR_EXPIRED,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108899
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_terminationGracePeriodDuration_larger_than_maxRuntimeDuration_creating_helm_based_task_on_GFN_negative(
        self,
        test_data_tasks,
        get_user_page: Page,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_helm_task_sample_terminationGracePeriodDuration_large_than_maxRuntimeDuration_GFN"
            ]
        else:
            task_info = test_data_tasks[
                "prod_helm_task_sample_terminationGracePeriodDuration_large_than_maxRuntimeDuration_GFN"
            ]
        logging.info("Start a creation of a task container function.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            upload_results=False,
            taskCreateOption=TaskCreateOptionENUM.GRACELARGERTHANRUNTIME,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4145401
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_logs_in_windows_view(
        self,
        get_user_page: Page,
        nvct_setup_and_teardown_without_upload,
    ):
        """
        Test case to verify logs in windows view
        Steps:
        1. Navigate to task details page
        2. Go to logs tab
        3. Switch to windows view and verify logs
        4. Assert test passes if either logs are found or "No logs" message is visible
        """
        task_id = nvct_setup_and_teardown_without_upload
        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)
        task_details_page.navigate_to_logs_tab()

        # Switch to windows view
        task_details_page.switch_to_windows_view()

        # Check logs using the common function
        log_results = task_details_page.check_logs_in_windows_view()

        # Test passes if either logs are found or "No logs" message is visible
        assert (
            log_results["has_logs"] or log_results["no_logs_message"]
        ), "Neither logs were found nor 'No logs' message was visible"

        if log_results["has_logs"]:
            logging.info(f"Found {log_results['log_count']} logs in windows view")
        else:
            logging.info("No logs were found, but 'No logs' message was visible")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108902
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_helm_based_task_result_in_status_EXCEEDED_MAX_RUNTIME_DURATION_negative(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_helm_task_sample_gfn_exceed_max_runtime_duration"
            ]
        else:
            task_info = test_data_tasks[
                "prod_helm_task_sample_gfn_exceed_max_runtime_duration"
            ]
        logging.info("Start a creation of a task container with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until TIMED OUT
        logging.info("Monitoring task status until TIMED OUT")
        task_details_page.wait_for_task_status(
            expected_status="TIMED OUT",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )
        assert (
            task_details_page.get_tooltip_text_for_task_status()
            == "Task has exceeded maximum runtime durationTask has exceeded maximum runtime duration"
        )
        logging.info("Test completed successfully")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108918
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_helm_based_task_deploying_on_GFN_from_task_list_page(
        self,
        get_user_page: Page,
        nvct_setup_without_teardown_helmchart,
    ):
        """
        Test Steps:
        1. In the [Tasks] page, choose 1 helm based task and click the 3-dots Actions button and click "Delete Task"
        2. Click "Delete Task" of the Cancel Task modal
        3. Check the task is deleted from the task list
        """
        task_id = nvct_setup_without_teardown_helmchart
        logging.info(f"Task ID: {task_id}")

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        task_list_page.click_delete_task()
        task_list_page.confirm_delete_task()
        get_user_page.wait_for_timeout(5000)

        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108919
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_helm_based_task_deploying_on_GFN_from_task_details_page_when_task_is_queued(
        self,
        get_user_page: Page,
        session_nvct_ssa_allscope,
        nvct_setup_without_teardown_helmchart,
    ):
        task_id = nvct_setup_without_teardown_helmchart
        logging.info(f"Task ID: {task_id}")

        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] == "QUEUED", "Task is not queued"

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)

        # delete task
        assert task_details_page.delete_task(), "Task is not deleted"
        get_user_page.wait_for_timeout(1000)

        # verify the task is deleted
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108920
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_helm_based_task_deploying_on_GFN_from_task_details_page_when_task_is_running(
        self,
        get_user_page: Page,
        session_nvct_ssa_allscope,
        nvct_setup_without_teardown_helmchart,
    ):
        task_id = nvct_setup_without_teardown_helmchart
        logging.info(f"Task ID: {task_id}")

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)

        task_details_page.wait_for_task_status(
            expected_status="RUNNING",
            timeout_seconds=3000,
            check_interval_seconds=30,
        )

        # delete task
        assert task_details_page.delete_task(), "Task is not deleted"
        get_user_page.wait_for_timeout(1000)

        # verify the task is deleted
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108922
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_helm_based_task_deploying_on_GFN_from_task_details_page_when_task_is_completed(
        self,
        get_user_page: Page,
        session_nvct_ssa_allscope,
        nvct_setup_without_teardown_helmchart,
    ):
        task_id = nvct_setup_without_teardown_helmchart
        logging.info(f"Task ID: {task_id}")

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)

        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=3000,
            check_interval_seconds=30,
        )

        # delete task
        assert task_details_page.delete_task(), "Task is not deleted"
        get_user_page.wait_for_timeout(1000)

        # verify the task is deleted
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108923
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_helm_based_task_deploying_on_GFN_from_task_details_page_when_task_is_canceled(
        self,
        get_user_page: Page,
        session_nvct_ssa_allscope,
        nvct_setup_without_teardown_helmchart,
    ):
        task_id = nvct_setup_without_teardown_helmchart
        logging.info(f"Task ID: {task_id}")

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        task_list_page.cancel_first_row_task()
        get_user_page.wait_for_timeout(1000)

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)

        task_details_page.wait_for_task_status(
            expected_status="CANCELED",
            timeout_seconds=3000,
            check_interval_seconds=30,
        )

        # delete task
        assert task_details_page.delete_task(), "Task is not deleted"
        get_user_page.wait_for_timeout(1000)

        # verify the task is deleted
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108924
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_personal_api_key_for_helm_based_task_from_task_list_page(
        self,
        get_user_page: Page,
        running_config,
        nvct_setup_and_teardown_without_upload_helmchart,
    ):
        task_id = nvct_setup_and_teardown_without_upload_helmchart
        logging.info(f"Task ID: {task_id}")

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        logging.info(f"Searching for task: {task_id}")
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        logging.info(f"Updating API key: {running_config}")
        assert task_list_page.update_api_key(
            running_config["nvcf_admin_alternative"]["api_key"]
        ), "API key is not updated successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108925
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_personal_api_key_for_helm_based_task_from_task_details_page(
        self,
        get_user_page: Page,
        running_config,
        nvct_setup_and_teardown_without_upload_helmchart,
    ):
        task_id = nvct_setup_and_teardown_without_upload_helmchart
        logging.info(f"Task ID: {task_id}")

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)
        logging.info(f"Updating API key: {running_config}")
        assert task_details_page.update_api_key(
            running_config["nvcf_admin_alternative"]["api_key"]
        ), "API key is not updated successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108926
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_personal_api_key_for_helm_based_task_from_task_list_page_using_invalid_key_negative(
        self,
        get_user_page: Page,
        running_config,
        nvct_setup_and_teardown_with_upload_helm,
    ):
        task_id = nvct_setup_and_teardown_with_upload_helm
        logging.info(f"Task ID: {task_id}")

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        logging.info(f"Searching for task: {task_id}")
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        logging.info(f"Updating API key: {running_config}")
        assert task_list_page.update_api_key(
            "test11",
            is_valid_key=False,
            fail_msg="Authentication Failed",
        ), "API key is not updated successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108927
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_update_personal_api_key_for_helm_based_task_from_task_list_page_with_personal_api_key_out_of_private_registry_scope_negative(
        self,
        get_user_page: Page,
        running_config,
        nvct_setup_and_teardown_with_upload_helm,
    ):
        task_id = nvct_setup_and_teardown_with_upload_helm
        logging.info(f"Task ID: {task_id}")

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        logging.info(f"Searching for task: {task_id}")
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        assert task_list_page.update_api_key(
            # running_config["nvcf_without_private_registry_scope"]["api_key"],
            running_config["nvcf_admin_without_pr"]["api_key"],
            is_valid_key=False,
            fail_msg="Access Denied",
        ), "API key is not updated successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904915
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_add_secrets_key_for_task_not_created_with_secrets_from_task_list_page(
        self,
        get_user_page: Page,
        running_config,
        nvct_setup_and_teardown_without_upload,
        session_nvct_ssa_allscope,
    ):
        task_id = nvct_setup_and_teardown_without_upload
        logging.info(f"Task ID: {task_id}")

        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] in [
            "QUEUED",
            "RUNNING",
        ], "Task is not queued or running"

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        logging.info(f"Searching for task: {task_id}")
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        add_secret = {
            "key": "test",
            "value": "test",
        }
        logging.info(f"Adding secret: {add_secret}")
        assert task_list_page.manage_secrets(
            [add_secret]
        ), "Secret is not added successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904916
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_add_secrets_key_for_task_not_created_with_secrets_from_task_details_page(
        self,
        get_user_page: Page,
        nvct_setup_and_teardown_without_upload,
    ):
        task_id = nvct_setup_and_teardown_without_upload
        logging.info(f"Task ID: {task_id}")

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)
        logging.info(f"Searching for task: {task_id}")
        add_secret = {
            "key": "test",
            "value": "test",
        }
        logging.info(f"Adding secret: {add_secret}")
        assert task_details_page.manage_secrets(
            [add_secret]
        ), "Secret is not added successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904917
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_add_additional_secrets_key_from_task_list_page(
        self,
        get_user_page: Page,
        running_config,
        nvct_setup_and_teardown_without_upload,
        session_nvct_ssa_allscope,
    ):
        task_id = nvct_setup_and_teardown_without_upload
        logging.info(f"Task ID: {task_id}")

        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] in [
            "QUEUED",
            "RUNNING",
        ], "Task is not queued or running"

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        logging.info(f"Searching for task: {task_id}")
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        add_secret1 = {
            "key": "test",
            "value": "test",
        }
        add_secret2 = {
            "key": "test2",
            "value": "test2",
        }
        assert task_list_page.manage_secrets(
            [add_secret1, add_secret2]
        ), "Secret is not added successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904918
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_add_additional_secrets_key_from_task_details_page(
        self,
        get_user_page: Page,
        nvct_setup_and_teardown_without_upload,
    ):
        task_id = nvct_setup_and_teardown_without_upload
        logging.info(f"Task ID: {task_id}")

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)
        logging.info(f"Searching for task: {task_id}")
        add_secret1 = {
            "key": "test",
            "value": "test",
        }
        add_secret2 = {
            "key": "test2",
            "value": "test2",
        }
        assert task_details_page.manage_secrets(
            [add_secret1, add_secret2]
        ), "Secret is not added successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904919
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_edit_existing_secrets_keys_from_task_list_page(
        self,
        get_user_page: Page,
        running_config,
        test_data_tasks,
        nvct_teardown,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["task_sample_on_GFN_stg"]
        else:
            task_info = test_data_tasks["task_sample_on_GFN"]
        logging.info("Start a creation of a task container with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            secrets={"test": "test"},
            upload_results=False,
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        logging.info(f"Searching for task: {task_id}")
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        add_secret = {"test": "test1"}
        logging.info(f"Modifying secret: {add_secret}")
        assert task_list_page.modify_secrets(
            [add_secret]
        ), "Secret is not modified successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904920
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_edit_existing_secrets_keys_from_task_details_page(
        self,
        get_user_page: Page,
        running_config,
        test_data_tasks,
        nvct_teardown,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["task_sample_on_GFN_stg"]
        else:
            task_info = test_data_tasks["task_sample_on_GFN"]
        logging.info("Start a creation of a task container with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            secrets={"test": "test"},
            upload_results=False,
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)

        assert task_details_page.modify_secrets(
            [{"test": "test1"}]
        ), "Secret is not modified successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3916909
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_task_container_with_empty_metadata_to_progress_file(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        """
        Test case to verify E2E flow of creating a container-based task with result upload on BYOC
        Steps:
        1. Create task with required fields
        2. Monitor task status until completion
        3. Verify results in NGC registry
        """
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_gfn_model_resource"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_gfn_model_resource"]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        environment_variables = {
            "INCLUDE_METADATA": "false",
        }
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            env=environment_variables,
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)

        data_dict = task_details_page.get_need_data(["Status"])
        assert data_dict["Status"] == "QUEUED", "Task is not queued"

        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )

        # Step 3: navigate to results tab and verify results
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        # Set expected_results_number to 1 if environment_variables doesn't exist
        if "environment_variables" in task_info and task_info["environment_variables"].get(
            "NUM_OF_RESULTS"
        ):
            expected_results_number = task_info["environment_variables"]["NUM_OF_RESULTS"]
        else:
            expected_results_number = 1
        task_details_page.verify_results_number(expected_results_number)
        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        task_details_page.verify_ngc_registry_results(
            task_info["ResultsUpload_model_name"], "output_result"
        )
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3924793
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_model_name_requirements_negative(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        """
        Test case to verify model name requirements for container-based task
        Steps:
        1. Create task with required fields
        2. Monitor task status until completion
        3. Verify results in NGC registry
        """
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["task_sample_on_GFN_stg"]
        else:
            task_info = test_data_tasks["task_sample_on_GFN"]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        nagative_model_name = "NegativeModelName"
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=nagative_model_name,
            Personal_key=running_config["nvcf_admin"]["api_key"],
            taskCreateOption=TaskCreateOptionENUM.NEGATIVE_MODEL_NAME,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904869
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_result_in_status_EXCEEDED_MAX_RUNTIME_DURATION_negative(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_container_task_sample_gfn_exceed_max_runtime_duration"
            ]
        else:
            task_info = test_data_tasks[
                "prod_container_task_sample_gfn_exceed_max_runtime_duration"
            ]
        logging.info("Start a creation of a task container with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            env=task_info["environment_variables"],
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until TIMED OUT
        logging.info("Monitoring task status until TIMED OUT")
        task_details_page.wait_for_task_status(
            expected_status="TIMED OUT",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=60,
        )
        assert (
            task_details_page.get_tooltip_text_for_task_status()
            == "Task has exceeded maximum runtime durationTask has exceeded maximum runtime duration"
        )
        logging.info("Test completed successfully")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108897
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_choose_none_for_maxruntimeduration_when_creating_helm_based_task_on_GFN_negative(
        self,
        test_data_tasks,
        get_user_page: Page,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_helm_task_NONE_runs_forever_GFN"]
        else:
            task_info = test_data_tasks["prod_helm_task_NONE_runs_forever_GFN"]
        logging.info("Start creating a helm based task.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            upload_results=False,
            taskCreateOption=TaskCreateOptionENUM.NONEFORMAXRUNTIMEDURATIONGFN,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108898
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_choose_large_than_8_hours_for_maxRuntimeDuration_when_creating_helm_based_task_on_GFN_negative(
        self,
        test_data_tasks,
        get_user_page: Page,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_helm_task_maxRuntimeDuration_larger_than_8_GFN"
            ]
        else:
            task_info = test_data_tasks[
                "prod_helm_task_maxRuntimeDuration_larger_than_8_GFN"
            ]
        logging.info("Start creating a helm based task.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            upload_results=False,
            taskCreateOption=TaskCreateOptionENUM.RUNTIMELARGERTHAN8,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5089743
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_placeholder_text_alignment_for_container_run_commane_in_create_task_page(
        self,
        get_user_page: Page,
    ):
        """
        Test check the placeholder text alignment for container run command in create task page
        """

        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()

        create_task_page.page.locator(
            create_task_page.Form.elements.RunCommandExpandBtn
        ).click()
        try:
            create_task_page.page.locator(
                create_task_page.Form.elements.RunCommandExpandBtn
            ).wait_for(state="visible")
        except Exception:
            logging.info("Model has not opened and need to click again to make it open")
            create_task_page.page.locator(
                create_task_page.Form.elements.RunCommandExpandBtn
            ).click()
        text_align = create_task_page.page.locator(
            create_task_page.Form.elements.RunCommandTextArea
        ).evaluate("el => window.getComputedStyle(el).textAlign")
        logging.info(f"text_align: {text_align}")
        assert text_align == "start", "The text alignment is not as expected."

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5089774
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_tag_input_box_in_create_task_page_for_container(
        self,
        get_user_page: Page,
    ):
        """
        Test check the tag input box in create task page for container
        """

        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()

        assert (
            create_task_page.Form.check_tag_input_box_in_create_task_page()
        ), "Tags did not wrap to multiple lines after adding sufficient tags."

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5089775
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_tag_input_box_in_create_task_page_for_helm_chart(
        self,
        get_user_page: Page,
    ):
        """
        Test check the tag input box in create task page for helm
        """

        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()

        create_task_page.page.get_by_role("radio", name="Custom Helm Chart").click()

        assert (
            create_task_page.Form.check_tag_input_box_in_create_task_page()
        ), "Tags did not wrap to multiple lines after adding sufficient tags."

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904885
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_clone_container_task_with_upload_results_GFN_from_task_list_page(
        self,
        test_data_tasks,
        nvct_setup_and_teardown_with_upload,
        session_nvct_ssa_allscope: dict[str, APISession],
        get_user_page: Page,
        nvct_teardown,
        get_personal_keys_with_cookies,
        personal_key_teardown,
    ):
        resp = get_personal_keys_with_cookies()
        logging.info(f"resp['apiKeys']: {resp['apiKeys']}")
        logging.info(f"exiting personal key number: {len(resp['apiKeys'])}")
        assert len(resp["apiKeys"]) < 28, "Personal API key has exceeded the limit"

        task_id = nvct_setup_and_teardown_with_upload

        task_details = NVCTUtils(session_nvct_ssa_allscope).retrieve_task_details(
            task_id=task_id
        )
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        task_list_page.click_clone_task()

        deployment_config = test_data_tasks["clone_task_deployment_config"]

        duration = deployment_config["generate_person_key_config"]["duration"]
        key_name = deployment_config["generate_person_key_config"]["key_name"]
        # Create cloned task
        task_name = add_timestamp("clonedTask", "%m%d%H%M%S")
        clone_task_page = TasksClonePage(get_user_page)
        clone_task_page.verify_clone_details(task_details.get("task"))
        personal_key = add_timestamp(key_name)
        clone_task_page.clone_task(
            task_name,
            gpu_type=deployment_config["gpu_type"],
            instance_type=deployment_config["instance_type"],
            clusters=None,
            generate_personal_key=True,
            generate_person_key_list=[personal_key, duration],
        )

        resp_later = get_personal_keys_with_cookies()
        logging.info(f"resp_later['apiKeys']: {resp_later['apiKeys']}")
        logging.info(f"exiting personal key number: {len(resp_later['apiKeys'])}")
        assert len(resp_later["apiKeys"]) < 28, "Personal API key has exceeded the limit"
        keys = [
            key["keyId"] for key in resp_later["apiKeys"] if key["name"] == personal_key
        ]
        if len(resp_later["apiKeys"]) > len(resp["apiKeys"]) and keys == "":
            diff = [d for d in resp_later["apiKeys"] if d not in resp["apiKeys"]][0]
            logging.info(f"diff: {diff}")
            keys = diff["keyId"]
        self.api_keys_remove_list.append(keys[0])

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Cloned task with ID: {task_id}")
        # Verify initial task status is QUEUED
        self.nvct_remove_list.append(
            {
                "task_id": task_id,
            }
        )
        data_dict = task_details_page.get_need_data(["Status"])
        assert data_dict["Status"] == "QUEUED", "Task is not queued"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5113775
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_clone_container_task_without_upload_results_GFN_from_task_list_page(
        self,
        test_data_tasks,
        nvct_setup_and_teardown_without_upload,
        session_nvct_ssa_allscope: dict[str, APISession],
        get_user_page: Page,
        nvct_teardown,
    ):
        task_id = nvct_setup_and_teardown_without_upload
        task_details = NVCTUtils(session_nvct_ssa_allscope).retrieve_task_details(
            task_id=task_id
        )
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        task_list_page.click_clone_task()

        deployment_config = test_data_tasks["clone_task_deployment_config"]
        # Create cloned task
        task_name = add_timestamp("clonedTask", "%m%d%H%M%S")
        clone_task_page = TasksClonePage(get_user_page)
        clone_task_page.verify_clone_details(task_details.get("task"))
        clone_task_page.clone_task(
            task_name,
            gpu_type=deployment_config["gpu_type"],
            instance_type=deployment_config["instance_type"],
            clusters=None,
            generate_personal_key=False,
            upload_results=False,
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Cloned task with ID: {task_id}")
        self.nvct_remove_list.append(
            {
                "task_id": task_id,
            }
        )
        # Verify initial task status is QUEUED
        data_dict = task_details_page.get_need_data(["Status"])
        assert data_dict["Status"] == "QUEUED", "Task is not queued"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5113782
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_clone_container_task_without_upload_results_Non_GFN_from_task_list_page(
        self,
        test_data_tasks,
        nvct_setup_and_teardown_without_upload,
        session_nvct_ssa_allscope: dict[str, APISession],
        get_user_page: Page,
        nvct_teardown,
    ):
        task_id = nvct_setup_and_teardown_without_upload
        task_details = NVCTUtils(session_nvct_ssa_allscope).retrieve_task_details(
            task_id=task_id
        )
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        task_list_page.click_clone_task()

        deployment_config = test_data_tasks["clone_task_non_gfn_deployment_config"]
        # Create cloned task
        task_name = add_timestamp("clonedTask", "%m%d%H%M%S")
        clone_task_page = TasksClonePage(get_user_page)
        clone_task_page.verify_clone_details(task_details.get("task"))
        clone_task_page.clone_task(
            task_name,
            gpu_type=deployment_config["gpu_type"],
            instance_type=deployment_config["instance_type"],
            clusters=None,
            generate_personal_key=False,
            upload_results=False,
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Cloned task with ID: {task_id}")
        self.nvct_remove_list.append(
            {
                "task_id": task_id,
            }
        )
        # Verify initial task status is QUEUED
        data_dict = task_details_page.get_need_data(["Status"])
        assert data_dict["Status"] == "QUEUED", "Task is not queued"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904886
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_clone_container_task_from_task_details_page(
        self,
        test_data_tasks,
        nvct_setup_and_teardown_with_upload,
        session_nvct_ssa_allscope: dict[str, APISession],
        get_user_page: Page,
        nvct_teardown,
        get_personal_keys_with_cookies,
        personal_key_teardown,
    ):
        """
        [E2E] Clone container task from task details page

        Test Procedure:
        1. In the [Tasks] page, choose 1 container based task, click the task name.
        2. It will lead to the Details page of the task.
        3. Click the 3-dots button on the right top of the page and click "Clone Task"
        4. It will lead to the [Clone Task] page.
        5. Check all the fields are prefilled with the same content as the copied function
           except for the Task Name and Person API Key. (cluster field is no longer pre-filled)
        6. Fill in the Task Name and use the "Generate Personal API Key" button to generate the Personal API Key.
        7. Click "Create Task". You will see "xxx successfully created." and it will switch to the task details page.
        8. The status of the task is Queued at first.
        """
        resp = get_personal_keys_with_cookies()
        logging.info(f"resp['apiKeys']: {resp['apiKeys']}")
        logging.info(f"exiting personal key number: {len(resp['apiKeys'])}")
        assert len(resp["apiKeys"]) < 28, "Personal API key has exceeded the limit"

        task_id = nvct_setup_and_teardown_with_upload

        # Step 1 & 2: Navigate to task list page and click on task name to go to details page
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()

        # Use the existing navigate_to_task_details_page method to go to details page
        task_list_page.navigate_to_task_details_page(task_id=task_id)

        # Now we're on the task details page
        task_details_page = TasksDetailsPage(get_user_page)

        # Get task details before cloning for verification
        task_details = NVCTUtils(session_nvct_ssa_allscope).retrieve_task_details(
            task_id=task_id
        )

        # Step 3: Click the 3-dots button and click "Clone Task"
        task_details_page.click_clone_task()

        # Step 4: We're now on the Clone Task page
        clone_task_page = TasksClonePage(get_user_page)

        # Step 5: Check all fields are prefilled with same content except Task Name and Personal API Key
        # (cluster field is no longer pre-filled according to the test template)
        clone_task_page.verify_clone_details(task_details.get("task"))

        # Step 6: Fill in Task Name and generate Personal API Key
        deployment_config = test_data_tasks["clone_task_deployment_config"]
        duration = deployment_config["generate_person_key_config"]["duration"]
        key_name = deployment_config["generate_person_key_config"]["key_name"]

        task_name = add_timestamp("clonedTaskFromDetails", "%m%d%H%M%S")
        personal_key = add_timestamp(key_name)

        clone_task_page.clone_task(
            task_name,
            gpu_type=deployment_config["gpu_type"],
            instance_type=deployment_config["instance_type"],
            clusters=None,
            generate_personal_key=True,
            generate_person_key_list=[personal_key, duration],
        )

        resp_later = get_personal_keys_with_cookies()
        logging.info(f"resp_later['apiKeys']: {resp_later['apiKeys']}")
        logging.info(f"exiting personal key number: {len(resp_later['apiKeys'])}")
        assert len(resp_later["apiKeys"]) < 28, "Personal API key has exceeded the limit"
        keys = [
            key["keyId"] for key in resp_later["apiKeys"] if key["name"] == personal_key
        ]
        if len(resp_later["apiKeys"]) > len(resp["apiKeys"]) and keys == "":
            diff = [d for d in resp_later["apiKeys"] if d not in resp["apiKeys"]][0]
            logging.info(f"diff: {diff}")
            keys = diff["keyId"]

        logging.info(keys)
        # tear down for the new dup one after rotation
        self.api_keys_remove_list.append(keys[0])

        # Step 7: Click "Create Task" - this should show "xxx successfully created."
        # and switch to task details page
        cloned_task_details_page = TasksDetailsPage(get_user_page)
        cloned_task_id = cloned_task_details_page.get_task_id_from_url()
        logging.info(f"Cloned task with ID: {cloned_task_id}")

        # Add to cleanup list
        self.nvct_remove_list.append(
            {
                "task_id": cloned_task_id,
            }
        )

        # Step 8: Verify the status of the task is QUEUED at first
        data_dict = cloned_task_details_page.get_need_data(["Status"])
        assert data_dict["Status"] == "QUEUED", "Task is not queued"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5091594
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_labels_for_multi_row_input_in_create_task_page(
        self,
        get_user_page: Page,
    ):
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()

        # check the labels for multi-row input for models
        create_task_page.page.locator(create_task_page.Form.elements.ModelExpandBtn).click()
        create_task_page.page.wait_for_load_state("load")
        create_task_page.page.get_by_role("button", name="Add Another Model").click()

        model_fields = create_task_page.page.locator(
            '[data-testid="kui-accordion"]'
        ).filter(has=create_task_page.page.get_by_text("Select Model(s)"))
        model_labels = [
            label
            for label in model_fields.locator(
                "label[data-testid='kui-label']:visible"
            ).all()
            if label.text_content().strip() == "Model"
        ]
        assert len(model_labels) == 1, "The number of Model labels is not as expected."
        model_version_labels = [
            label
            for label in model_fields.locator(
                "label[data-testid='kui-label']:visible"
            ).all()
            if label.text_content().strip() == "Model Version"
        ]
        assert (
            len(model_version_labels) == 1
        ), "The number of Model Version labels is not as expected."
        model_name_labels = [
            label
            for label in model_fields.locator(
                "label[data-testid='kui-label']:visible"
            ).all()
            if label.text_content().strip() == "Name"
        ]
        assert (
            len(model_name_labels) == 1
        ), "The number of Name labels in model-fields is not as expected."

        # check the labels for multi-row input for resources
        create_task_page.page.locator(
            create_task_page.Form.elements.ResourceExpandBtn
        ).click()
        create_task_page.page.wait_for_load_state("load")
        create_task_page.page.get_by_role("button", name="Add Another Resource").click()

        resource_fields = create_task_page.page.locator(
            '[data-testid="kui-accordion"]'
        ).filter(has=create_task_page.page.get_by_text("Select Resource(s)"))
        resource_labels = [
            label
            for label in resource_fields.locator(
                "label[data-testid='kui-label']:visible"
            ).all()
            if label.text_content().strip() == "Resource"
        ]
        assert (
            len(resource_labels) == 1
        ), "The number of Resource labels is not as expected."
        resource_version_labels = [
            label
            for label in resource_fields.locator(
                "label[data-testid='kui-label']:visible"
            ).all()
            if label.text_content().strip() == "Resource Version"
        ]
        assert (
            len(resource_version_labels) == 1
        ), "The number of Resource Version labels is not as expected."
        resource_name_labels = [
            label
            for label in resource_fields.locator(
                "label[data-testid='kui-label']:visible"
            ).all()
            if label.text_content().strip() == "Name"
        ]
        assert (
            len(resource_name_labels) == 1
        ), "The number of Name labels in recipe-fields is not as expected."

        # check the labels for multi-row input for environment variables
        create_task_page.page.locator(create_task_page.Form.elements.EnvExpandBtn).click()
        create_task_page.page.wait_for_load_state("load")
        create_task_page.page.get_by_role(
            "button", name="Add Another Environment Variable"
        ).click()

        env_fields = create_task_page.page.locator('[data-testid="kui-accordion"]').filter(
            has=create_task_page.page.get_by_text("Environment Variables")
        )
        env_key_labels = [
            label
            for label in env_fields.locator("label[data-testid='kui-label']:visible").all()
            if label.text_content().strip() == "Environment Key"
        ]
        assert (
            len(env_key_labels) == 1
        ), "The number of Environment Key labels is not as expected."
        env_value_labels = [
            label
            for label in env_fields.locator("label[data-testid='kui-label']:visible").all()
            if label.text_content().strip() == "Environment Value"
        ]
        assert (
            len(env_value_labels) == 1
        ), "The number of Environment Value labels in env-fields is not as expected."

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5096145
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_tag_input_description_in_create_task_page_for_container(
        self,
        get_user_page: Page,
    ):
        """
        Test check the tag input description in create task page for container
        """

        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()

        assert (
            create_task_page.Form.check_tag_input_description_in_create_task_page()
        ), "Tags description is not as expected."

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5096146
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_tag_input_description_in_create_task_page_for_helm_chart(
        self,
        get_user_page: Page,
    ):
        """
        Test check the tag input description in create task page for helm
        """

        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()

        create_task_page.page.get_by_role("radio", name="Custom Helm Chart").click()

        assert (
            create_task_page.Form.check_tag_input_description_in_create_task_page()
        ), "Tags description is not as expected."

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5096154
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_helper_description_text_for_model_resource_in_create_container_task_page(
        self,
        get_user_page: Page,
    ):
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()

        expected_model_desc = (
            "Select model(s) to externally load into your container runtime if not packaged within. "
            "Model(s) will be available under /config/models/{modelName}"
        )
        expected_resource_desc = (
            "Select resource(s) to externally load into your container runtime if not packaged within. "
            "Resource(s) will be available under /config/resources/{resourceName}"
        )

        # check the helper description text for [Select Model(s)] accordion
        model_accordion = create_task_page.page.locator(
            '[data-testid="kui-accordion"]'
        ).filter(has=create_task_page.page.get_by_text("Select Model(s)"))
        model_desc = model_accordion.locator(
            "p.text-gray-600 span.text-regular-md"
        ).text_content()
        model_desc = " ".join(model_desc.split())
        logging.info(f"model_desc: {model_desc}")
        assert (
            model_desc == expected_model_desc
        ), f"Model helper desc not as expected: {model_desc}"

        # check the helper description text for [Select Resource(s)] accordion
        resource_accordion = create_task_page.page.locator(
            '[data-testid="kui-accordion"]'
        ).filter(has=create_task_page.page.get_by_text("Select Resource(s)"))
        resource_desc = resource_accordion.locator(
            "p.text-gray-600 span.text-regular-md"
        ).text_content()
        resource_desc = " ".join(resource_desc.split())
        logging.info(f"resource_desc: {resource_desc}")
        assert (
            resource_desc == expected_resource_desc
        ), f"Resource helper desc not as expected: {resource_desc}"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5096155
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_model_resource_mount_points_for_1_model_resource_in_function_version_details_page_for_container_task(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_teardown,
        running_config,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_gfn_model_resource"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_gfn_model_resource"]
        logging.info("Start a creation of container task with all information.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            model=task_info["model"],
            resource=task_info["resource"],
        )
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        self.nvct_remove_list.append(
            {
                "task_id": task_id,
            }
        )
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)

        task_config_details = task_details_page.get_task_config_details()
        logging.info(f"task_config_details: {task_config_details}")
        expected_model_mount_points = "/config/models/" + task_info["model"][0][2]
        expected_resource_mount_points = "/config/resources/" + task_info["resource"][0][2]
        assert (
            task_config_details["Model Mount Points"][0] == expected_model_mount_points
        ), f"Model mount points not as expected: {task_config_details['Model Mount Points']}"
        assert (
            task_config_details["Resource Mount Points"][0]
            == expected_resource_mount_points
        ), f"Resource mount points not as expected: {task_config_details['Resource Mount Points']}"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5096156
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_model_resource_mount_points_for_1_model_and_multi_resources_in_function_version_details_page_for_container_task(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_teardown,
        running_config,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_gfn_model_resource"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_gfn_model_resource"]
        logging.info("Start a creation of container task with all information.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        single_resource = task_info["resource"]
        multi_resources = [copy.deepcopy(x) for x in single_resource] + [
            copy.deepcopy(x) for x in single_resource
        ]
        multi_resources[1][-1] = "a"

        logging.info(f"multi_resources: {multi_resources}")

        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            model=task_info["model"],
            resource=multi_resources,
        )
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        self.nvct_remove_list.append(
            {
                "task_id": task_id,
            }
        )
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)

        task_config_details = task_details_page.get_task_config_details()
        logging.info(f"task_config_details: {task_config_details}")
        expected_model_mount_points = "/config/models/" + task_info["model"][0][2]
        expected_resource_mount_points = [
            "/config/resources/" + multi_resources[0][2],
            "/config/resources/" + multi_resources[1][2],
        ]
        assert (
            task_config_details["Model Mount Points"][0] == expected_model_mount_points
        ), f"Model mount points not as expected: {task_config_details['Model Mount Points']}"
        assert (
            sorted(task_config_details["Resource Mount Points"])
            == sorted(expected_resource_mount_points)
        ), f"Resource mount points not as expected: {task_config_details['Resource Mount Points']}"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5096157
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_model_resource_mount_points_for_multi_models_and_multi_resources_in_function_version_details_page_for_container_task(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_teardown,
        running_config,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_gfn_model_resource"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_gfn_model_resource"]
        logging.info("Start a creation of container task with all information.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        single_model = task_info["model"]
        multi_models = [copy.deepcopy(x) for x in single_model] + [
            copy.deepcopy(x) for x in single_model
        ]
        multi_models[1][-1] = "b"

        logging.info(f"multi_models: {multi_models}")

        single_resource = task_info["resource"]
        multi_resources = [copy.deepcopy(x) for x in single_resource] + [
            copy.deepcopy(x) for x in single_resource
        ]
        multi_resources[1][-1] = "a"

        logging.info(f"multi_resources: {multi_resources}")

        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            model=multi_models,
            resource=multi_resources,
        )
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        self.nvct_remove_list.append(
            {
                "task_id": task_id,
            }
        )
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)

        task_config_details = task_details_page.get_task_config_details()
        logging.info(f"task_config_details: {task_config_details}")
        expected_model_mount_points = [
            "/config/models/" + multi_models[0][2],
            "/config/models/" + multi_models[1][2],
        ]
        expected_resource_mount_points = [
            "/config/resources/" + multi_resources[0][2],
            "/config/resources/" + multi_resources[1][2],
        ]
        assert (
            sorted(task_config_details["Model Mount Points"])
            == sorted(expected_model_mount_points)
        ), f"Model mount points not as expected: {task_config_details['Model Mount Points']}"
        assert (
            sorted(task_config_details["Resource Mount Points"])
            == sorted(expected_resource_mount_points)
        ), f"Resource mount points not as expected: {task_config_details['Resource Mount Points']}"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904860
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_with_secrets_and_result_upload_deploying_on_GFN(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        """
        Test case to verify E2E flow of creating a container-based task with result upload on GFN
        Steps:
        1. Create task with required fields
        2. Monitor task status until completion
        3. Verify results in NGC registry
        """
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_container_task_sample_gfn_upload_result_with_secrets"
            ]
        else:
            task_info = test_data_tasks[
                "prod_container_task_sample_gfn_upload_result_with_secrets"
            ]
        logging.info("Start a creation of a container task with secrets and result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            secrets=task_info["secrets"],
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )
        # Step 3: navigate to results tab and verify results
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        # Set expected_results_number to 1 if environment_variables doesn't exist
        if "environment_variables" in task_info and task_info["environment_variables"].get(
            "NUM_OF_RESULTS"
        ):
            expected_results_number = task_info["environment_variables"]["NUM_OF_RESULTS"]
        else:
            expected_results_number = 1
        task_details_page.verify_results_number(expected_results_number)
        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        task_details_page.verify_ngc_registry_results(
            task_info["ResultsUpload_model_name"], "output_result"
        )
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108894
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_helm_based_task_with_secrets_and_result_upload_deploying_on_GFN(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_helm_task_sample_gfn_upload_result_with_secrets"
            ]
        else:
            task_info = test_data_tasks[
                "prod_helm_task_sample_gfn_upload_result_with_secrets"
            ]
        logging.info("Start a creation of a helm task with secrets and result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            secrets=task_info["secrets"],
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )
        # Step 3: navigate to results tab and verify results
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        expected_results_number = EXPECTED_RESULTS_NUMBER_WITH_HELM_CHART
        task_details_page.verify_results_number(expected_results_number)
        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        task_details_page.verify_ngc_registry_results(
            task_info["ResultsUpload_model_name"], "output_result"
        )
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904862
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_with_container_run_command_and_deploying_on_GFN(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
        nvct_teardown,
        session_nvct_ssa_allscope,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_container_task_sample_gfn_upload_result_with_run_command"
            ]
        else:
            task_info = test_data_tasks[
                "prod_container_task_sample_gfn_upload_result_with_run_command"
            ]
        logging.info(
            "Start a creation of a container task with run command and result upload."
        )
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            command=task_info["run_command"],
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )
        # Step 3: navigate to results tab and verify results
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        # Set expected_results_number to 1 if environment_variables doesn't exist
        if "environment_variables" in task_info and task_info["environment_variables"].get(
            "NUM_OF_RESULTS"
        ):
            expected_results_number = task_info["environment_variables"]["NUM_OF_RESULTS"]
        else:
            expected_results_number = 1
        task_details_page.verify_results_number(expected_results_number, "aaa")
        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        task_details_page.verify_ngc_registry_results(
            task_info["ResultsUpload_model_name"], "aaa"
        )
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904863
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_with_environment_variables_deploying_on_GFN(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_gfn_full_config"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_gfn_full_config"]
        logging.info("Start a creation of a container task with environment variables.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            env=task_info["environment_variables"],
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )
        # Step 3: navigate to results tab and verify results
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        # Set expected_results_number to 1 if environment_variables doesn't exist
        if "environment_variables" in task_info and task_info["environment_variables"].get(
            "NUM_OF_RESULTS"
        ):
            expected_results_number = task_info["environment_variables"]["NUM_OF_RESULTS"]
        else:
            expected_results_number = 1
        task_details_page.verify_results_number(expected_results_number)
        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        task_details_page.verify_ngc_registry_results(
            task_info["ResultsUpload_model_name"], "output_result"
        )
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904890
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_cancel_task_deploying_on_GFN_from_task_details_page_when_task_is_running(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_with_upload,
    ):
        task_id = nvct_setup_and_teardown_with_upload
        logging.info(f"Task ID: {task_id}")

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)

        task_details_page.wait_for_task_status(
            expected_status="RUNNING",
            timeout_seconds=1800,
            check_interval_seconds=30,
        )
        task_details_page.cancel_task()
        get_user_page.wait_for_timeout(1000)
        # check task is cancelled
        check_val_list = ["Status", "CleanupDate"]
        real_val = task_details_page.get_need_data(check_val_list)
        logging.info(f"Status: {real_val['Status']}")
        logging.info(f"CleanupDate: {real_val['CleanupDate']}")
        assert real_val["Status"] == "CANCELED", "Status is not correct"
        assert real_val["CleanupDate"] != "-", "Cleanup date is not updated"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904901
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_task_deploying_on_GFN_from_task_details_page_when_task_is_error(
        self,
        test_data_tasks,
        get_user_page: Page,
        session_nvct_ssa_allscope,
        nvct_setup_without_teardown_error,
    ):
        task_id = nvct_setup_without_teardown_error
        logging.info(f"Task ID: {task_id}")

        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] == "QUEUED", "Task is not queued"

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)
        # check task is error
        task_details_page.wait_for_task_status(
            expected_status="ERRORED",
            timeout_seconds=1800,
            check_interval_seconds=30,
        )

        # delete task
        assert task_details_page.delete_task(), "Task is not deleted"
        get_user_page.wait_for_timeout(1000)

        # verify the task is deleted
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904908
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_task_deploying_on_Non_GFN_from_task_details_page_when_task_is_error(
        self,
        test_data_tasks,
        get_user_page: Page,
        session_nvct_ssa_allscope,
        nvct_setup_without_teardown_error_non_gfn,
    ):
        task_id = nvct_setup_without_teardown_error_non_gfn
        logging.info(f"Task ID: {task_id}")

        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] == "QUEUED", "Task is not queued"

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)
        # check task is error
        task_details_page.wait_for_task_status(
            expected_status="ERRORED",
            timeout_seconds=1800,
            check_interval_seconds=30,
        )

        # delete task
        assert task_details_page.delete_task(), "Task is not deleted"
        get_user_page.wait_for_timeout(1000)

        # verify the task is deleted
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108931
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_add_addtional_secrets_keys_for_helm_based_task_from_task_details_page(
        self,
        conf,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_with_upload_helm,
        running_config,
    ):
        task_id = nvct_setup_and_teardown_with_upload_helm
        logging.info(f"Task ID: {task_id}")
        task_details_page = TasksDetailsPage(get_user_page)

        task_details_page.navigate_to_page(task_id=task_id)

        task_details_page.page.locator("//button[@title='Task Action Menu']").click()
        task_details_page.page.wait_for_selector(
            "//div[text()='Manage Secrets']", state="visible"
        )
        task_details_page.page.locator("//div[text()='Manage Secrets']").click()
        task_details_page.page.wait_for_selector(
            "//div[text()='Manage Secrets']", state="visible"
        )

        task_details_page.page.locator("//button[text()=' Add Another Secret']").click()

        task_details_page.page.locator("//input[@placeholder='Enter a key']").last.fill(
            "test_key"
        )
        task_details_page.page.locator(
            "//textarea[@placeholder='Enter a value as a text string or JSON']"
        ).last.fill("test_value")

        task_details_page.page.locator("//button[text()='Save Secrets']").click()
        task_details_page.page.wait_for_selector(
            "//span[contains(text(),'succesfully updated secrets')]", state="visible"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4139137
    @pytest.mark.skipif(
        CURRENT_ENV == "production" or CURRENT_ENV == "canary",
        reason="This case can only be tested in staging environment as byoo nvct ui hasn't been released to prod/canary",
    )
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_with_telemetries_logs_http_Grafana_and_deploying_on_GFN(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
        nvct_teardown,
        session_nvct_ssa_allscope,
    ):
        task_info = test_data_tasks[
            "stg_container_task_sample_gfn_upload_result_with_telemetries"
        ]
        logging.info(
            "Start a creation of a container task with telemetries logs http Grafana and result upload."
        )
        task_name = add_timestamp(Tools.get_case_name(), "%m%d%H%M%S")
        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            telemetry_logs=LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID,
            env=task_info["environment_variables"],
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        assert NVCT_Util.wait_for_task_status(
            task_id,
            expected_status="COMPLETED",
            timeout=task_info["time_out"],
            running_config=running_config,
        ), f"The task {task_id} is not COMPLETED as expected."
        time.sleep(60)
        start_time = (datetime.now(timezone.utc) - timedelta(hours=1)).strftime(
            "%Y-%m-%dT%H:%M:%SZ"
        )
        end_time = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
        wrapper_type = WrapperType("task")
        loki_url = running_config["grafana_cloud_loki"]["url"]
        loki_username = running_config["grafana_logs"]["email"]
        loki_password = running_config["grafana_logs"]["password"]
        LogsValidator = GrafanaLogsValidator(loki_url, loki_username, loki_password)
        logs_list = LogsValidator.query_logs(wrapper_type, task_id, start_time, end_time)
        logging.info(f"logs_list: {logs_list}")
        assert (
            "Ouput result: output_result_2, task progress: 100%" in logs_list
            and "Ouput result: output_result_1, task progress: 66%" in logs_list
            and "Ouput result: output_result_0, task progress: 33%" in logs_list
        ), "Not have all the output results logs"
        assert (
            "The following models was found:" in logs_list
        ), "The log result didn't contain the following models, which is the first log."

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4139141
    @pytest.mark.skipif(
        CURRENT_ENV == "production" or CURRENT_ENV == "canary",
        reason="This case can only be tested in staging environment as byoo nvct ui hasn't been released to prod/canary",
    )
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_helm_based_task_with_telemetries_metrics_http_Grafana_and_deploying_on_GFN(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
        nvct_teardown,
        session_nvct_ssa_allscope,
    ):
        logging.info(f"running_config: {running_config}")
        task_info = test_data_tasks[
            "stg_helm_task_sample_gfn_upload_result_with_telemetries"
        ]
        logging.info(
            "Start a creation of a helm task with telemetries metrics http Grafana and result upload."
        )
        task_name = add_timestamp(Tools.get_case_name(), "%m%d%H%M%S")
        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            telemetry_metrics=LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID,
            telemetry_logs=LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID,
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        assert NVCT_Util.wait_for_task_status(
            task_id,
            expected_status="COMPLETED",
            timeout=task_info["time_out"],
            running_config=running_config,
        ), f"The task {task_id} is not COMPLETED as expected."
        time.sleep(60)
        start_time = (datetime.now(timezone.utc) - timedelta(hours=1)).strftime(
            "%Y-%m-%dT%H:%M:%SZ"
        )
        end_time = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
        prometheus_url = running_config["grafana_cloud_prometheus"]["url"]
        username = running_config["grafana_metrics"]["email"]
        password = running_config["grafana_metrics"]["password"]
        MetricsValidator = GrafanaMetricsValidator(prometheus_url, username, password)
        wrapper_type = WrapperType("task")
        workload_type = WorkloadType("helm")
        cloudprovider = CloudProvider("gfn")
        validator_result = MetricsValidator.validate(
            wrapper_type,
            workload_type,
            cloudprovider,
            task_id,
            start_time,
            end_time,
            golden=True,
        )
        assert (
            validator_result is True
        ), "The validat for grafana metrics is not True as expected."

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904874
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_with_required_fields_and_result_upload_deploying_on_BYOC(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        """
        Test case to verify E2E flow of creating a container-based task with result upload on BYOC
        Steps:
        1. Create task with required fields
        2. Monitor task status until completion
        3. Verify results in NGC registry
        """
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_byoc_upload_result"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_byoc_upload_result"]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            clusters=task_info["cluster_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )
        # Step 3: navigate to results tab and verify results
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        # Set expected_results_number to 1 if environment_variables doesn't exist
        if "environment_variables" in task_info and task_info["environment_variables"].get(
            "NUM_OF_RESULTS"
        ):
            expected_results_number = task_info["environment_variables"]["NUM_OF_RESULTS"]
        else:
            expected_results_number = 1
        task_details_page.verify_results_number(expected_results_number)
        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        task_details_page.verify_ngc_registry_results(
            task_info["ResultsUpload_model_name"], "output_result"
        )
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3931918
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_with_model_resource_and_result_upload_deploying_on_BYOC(
        self, test_data_tasks, running_config, get_user_page: Page, nvct_teardown
    ):
        """
        Test case to verify E2E flow of creating a container-based task with model, resource and result upload on BYOC
        Steps:
        1. Create task with model, resource and result upload
        2. Monitor task status until completion
        3. Verify results in NGC registry
        """
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_byoc_model_resource"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_byoc_model_resource"]
        logging.info(
            "Start a creation of a task container function with model, resource and result upload."
        )
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")
        model = task_info["model"][0][0].split("/")
        model_version = task_info["model"][0][1]
        expected_match_model = f"/v2/org/{model[0]}/models/{model[1]}/{model_version}/files"
        logging.info(f"Expected match model: {expected_match_model}")
        resource = task_info["resource"][0][0].split("/")
        resource_version = task_info["resource"][0][1]
        expected_match_resource = (
            f"/v2/org/{resource[0]}/resources/{resource[1]}/{resource_version}/files"
        )
        logging.info(f"Expected match resource: {expected_match_resource}")
        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            clusters=task_info["cluster_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            model=task_info["model"],
            resource=task_info["resource"],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        config_details = task_details_page.get_task_config_details()
        logging.info(f"Task configuration details: {config_details}")
        model_list = config_details["Models"]
        resource_list = config_details["Resources"]
        logging.info(f"Model list: {model_list}")
        logging.info(f"Resource list: {resource_list}")
        assert any(
            expected_match_model in url for url in model_list
        ), f"Expected match model: {expected_match_model} not found in model list: {model_list}"
        assert any(
            expected_match_resource in url for url in resource_list
        ), f"Expected match resource: {expected_match_resource} not found in resource list: {resource_list}"
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )

        # Step 3: navigate to results tab and verify results
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        # Set expected_results_number to 1 if environment_variables doesn't exist
        if "environment_variables" in task_info and task_info["environment_variables"].get(
            "NUM_OF_RESULTS"
        ):
            expected_results_number = task_info["environment_variables"]["NUM_OF_RESULTS"]
        else:
            expected_results_number = 1
        task_details_page.verify_results_number(expected_results_number)

        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        task_details_page.verify_ngc_registry_results(
            task_info["ResultsUpload_model_name"], "output_result"
        )
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904872
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_with_required_fields_and_result_upload_deploying_non_gfn_non_byoc(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        """
        Test case to verify E2E flow of creating a container-based task with result upload on non-GFN and non-BYOC
        Steps:
        1. Create task with required fields
        2. Monitor task status until completion
        3. Verify results in NGC registry
        """
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_container_task_sample_non_gfn_non_byoc_upload_result"
            ]
        else:
            task_info = test_data_tasks[
                "prod_container_task_sample_non_gfn_non_byoc_upload_result"
            ]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            clusters=task_info["cluster_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )
        # Step 3: navigate to results tab and verify results
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        # Set expected_results_number to 1 if environment_variables doesn't exist
        if "environment_variables" in task_info and task_info["environment_variables"].get(
            "NUM_OF_RESULTS"
        ):
            expected_results_number = task_info["environment_variables"]["NUM_OF_RESULTS"]
        else:
            expected_results_number = 1
        task_details_page.verify_results_number(expected_results_number)
        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        task_details_page.verify_ngc_registry_results(
            task_info["ResultsUpload_model_name"], "output_result"
        )
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108912
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_e2e_clone_helm_chart_task_from_task_list_page(
        self,
        test_data_tasks,
        get_user_page: Page,
        session_nvct_ssa_allscope,
        nvct_setup_and_teardown_with_upload_helm,
        running_config,
        get_personal_keys_with_cookies,
        personal_key_teardown,
    ):
        resp = get_personal_keys_with_cookies()
        logging.info(f"resp['apiKeys']: {resp['apiKeys']}")
        logging.info(f"exiting personal key number: {len(resp['apiKeys'])}")
        assert len(resp["apiKeys"]) < 28, "Personal API key has exceeded the limit"

        task_id = nvct_setup_and_teardown_with_upload_helm
        task_details = NVCTUtils(session_nvct_ssa_allscope).retrieve_task_details(
            task_id=task_id
        )

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        task_list_page.click_clone_task()
        if CURRENT_ENV == "staging":
            deployment_config = test_data_tasks["clone_task_deployment_config_stg"]
        else:
            deployment_config = test_data_tasks["clone_task_deployment_config"]
        duration = deployment_config["generate_person_key_config"]["duration"]
        key_name = deployment_config["generate_person_key_config"]["key_name"]
        # Create cloned task

        personal_key = add_timestamp(key_name)

        # Create cloned task
        task_name = add_timestamp("clonedTask", "%m%d%H%M%S")
        clone_task_page = TasksClonePage(get_user_page)
        clone_task_page.verify_clone_details(task_details.get("task"), function_type="helm")
        if CURRENT_ENV == "staging":
            clone_task_page.clone_task(
                task_name,
                gpu_type=deployment_config["gpu_type"],
                instance_type=deployment_config["instance_type"],
                clusters=deployment_config["clusters"],
                generate_personal_key=True,
                generate_person_key_list=[personal_key, duration],
                upload_results=True,
            )
        else:
            clone_task_page.clone_task(
                task_name,
                gpu_type=deployment_config["gpu_type"],
                instance_type=deployment_config["instance_type"],
                clusters=None,
                generate_personal_key=True,
                generate_person_key_list=[personal_key, duration],
                upload_results=True,
            )

        # Teardown personal key
        resp_later = get_personal_keys_with_cookies()
        logging.info(f"resp_later['apiKeys']: {resp_later['apiKeys']}")
        logging.info(f"exiting personal key number: {len(resp_later['apiKeys'])}")
        assert len(resp_later["apiKeys"]) < 28, "Personal API key has exceeded the limit"
        keys = [
            key["keyId"] for key in resp_later["apiKeys"] if key["name"] == personal_key
        ]
        if len(resp_later["apiKeys"]) > len(resp["apiKeys"]) and keys == "":
            diff = [d for d in resp_later["apiKeys"] if d not in resp["apiKeys"]][0]
            logging.info(f"diff: {diff}")
            keys = diff["keyId"]

        logging.info(keys)
        # tear down for the new dup one after rotation
        self.api_keys_remove_list.append(keys[0])

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id_cloned = task_details_page.get_task_id_from_url()
        logging.info(f"Cloned task with ID: {task_id}")
        self.nvct_remove_list.append(
            {
                "task_id": task_id_cloned,
            }
        )
        # Verify initial task status is QUEUED
        data_dict = task_details_page.get_need_data(["Status"])
        assert data_dict["Status"] == "QUEUED", "Task is not queued"

        task_details_page.wait_for_task_status(
            expected_status="COMPLETED", timeout_seconds=1800
        )
        logging.info(f"{Tools.get_case_name()} task completed.\n")

        # Verify the details between GUI and API
        task_cloned_details = NVCTUtils(session_nvct_ssa_allscope).retrieve_task_details(
            task_id=task_id_cloned
        )

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.verify_details_between_ui_and_api(task_cloned_details.get("task"))

        # verify the results numbers and ngc registry
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        task_details_page.verify_results_number(expected_number="5")
        task_details_page.navigate_to_ngc_registry()
        model_name = task_cloned_details.get("task")["resultsLocation"].split("/")[-1]
        task_details_page.verify_ngc_registry_results(
            model_name=model_name, output_result="output_result"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5113784
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_clone_helm_chart_task_without_uploading_GFN_from_task_list_page(
        self,
        test_data_tasks,
        get_user_page: Page,
        session_nvct_ssa_allscope,
        nvct_setup_and_teardown_without_upload_helmchart,
    ):
        """
        [E2E] Clone helm chart task without uploading(GFN) from task list page

        Test Procedure:
        1. In the [Tasks] page, choose 1 helm chart without uploading (GFN) based task and click the 3-dots Actions button and click "Clone Task".
        2. It will lead to the [Clone Task] page.
        3. Check all the fields are prefilled with the same content as the copied function except for the Task Name and Person API Key.
        4. Fill in Task Name and generate Personal API Key.
        5. Click "Clone Task" button.
        6. Verify the task is created successfully and the status is "QUEUED".
        7. Verify the task details between GUI and API.
        """
        task_id = nvct_setup_and_teardown_without_upload_helmchart
        task_details = NVCTUtils(session_nvct_ssa_allscope).retrieve_task_details(
            task_id=task_id
        )

        # Step 1: Navigate to Tasks page and find the helm chart task
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        task_list_page.click_clone_task()

        # Step 2: Verify Clone Task page and prefilled fields
        deployment_config = test_data_tasks["clone_task_deployment_config"]
        task_name = add_timestamp("clonedHelmTask", "%m%d%H%M%S")

        clone_task_page = TasksClonePage(get_user_page)

        # Step 3: Verify all fields are prefilled correctly (except Task Name and Personal API Key)
        clone_task_page.verify_clone_details(task_details.get("task"), function_type="helm")

        # Step 4 & 5: Fill in Task Name, generate Personal API Key, and clone task
        clone_task_page.clone_task(
            task_name,
            gpu_type=deployment_config["gpu_type"],
            instance_type=deployment_config["instance_type"],
            clusters=None,
            generate_personal_key=False,
            upload_results=False,  # Key difference: no upload for this test
        )

        # Step 6: Initialize task details page and get cloned task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id_cloned = task_details_page.get_task_id_from_url()
        logging.info(f"Cloned task with ID: {task_id_cloned}")
        self.nvct_remove_list.append(
            {
                "task_id": task_id_cloned,
            }
        )

        # Verify initial task status is QUEUED
        data_dict = task_details_page.get_need_data(["Status"])
        assert data_dict["Status"] == "QUEUED", "Task is not queued"

        # Step 7: Verify the details between GUI and API
        task_cloned_details = NVCTUtils(session_nvct_ssa_allscope).retrieve_task_details(
            task_id=task_id_cloned
        )

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.verify_details_between_ui_and_api(task_cloned_details.get("task"))

        # Since this is without upload, verify that Results tab is not visible
        logging.info("Verifying task has no results tab (without upload)")
        assert (
            not task_details_page.check_results_tab_visible()
        ), "Results tab should not be visible for tasks without upload"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108913
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_clone_helm_chart_task_from_task_details_page(
        self,
        test_data_tasks,
        get_user_page: Page,
        session_nvct_ssa_allscope,
        nvct_setup_and_teardown_with_upload_helm,
        running_config,
        get_personal_keys_with_cookies,
        personal_key_teardown,
    ):
        """
        [E2E] Clone Helm Chart task from task details page

        Test Procedure:
        1. In the [Tasks] page, choose 1 Helm-based task, click the task name.
        2. It will lead to the Details page of the task.
        3. Click the 3-dots button on the right top of the page and click "Clone Task"
        4. It will lead to the [Clone Task] page.
        5. Check all the fields are prefilled with the same content as the copied function
           except for the Task Name and Personal API Key.
        6. Fill in Task Name and generate Personal API Key
        7. Click "Create Task" button
        8. Verify the task is created successfully and status is QUEUED
        """
        resp = get_personal_keys_with_cookies()
        logging.info(f"resp['apiKeys']: {resp['apiKeys']}")
        logging.info(f"exiting personal key number: {len(resp['apiKeys'])}")
        assert len(resp["apiKeys"]) < 28, "Personal API key has exceeded the limit"

        task_id = nvct_setup_and_teardown_with_upload_helm
        logging.info(f"Original Helm task ID: {task_id}")

        # Get original task details for comparison
        original_task_details = NVCTUtils(session_nvct_ssa_allscope).retrieve_task_details(
            task_id=task_id
        )

        # Step 1: Navigate to Tasks page and find the helm task
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)

        # Step 2: Click the task name to go to task details page
        task_list_page.navigate_to_task_details_page(task_id)

        # Step 3: Click 3-dots menu and select "Clone Task"
        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.click_clone_task()

        # Step 4: Verify we're on the Clone Task page
        task_clone_page = TasksClonePage(get_user_page)

        task_clone_page.verify_clone_details(
            original_task_details.get("task"), function_type="helm"
        )

        # Step 6: Fill in Task Name and generate Personal API Key
        task_name = f"Cloned-Helm-Task-{Tools.generate_random_string(8)}"
        if CURRENT_ENV == "staging":
            deployment_config = test_data_tasks["clone_task_deployment_config_stg"]
        else:
            deployment_config = test_data_tasks["clone_task_deployment_config"]
        duration = deployment_config["generate_person_key_config"]["duration"]
        key_name = deployment_config["generate_person_key_config"]["key_name"]
        # Create cloned task

        personal_key = add_timestamp(key_name)
        if CURRENT_ENV == "staging":
            task_clone_page.clone_task(
                task_name,
                gpu_type=deployment_config["gpu_type"],
                instance_type=deployment_config["instance_type"],
                clusters=deployment_config["clusters"],
                generate_personal_key=True,
                generate_person_key_list=[personal_key, duration],
                upload_results=True,
            )
        else:
            task_clone_page.clone_task(
                task_name,
                gpu_type=deployment_config["gpu_type"],
                instance_type=deployment_config["instance_type"],
                clusters=None,
                generate_personal_key=True,
                generate_person_key_list=[personal_key, duration],
            )

        resp_later = get_personal_keys_with_cookies()
        logging.info(f"resp_later['apiKeys']: {resp_later['apiKeys']}")
        logging.info(f"exiting personal key number: {len(resp_later['apiKeys'])}")
        assert len(resp_later["apiKeys"]) < 28, "Personal API key has exceeded the limit"
        keys = [
            key["keyId"] for key in resp_later["apiKeys"] if key["name"] == personal_key
        ]
        if len(resp_later["apiKeys"]) > len(resp["apiKeys"]) and keys == "":
            diff = [d for d in resp_later["apiKeys"] if d not in resp["apiKeys"]][0]
            logging.info(f"diff: {diff}")
            keys = diff["keyId"]

        logging.info(keys)
        # tear down for the new dup one after rotation
        self.api_keys_remove_list.append(keys[0])

        # Step 7: Verify task creation and get cloned task ID
        cloned_task_details_page = TasksDetailsPage(get_user_page)
        cloned_task_id = cloned_task_details_page.get_task_id_from_url()
        logging.info(f"Cloned Helm task with ID: {cloned_task_id}")

        # Add to cleanup list
        self.nvct_remove_list.append(
            {
                "task_id": cloned_task_id,
            }
        )

        # Step 8: Verify initial task status is QUEUED
        data_dict = cloned_task_details_page.get_need_data(["Status"])
        assert data_dict["Status"] == "QUEUED", "Cloned Helm task is not queued"

        # Verify the details between GUI and API for cloned task
        task_cloned_details = NVCTUtils(session_nvct_ssa_allscope).retrieve_task_details(
            task_id=cloned_task_id
        )
        cloned_task_details_page.verify_details_between_ui_and_api(
            task_cloned_details.get("task")
        )

        logging.info(
            "Test completed successfully - Helm Chart task cloned from details page"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4139144
    @pytest.mark.skipif(
        CURRENT_ENV == "production" or CURRENT_ENV == "canary",
        reason="This case can only be tested in staging environment as byoo nvct ui hasn't been released to prod/canary",
    )
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_clone_helm_based_task_with_telemetries_from_task_details_page(
        self,
        test_data_tasks,
        get_user_page: Page,
        session_nvct_ssa_allscope,
        running_config,
        nvct_teardown,
        get_personal_keys_with_cookies,
        personal_key_teardown,
    ):
        """
        [E2E] Clone Helm-based task with telemetries from task details page

        Test Procedure:
        1. In the [Tasks] page, choose 1 Helm-based task with telemetries logs_metrics_http_Grafana
        2. Click the task name to go to task details page
        3. Click the 3-dots Actions button and click "Clone Task"
        4. It will lead to the [Clone Task] page
        5. Check all the fields are prefilled with the same content as the copied function except for the Task Name and Personal API Key
        6. Fill in the Task Name and generate Personal API Key, click [Create Task] button
        7. Verify the task is created successfully and navigate to task details page
        8. Verify the telemetries configuration is cloned correctly
        """
        resp = get_personal_keys_with_cookies()
        logging.info(f"resp['apiKeys']: {resp['apiKeys']}")
        logging.info(f"exiting personal key number: {len(resp['apiKeys'])}")
        assert len(resp["apiKeys"]) < 28, "Personal API key has exceeded the limit"

        # Step 1: Create a Helm task with telemetries
        task_info = test_data_tasks[
            "stg_helm_task_sample_gfn_upload_result_with_telemetries"
        ]
        original_task_name = add_timestamp("helm_telemetry_task_for_clone", "%m%d%H%M%S")

        logging.info("Creating Helm task with telemetries for cloning")
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            original_task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            telemetry_metrics=LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID,
            telemetry_logs=LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID,
        )

        # Get task ID from the created task
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created Helm telemetries task with ID: {task_id}")

        # Add to cleanup list
        self.nvct_remove_list.append({"task_id": task_id})

        # Get task details for verification
        task_details = NVCTUtils(session_nvct_ssa_allscope).retrieve_task_details(
            task_id=task_id
        )

        # Step 2: Navigate to Tasks page and search for the helm task
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)

        # Step 3: Click task name to go to task details page
        task_list_page.navigate_to_task_details_page(task_id)

        # Step 4: Click 3-dots menu and select "Clone Task"
        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.click_clone_task()

        # Step 5: Verify we're on the Clone Task page and check prefilled fields
        task_clone_page = TasksClonePage(get_user_page)

        # Verify fields are prefilled (except Task Name and Personal API Key)
        expected_details = task_details.get("task", {})
        task_clone_page.verify_clone_details(
            expected_details=task_details.get("task"), function_type="helm"
        )
        # Step 6: Fill in Task Name and generate Personal API Key
        task_name = add_timestamp("cloned_helm_telemetry_task", "%m%d%H%M%S")
        personal_key = add_timestamp("cloned_helm_telemetry_key", "%m%d%H%M%S")
        duration = "30 days"  # 30 days

        deployment_config = test_data_tasks["clone_task_deployment_config"]

        # Clone the task with telemetries
        task_clone_page.clone_task(
            task_name,
            gpu_type=deployment_config["gpu_type"],
            instance_type=deployment_config["instance_type"],
            clusters=None,
            generate_personal_key=True,
            generate_person_key_list=[personal_key, duration],
            upload_results=True,
        )

        resp_later = get_personal_keys_with_cookies()
        logging.info(f"resp_later['apiKeys']: {resp_later['apiKeys']}")
        logging.info(f"exiting personal key number: {len(resp_later['apiKeys'])}")
        assert len(resp_later["apiKeys"]) < 28, "Personal API key has exceeded the limit"
        keys = [
            key["keyId"] for key in resp_later["apiKeys"] if key["name"] == personal_key
        ]
        if len(resp_later["apiKeys"]) > len(resp["apiKeys"]) and keys == "":
            diff = [d for d in resp_later["apiKeys"] if d not in resp["apiKeys"]][0]
            logging.info(f"diff: {diff}")
            keys = diff["keyId"]

        logging.info(keys)
        # tear down for the new dup one after rotation
        self.api_keys_remove_list.append(keys[0])

        # Step 7: Verify task creation and get cloned task details
        cloned_task_details_page = TasksDetailsPage(get_user_page)
        cloned_task_id = cloned_task_details_page.get_task_id_from_url()
        logging.info(f"Cloned Helm telemetries task with ID: {cloned_task_id}")

        # Add to cleanup list
        self.nvct_remove_list.append({"task_id": cloned_task_id})

        # Step 8: Verify the telemetries configuration is cloned correctly
        cloned_task_details = NVCTUtils(session_nvct_ssa_allscope).retrieve_task_details(
            task_id=cloned_task_id
        )
        original_telemetries = expected_details.get("telemetries", {})
        cloned_telemetries = cloned_task_details.get("task", {}).get("telemetries", {})

        # Verify telemetries logs and metrics are the same
        if original_telemetries:
            assert cloned_telemetries.get("logs") == original_telemetries.get(
                "logs"
            ), "Telemetries logs configuration not cloned correctly"
            assert cloned_telemetries.get("metrics") == original_telemetries.get(
                "metrics"
            ), "Telemetries metrics configuration not cloned correctly"
            logging.info("Telemetries configuration verified successfully")

        # Step 9: Verify initial task status is QUEUED
        data_dict = cloned_task_details_page.get_need_data(["Status"])
        assert data_dict["Status"] == "QUEUED", "Cloned task is not queued"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108928
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_add_secrets_key_for_helm_based_task_not_created_with_secrets_from_task_list_page(
        self,
        get_user_page: Page,
        running_config,
        nvct_setup_and_teardown_with_upload_helm,
        session_nvct_ssa_allscope,
    ):
        task_id = nvct_setup_and_teardown_with_upload_helm
        logging.info(f"Task ID: {task_id}")

        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] in [
            "QUEUED",
            "RUNNING",
        ], "Task is not queued or running"

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        logging.info(f"Searching for task: {task_id}")
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        add_secret = {
            "key": "test",
            "value": "test",
        }
        logging.info(f"Adding secret: {add_secret}")
        assert task_list_page.manage_secrets(
            [add_secret]
        ), "Secret is not added successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108930
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_add_additional_secrets_key_for_helm_based_task_from_task_list_page(
        self,
        get_user_page: Page,
        running_config,
        nvct_setup_and_teardown_with_upload_helm,
        session_nvct_ssa_allscope,
    ):
        task_id = nvct_setup_and_teardown_with_upload_helm
        logging.info(f"Task ID: {task_id}")

        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] in [
            "QUEUED",
            "RUNNING",
        ], "Task is not queued or running"

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        logging.info(f"Searching for task: {task_id}")
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        add_secret1 = {
            "key": "test",
            "value": "test",
        }
        add_secret2 = {
            "key": "test2",
            "value": "test2",
        }
        assert task_list_page.manage_secrets(
            [add_secret1, add_secret2]
        ), "Secret is not added successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5071371
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_create_container_based_task_with_ess_agent_init_environment(
        self, test_data_tasks, running_config, get_user_page: Page, nvct_teardown
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_byoc_model_resource"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_byoc_model_resource"]
        logging.info(
            "Start a creation of a task container function with model, resource and result upload."
        )
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")
        environment_variables = {
            "ESS_AGENT_INIT": "true",
        }
        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            clusters=task_info["cluster_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            env=environment_variables,
            upload_results=False,
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5058706
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_with_required_fields_and_result_upload_deploying_on_dgxc(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        """
        Test case to verify E2E flow of creating a container-based task with result upload on non-DGXC
        Steps:
        1. Create task with required fields
        2. Monitor task status until completion
        3. Verify results in NGC registry
        """
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_container_task_sample_non_gfn_non_byoc_upload_result"
            ]
        else:
            task_info = test_data_tasks[
                "prod_container_task_sample_non_gfn_non_byoc_upload_result"
            ]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            clusters=task_info["cluster_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )
        # Step 3: navigate to results tab and verify results
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        # Set expected_results_number to 1 if environment_variables doesn't exist
        if "environment_variables" in task_info and task_info["environment_variables"].get(
            "NUM_OF_RESULTS"
        ):
            expected_results_number = task_info["environment_variables"]["NUM_OF_RESULTS"]
        else:
            expected_results_number = 1
        task_details_page.verify_results_number(expected_results_number)
        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        task_details_page.verify_ngc_registry_results(
            task_info["ResultsUpload_model_name"], "output_result"
        )
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108895
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_helm_based_task_with_model_resource_and_result_upload_deploying_on_GFN(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_helm_task_sample_gfn_upload_result"]
        else:
            task_info = test_data_tasks["prod_helm_task_sample_gfn_upload_result"]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            model=task_info["model"],
            resource=task_info["resource"],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )
        # Step 3: navigate to results tab and verify results
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        expected_results_number = EXPECTED_RESULTS_NUMBER_WITH_HELM_CHART
        task_details_page.verify_results_number(expected_results_number)
        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        task_details_page.verify_ngc_registry_results(
            task_info["ResultsUpload_model_name"], "output_result"
        )
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5158627
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_clusters_in_task_details_page_for_non_gfn(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_container_task_sample_non_gfn_non_byoc_upload_result"
            ]
        else:
            task_info = test_data_tasks[
                "prod_container_task_sample_non_gfn_non_byoc_upload_result"
            ]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            clusters=task_info["cluster_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)

        config_details = task_details_page.get_task_config_details()
        logging.info(f"Task configuration details: {config_details}")
        cluster_list = config_details["Clusters"]
        logging.info(f"Cluster list: {cluster_list}")
        assert cluster_list.strip() == task_info["cluster_type"].strip()

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5158628
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_clusters_in_task_details_page_for_gfn(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_gfn_upload_result"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_gfn_upload_result"]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)

        config_details = task_details_page.get_task_config_details()
        logging.info(f"Task configuration details: {config_details}")
        cluster_list = config_details["Clusters"]
        logging.info(f"Cluster list: {cluster_list}")
        assert cluster_list.strip() == "GFN"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904884
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_task_details_for_error_task(
        self,
        test_data_tasks,
        get_user_page: Page,
        session_nvct_ssa_allscope,
        nvct_setup_and_teardown_error,
    ):
        task_id = nvct_setup_and_teardown_error
        logging.info(f"Task ID: {task_id}")

        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] == "QUEUED", "Task is not queued"

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)
        # check task is error
        task_details_page.wait_for_task_status(
            expected_status="ERRORED",
            timeout_seconds=1800,
            check_interval_seconds=30,
        )
        assert task_details_page.page.locator(
            "//div[@data-testid='kui-banner-icon']"
        ).is_visible(), "warning icon is not visible"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T3904875
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_task_list_pagination(
        self,
        get_user_page: Page,
    ):
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        # check that users can jump to/jump between certain pages
        task_list_page.page.locator(
            "//button/*[name()='svg' and @data-icon-name='shapes-chevron-right']"
        ).click()
        assert (
            task_list_page.page.locator(
                "//input[@data-testid='kui-text-input-element']"
            ).last.get_attribute("value")
            == "2"
        ), "Page is not jumped to page 2"
        task_list_page.page.locator(
            "//button/*[name()='svg' and @data-icon-name='shapes-chevron-left']"
        ).click()
        assert (
            task_list_page.page.locator(
                "//input[@data-testid='kui-text-input-element']"
            ).last.get_attribute("value")
            == "1"
        ), "Page is not jumped to page 3"

        # Check that users can choose the number of task lists to show on one page
        task_list_page.page.locator(
            "//input[@data-testid='kui-text-input-element']"
        ).last.fill("2")
        task_list_page.page.locator("//h2[text()='Tasks']").click()
        assert (
            task_list_page.page.locator(
                "//input[@data-testid='kui-text-input-element']"
            ).last.get_attribute("value")
            == "2"
        ), "Number of task lists to show on one page is not changed"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T4108903
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_task_list_for_task_type(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
    ):
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        get_user_page.wait_for_timeout(1000)
        type_list = task_list_page.type_list
        for type in type_list:
            is_true = task_list_page.check_task_type(type)
            assert is_true, f"Task type {type} is not in the list"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4139139
    @pytest.mark.skipif(
        CURRENT_ENV == "production" or CURRENT_ENV == "canary",
        reason="This case can only be tested in staging environment as byoo nvct ui hasn't been released to prod/canary",
    )
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_with_telemetries_logs_grpc_Grafana_and_deploying_on_DGXC(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
        nvct_teardown,
        session_nvct_ssa_allscope,
    ):
        task_info = test_data_tasks[
            "stg_container_task_sample_dgxc_upload_result_with_telemetries"
        ]
        logging.info(
            "Start a creation of a container task with telemetries logs http Grafana and result upload."
        )
        task_name = add_timestamp(Tools.get_case_name(), "%m%d%H%M%S")
        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            telemetry_logs=LOGS_METRICS_GRPC_GRAFANA_TELEMETRY_ID,
            env=task_info["environment_variables"],
            clusters=task_info["clusters"],
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        assert NVCT_Util.wait_for_task_status(
            task_id,
            expected_status="COMPLETED",
            timeout=task_info["time_out"],
            running_config=running_config,
        ), f"The task {task_id} is not COMPLETED as expected."
        time.sleep(60)
        start_time = (datetime.now(timezone.utc) - timedelta(hours=1)).strftime(
            "%Y-%m-%dT%H:%M:%SZ"
        )
        end_time = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
        wrapper_type = WrapperType("task")
        loki_url = running_config["grafana_cloud_loki"]["url"]
        loki_username = running_config["grafana_logs"]["email"]
        loki_password = running_config["grafana_logs"]["password"]
        LogsValidator = GrafanaLogsValidator(loki_url, loki_username, loki_password)
        logs_list = LogsValidator.query_logs(wrapper_type, task_id, start_time, end_time)
        logging.info(f"logs_list: {logs_list}")
        assert (
            "Ouput result: output_result_2, task progress: 100%" in logs_list
            and "Ouput result: output_result_1, task progress: 66%" in logs_list
            and "Ouput result: output_result_0, task progress: 33%" in logs_list
        ), "Not have all the output results logs"
        assert (
            "The following models was found:" in logs_list
        ), "The log result didn't contain the following models, which is the first log."

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5103453
    @pytest.mark.skipif(
        CURRENT_ENV == "production" or CURRENT_ENV == "canary",
        reason="This case can only be tested in staging environment as byoo nvct ui hasn't been released to prod/canary",
    )
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_Helm_based_task_with_telemetries_metrics_grpc_Thanos_deploying_on_DGXC(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
        nvct_teardown,
        session_nvct_ssa_allscope,
        kratos_thanos_ssa_token,
    ):
        task_info = test_data_tasks[
            "stg_helm_task_sample_dgxc_upload_result_with_telemetries"
        ]
        logging.info(
            "Start a creation of a helm task with telemetries metrics grpc thanos and result upload."
        )
        task_name = add_timestamp(Tools.get_case_name(), "%m%d%H%M%S")
        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            telemetry_metrics=METRICS_GRPC_THANOS_TELEMETRY_ID,
            clusters=task_info["clusters"],
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        assert NVCT_Util.wait_for_task_status(
            task_id,
            expected_status="COMPLETED",
            timeout=task_info["time_out"],
            running_config=running_config,
        ), f"The task {task_id} is not COMPLETED as expected."

        logging.info("Waiting for 90 seconds to send telemetry data")
        time.sleep(90)

        ssa_token = kratos_thanos_ssa_token
        base_url = f"{running_config['kratos_thanos_ssa_info']['endpoint_url']}"

        start_time = (datetime.now(timezone.utc) - timedelta(hours=0.25)).strftime(
            "%Y-%m-%dT%H:%M:%SZ"
        )
        end_time = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")

        kratos_thanos_metrics_validator = KratosThanosMetricsValidator(base_url, ssa_token)
        result = kratos_thanos_metrics_validator.validate(
            wrapper_type=WrapperType.TASK,
            workload_type=WorkloadType.HELM,
            cloud_provider=CloudProvider.NON_GFN,
            id=task_id,
            start=start_time,
            end=end_time,
        )
        logging.info(f"result is : {result}")
        assert (
            result["validation_result"] is True
        ), "The validation for kratos thanos metrics is not as expected."

        assert (
            NCA_ID in result["nca_id"]
        ), f"The NCA ID {result['nca_id']} not in expected {NCA_ID}"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4139142
    @pytest.mark.Partial_automation
    @pytest.mark.skipif(
        CURRENT_ENV == "production" or CURRENT_ENV == "canary",
        reason="This case can only be tested in staging environment as byoo nvct ui hasn't been released to prod/canary",
    )
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_Helm_based_task_with_telemetries_logs_metrics_grpc_Datadog_deploying_on_GFN(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
        nvct_teardown,
        session_nvct_ssa_allscope,
    ):
        task_info = test_data_tasks[
            "stg_helm_task_sample_gfn_upload_result_with_telemetries"
        ]
        logging.info(
            "Start a creation of a helm task with telemetries logs metrics grpc datadog and result upload."
        )
        task_name = add_timestamp(Tools.get_case_name(), "%m%d%H%M%S")
        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            telemetry_logs=LOGS_METRICS_GRPC_DATADOG_TELEMETRY_ID,
            telemetry_metrics=LOGS_METRICS_GRPC_DATADOG_TELEMETRY_ID,
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        assert NVCT_Util.wait_for_task_status(
            task_id,
            expected_status="COMPLETED",
            timeout=task_info["time_out"],
            running_config=running_config,
        ), f"The task {task_id} is not COMPLETED as expected."
        logging.info(
            "This case is partial automation, please check the logs and results in provider endpoint UI"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5103452
    @pytest.mark.skipif(
        CURRENT_ENV == "production" or CURRENT_ENV == "canary",
        reason="This case can only be tested in staging environment as byoo nvct ui hasn't been released to prod/canary",
    )
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_with_telemetries_metrics_grpc_Thanos_and_deploying_on_non_DGXC(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
        nvct_teardown,
        session_nvct_ssa_allscope,
        kratos_thanos_ssa_token,
    ):
        task_info = test_data_tasks[
            "stg_container_task_sample_non_dgxc_upload_result_with_telemetries"
        ]
        logging.info("Start a creation of a container task with telemetrie.")
        task_name = add_timestamp(Tools.get_case_name(), "%m%d%H%M%S")
        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            telemetry_metrics=METRICS_GRPC_THANOS_TELEMETRY_ID,
            env=task_info["environment_variables"],
            clusters=task_info["clusters"],
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        assert NVCT_Util.wait_for_task_status(
            task_id,
            expected_status="COMPLETED",
            timeout=task_info["time_out"],
            running_config=running_config,
        ), f"The task {task_id} is not COMPLETED as expected."

        logging.info("Waiting for 90 seconds to send telemetry data")
        time.sleep(90)

        ssa_token = kratos_thanos_ssa_token
        base_url = f"{running_config['kratos_thanos_ssa_info']['endpoint_url']}"

        start_time = (datetime.now(timezone.utc) - timedelta(hours=0.25)).strftime(
            "%Y-%m-%dT%H:%M:%SZ"
        )
        end_time = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")

        kratos_thanos_metrics_validator = KratosThanosMetricsValidator(base_url, ssa_token)
        result = kratos_thanos_metrics_validator.validate(
            wrapper_type=WrapperType.TASK,
            workload_type=WorkloadType.CONTAINER,
            cloud_provider=CloudProvider.NON_GFN,
            id=task_id,
            start=start_time,
            end=end_time,
        )
        logging.info(f"result is : {result}")
        assert (
            result["validation_result"] is True
        ), "The validation for kratos thanos metrics is not as expected."

        assert (
            NCA_ID in result["nca_id"]
        ), f"The NCA ID {result['nca_id']} not in expected {NCA_ID}"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5103451
    @pytest.mark.skipif(
        CURRENT_ENV == "production" or CURRENT_ENV == "canary",
        reason="This case can only be tested in staging environment as byoo nvct ui hasn't been released to prod/canary",
    )
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_with_telemetry_metrics_http_Thanos_deploying_on_GFN(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
        nvct_teardown,
        session_nvct_ssa_allscope,
        session_nvcf_admin_sak,
        kratos_thanos_ssa_token,
    ):
        task_info = test_data_tasks[
            "stg_container_task_sample_gfn_upload_result_with_telemetries"
        ]
        logging.info(
            "Start a creation of a container task with telemetries logs http Grafana and result upload."
        )
        task_name = add_timestamp(Tools.get_case_name(), "%m%d%H%M%S")
        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            telemetry_metrics=METRICS_HTTP_THANOS_TELEMETRY_ID,
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        assert NVCT_Util.wait_for_task_status(
            task_id,
            expected_status="COMPLETED",
            timeout=task_info["time_out"],
            running_config=running_config,
        ), f"The task {task_id} is not COMPLETED as expected."

        logging.info("Waiting for 90 seconds to send telemetry data")
        time.sleep(90)

        ssa_token = kratos_thanos_ssa_token
        base_url = f"{running_config['kratos_thanos_ssa_info']['endpoint_url']}"

        start_time = (datetime.now(timezone.utc) - timedelta(hours=0.25)).strftime(
            "%Y-%m-%dT%H:%M:%SZ"
        )
        end_time = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")

        kratos_thanos_metrics_validator = KratosThanosMetricsValidator(base_url, ssa_token)
        result = kratos_thanos_metrics_validator.validate(
            wrapper_type=WrapperType.TASK,
            workload_type=WorkloadType.CONTAINER,
            cloud_provider=CloudProvider.GFN,
            id=task_id,
            start=start_time,
            end=end_time,
        )
        logging.info(f"result is : {result}")
        assert (
            result["validation_result"] is True
        ), "The validation for kratos thanos metrics is not as expected."

        assert (
            NCA_ID in result["nca_id"]
        ), f"The NCA ID {result['nca_id']} not in expected {NCA_ID}"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4139140
    @pytest.mark.Partial_automation
    @pytest.mark.skipif(
        CURRENT_ENV == "production" or CURRENT_ENV == "canary",
        reason="This case can only be tested in staging environment as byoo nvct ui hasn't been released to prod/canary",
    )
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_with_telemetries_logs_metrics_grpc_Datadog_deploying_on_non_DGXC(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
        nvct_teardown,
        session_nvct_ssa_allscope,
    ):
        task_info = test_data_tasks[
            "stg_container_task_sample_non_dgxc_upload_result_with_telemetries"
        ]
        logging.info("Start a creation of a container task with telemetrie.")
        task_name = add_timestamp(Tools.get_case_name(), "%m%d%H%M%S")
        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            telemetry_metrics=LOGS_METRICS_GRPC_DATADOG_TELEMETRY_ID,
            telemetry_logs=LOGS_METRICS_GRPC_DATADOG_TELEMETRY_ID,
            env=task_info["environment_variables"],
            clusters=task_info["clusters"],
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        assert NVCT_Util.wait_for_task_status(
            task_id,
            expected_status="COMPLETED",
            timeout=task_info["time_out"],
            running_config=running_config,
        ), f"The task {task_id} is not COMPLETED as expected."
        logging.info(
            "This case is partial automation, please check the logs and results in provider endpoint UI"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4139138
    @pytest.mark.Partial_automation
    @pytest.mark.skipif(
        CURRENT_ENV == "production" or CURRENT_ENV == "canary",
        reason="This case can only be tested in staging environment as byoo nvct ui hasn't been released to prod/canary",
    )
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_E2E_create_container_based_task_with_telemetry_logs_metrics_http_Datadog_deploying_on_GFN(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
        nvct_teardown,
        session_nvct_ssa_allscope,
    ):
        task_info = test_data_tasks[
            "stg_container_task_sample_gfn_upload_result_with_telemetries"
        ]
        logging.info("Start a creation of a container task with telemetries.")
        task_name = add_timestamp(Tools.get_case_name(), "%m%d%H%M%S")
        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            telemetry_logs=LOGS_METRICS_HTTP_DATADOG_TELEMETRY_ID,
            telemetry_metrics=LOGS_METRICS_HTTP_DATADOG_TELEMETRY_ID,
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        assert NVCT_Util.wait_for_task_status(
            task_id,
            expected_status="COMPLETED",
            timeout=task_info["time_out"],
            running_config=running_config,
        ), f"The task {task_id} is not COMPLETED as expected."
        logging.info(
            "This case is partial automation, please check the logs and results in provider endpoint UI"
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5190016
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_adding_descriptions_for_helm_chart_tasks_model_and_resource(
        self,
        get_user_page: Page,
    ):
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.page.get_by_role("radio", name="Custom Helm Chart").click()

        expected_model_desc = (
            "Select model(s) to externally load into your container runtime if not packaged within. "
            "Volume model-data must be specified in and mounted by container(s) of at least one Pod in your helm chart."
        )
        expected_resource_desc = (
            "Select resource(s) to externally load into your container runtime if not packaged within. "
            "Volume resource-data must be specified in and mounted by container(s) of at least one Pod in your helm chart."
        )

        # check the helper description text for [Select Model(s)] accordion
        model_accordion = create_task_page.page.locator(
            '[data-testid="kui-accordion"]'
        ).filter(has=create_task_page.page.get_by_text("Select Model(s)"))
        model_desc = model_accordion.locator(
            "p.text-gray-600 span.text-regular-md"
        ).text_content()
        model_desc = " ".join(model_desc.split())
        logging.info(f"model_desc: {model_desc}")
        assert (
            model_desc == expected_model_desc
        ), f"Model helper desc not as expected: {model_desc}"

        # check the helper description text for [Select Resource(s)] accordion
        resource_accordion = create_task_page.page.locator(
            '[data-testid="kui-accordion"]'
        ).filter(has=create_task_page.page.get_by_text("Select Resource(s)"))
        resource_desc = resource_accordion.locator(
            "p.text-gray-600 span.text-regular-md"
        ).text_content()
        resource_desc = " ".join(resource_desc.split())
        logging.info(f"resource_desc: {resource_desc}")
        assert (
            resource_desc == expected_resource_desc
        ), f"Resource helper desc not as expected: {resource_desc}"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5009558
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_cancel_helm_based_task_deploying_on_dgxc_from_task_details_page_when_task_is_queued(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_and_teardown_without_upload_helmchart_dgxc,
        running_config,
        session_nvct_ssa_allscope,
    ):
        task_id = nvct_setup_and_teardown_without_upload_helmchart_dgxc
        logging.info(f"Task ID: {task_id}")

        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] == "QUEUED", "Task is not queued"

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)

        assert task_details_page.cancel_task(), "Task is not cancelled"
        get_user_page.wait_for_timeout(1000)

        # check the cleanup date is update
        check_val_list = ["Status", "CleanupDate"]
        real_val = task_details_page.get_need_data(check_val_list)
        logging.info(f"Status: {real_val['Status']}")
        logging.info(f"CleanupDate: {real_val['CleanupDate']}")
        assert real_val["Status"] == "CANCELED", "Status is not correct"
        assert real_val["CleanupDate"] != "-", "Cleanup date is not updated"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5009559
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_helm_based_task_deploying_on_non_dgxc_from_task_list_page(
        self,
        test_data_tasks,
        get_user_page: Page,
        nvct_setup_without_teardown_without_upload_helmchart_non_dgxc,
    ):
        """
        Test Steps:
        1. In the [Tasks] page, choose 1 container based task and click the 3-dots Actions button and click "Delete Task"
        2. Click "Delete Task" of the Cancel Task modal
        3. Check the task is deleted from the task list
        """
        task_id = nvct_setup_without_teardown_without_upload_helmchart_non_dgxc
        logging.info(f"Task ID: {task_id}")

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        task_list_page.click_delete_task()
        task_list_page.confirm_delete_task()
        get_user_page.wait_for_timeout(5000)

        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5009560
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_dgxc_update_personal_api_key_for_helm_based_task_from_task_details_page(
        self,
        test_data_tasks,
        get_user_page: Page,
        running_config,
        nvct_setup_and_teardown_without_upload_helmchart_dgxc,
    ):
        task_id = nvct_setup_and_teardown_without_upload_helmchart_dgxc
        logging.info(f"Task ID: {task_id}")

        task_details_page = TasksDetailsPage(get_user_page)
        task_details_page.navigate_to_page(task_id=task_id)
        logging.info(f"Updating API key: {running_config}")
        assert task_details_page.update_api_key(
            running_config["nvcf_admin_alternative"]["api_key"]
        ), "API key is not updated successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5009561
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_non_dgxc_add_secrets_key_for_helm_based_task_not_created_with_secrets_from_task_list_page(
        self,
        get_user_page: Page,
        running_config,
        nvct_setup_and_teardown_without_upload_helmchart_non_dgxc,
        session_nvct_ssa_allscope,
    ):
        task_id = nvct_setup_and_teardown_without_upload_helmchart_non_dgxc
        logging.info(f"Task ID: {task_id}")

        NVCT_Util = NVCTUtils(session_nvct_ssa_allscope)
        task_details = NVCT_Util.retrieve_task_details(task_id)
        logging.info(f"Task details: {task_details}")
        logging.info(f"Task ID: {task_id}")
        assert task_details["task"]["status"] in [
            "QUEUED",
            "RUNNING",
        ], "Task is not queued or running"

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        logging.info(f"Searching for task: {task_id}")
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        add_secret = {
            "key": "test",
            "value": "test",
        }
        logging.info(f"Adding secret: {add_secret}")
        assert task_list_page.manage_secrets(
            [add_secret]
        ), "Secret is not added successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5062030
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "canary" or CURRENT_ENV == "production",
        reason="Skip this test case because helm chart reavl is not support",
    )
    def test_verify_view_helm_chart_security_guidelines_in_task_helm_chart_creating_page(
        self,
        get_user_page: Page,
    ):
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.page.get_by_role("radio", name="Custom Helm Chart").click()

        assert create_task_page.page.locator(
            "//div[text()='Before You Start']"
        ).is_visible(), "Before You Start can not be visible"
        assert create_task_page.page.locator(
            "//div[text()='Helm Charts must adhere to a set of security guidelines in order to be deployed successfully. To avoid issues later in the process, make sure your Helm Chart is compliant before proceeding.']"
        ).is_visible(), "Warning text can not be visible"

        with create_task_page.page.context.expect_page() as new_page_info:
            create_task_page.page.locator(
                "//a[text()='View Helm Chart Security Guidelines']"
            ).click()
        new_page = new_page_info.value
        new_page.wait_for_selector(
            "//h3[text()='Limitations']", state="visible", timeout=100000
        )
        new_page.bring_to_front()

        new_url = new_page.url
        logging.info(f"New page URL: {new_url}")
        expected_path = "https://docs.nvidia.com/cloud-functions/user-guide/latest/cloud-function/function-creation.html#limitations"
        assert (
            expected_path in new_url
        ), f"Expected URL path '{expected_path}' not found in '{new_url}'"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5009556
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "canary" or CURRENT_ENV == "production",
        reason="Skip this test case because not support helm chart on dgxc",
    )
    def test_E2E_create_helm_based_task_with_all_fields_and_result_upload_deploying_on_dgxc(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_helm_task_sample_dgxc_upload_result"]
        else:
            pass
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            helm_chart_overrides_data=task_info["helm_chart_overrides_data"],
            model=task_info["model"],
            resource=task_info["resource"],
            clusters=task_info["clusters"],
            secrets={"test": "test"},
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )

        # check configuration details
        task_config_details = task_details_page.get_task_config_details_helmchart()
        logging.info(f"Task config details: {task_config_details}")

        # check helm chart overrides
        import json

        helm_chart_overrides_data = json.loads(task_config_details["Helm Chart Overrides"])
        assert (
            helm_chart_overrides_data == task_info["helm_chart_overrides_data"]
        ), "Helm chart overrides is not correct"

        # Step 3: navigate to results tab and verify results
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        expected_results_number = 2
        task_details_page.verify_results_number(expected_results_number)
        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        task_details_page.verify_ngc_registry_results(
            task_info["ResultsUpload_model_name"], "output_result"
        )
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108932
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_edit_existing_secrets_keys_for_helm_based_task_from_task_list_page(
        self,
        get_user_page: Page,
        running_config,
        test_data_tasks,
        nvct_teardown,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_helm_task_sample_gfn_upload_result_with_secrets"
            ]
        else:
            task_info = test_data_tasks[
                "prod_helm_task_sample_gfn_upload_result_with_secrets"
            ]
        logging.info("Start a creation of a task container with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            secrets=task_info["secrets"],
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)

        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        logging.info(f"Searching for task: {task_id}")
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        add_secret = {"key11": "test1"}
        logging.info(f"Modifying secret: {add_secret}")
        assert task_list_page.modify_secrets(
            [add_secret]
        ), "Secret is not modified successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108933
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_edit_existing_secrets_keys_for_helm_based_task_from_task_details_page(
        self,
        get_user_page: Page,
        running_config,
        test_data_tasks,
        nvct_teardown,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_helm_task_sample_gfn_upload_result_with_secrets"
            ]
        else:
            task_info = test_data_tasks[
                "prod_helm_task_sample_gfn_upload_result_with_secrets"
            ]
        logging.info("Start a creation of a task container with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            secrets=task_info["secrets"],
        )
        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)

        assert task_details_page.modify_secrets(
            [{"key11": "test1"}]
        ), "Secret is not modified successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108911
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_helm_based_task_details_for_error_tasks(
        self,
        get_user_page: Page,
        running_config,
        test_data_tasks,
        nvct_teardown,
        session_nvct_ssa_allscope,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_helm_task_sample_gfn_without_upload_return_error_result"
            ]
        else:
            task_info = test_data_tasks[
                "prod_helm_task_sample_gfn_without_upload_return_error_result"
            ]
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            upload_results=False,
        )

        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        # nvct teardown
        self.nvct_remove_list.append(added_function_info)

        task_details_page.wait_for_task_status(
            expected_status="ERRORED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=30,
        )

        assert task_details_page.validate_error_message(
            error_message="task has not completed when terminating"
        ), "Error message not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4108921
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_helm_based_task_deploying_on_gfn_from_task_details_page_when_task_is_error(
        self,
        get_user_page: Page,
        running_config,
        test_data_tasks,
        session_nvct_ssa_allscope,
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks[
                "stg_helm_task_sample_gfn_without_upload_return_error_result"
            ]
        else:
            task_info = test_data_tasks[
                "prod_helm_task_sample_gfn_without_upload_return_error_result"
            ]
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            upload_results=False,
        )

        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")

        task_details_page.wait_for_task_status(
            expected_status="ERRORED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=30,
        )

        logging.info(f"Deleting task id: {task_id}")
        assert task_details_page.delete_task(), "Task is not deleted"

        # verify if the task is deleted
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        assert (
            task_list_page.get_total_task_count() == 0
        ), "Task was not deleted successfully"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5062031
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "canary" or CURRENT_ENV == "production",
        reason="Skip this test case because helm chart reavl is not support",
    )
    def test_create_a_helm_chart_task_with_overrides_parameters_to_make_the_validation_failed(
        self,
        get_user_page: Page,
        test_data_tasks,
    ):
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_helm_task_helm_reval_service"]
        else:
            pass
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            helm_chart_overrides_data=task_info["helm_chart_overrides_data"],
            taskCreateOption=TaskCreateOptionENUM.HELM_REVAL_SERVICE,
            upload_results=False,
        )

        (
            check_flag,
            error_message,
            real_message,
        ) = create_task_page.Form.check_the_pop_up_validation_issues_in_create_task_page()
        assert check_flag, f"Validation issues: {error_message}"
        logging.info(f"real_message: {real_message}")

        (
            check_flag,
            error_message,
        ) = create_task_page.Form.check_the_view_validation_issues_in_create_task_page()
        assert check_flag, f"Validation issues: {error_message}"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5236471
    @pytest.mark.skipif(
        CURRENT_ENV == "production",
        reason="Skip this test case because helm chart reavl is not support",
    )
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_pre_populate_model_resource_name_for_container_in_create_task_page(
        self,
        get_user_page: Page,
    ):
        """
        Test check the tag input box in create task page for container
        """
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()

        assert create_task_page.Form.check_the_pre_populate_model_resource_name()

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5236472
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "production",
        reason="Skip this test case because it is not support",
    )
    def test_check_the_pre_populate_model_resource_name_for_helm_chart_in_create_task_page(
        self,
        get_user_page: Page,
    ):
        """
        Test check the tag input box in create task page for helm
        """

        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()

        create_task_page.page.get_by_role("radio", name="Custom Helm Chart").click()

        assert create_task_page.Form.check_the_pre_populate_model_resource_name()

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5237011
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "production",
        reason="Skip this test case because helm chart reavl is not support",
    )
    def test_check_the_model_resource_name_content_for_container_in_create_task_page(
        self,
        get_user_page: Page,
    ):
        """
        Test check the tag input box in create task page for container
        """
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()

        assert create_task_page.Form.check_the_model_resource_name_content()

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5237012
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "production",
        reason="Skip this test case because helm chart reavl is not support",
    )
    def test_check_the_model_resource_name_content_for_helm_chart_in_create_task_page(
        self,
        get_user_page: Page,
    ):
        """
        Test check the tag input box in create task page for helm
        """

        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()

        create_task_page.page.get_by_role("radio", name="Custom Helm Chart").click()

        assert create_task_page.Form.check_the_pre_populate_model_resource_name()

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5238452
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "production",
        reason="Skip this test case because helm chart reavl is not support",
    )
    def test_check_the_description_in_create_task_page_for_container(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_container_task_sample_gfn_upload_result"]
        else:
            task_info = test_data_tasks["prod_container_task_sample_gfn_upload_result"]
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        # Fill Function Name
        create_task_page.page.locator(create_task_page.Form.elements.NameInput).fill(
            task_name
        )

        # Select Container and Tag
        create_task_page.page.locator(create_task_page.Form.elements.ContainerInput).fill(
            task_info["container"]
        )
        create_task_page.page.wait_for_load_state("load")
        create_task_page.page.locator(
            create_task_page.Form.elements.SelectorChoosebyName.format(
                task_info["container"]
            )
        ).click()
        create_task_page.page.locator(create_task_page.Form.elements.TagInput).fill(
            task_info["tag"]
        )
        create_task_page.page.wait_for_load_state("load")
        create_task_page.page.locator(
            create_task_page.Form.elements.SelectorChoosebyName.format(task_info["tag"])
        ).click()

        # set GPU type and Instance Types
        create_task_page.page.locator(create_task_page.Form.elements.GpuTypeInput).fill(
            task_info["gpu_type"]
        )
        create_task_page.page.wait_for_load_state("load")
        create_task_page.page.locator(
            create_task_page.Form.elements.SelectorChoosebyName.format(
                task_info["gpu_type"]
            )
        ).click()
        create_task_page.page.locator(
            create_task_page.Form.elements.InstanceTpyeInput
        ).fill(task_info["instance_type"])
        create_task_page.page.wait_for_load_state("load")
        create_task_page.page.locator(
            create_task_page.Form.elements.SelectorChoosebyName.format(
                task_info["instance_type"]
            )
        ).click()

        # set maximum_runtime_duration,maximum_queued_duration,termination_grace_period_duration
        if task_info["maximum_runtime_duration"]:
            MaxRuntimeDurationBox_element = (
                create_task_page.Form.MaximumRuntimeDurationDropDownSelector.elements[
                    "MaxRuntimeDurationBox"
                ]
            )
            create_task_page.Form.MaximumRuntimeDurationDropDownSelector.select(
                MaxRuntimeDurationBox_element, task_info["maximum_runtime_duration"]
            )
        if task_info["maximum_queued_duration"]:
            MaxQueDurationBox_element = (
                create_task_page.Form.MaximumQueuedDurationDropDownSelector.elements[
                    "MaxQueDurationBox"
                ]
            )
            create_task_page.Form.MaximumRuntimeDurationDropDownSelector.select(
                MaxQueDurationBox_element, task_info["maximum_queued_duration"]
            )
        if task_info["termination_grace_period_duration"]:
            TerminationGraceDurationBox_element = (
                create_task_page.Form.TerminationGracePeriodDropDownSelector.elements[
                    "TerminationGraceDurationBox"
                ]
            )
            create_task_page.Form.MaximumRuntimeDurationDropDownSelector.select(
                TerminationGraceDurationBox_element,
                task_info["termination_grace_period_duration"],
            )

        create_task_page.page.locator(
            create_task_page.Form.elements.ResultsUploadNoneChoose
        ).click()
        # check the description
        long_description = "This is a long description. This is a long description." * 10
        assert create_task_page.Form.check_the_description_in_create_task_page(
            long_description
        ), "The description is not expanded"

        # Click Create Function Button
        create_task_page.page.locator(create_task_page.Form.elements.CreateTaskBtn).click()

        create_task_page.page.wait_for_selector(
            create_task_page.Form.elements.TaskCreateBanner, state="visible"
        )
        create_task_page.page.wait_for_selector(
            create_task_page.Form.elements.TaskCreateBanner, state="hidden"
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)

        config_details = task_details_page.get_need_data(["Description"])
        logging.info(f"Task configuration details: {config_details}")
        assert (
            config_details["Description"] == long_description
        ), "The description is not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5238453
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "production",
        reason="Skip this test case because it is not support",
    )
    def test_check_the_description_in_create_task_page_for_helm_chart(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_helm_task_sample_gfn_upload_result"]
        else:
            task_info = test_data_tasks["prod_helm_task_sample_gfn_upload_result"]
        logging.info("Start a creation of a task helm chart function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.page.get_by_role("radio", name="Custom Helm Chart").click()

        # Fill Function Name
        create_task_page.page.locator(create_task_page.Form.elements.NameInput).fill(
            task_name
        )
        # Select Helm Chart and Helm Chart Version
        create_task_page.page.locator(
            "//label[text()='Helm Chart']/../..//div[text()='Select one']/..//input"
        ).fill(task_info["Helm_Chart"])
        create_task_page.page.wait_for_load_state("load")
        create_task_page.page.locator(
            "//div[text()='{0}']".format(task_info["Helm_Chart"])
        ).click()
        create_task_page.page.locator(
            "//label[text()='Helm Chart Version']/../..//div[text()='Select a version']/..//input"
        ).fill(task_info["Helm_Chart_Version"])
        create_task_page.page.wait_for_load_state("load")
        create_task_page.page.locator(
            "//div[text()='{0}']".format(task_info["Helm_Chart_Version"])
        ).click()

        # set GPU type and Instance Types
        create_task_page.page.locator(create_task_page.Form.elements.GpuTypeInput).fill(
            task_info["gpu_type"]
        )
        create_task_page.page.wait_for_load_state("load")
        create_task_page.page.locator(
            create_task_page.Form.elements.SelectorChoosebyName.format(
                task_info["gpu_type"]
            )
        ).click()
        create_task_page.page.locator(
            create_task_page.Form.elements.InstanceTpyeInput
        ).fill(task_info["instance_type"])
        create_task_page.page.wait_for_load_state("load")
        create_task_page.page.locator(
            create_task_page.Form.elements.SelectorChoosebyName.format(
                task_info["instance_type"]
            )
        ).click()

        # set maximum_runtime_duration,maximum_queued_duration,termination_grace_period_duration
        if task_info["maximum_runtime_duration"]:
            MaxRuntimeDurationBox_element = (
                create_task_page.Form.MaximumRuntimeDurationDropDownSelector.elements[
                    "MaxRuntimeDurationBox"
                ]
            )
            create_task_page.Form.MaximumRuntimeDurationDropDownSelector.select(
                MaxRuntimeDurationBox_element, task_info["maximum_runtime_duration"]
            )
        if task_info["maximum_queued_duration"]:
            MaxQueDurationBox_element = (
                create_task_page.Form.MaximumQueuedDurationDropDownSelector.elements[
                    "MaxQueDurationBox"
                ]
            )
            create_task_page.Form.MaximumRuntimeDurationDropDownSelector.select(
                MaxQueDurationBox_element, task_info["maximum_queued_duration"]
            )
        if task_info["termination_grace_period_duration"]:
            TerminationGraceDurationBox_element = (
                create_task_page.Form.TerminationGracePeriodDropDownSelector.elements[
                    "TerminationGraceDurationBox"
                ]
            )
            create_task_page.Form.MaximumRuntimeDurationDropDownSelector.select(
                TerminationGraceDurationBox_element,
                task_info["termination_grace_period_duration"],
            )

        create_task_page.page.locator(
            create_task_page.Form.elements.ResultsUploadNoneChoose
        ).click()
        # check the description
        long_description = "This is a long description. This is a long description." * 10
        assert create_task_page.Form.check_the_description_in_create_task_page(
            long_description
        ), "The description is not expanded"

        # Click Create Function Button
        create_task_page.page.locator(create_task_page.Form.elements.CreateTaskBtn).click()

        create_task_page.page.wait_for_selector(
            create_task_page.Form.elements.TaskCreateBanner, state="visible"
        )
        create_task_page.page.wait_for_selector(
            create_task_page.Form.elements.TaskCreateBanner, state="hidden"
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)

        config_details = task_details_page.get_need_data(["Description"])
        logging.info(f"Task configuration details: {config_details}")
        assert (
            config_details["Description"] == long_description
        ), "The description is not correct"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5062032
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "canary" or CURRENT_ENV == "production",
        reason="Skip this test case because helm chart reavl is not support",
    )
    def test_create_a_helm_chart_task_failed_at_validation_and_fix_them_to_let_the_warning_info_disappear(
        self,
        get_user_page: Page,
        test_data_tasks,
        nvct_teardown,
    ):
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_helm_task_helm_reval_service"]
        else:
            pass
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            helm_chart_overrides_data=task_info["helm_chart_overrides_data"],
            taskCreateOption=TaskCreateOptionENUM.HELM_REVAL_SERVICE,
            upload_results=False,
        )

        (
            check_flag,
            error_message,
            real_message,
        ) = create_task_page.Form.check_the_pop_up_validation_issues_in_create_task_page()
        assert check_flag, f"Validation issues: {error_message}"
        logging.info(f"real_message: {real_message}")

        create_task_page.page.locator("//div[@role='textbox']").fill("")
        create_task_page.page.locator(create_task_page.Form.elements.CreateTaskBtn).click()

        create_task_page.page.wait_for_selector(
            create_task_page.Form.elements.TaskCreateBanner, state="visible"
        )
        create_task_page.page.wait_for_selector(
            create_task_page.Form.elements.TaskCreateBanner, state="hidden"
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5250761
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "canary" or CURRENT_ENV == "production",
        reason="Skip this test case because helm chart reavl is not support",
    )
    def test_verify_the_baseline_violation_runasuser_task_negative(
        self,
        get_user_page: Page,
        test_data_tasks,
    ):
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_helm_task_helm_reval_service"]
        else:
            pass
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            "0.0.4",
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            helm_chart_overrides_data=task_info["helm_chart_overrides_data"],
            taskCreateOption=TaskCreateOptionENUM.HELM_REVAL_SERVICE,
            upload_results=False,
        )

        (
            check_flag,
            error_message,
            error_message_list,
        ) = create_task_page.Form.check_the_pop_up_validation_issues_in_create_task_page()
        assert check_flag, f"Validation issues: {error_message}"
        assert (
            'containers["nvcf-test-func"].securityContext.runAsUser or podSpec.securityContext.runAsUser must not be 0'
            in error_message_list
        ), 'containers["nvcf-test-func"].securityContext.runAsUser or podSpec.securityContext.runAsUser must not be 0'

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5250764
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "canary" or CURRENT_ENV == "production",
        reason="Skip this test case because helm chart reavl is not support",
    )
    def test_verify_the_baseline_violation_readonlyrootfilesystem_task_negative(
        self,
        get_user_page: Page,
        test_data_tasks,
    ):
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_helm_task_helm_reval_service"]
        else:
            pass
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            "0.0.5",
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            helm_chart_overrides_data=task_info["helm_chart_overrides_data"],
            taskCreateOption=TaskCreateOptionENUM.HELM_REVAL_SERVICE,
            upload_results=False,
        )

        (
            check_flag,
            error_message,
            error_message_list,
        ) = create_task_page.Form.check_the_pop_up_validation_issues_in_create_task_page()
        assert check_flag, f"Validation issues: {error_message}"
        assert (
            'containers["nvcf-test-func"].securityContext.readOnlyRootFilesystem must be set to true'
            in error_message_list
        ), 'containers["nvcf-test-func"].securityContext.readOnlyRootFilesystem must be set to true'

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5250766
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "canary" or CURRENT_ENV == "production",
        reason="Skip this test case because helm chart reavl is not support",
    )
    def test_verify_the_baseline_violation_runasnonroot_task_negative(
        self,
        get_user_page: Page,
        test_data_tasks,
    ):
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_helm_task_helm_reval_service"]
        else:
            pass
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            "0.0.3",
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            helm_chart_overrides_data=task_info["helm_chart_overrides_data"],
            taskCreateOption=TaskCreateOptionENUM.HELM_REVAL_SERVICE,
            upload_results=False,
        )

        (
            check_flag,
            error_message,
            error_message_list,
        ) = create_task_page.Form.check_the_pop_up_validation_issues_in_create_task_page()
        assert check_flag, f"Validation issues: {error_message}"
        assert (
            'containers["nvcf-test-func"].securityContext.runAsNonRoot or podSpec.securityContext.runAsNonRoot must be set to true'
            in error_message_list
        ), 'containers["nvcf-test-func"].securityContext.runAsNonRoot or podSpec.securityContext.runAsNonRoot must be set to true'

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5250768
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "canary" or CURRENT_ENV == "production",
        reason="Skip this test case because helm chart reavl is not support",
    )
    def test_verify_the_baseline_violation_multiple_validation_errors_task_negative(
        self,
        get_user_page: Page,
        test_data_tasks,
    ):
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_helm_task_helm_reval_service"]
        else:
            pass
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            "0.0.6",
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            helm_chart_overrides_data=task_info["helm_chart_overrides_data"],
            taskCreateOption=TaskCreateOptionENUM.HELM_REVAL_SERVICE,
            upload_results=False,
        )

        (
            check_flag,
            error_message,
            error_message_list,
        ) = create_task_page.Form.check_the_pop_up_validation_issues_in_create_task_page()
        assert check_flag, f"Validation issues: {error_message}"
        assert (
            'containers["nvcf-test-func"].securityContext.readOnlyRootFilesystem must be set to true'
            in error_message_list
        ), 'containers["nvcf-test-func"].securityContext.readOnlyRootFilesystem must be set to true'
        assert (
            'containers["nvcf-test-func"].securityContext.runAsNonRoot or podSpec.securityContext.runAsNonRoot must be set to true'
            in error_message_list
        ), 'containers["nvcf-test-func"].securityContext.runAsNonRoot or podSpec.securityContext.runAsNonRoot must be set to true'
        assert (
            'containers["nvcf-test-func"].securityContext.runAsUser or podSpec.securityContext.runAsUser must not be 0'
            in error_message_list
        ), 'containers["nvcf-test-func"].securityContext.runAsUser or podSpec.securityContext.runAsUser must not be 0'
        assert "service is not ClusterIP" in error_message_list, "service is not ClusterIP"

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T5009557
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "canary" or CURRENT_ENV == "production",
        reason="Skip this test case because not support helm chart on non-dgxc",
    )
    def test_E2E_create_helm_based_task_with_all_fields_and_result_upload_deploying_on_non_dgxc(
        self, test_data_tasks, get_user_page: Page, running_config, nvct_teardown
    ):
        if CURRENT_ENV == "staging":
            task_info = test_data_tasks["stg_helm_task_sample_non_dgxc_upload_result"]
        else:
            pass
        logging.info("Start a creation of a task container function with result upload.")
        task_name = add_timestamp(task_info["name"], "%m%d%H%M%S")

        # Step 1: Create task
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task_helm(
            task_name,
            task_info["Helm_Chart"],
            task_info["Helm_Chart_Version"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            helm_chart_overrides_data=task_info["helm_chart_overrides_data"],
            model=task_info["model"],
            resource=task_info["resource"],
            clusters=task_info["clusters"],
            secrets={"test": "test"},
        )

        # Initialize task details page and get task ID
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created task with ID: {task_id}")
        added_function_info = {
            "task_id": task_id,
        }
        self.nvct_remove_list.append(added_function_info)
        # Step 2: Monitor task status until completion
        logging.info("Monitoring task status until completion")
        task_details_page.wait_for_task_status(
            expected_status="COMPLETED",
            timeout_seconds=task_info["time_out"],
            check_interval_seconds=120,
        )

        # check configuration details
        task_config_details = task_details_page.get_task_config_details_helmchart()
        logging.info(f"Task config details: {task_config_details}")

        # check helm chart overrides
        import json

        helm_chart_overrides_data = json.loads(task_config_details["Helm Chart Overrides"])
        assert (
            helm_chart_overrides_data == task_info["helm_chart_overrides_data"]
        ), "Helm chart overrides is not correct"

        # Step 3: navigate to results tab and verify results
        logging.info("Verifying task results")
        task_details_page.navigate_to_results_tab()
        expected_results_number = 2
        task_details_page.verify_results_number(expected_results_number)
        # Step 4: Navigate to NGC registry and verify results
        task_details_page.navigate_to_ngc_registry()
        task_details_page.verify_ngc_registry_results(
            task_info["ResultsUpload_model_name"], "output_result"
        )
        logging.info("Test completed successfully - All results verified")

    @pytest.mark.CloudFunctions
    @pytest.mark.nvct
    @pytest.mark.T4139143
    @pytest.mark.skipif(
        CURRENT_ENV == "production" or CURRENT_ENV == "canary",
        reason="This case can only be tested in staging environment as byoo nvct ui hasn't been released to prod/canary",
    )
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_clone_container_based_task_with_telemetries_from_task_list_page(
        self,
        test_data_tasks,
        get_user_page: Page,
        session_nvct_ssa_allscope,
        running_config,
        nvct_teardown,
        get_personal_keys_with_cookies,
        personal_key_teardown,
    ):
        """
        [E2E] Clone container-based task with telemetries from task list page

        Test Procedure:
        1. In the [Tasks] page, choose 1 container-based task with telemetries logs_metrics_http_Grafana
        2. Click the task name to go to task details page
        3. Click the 3-dots Actions button and click "Clone Task"
        4. It will lead to the [Clone Task] page
        5. Check all the fields are prefilled with the same content as the copied function except for the Task Name and Personal API Key
        6. Fill in the Task Name and generate Personal API Key, click [Create Task] button
        7. Verify the task is created successfully and navigate to task details page
        8. Verify the telemetries configuration is cloned correctly
        """
        resp = get_personal_keys_with_cookies()
        logging.info(f"resp['apiKeys']: {resp['apiKeys']}")
        logging.info(f"exiting personal key number: {len(resp['apiKeys'])}")
        assert len(resp["apiKeys"]) < 28, "Personal API key has exceeded the limit"

        # Step 1: Create a container-based task with telemetries
        task_info = test_data_tasks[
            "stg_container_task_sample_gfn_upload_result_with_telemetries"
        ]
        original_task_name = add_timestamp(
            "container_telemetry_task_for_clone", "%m%d%H%M%S"
        )

        logging.info("Creating container-based task with telemetries for cloning")
        create_task_page = TasksCreatePage(get_user_page)
        create_task_page.navigate_to_page()
        create_task_page.Form.create_task(
            original_task_name,
            task_info["container"],
            task_info["tag"],
            task_info["gpu_type"],
            task_info["instance_type"],
            maximum_runtime_duration=task_info["maximum_runtime_duration"],
            maximum_queued_duration=task_info["maximum_queued_duration"],
            termination_grace_period_duration=task_info[
                "termination_grace_period_duration"
            ],
            ResultsUpload_model_name=task_info["ResultsUpload_model_name"],
            Personal_key=running_config["nvcf_admin"]["api_key"],
            telemetry_metrics=LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID,
            telemetry_logs=LOGS_METRICS_HTTP_GRAFANA_TELEMETRY_ID,
        )

        # Get task ID from the created task
        task_details_page = TasksDetailsPage(get_user_page)
        task_id = task_details_page.get_task_id_from_url()
        logging.info(f"Created container-based telemetries task with ID: {task_id}")

        # Add to cleanup list
        self.nvct_remove_list.append({"task_id": task_id})

        # Get task details for verification
        task_details = NVCTUtils(session_nvct_ssa_allscope).retrieve_task_details(
            task_id=task_id
        )

        # Step 2: Navigate to Tasks page and search for the helm task
        task_list_page = TasksListPage(get_user_page)
        task_list_page.navigate_to_page()
        task_list_page.search_task_exact_match(task_id)
        task_list_page.click_three_dots_menu()
        task_list_page.click_clone_task()

        # Step 5: Verify we're on the Clone Task page and check prefilled fields
        task_clone_page = TasksClonePage(get_user_page)

        # Verify fields are prefilled (except Task Name and Personal API Key)
        expected_details = task_details.get("task", {})
        task_clone_page.verify_clone_details(
            expected_details=task_details.get("task"), function_type="container"
        )
        # Step 6: Fill in Task Name and generate Personal API Key
        task_name = add_timestamp("cloned_container_telemetry_task", "%m%d%H%M%S")
        personal_key = add_timestamp("cloned_container_telemetry_key", "%m%d%H%M%S")
        duration = "30 days"  # 30 days

        deployment_config = test_data_tasks["clone_task_deployment_config"]

        # Clone the task with telemetries
        task_clone_page.clone_task(
            task_name,
            gpu_type=deployment_config["gpu_type"],
            instance_type=deployment_config["instance_type"],
            clusters=None,
            generate_personal_key=True,
            generate_person_key_list=[personal_key, duration],
            upload_results=True,
        )

        resp_later = get_personal_keys_with_cookies()
        logging.info(f"resp_later['apiKeys']: {resp_later['apiKeys']}")
        logging.info(f"exiting personal key number: {len(resp_later['apiKeys'])}")
        assert len(resp_later["apiKeys"]) < 28, "Personal API key has exceeded the limit"
        keys = [
            key["keyId"] for key in resp_later["apiKeys"] if key["name"] == personal_key
        ]
        if len(resp_later["apiKeys"]) > len(resp["apiKeys"]) and keys == "":
            diff = [d for d in resp_later["apiKeys"] if d not in resp["apiKeys"]][0]
            logging.info(f"diff: {diff}")
            keys = diff["keyId"]

        logging.info(keys)
        # tear down for the new dup one after rotation
        self.api_keys_remove_list.append(keys[0])

        # Step 7: Verify task creation and get cloned task details
        cloned_task_details_page = TasksDetailsPage(get_user_page)
        cloned_task_id = cloned_task_details_page.get_task_id_from_url()
        logging.info(f"Cloned Helm telemetries task with ID: {cloned_task_id}")

        # Add to cleanup list
        self.nvct_remove_list.append({"task_id": cloned_task_id})

        # Step 8: Verify the telemetries configuration is cloned correctly
        cloned_task_details = NVCTUtils(session_nvct_ssa_allscope).retrieve_task_details(
            task_id=cloned_task_id
        )
        original_telemetries = expected_details.get("telemetries", {})
        cloned_telemetries = cloned_task_details.get("task", {}).get("telemetries", {})

        # Verify telemetries logs and metrics are the same
        if original_telemetries:
            assert cloned_telemetries.get("logs") == original_telemetries.get(
                "logs"
            ), "Telemetries logs configuration not cloned correctly"
            assert cloned_telemetries.get("metrics") == original_telemetries.get(
                "metrics"
            ), "Telemetries metrics configuration not cloned correctly"
            logging.info("Telemetries configuration verified successfully")

        # Step 9: Verify initial task status is QUEUED
        data_dict = cloned_task_details_page.get_need_data(["Status"])
        assert data_dict["Status"] == "QUEUED", "Cloned task is not queued"
