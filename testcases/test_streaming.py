import pytest
import os
import logging
import yaml
import subprocess
import time
import signal
import zipfile
import shutil
import cv2

from skimage.metrics import structural_similarity as ssim
from pathlib import Path
from playwright.sync_api import Page, TimeoutError

from config.elements import elements_streaming_client

from config.consts import CURRENT_ENV
from cloudia.utils.tools import Tools
from cloudia.api.http.api_session import APISession
from cloudia.api.http.api_validate import APIValidation
from cloudia.utils.backend_service.nvcf.nvcf_utils import NVCFUtils, StreamingAliasEnum
from cloudia.utils.backend_service.nvcf.nvcf_const import (
    NVCF_DATA,
    STREAMING_CONTAINER_GFN_PREFIX,
    STREAMING_CONTAINER_NON_GFN_PREFIX,
    STREAMING_HELM_CHART_GFN_PREFIX,
    STREAMING_HELM_CHART_NON_GFN_PREFIX,
    STREAMING_E2E_TEST_PREFIX,
)


class TestStreaming:
    func_remove_list = list()
    func_scale_down_list = list()

    SCALE_DOWN_COUNT = 1
    SCALE_UP_COUNT = 3
    LOCAL_IP = "127.0.0.1"

    entry_elements = elements_streaming_client.elements["Entry"]
    streaming_elements = elements_streaming_client.elements["StreamingPage"]

    @pytest.fixture(scope="session")
    def streaming_session_setup_and_teardown(
        self,
        request,
        session_nvcf_admin_sak: dict[str, APISession],
    ):
        """
        A session-scoped fixture that cleans up all streaming functions before and after test execution.

        Args:
            request: Built-in pytest fixture for accessing test configuration
            session_nvcf_admin_sak (dict[str, APISession]): NVCF admin session information

        Behavior:
            Setup:
                - Deletes all streaming functions
            Teardown:
                - Deletes all streaming functions
        """
        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)

        try:
            logging.info("Executing setup to clean up all streaming functions...")
            NVCF_Util.delete_all_streaming_functions()
        except Exception as e:
            logging.error(f"Setup failed with error: {e}")
            raise

        yield

        logging.info("Executing teardown to clean up all streaming functions...")
        try:
            NVCF_Util.delete_all_streaming_functions()
        except Exception as e:
            logging.error(f"Teardown failed with error: {e}")
            raise

    @pytest.fixture(scope="function")
    def setup_console_logging(self, nvcf_ibeta_streaming_client: Page, request):
        """
        A function-scoped fixture that sets up console logging for all tests in TestStreaming.
        """
        client_page = nvcf_ibeta_streaming_client
        test_name = request.node.name

        def console_handler(msg):
            log_type = msg.type
            text = msg.text
            location = msg.location
            logging.info(
                f"[{test_name}] Browser Console [{log_type}] "
                f"at {location.get('url', 'N/A')}:{location.get('lineNumber', 'N/A')}: {text}"
            )

        def page_error_handler(error):
            logging.error(f"[{test_name}] Page Error: {error}")

        def request_failed_handler(request):
            logging.error(f"[{test_name}] Request Failed: {request.url}")

        # Register all event listeners
        client_page.on("console", console_handler)
        client_page.on("pageerror", page_error_handler)
        client_page.on("requestfailed", request_failed_handler)

        logging.info(f"[{test_name}] Console logging setup completed")

        yield

        logging.info(f"[{test_name}] Test completed. All console logs have been recorded")

    @pytest.fixture()
    def func_setup(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
        request,
    ):
        func_alias, prefix = request.param
        logging.info(f"The create function alias is {func_alias}")
        func_id, vers_id = NVCFUtils(session_nvcf_admin_sak).create_function(
            func_alias, prefix
        )
        added_function_info = {
            "func_id": func_id,
            "version_id": vers_id,
        }
        self.func_remove_list.append(added_function_info)
        return func_id, vers_id

    @pytest.fixture()
    def func_setup_for_deploy_tests(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
        request,
    ):
        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)
        func_alias, prefix = request.param
        logging.info(f"The create function alias is {func_alias}")
        active_streaming_func_list = NVCF_Util.get_active_streaming_funcs_list()
        for active_function in active_streaming_func_list[:]:
            if active_function["alias"] == func_alias:
                logging.info(
                    f"The streaming function is ACTIVE, delete the function of {func_alias} before testing..."
                )
                NVCF_Util.delete_single_function(
                    active_function["func_id"], active_function["vers_id"]
                )
        func_id, vers_id = NVCFUtils(session_nvcf_admin_sak).create_function(
            func_alias, prefix
        )
        return func_id, vers_id

    @pytest.fixture()
    def func_setup_for_deployed_tests(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
        streaming_deploy_info_gfn: dict,
        streaming_deploy_info_cluster: dict,
        request,
        streaming_invoke_function_id: str,
        test_cluster_params: dict,
        cluster_validation_deploy_info: dict,
    ):
        if streaming_invoke_function_id:
            return streaming_invoke_function_id, ""

        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)

        # For cluster validation function creation and deployment
        if request.node.get_closest_marker("cluster_validation"):
            prefix = test_cluster_params.get("func_name") + "_streaming_"
            if request.node.get_closest_marker("lls_helm_chart"):
                func_alias = StreamingAliasEnum.streaming_helm_chart_non_gfn.value
            else:
                func_alias = StreamingAliasEnum.streaming_container_non_gfn.value

            # Create the cluster validation function
            func_id, vers_id = NVCF_Util.create_function(func_alias, prefix)
            added_function_info = {
                "func_id": func_id,
                "version_id": vers_id,
            }
            self.func_remove_list.append(added_function_info)

            # Deploy the cluster validation function
            deploy_data = cluster_validation_deploy_info
            assert NVCF_Util.deploy_function_to_active(
                func_id, vers_id, deploy_data
            ), "The function not deployed to ACTIVE."
            return func_id, vers_id

        # For LLS E2E test function creation and deployment
        if request.node.get_closest_marker(
            "nvcf_worker_lls_dev_nightly"
        ) or request.node.get_closest_marker("nvcf_worker_lls_dev"):
            logging.info("Create the LLS E2E test function")
            # Create the LLS E2E test function
            func_alias, prefix = request.param
            func_id, vers_id = NVCF_Util.create_function(func_alias, prefix)

            # Deploy the LLS E2E test function
            logging.info("Deploy the LLS E2E test function")
            if (
                func_alias == StreamingAliasEnum.streaming_container_gfn.value
                or func_alias == StreamingAliasEnum.streaming_helm_chart_gfn.value
            ):
                deploy_data = streaming_deploy_info_gfn
            elif (
                func_alias == StreamingAliasEnum.streaming_container_non_gfn.value
                or func_alias == StreamingAliasEnum.streaming_helm_chart_non_gfn.value
            ):
                deploy_data = streaming_deploy_info_cluster
            assert NVCF_Util.deploy_function_to_active(
                func_id, vers_id, deploy_data
            ), "The function not deployed to ACTIVE."
            return func_id, vers_id

        active_streaming_func_list = NVCF_Util.get_active_streaming_funcs_list()

        func_alias, prefix = request.param
        for active_func in active_streaming_func_list:
            if active_func["alias"] == func_alias:
                return active_func["func_id"], active_func["vers_id"]
        # If not found existed active function, create and deploy a new one
        func_id, vers_id = NVCFUtils(session_nvcf_admin_sak).create_function(
            func_alias, prefix
        )
        if (
            prefix == STREAMING_CONTAINER_GFN_PREFIX
            or prefix == STREAMING_HELM_CHART_GFN_PREFIX
        ):
            deploy_data = streaming_deploy_info_gfn
        elif (
            prefix == STREAMING_CONTAINER_NON_GFN_PREFIX
            or prefix == STREAMING_HELM_CHART_NON_GFN_PREFIX
        ):
            deploy_data = streaming_deploy_info_cluster
        assert NVCF_Util.deploy_function_to_active(
            func_id, vers_id, deploy_data
        ), "The function not deployed to ACTIVE."
        return func_id, vers_id

    @pytest.fixture()
    def func_teardown(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
    ):
        yield
        test_data_del = Tools.load_case_data(NVCF_DATA, "DELETE_FUNCTION")
        session = session_nvcf_admin_sak["ngc"]
        function_path = test_data_del["req"]["path"]
        while self.func_remove_list:
            function_dict = self.func_remove_list.pop()
            logging.info(f"Deleting function {function_dict}")
            test_data_del["req"][
                "path"
            ] = f"{function_path}/{function_dict['func_id']}/versions/{function_dict['version_id']}"
            resp_del = session.request(**test_data_del["req"])
            APIValidation(resp_del, test_data_del["exp"]).validate_response()

    @pytest.fixture()
    def func_scale_teardown(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
        request,
        streaming_deploy_info_gfn,
        streaming_deploy_info_cluster,
    ):
        yield
        prefix = request.param
        if (
            prefix == STREAMING_CONTAINER_GFN_PREFIX
            or prefix == STREAMING_HELM_CHART_GFN_PREFIX
        ):
            deploy_data = streaming_deploy_info_gfn
        elif (
            prefix == STREAMING_CONTAINER_NON_GFN_PREFIX
            or prefix == STREAMING_HELM_CHART_NON_GFN_PREFIX
        ):
            deploy_data = streaming_deploy_info_cluster
        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)
        while self.func_scale_down_list:
            function_dict = self.func_scale_down_list.pop()
            logging.info(f"Scale down function {function_dict}")
            NVCF_Util.scale_mofification_with_deploy_data(
                function_dict["func_id"],
                function_dict["vers_id"],
                deploy_data,
                min_instance=self.SCALE_DOWN_COUNT,
                max_instance=self.SCALE_DOWN_COUNT,
            )
            assert NVCF_Util.wait_until_active_instance_updated(
                function_dict["func_id"],
                function_dict["vers_id"],
                expected_count=self.SCALE_DOWN_COUNT,
            ), "Wait time over but instance count not changed as expected."
        time.sleep(120)

    def edit_lls_proxy_config(
        self,
        function_id: str,
        streaming_invoke_api_key: str = None,
        running_config: dict = None,
    ):
        """
        Edit the LLS proxy config

        :param function_id: The function ID to edit
        :param streaming_invoke_api_key: The streaming invoke API Key if existed
        """
        current_dir = Path(__file__).resolve().parent
        nvcf_root_dir = current_dir.parent.parent
        relative_path = "nvcf-lls-intermediary-proxy/properties/application.yaml"
        yaml_file_path = nvcf_root_dir / relative_path
        with yaml_file_path.open("r") as file:
            app_config = yaml.safe_load(file)
            logging.info(f"File content {app_config}")
        if streaming_invoke_api_key:
            api_key = streaming_invoke_api_key
        else:
            api_key = running_config["nvcf_admin"]["api_key"]
        if CURRENT_ENV == "staging":
            app_config["environment"] = "stage"
        elif CURRENT_ENV == "production":
            app_config["environment"] = "prod"
        app_config["functionDetails"]["functionId"] = function_id
        app_config["functionDetails"]["apiKey"] = api_key
        with open(yaml_file_path, "w") as file:
            yaml.dump(app_config, file, default_flow_style=False, sort_keys=False)
        logging.info(f"File content {app_config}")

    @pytest.fixture()
    def lls_proxy_update(
        self, func_setup_for_deployed_tests, streaming_invoke_api_key, running_config
    ):
        func_id, vers_id = func_setup_for_deployed_tests
        self.edit_lls_proxy_config(func_id, streaming_invoke_api_key, running_config)
        yield func_id, vers_id

    @pytest.fixture()
    def go_proxy_service(self, lls_proxy_update):
        func_id, vers_id = lls_proxy_update
        current_dir = Path(__file__).resolve().parent
        target_dir = current_dir.parent.parent / "nvcf-lls-intermediary-proxy"

        go_process = subprocess.Popen(
            ["go", "run", "./main.go"], cwd=target_dir, start_new_session=True
        )

        # Wait longer and verify the service is actually working
        time.sleep(10)

        yield func_id, vers_id

        os.killpg(os.getpgid(go_process.pid), signal.SIGTERM)
        time.sleep(30)
        go_process.wait()

    @pytest.fixture(scope="function")
    def nvcf_ibeta_streaming_client(self, chrome_page: Page, go_proxy_service):
        func_id, _ = go_proxy_service
        logging.info(f"Starting client on function {func_id}")
        current_dir = Path(__file__).resolve().parent
        zip_file_path = (
            current_dir.parent.parent
            / "nvcf-low-latency-streaming-docs"
            / "utils"
            / "nvcf_ibeta_client.zip"
        )
        extract_dir = (
            current_dir.parent.parent
            / "nvcf-low-latency-streaming-docs"
            / "utils"
            / "nvcf_ibeta_client"
        )

        with zipfile.ZipFile(zip_file_path, "r") as zip_ref:
            zip_ref.extractall(extract_dir)

        html_file_path = os.path.join(extract_dir, "index.html")
        chrome_page.goto(f"file://{html_file_path}")
        chrome_page.wait_for_load_state("load")

        yield chrome_page

        if chrome_page.locator(self.streaming_elements["StopStreamingBtn"]).count() >= 1:
            chrome_page.locator(self.streaming_elements["StopStreamingBtn"]).click()
            chrome_page.wait_for_timeout(10000)

        if os.path.exists(extract_dir):
            shutil.rmtree(extract_dir)

    def start_streaming_invoke(self, page: Page, time_out=5):
        """
        Start the streaming invoke from the streaming client page

        :param page: The NVCF streaming client page
        :return bool: If the invoke start is successful
        """
        page.wait_for_timeout(10000)
        page.locator(self.entry_elements["ServerIPInput"]).fill(self.LOCAL_IP)
        page.locator(self.entry_elements["StartStreamingBtn"]).click()
        page.wait_for_timeout(10000)
        if page.locator(self.entry_elements["FailedInfo"]).count() >= 1:
            error_info = page.locator(self.entry_elements["FailedInfo"]).text_content()
            logging.error(f"Streaming invoke failed, The error info is: {error_info}")
            return False
        page.wait_for_timeout(time_out * 1000)
        try:
            page.locator(self.streaming_elements["StopStreamingBtn"]).wait_for()
        except TimeoutError:
            logging.error("Streaming invoke start failed, not found the streaming page.")
            return False
        return True

    def check_streaming_result(
        self, page: Page, function_type: str, expected_similarity=0.95
    ):
        """
        Start the streaming invoke from the streaming client page

        :param page: The NVCF streaming client page
        :param function_type: The function type of streaming invocation
        :param expected_similarity: The expected image similarity threshold, default as 0.95

        :return bool: If the similarity comparision passed over the threshold
        """
        current_dir = Path(__file__).resolve().parent
        if (
            function_type == STREAMING_CONTAINER_GFN_PREFIX
            or function_type == STREAMING_HELM_CHART_GFN_PREFIX
        ):
            image_path = (
                current_dir.parent
                / "config"
                / "screenshots"
                / "nvcf_streaming_screenshot_still_life.png"
            )
        elif (
            function_type == STREAMING_CONTAINER_NON_GFN_PREFIX
            or function_type == STREAMING_HELM_CHART_NON_GFN_PREFIX
        ):
            image_path = (
                current_dir.parent
                / "config"
                / "screenshots"
                / "nvcf_streaming_screenshot_grid_ground.png"
            )
        expected_img = cv2.imread(image_path)
        current_screenshot_path = "config/screenshots/new_screenshot.png"
        page.screenshot(path=current_screenshot_path)
        current_img = cv2.imread(current_screenshot_path)

        expected_gray = cv2.cvtColor(expected_img, cv2.COLOR_BGR2GRAY)
        current_gray = cv2.cvtColor(current_img, cv2.COLOR_BGR2GRAY)

        similarity_index, _ = ssim(expected_gray, current_gray, full=True)

        if similarity_index > expected_similarity:
            logging.info(f"Similarity index: {similarity_index}, test passed.")
            return True
        else:
            logging.info(
                f"Similarity index: {similarity_index}, expected {expected_similarity}, test failed."
            )
            return False

    @pytest.mark.CloudFunctions
    @pytest.mark.streaming_test
    @pytest.mark.T3947990
    def test_create_a_new_streaming_function_container_GFN(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
        func_teardown,
        streaming_session_setup_and_teardown,
    ):
        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)
        func_id, version_id = NVCF_Util.create_function(
            StreamingAliasEnum.streaming_container_gfn.value, STREAMING_CONTAINER_GFN_PREFIX
        )
        added_function_info = {
            "func_id": func_id,
            "version_id": version_id,
        }
        self.func_remove_list.append(added_function_info)
        assert NVCF_Util.check_if_function_version_listed(
            func_id,
            version_id,
        ), "The new created function was not listed."

    @pytest.mark.CloudFunctions
    @pytest.mark.streaming_test
    @pytest.mark.T3948002
    def test_create_a_new_streaming_function_container_non_GFN(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
        func_teardown,
        streaming_session_setup_and_teardown,
    ):
        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)
        func_id, version_id = NVCF_Util.create_function(
            StreamingAliasEnum.streaming_container_non_gfn.value,
            STREAMING_CONTAINER_NON_GFN_PREFIX,
        )
        added_function_info = {
            "func_id": func_id,
            "version_id": version_id,
        }
        self.func_remove_list.append(added_function_info)
        assert NVCF_Util.check_if_function_version_listed(
            func_id,
            version_id,
        ), "The new created function was not listed."

    @pytest.mark.CloudFunctions
    @pytest.mark.streaming_test
    @pytest.mark.T3948011
    def test_create_a_new_streaming_function_helm_chart_GFN(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
        func_teardown,
        streaming_session_setup_and_teardown,
    ):
        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)
        func_id, version_id = NVCF_Util.create_function(
            StreamingAliasEnum.streaming_helm_chart_gfn.value,
            STREAMING_HELM_CHART_GFN_PREFIX,
        )
        added_function_info = {
            "func_id": func_id,
            "version_id": version_id,
        }
        self.func_remove_list.append(added_function_info)
        assert NVCF_Util.check_if_function_version_listed(
            func_id,
            version_id,
        ), "The new created function was not listed."

    @pytest.mark.CloudFunctions
    @pytest.mark.streaming_test
    @pytest.mark.skip("Currently Helm Chart non GFN is not supported.")
    @pytest.mark.T3948020
    def test_create_a_new_streaming_function_helm_chart_non_GFN(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
        func_teardown,
        streaming_session_setup_and_teardown,
    ):
        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)
        func_id, version_id = NVCF_Util.create_function(
            StreamingAliasEnum.streaming_helm_chart_non_gfn.value,
            STREAMING_HELM_CHART_NON_GFN_PREFIX,
        )
        added_function_info = {
            "func_id": func_id,
            "version_id": version_id,
        }
        self.func_remove_list.append(added_function_info)
        assert NVCF_Util.check_if_function_version_listed(
            func_id,
            version_id,
        ), "The new created function was not listed."

    @pytest.mark.CloudFunctions
    @pytest.mark.streaming_test
    @pytest.mark.T3947991
    @pytest.mark.parametrize(
        "func_setup_for_deploy_tests",
        [
            (
                StreamingAliasEnum.streaming_container_gfn.value,
                STREAMING_CONTAINER_GFN_PREFIX,
            )
        ],
        indirect=True,
    )
    def test_deploy_streaming_function_on_container_GFN(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
        func_setup_for_deploy_tests,
        streaming_deploy_info_gfn,
    ):
        func_id, vers_id = func_setup_for_deploy_tests
        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)
        assert NVCF_Util.deploy_function_to_active(
            func_id, vers_id, streaming_deploy_info_gfn
        ), "The function not deployed to ACTIVE."

    @pytest.mark.CloudFunctions
    @pytest.mark.streaming_test
    @pytest.mark.T3948003
    @pytest.mark.parametrize(
        "func_setup_for_deploy_tests",
        [
            (
                StreamingAliasEnum.streaming_container_non_gfn.value,
                STREAMING_CONTAINER_NON_GFN_PREFIX,
            )
        ],
        indirect=True,
    )
    def test_deploy_streaming_function_on_container_non_GFN(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
        func_setup_for_deploy_tests,
        streaming_deploy_info_cluster,
    ):
        func_id, vers_id = func_setup_for_deploy_tests
        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)
        logging.info(f"Deploy data is {streaming_deploy_info_cluster}")
        assert NVCF_Util.deploy_function_to_active(
            func_id, vers_id, streaming_deploy_info_cluster
        ), "The function not deployed to ACTIVE."

    @pytest.mark.CloudFunctions
    @pytest.mark.streaming_test
    @pytest.mark.T3948012
    @pytest.mark.parametrize(
        "func_setup_for_deploy_tests",
        [
            (
                StreamingAliasEnum.streaming_helm_chart_gfn.value,
                STREAMING_HELM_CHART_GFN_PREFIX,
            )
        ],
        indirect=True,
    )
    def test_deploy_streaming_function_on_helm_chart_GFN(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
        func_setup_for_deploy_tests,
        streaming_deploy_info_gfn,
    ):
        func_id, vers_id = func_setup_for_deploy_tests
        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)
        assert NVCF_Util.deploy_function_to_active(
            func_id, vers_id, streaming_deploy_info_gfn
        ), "The function not deployed to ACTIVE."

    @pytest.mark.CloudFunctions
    @pytest.mark.streaming_test
    @pytest.mark.skip("Currently Helm Chart non GFN is not supported.")
    @pytest.mark.T3948021
    @pytest.mark.parametrize(
        "func_setup_for_deploy_tests",
        [
            (
                StreamingAliasEnum.streaming_helm_chart_non_gfn.value,
                STREAMING_HELM_CHART_NON_GFN_PREFIX,
            )
        ],
        indirect=True,
    )
    def test_deploy_streaming_function_on_helm_chart_non_GFN(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
        func_setup_for_deploy_tests,
        streaming_deploy_info_cluster,
    ):
        func_id, vers_id = func_setup_for_deploy_tests
        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)
        assert NVCF_Util.deploy_function_to_active(
            func_id, vers_id, streaming_deploy_info_cluster
        ), "The function not deployed to ACTIVE."

    @pytest.mark.CloudFunctions
    @pytest.mark.T3947992
    @pytest.mark.streaming_test
    @pytest.mark.parametrize(
        "func_setup_for_deployed_tests",
        [
            (
                StreamingAliasEnum.streaming_container_gfn.value,
                STREAMING_CONTAINER_GFN_PREFIX,
            )
        ],
        indirect=True,
    )
    def test_invoke_and_initial_cleanup_of_existing_streaming_function_container_GFN(
        self,
        nvcf_ibeta_streaming_client: Page,
        setup_console_logging,
    ):
        client_page = nvcf_ibeta_streaming_client
        assert self.start_streaming_invoke(
            client_page
        ), "The streaming invoke start failed."
        assert self.check_streaming_result(
            client_page, STREAMING_CONTAINER_GFN_PREFIX
        ), "The streaming check failed."

    @pytest.mark.CloudFunctions
    @pytest.mark.T3948004
    @pytest.mark.streaming_test
    @pytest.mark.parametrize(
        "func_setup_for_deployed_tests",
        [
            (
                StreamingAliasEnum.streaming_container_non_gfn.value,
                STREAMING_CONTAINER_NON_GFN_PREFIX,
            )
        ],
        indirect=True,
    )
    def test_invoke_and_initial_cleanup_of_existing_streaming_function_container_non_GFN(
        self,
        nvcf_ibeta_streaming_client: Page,
        setup_console_logging,
    ):
        client_page = nvcf_ibeta_streaming_client
        assert self.start_streaming_invoke(
            client_page
        ), "The streaming invoke start failed."
        assert self.check_streaming_result(
            client_page, STREAMING_CONTAINER_NON_GFN_PREFIX
        ), "The streaming check failed."

    @pytest.mark.CloudFunctions
    @pytest.mark.T3948013
    @pytest.mark.streaming_test
    @pytest.mark.parametrize(
        "func_setup_for_deployed_tests",
        [
            (
                StreamingAliasEnum.streaming_helm_chart_gfn.value,
                STREAMING_HELM_CHART_GFN_PREFIX,
            )
        ],
        indirect=True,
    )
    def test_invoke_and_initial_cleanup_of_existing_streaming_function_helm_chart_GFN(
        self,
        nvcf_ibeta_streaming_client: Page,
        setup_console_logging,
    ):
        client_page = nvcf_ibeta_streaming_client
        assert self.start_streaming_invoke(
            client_page
        ), "The streaming invoke start failed."
        assert self.check_streaming_result(
            client_page, STREAMING_HELM_CHART_GFN_PREFIX
        ), "The streaming check failed."

    @pytest.mark.CloudFunctions
    @pytest.mark.T3948022
    @pytest.mark.streaming_test
    @pytest.mark.skip("Currently Helm Chart non GFN is not supported.")
    @pytest.mark.parametrize(
        "func_setup_for_deployed_tests",
        [
            (
                StreamingAliasEnum.streaming_helm_chart_non_gfn.value,
                STREAMING_HELM_CHART_NON_GFN_PREFIX,
            )
        ],
        indirect=True,
    )
    def test_invoke_and_initial_cleanup_of_existing_streaming_function_helm_chart_non_GFN(
        self,
        nvcf_ibeta_streaming_client: Page,
        setup_console_logging,
    ):
        client_page = nvcf_ibeta_streaming_client
        assert self.start_streaming_invoke(
            client_page
        ), "The streaming invoke start failed."
        assert self.check_streaming_result(
            client_page, STREAMING_HELM_CHART_NON_GFN_PREFIX
        ), "The streaming check failed."

    @pytest.mark.CloudFunctions
    @pytest.mark.streaming_test
    @pytest.mark.T3947998
    @pytest.mark.parametrize(
        "func_setup_for_deployed_tests",
        [
            (
                StreamingAliasEnum.streaming_container_gfn.value,
                STREAMING_CONTAINER_GFN_PREFIX,
            )
        ],
        indirect=True,
    )
    @pytest.mark.parametrize(
        "func_scale_teardown", [STREAMING_CONTAINER_GFN_PREFIX], indirect=True
    )
    def test_scale_up_test_for_streaming_function_container_GFN(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
        func_setup_for_deployed_tests,
        streaming_deploy_info_gfn,
        func_scale_teardown,
    ):
        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)
        func_id, vers_id = func_setup_for_deployed_tests
        NVCF_Util.scale_mofification_with_deploy_data(
            func_id,
            vers_id,
            streaming_deploy_info_gfn,
            min_instance=self.SCALE_UP_COUNT,
            max_instance=self.SCALE_UP_COUNT,
        )
        assert NVCF_Util.wait_until_active_instance_updated(
            func_id, vers_id, expected_count=self.SCALE_UP_COUNT
        ), "Wait time over but instance count not changed as expected."
        func_scale_down_info = {
            "func_id": func_id,
            "vers_id": vers_id,
        }
        self.func_scale_down_list.append(func_scale_down_info)

    @pytest.mark.CloudFunctions
    @pytest.mark.streaming_test
    @pytest.mark.T3948007
    @pytest.mark.parametrize(
        "func_setup_for_deployed_tests",
        [
            (
                StreamingAliasEnum.streaming_container_non_gfn.value,
                STREAMING_CONTAINER_NON_GFN_PREFIX,
            )
        ],
        indirect=True,
    )
    @pytest.mark.parametrize(
        "func_scale_teardown", [STREAMING_CONTAINER_NON_GFN_PREFIX], indirect=True
    )
    def test_scale_up_test_for_streaming_function_container_non_GFN(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
        func_setup_for_deployed_tests,
        streaming_deploy_info_cluster,
        func_scale_teardown,
    ):
        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)
        func_id, vers_id = func_setup_for_deployed_tests
        NVCF_Util.scale_mofification_with_deploy_data(
            func_id,
            vers_id,
            streaming_deploy_info_cluster,
            min_instance=self.SCALE_UP_COUNT,
            max_instance=self.SCALE_UP_COUNT,
        )
        assert NVCF_Util.wait_until_active_instance_updated(
            func_id, vers_id, expected_count=self.SCALE_UP_COUNT
        ), "Wait time over but instance count not changed as expected."

    @pytest.mark.CloudFunctions
    @pytest.mark.streaming_test
    @pytest.mark.T3948016
    @pytest.mark.parametrize(
        "func_setup_for_deployed_tests",
        [
            (
                StreamingAliasEnum.streaming_helm_chart_gfn.value,
                STREAMING_HELM_CHART_GFN_PREFIX,
            )
        ],
        indirect=True,
    )
    @pytest.mark.parametrize(
        "func_scale_teardown", [STREAMING_HELM_CHART_GFN_PREFIX], indirect=True
    )
    def test_scale_up_test_for_streaming_function_helm_chart_GFN(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
        func_setup_for_deployed_tests,
        streaming_deploy_info_gfn,
        func_scale_teardown,
    ):
        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)
        func_id, vers_id = func_setup_for_deployed_tests
        NVCF_Util.scale_mofification_with_deploy_data(
            func_id,
            vers_id,
            streaming_deploy_info_gfn,
            min_instance=self.SCALE_UP_COUNT,
            max_instance=self.SCALE_UP_COUNT,
        )
        assert NVCF_Util.wait_until_active_instance_updated(
            func_id, vers_id, expected_count=self.SCALE_UP_COUNT
        ), "Wait time over but instance count not changed as expected."

    @pytest.mark.CloudFunctions
    @pytest.mark.streaming_test
    @pytest.mark.skip("Currently Helm Chart non GFN is not supported.")
    @pytest.mark.T3948025
    @pytest.mark.parametrize(
        "func_setup_for_deployed_tests",
        [
            (
                StreamingAliasEnum.streaming_helm_chart_non_gfn.value,
                STREAMING_HELM_CHART_NON_GFN_PREFIX,
            )
        ],
        indirect=True,
    )
    @pytest.mark.parametrize(
        "func_scale_teardown", [STREAMING_HELM_CHART_NON_GFN_PREFIX], indirect=True
    )
    def test_scale_up_test_for_streaming_function_helm_chart_non_GFN(
        self,
        session_nvcf_admin_sak: dict[str, APISession],
        func_setup_for_deployed_tests,
        streaming_deploy_info_cluster,
        func_scale_teardown,
    ):
        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)
        func_id, vers_id = func_setup_for_deployed_tests
        NVCF_Util.scale_mofification_with_deploy_data(
            func_id,
            vers_id,
            streaming_deploy_info_cluster,
            min_instance=self.SCALE_UP_COUNT,
            max_instance=self.SCALE_UP_COUNT,
        )
        assert NVCF_Util.wait_until_active_instance_updated(
            func_id, vers_id, expected_count=self.SCALE_UP_COUNT
        ), "Wait time over but instance count not changed as expected."

    @pytest.mark.CloudFunctions
    @pytest.mark.T4066370
    @pytest.mark.streaming_invoke
    def test_single_invoke_a_streaming_function(
        self,
        nvcf_ibeta_streaming_client: Page,
        setup_console_logging,
    ):
        client_page = nvcf_ibeta_streaming_client
        assert self.start_streaming_invoke(
            client_page, time_out=300
        ), "The streaming invoke start failed."

    @pytest.mark.CloudFunctions
    @pytest.mark.cluster_validation
    @pytest.mark.L0
    @pytest.mark.lls_container
    @pytest.mark.T5196146
    def test_low_latency_streaming_function_container_cluster_validation(
        self,
        nvcf_ibeta_streaming_client: Page,
        setup_console_logging,
        func_setup_for_deployed_tests,
        func_teardown,
    ):
        client_page = nvcf_ibeta_streaming_client
        assert self.start_streaming_invoke(
            client_page
        ), "The streaming invoke start failed."
        assert self.check_streaming_result(
            client_page, STREAMING_CONTAINER_NON_GFN_PREFIX
        ), "The streaming check failed."

    @pytest.mark.CloudFunctions
    @pytest.mark.cluster_validation
    @pytest.mark.L0
    @pytest.mark.lls_helm_chart
    @pytest.mark.T5196147
    @pytest.mark.skip(
        "LLS with helm chart function is not supported in non GFN environment."
    )
    def test_low_latency_streaming_function_helm_chart_cluster_validation(
        self,
        nvcf_ibeta_streaming_client: Page,
        setup_console_logging,
        func_setup_for_deployed_tests,
        func_teardown,
    ):
        client_page = nvcf_ibeta_streaming_client
        assert self.start_streaming_invoke(
            client_page
        ), "The streaming invoke start failed."
        assert self.check_streaming_result(
            client_page, STREAMING_HELM_CHART_NON_GFN_PREFIX
        ), "The streaming check failed."

    @pytest.mark.CloudFunctions
    @pytest.mark.T5238100
    @pytest.mark.nvcf_worker_lls_dev_nightly
    @pytest.mark.parametrize(
        "func_setup_for_deployed_tests",
        [
            (
                StreamingAliasEnum.streaming_container_non_gfn.value,
                STREAMING_E2E_TEST_PREFIX,
            )
        ],
        indirect=True,
    )
    def test_E2E_test_low_latency_streaming_container_non_GFN(
        self,
        nvcf_ibeta_streaming_client: Page,
        setup_console_logging,
        func_setup_for_deployed_tests,
        func_teardown,
    ):
        logging.info("Invoke the LLS E2E test function")
        client_page = nvcf_ibeta_streaming_client
        assert self.start_streaming_invoke(
            client_page, time_out=300
        ), "The streaming invoke start failed."
        logging.info("Check the streaming result")
        assert self.check_streaming_result(
            client_page, STREAMING_CONTAINER_NON_GFN_PREFIX
        ), "The streaming check failed."

        logging.info("Clean up the LLS E2E test function")
        func_id, vers_id = func_setup_for_deployed_tests
        added_function_info = {
            "func_id": func_id,
            "version_id": vers_id,
        }
        self.func_remove_list.append(added_function_info)

    @pytest.mark.CloudFunctions
    @pytest.mark.T5289041
    @pytest.mark.nvcf_worker_lls_dev
    @pytest.mark.nvcf_worker_lls_dev_nightly
    @pytest.mark.parametrize(
        "func_setup_for_deployed_tests",
        [
            (
                StreamingAliasEnum.streaming_helm_chart_gfn.value,
                STREAMING_E2E_TEST_PREFIX,
            )
        ],
        indirect=True,
    )
    def test_E2E_test_low_latency_streaming_helm_chart_GFN(
        self,
        nvcf_ibeta_streaming_client: Page,
        setup_console_logging,
        func_setup_for_deployed_tests,
        func_teardown,
    ):
        logging.info("Invoke the LLS E2E test function")
        client_page = nvcf_ibeta_streaming_client
        assert self.start_streaming_invoke(
            client_page, time_out=300
        ), "The streaming invoke start failed."

        logging.info("Check the streaming result")
        assert self.check_streaming_result(
            client_page, STREAMING_CONTAINER_GFN_PREFIX
        ), "The streaming check failed."

        logging.info("Clean up the LLS E2E test function")
        func_id, vers_id = func_setup_for_deployed_tests
        added_function_info = {
            "func_id": func_id,
            "version_id": vers_id,
        }
        self.func_remove_list.append(added_function_info)
