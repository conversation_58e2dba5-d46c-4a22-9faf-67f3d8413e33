import pytest
import logging
import re

from playwright.sync_api import Page
from pages.Deployments.DeploymentsCreatePage import DeploymentsCreatePage
from pages.Deployments.DeploymentsListPage import DeploymentsListPage
from pages.Deployments.DeploymentsEditPage import DeploymentsEditPage
from pages.Functions.FunctionsListPage import FunctionListPage
from pages.Functions.FunctionVerDetailPage import FunctionVerDetailPage
from pages.Functions.FunctionDetailPage import FunctionDetailPage
from config.consts import TEST_DATA_CF, CURRENT_ENV, FUNCTION_INVOCATION_DATA
from cloudia.utils.backend_service.nvcf.nvcf_utils import NVCFUtils
from cloudia.utils.tools import Tools
from utils.common.tools import add_timestamp


class TestDeployments:
    NVCF_ADMIN_USER = ["org_admin-nvcf_admin"]
    NVCF_USER_USESR = ["org_user-nvcf_user"]
    NVCF_VIEWER_USER = ["org_user-nvcf_viewer"]
    NVCF_PROD_ADMIN = ["nvcf_admin"]

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3828090
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_deploy_container_function_with_default_instance_type_settings_and_default_instance_configuration_settings_without_BYOC(
        self, new_container_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page.deploy_function_version(
            get_user_page,
            new_container_function[0],
            new_container_function[1],
            wait_until_deployed=False,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3828095
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_deploy_container_function_with_instance_configuration_settings_with_BYOC(
        self, new_container_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        if CURRENT_ENV == "staging":
            test_data = TEST_DATA_CF[
                "deployment_with_instance_and_deployment_spec_byoc_stg"
            ]
        else:
            test_data = TEST_DATA_CF["deployment_with_instance_and_deployment_spec_byoc"]
        deployments_create_page.deploy_function_version(
            get_user_page,
            new_container_function[0],
            new_container_function[1],
            **test_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3828093
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_deploy_container_function_with_default_instance_configuration_settings_with_BYOC(
        self, new_container_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        if CURRENT_ENV == "staging":
            deployments_create_page.deploy_function_version(
                get_user_page,
                func_id=new_container_function[0],
                func_vers_id=new_container_function[1],
                wait_until_deployed=False,
                gpu_list=TEST_DATA_CF["deployment_byoc_stg"]["gpu_list"],
                instance_clusters_filter_list=TEST_DATA_CF["deployment_byoc_stg"][
                    "cluster_list"
                ],
                session_nvcf_admin_sak=session_nvcf_admin_sak,
            )
        else:
            deployments_create_page.deploy_function_version(
                get_user_page,
                func_id=new_container_function[0],
                func_vers_id=new_container_function[1],
                wait_until_deployed=False,
                gpu_list=TEST_DATA_CF["deployment_byoc"]["gpu_list"],
                instance_clusters_filter_list=TEST_DATA_CF["deployment_byoc"][
                    "cluster_list"
                ],
                session_nvcf_admin_sak=session_nvcf_admin_sak,
            )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3828092
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_deploy_container_function_with_default_instance_configuration_settings_without_BYOC(
        self, new_container_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_with_instance_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_with_instance"]
        deployments_create_page.deploy_function_version(
            get_user_page,
            new_container_function[0],
            new_container_function[1],
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3828094
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_deploy_container_function_with_instance_configuration_settings_without_BYOC(
        self, new_container_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_with_instance_and_deployment_spec_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_with_instance_and_deployment_spec"]
        deployments_create_page.deploy_function_version(
            get_user_page,
            new_container_function[0],
            new_container_function[1],
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3828096
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_deploy_helm_chart_function_with_advanced_configuration_settings_without_BYOC(
        self, new_helm_chart_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_with_instance_and_deployment_spec_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_with_instance_and_deployment_spec"]
        deployments_create_page.deploy_function_version(
            get_user_page,
            new_helm_chart_function[0],
            new_helm_chart_function[1],
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3784038
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "staging",
        reason="Skip this test case because L40 is not available on staging",
    )
    def test_deploy_container_function_with_instance_types_dual_L40(
        self, new_gpu_comunication_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            new_gpu_comunication_function[0],
            new_gpu_comunication_function[1],
            **TEST_DATA_CF["deployment_with_dual_L40"],
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3784039
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_deploy_container_function_with_instance_types_dual_L40S(
        self, new_gpu_comunication_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            new_gpu_comunication_function[0],
            new_gpu_comunication_function[1],
            wait_until_deployed=False,
            **TEST_DATA_CF["deployment_with_dual_L40S"],
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3784040
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "staging",
        reason="Skip this test case because L40G is not available on staging",
    )
    def test_deploy_container_function_with_instance_types_dual_L40G_br20(
        self, new_gpu_comunication_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            new_gpu_comunication_function[0],
            new_gpu_comunication_function[1],
            **TEST_DATA_CF["deployment_with_dual_L40G_br20"],
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3910261
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "staging",
        reason="Skip this test case because L40G is not available on staging",
    )
    def test_deploy_container_function_with_instance_types_dual_L40G_br25(
        self, new_gpu_comunication_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            new_gpu_comunication_function[0],
            new_gpu_comunication_function[1],
            wait_until_deployed=False,
            **TEST_DATA_CF["deployment_with_dual_L40G_br25"],
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3765381
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_deploy_container_function_on_multiple_instances(
        self, new_container_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_with_multiple_instances_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_with_multiple_instances"]
        deployments_create_page.deploy_function_version(
            get_user_page,
            new_container_function[0],
            new_container_function[1],
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3828097
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_edit_configuration_during_deploying_function_without_BYOC(
        self, new_container_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page.deploy_function_version(
            get_user_page,
            new_container_function[0],
            new_container_function[1],
            wait_until_deployed=False,
            edit_configuration_in_review=True,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3712457
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_deploy_helm_chart_function_in_deployments_page(
        self, new_helm_chart_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page.deploy_function_version(
            get_user_page,
            new_helm_chart_function[0],
            new_helm_chart_function[1],
            wait_until_deployed=False,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3712459
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_deploy_helm_chart_function_with_helm_chart_overrides_in_deployments_page(
        self, new_helm_chart_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page.deploy_function_version(
            get_user_page,
            new_helm_chart_function[0],
            new_helm_chart_function[1],
            helm_chart_overrides_data=TEST_DATA_CF["helm_chart_overrides_data"],
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3712460
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_review_deployment_configuration_with_helm_chart_overrides_in_deployments_page(
        self, new_helm_chart_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page.deploy_function_version(
            get_user_page,
            new_helm_chart_function[0],
            new_helm_chart_function[1],
            wait_until_deployed=False,
            edit_configuration_in_review=True,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3828099
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_verify_inactive_function_version_shows_in_deploy_function_page(
        self, new_container_function, get_user_page: Page
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        assert deployments_create_page.check_if_function_listed_in_deployment_candidate(
            new_container_function[0],
            new_container_function[1],
        ), "The function ID or function version ID not found in candidate"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3828100
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_error_hint_should_display_when_setting_invalid_max_instances(
        self, new_container_function, get_user_page: Page
    ):
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        assert deployments_create_page.check_error_hint_when_beyond_max_instances(
            new_container_function[0],
            new_container_function[1],
            **deploy_data,
        ), "The error hint not found."

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3842323
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_nvcf_deployments_list_show_correctly_in_deployments_page(
        self, get_user_page: Page, session_nvcf_admin_sak
    ):
        session_dict = session_nvcf_admin_sak
        funcs_info = NVCFUtils(session_dict).get_functions_info()
        function_vers_id = funcs_info["pytriton_echo"]["versionId"]
        function_name = funcs_info["pytriton_echo"]["name"]
        deployments_list_page = DeploymentsListPage(get_user_page)
        deployments_list_page.navigate_to_page()
        deployments_list_page.DeploymentsListPageSearchBar.search(function_vers_id)
        current_page_data = (
            deployments_list_page.DeploymentsListPagePaginationTable.get_current_page_data()
        )
        logging.info(f"Current page data {current_page_data}")
        assert (
            function_name == current_page_data[0]["FunctionName"]
            and function_vers_id == current_page_data[0]["VersionID"]
        ), "The deployment data not correct."
        assert (
            deployments_list_page.check_no_unexpected_tab_in_page()
        ), "There is unexpected button in page."

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3842101
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_creating_service_key_in_deployments_page(
        self, test_data, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_list_page = DeploymentsListPage(get_user_page)
        deployments_list_page.navigate_to_page()
        key_name = add_timestamp("test_key", "%m%d%H%M%S")
        api_key = deployments_list_page.DeploymentsListPageMisc.generate_key_in_deployments_list_page(
            key_name, "1 hour"
        )
        logging.info(f"generated API Key: {api_key}")
        assert api_key is not None, "API Key is not generated."
        NVCF_Util = NVCFUtils(session_nvcf_admin_sak)
        invoke_data = Tools.load_case_data(
            FUNCTION_INVOCATION_DATA, "INVOKE_FUNCTION_PROXYCACHE_HIT"
        )
        logging.info(f"invoke_data: {invoke_data}")
        invoke_data["req"]["headers"] = {"Authorization": f"Bearer {api_key}"}
        if CURRENT_ENV == "staging":
            func_info = test_data["fastapi_echo_function_stg"]
        else:
            func_info = test_data["fastapi_echo_function"]
        resp = NVCF_Util.invoke_function(
            func_info["func_id"], func_info["func_vers_id"], invoke_data
        )
        assert (
            resp.status_code == invoke_data["exp"]["status_code"]
        ), "The invoke result is not correct."

        # delete the service key
        deployments_list_page.DeploymentsListPageMisc.delete_key_in_deployments_list_page(
            key_name
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3862642
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_edit_deployment_for_an_active_container_function_in_deployment_page(
        self, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_edit_page = DeploymentsEditPage(get_user_page)
        container_func_info = NVCFUtils(session_nvcf_admin_sak).get_functions_info()
        func_id = container_func_info["fastapi_asset"]["id"]
        vers_id = container_func_info["fastapi_asset"]["versionId"]

        # Navigate to the edit deployment page
        deployments_edit_page.navigate_to_page("deployments_entry", func_id, vers_id)

        # Check the displayed infomation in deployment edit page correct
        assert deployments_edit_page.check_edit_deployment_page_display(
            func_id, vers_id, session_nvcf_admin_sak
        ), "Deployment info check failed."

        # Check the error hint message when min instance larger than max
        assert (
            deployments_edit_page.check_error_min_instance_larger_than_max_instance()
        ), "There is no error hint message when min instance larger than max."

        # Check the error hint message when max instance number exceed limitaion
        assert (
            deployments_edit_page.check_error_max_instance_exceed_limitation()
        ), "There is no error hint message when max instance number exceed the limitation."

        # Modify the first instance max concurrency number
        deployments_edit_page.modify_first_instance_max_concurrency()

        # Save the modification, and check the modificaction result
        assert (
            deployments_edit_page.save_edit_max_concurrency_changes_and_check()
        ), "The check after edit deployment failed."

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3862657
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_edit_deployment_for_an_active_helm_chart_function_in_deployment_page(
        self, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_edit_page = DeploymentsEditPage(get_user_page)
        container_func_info = NVCFUtils(session_nvcf_admin_sak).get_functions_info()
        func_id = container_func_info["inference_helm_chart"]["id"]
        vers_id = container_func_info["inference_helm_chart"]["versionId"]

        # Navigate to the edit deployment page
        deployments_edit_page.navigate_to_page("deployments_entry", func_id, vers_id)

        # Check the displayed infomation in deployment edit page correct
        assert deployments_edit_page.check_edit_deployment_page_display(
            func_id, vers_id, session_nvcf_admin_sak
        ), "Deployment info check failed."

        # Check the error hint message when min instance larger than max
        assert (
            deployments_edit_page.check_error_min_instance_larger_than_max_instance()
        ), "There is no error hint message when min instance larger than max."

        # Check the error hint message when max instance number exceed limitaion
        assert (
            deployments_edit_page.check_error_max_instance_exceed_limitation()
        ), "There is no error hint message when max instance number exceed the limitation."

        # Modify the first instance max concurrency number
        deployments_edit_page.modify_first_instance_max_concurrency()

        # Save the modification, and check the modificaction result
        assert (
            deployments_edit_page.save_edit_max_concurrency_changes_and_check()
        ), "The check after edit deployment failed."

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3862660
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_edit_deployment_for_an_active_container_function_in_function_version_details_page(
        self, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_edit_page = DeploymentsEditPage(get_user_page)
        container_func_info = NVCFUtils(session_nvcf_admin_sak).get_functions_info()
        func_id = container_func_info["fastapi_asset"]["id"]
        vers_id = container_func_info["fastapi_asset"]["versionId"]

        # Navigate to the edit deployment page
        deployments_edit_page.navigate_to_page("function_version_entry", func_id, vers_id)

        # Check the displayed infomation in deployment edit page correct
        assert deployments_edit_page.check_edit_deployment_page_display(
            func_id, vers_id, session_nvcf_admin_sak
        ), "Deployment info check failed."

        # Check the error hint message when min instance larger than max
        assert (
            deployments_edit_page.check_error_min_instance_larger_than_max_instance()
        ), "There is no error hint message when min instance larger than max."

        # Check the error hint message when max instance number exceed limitaion
        assert (
            deployments_edit_page.check_error_max_instance_exceed_limitation()
        ), "There is no error hint message when max instance number exceed the limitation."

        # Modify the first instance max concurrency number
        deployments_edit_page.modify_first_instance_max_concurrency()

        # Save the modification, and check the modificaction result
        assert (
            deployments_edit_page.save_edit_max_concurrency_changes_and_check()
        ), "The check after edit deployment failed."

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3862661
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_edit_deployment_for_an_active_helm_chart_function_in_function_version_details_page(
        self, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_edit_page = DeploymentsEditPage(get_user_page)
        container_func_info = NVCFUtils(session_nvcf_admin_sak).get_functions_info()
        func_id = container_func_info["inference_helm_chart"]["id"]
        vers_id = container_func_info["inference_helm_chart"]["versionId"]

        # Navigate to the edit deployment page
        deployments_edit_page.navigate_to_page("function_version_entry", func_id, vers_id)

        # Check the displayed infomation in deployment edit page correct
        assert deployments_edit_page.check_edit_deployment_page_display(
            func_id, vers_id, session_nvcf_admin_sak
        ), "Deployment info check failed."

        # Check the error hint message when min instance larger than max
        assert (
            deployments_edit_page.check_error_min_instance_larger_than_max_instance()
        ), "There is no error hint message when min instance larger than max."

        # Check the error hint message when max instance number exceed limitaion
        assert (
            deployments_edit_page.check_error_max_instance_exceed_limitation()
        ), "There is no error hint message when max instance number exceed the limitation."

        # Modify the first instance max concurrency number
        deployments_edit_page.modify_first_instance_max_concurrency()

        # Save the modification, and check the modificaction result
        assert (
            deployments_edit_page.save_edit_max_concurrency_changes_and_check()
        ), "The check after edit deployment failed."

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3712463
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_edit_deployment_for_an_active_helm_chart_function(
        self, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_edit_page = DeploymentsEditPage(get_user_page)
        container_func_info = NVCFUtils(session_nvcf_admin_sak).get_functions_info()
        func_id = container_func_info["inference_helm_chart"]["id"]
        vers_id = container_func_info["inference_helm_chart"]["versionId"]
        # Navigate to the edit deployment page
        deployments_edit_page.navigate_to_page("deployments_entry", func_id, vers_id)

        assert deployments_edit_page.check_edit_deployment_page_display(
            func_id, vers_id, session_nvcf_admin_sak
        ), "Deployment info check failed."
        deployments_edit_page.modify_first_instance_max_concurrency()
        # Save the modification, and check the modificaction result
        assert (
            deployments_edit_page.save_edit_max_concurrency_changes_and_check()
        ), "The check after edit deployment failed."

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3712464
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_disable_version_for_an_active_helm_chart_function(
        self, new_helm_chart_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        func_id = new_helm_chart_function[0]
        vers_id = new_helm_chart_function[1]

        deployments_create_page = DeploymentsCreatePage(get_user_page)
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            vers_id,
            wait_until_deployed=True,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

        # Disable function version for an Active helm chart function
        func_page = FunctionListPage(get_user_page)
        func_page.navigate_to_page()
        func_page.FunctionPagination.disable_function_version_helm_chart(func_id)

        # Deploy the inactive helm chart function for next test
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            vers_id,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3712456
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_deploy_helm_chart_function_in_function_list_page(
        self,
        new_helm_chart_function,
        get_user_page: Page,
        session_nvcf_admin_sak,
    ):
        func_id = new_helm_chart_function[0]
        vers_id = new_helm_chart_function[1]
        # Deploy function for an helm chart function
        func_page = FunctionListPage(get_user_page)
        deploy_page = func_page.FunctionPagination.deploy_function_version_helm_chart(
            func_id
        )
        deploy_page = DeploymentsCreatePage(deploy_page)
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deploy_page.deploy_function_version(
            get_user_page,
            func_id,
            vers_id,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3765390
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_edit_deployment_in_function_version_details_page(
        self, get_user_page: Page, session_nvcf_admin_sak
    ):
        deployments_edit_page = DeploymentsEditPage(get_user_page)
        container_func_info = NVCFUtils(session_nvcf_admin_sak).get_functions_info()
        func_id = container_func_info["fastapi_asset"]["id"]
        vers_id = container_func_info["fastapi_asset"]["versionId"]
        func_name = container_func_info["fastapi_asset"]["name"]
        deployments_edit_page.navigate_to_page(
            navigate_entry_path="function_version_entry",
            func_id=func_id,
            vers_id=vers_id,
        )

        deployments_edit_page.modify_deployment_cancel_changes_and_check()
        func_version_detail_page = FunctionVerDetailPage(
            page=get_user_page, func_id=func_id, vers_id=vers_id, func_name=func_name
        )
        func_version_detail_page.switch_tab(
            tab_name="Deployments Details",
            func_name=func_name,
            func_id=func_id,
            vers_id=vers_id,
        )
        assert (
            int(func_version_detail_page.get_max_concurrency()) == 1
        ), "The max concurrency is not 1"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3884709
    @pytest.mark.parametrize(
        "pre_check_available_function_version",
        ["disable_pytriton_echo_sample_container"],
        indirect=True,
    )
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_disable_function_version_for_an_active_container_function(
        self,
        pre_check_available_function_version,
        get_user_page: Page,
        session_nvcf_admin_sak,
    ):
        try:
            func_id = pre_check_available_function_version["id"]
            vers_id = pre_check_available_function_version["versionId"]
            logging.info(f"fun_id is {func_id}, vers_id is {vers_id}")
        except Exception as e:
            pytest.skip(
                f"Required function disable_pytriton_echo_sample_container not found: {e}"
            )

        func_page = FunctionListPage(get_user_page)
        func_page.FunctionPagination.action_to(uuid=func_id, entry="Disable Version")
        deploy_spec = (
            TEST_DATA_CF["deployment_min"]
            if CURRENT_ENV in ["production", "canary"]
            else TEST_DATA_CF["deployment_with_L40G_br25"]
        )

        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            vers_id,
            **deploy_spec,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3862268
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_deploy_container_function_with_secrets_GFN(
        self, new_ess_container_function, get_user_page: Page, session_nvcf_admin_sak
    ):
        func_id, func_vers_id = new_ess_container_function
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        if CURRENT_ENV == "staging":
            deploy_data = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_data = TEST_DATA_CF["deployment_min"]
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_vers_id,
            **deploy_data,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3862277
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_add_secret_to_deploying_function_negative(
        self, new_container_function_and_deploying, get_user_page: Page
    ):
        func_id, _ = new_container_function_and_deploying

        # Step 1, Enter [Functions List] Page
        func_page = FunctionListPage(get_user_page)

        # Step 2, Click 3 dots button for a deploying function, and then choose [Manage Secrets]
        dialog_page = func_page.FunctionPagination.action_to(
            uuid=func_id, entry="Manage Secrets"
        )

        # Step 3, Check if the specified text exists in the dialog
        expected_message = """Secrets cannot be added or updated while a function version is in a deploying state.\n
To manage secrets, please ensure your function version is in an inactive state."""
        assert dialog_page.get_by_text(
            expected_message
        ).is_visible, "The secrets reminder was not found in the dialog."

        # Step 4, Click Okay to close the dialog
        dialog_page.get_by_role("button", name=re.compile("Okay", re.IGNORECASE)).click()

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3862306
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_add_secret_to_deploying_function_func_detail_page_negative(
        self, new_container_function_and_deploying, get_user_page: Page
    ):
        func_id, func_vers_id = new_container_function_and_deploying

        # Step 1, Enter [Deployments] Page
        deployments_list_page = DeploymentsListPage(get_user_page)
        deployments_list_page.navigate_to_page()
        deployments_list_page.wait_deployment_to_expected_status(func_vers_id, "Deploying")

        # Step 2, Click 3 dots button for a deploying function, and choose [View Function Details]
        page = deployments_list_page.DeploymentsListPagePaginationTable.action_to(
            uuid=func_vers_id, entry="View Function Details"
        )

        # Step 3, Click 3 dots button for the deploying version and choose [Manage Secrets]
        func_detail_page = FunctionDetailPage(page)
        func_detail_page.wait_for_page_ready()
        dialog_page = func_detail_page.FunctionPagination.action_to(
            uuid=func_vers_id, entry="Manage Secrets"
        )

        # Step 4, Check if the specified text exists in the dialog
        expected_message = """Secrets cannot be added or updated while a function version is in a deploying state.\n
To manage secrets, please ensure your function version is in an inactive state."""
        assert dialog_page.get_by_text(
            expected_message
        ).is_visible, "The secrets reminder was not found in the dialog."

        # Step 5, Click Okay to close the dialog
        dialog_page.get_by_role("button", name=re.compile("Okay", re.IGNORECASE)).click()

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3862309
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_add_secret_to_deploying_function_func_version_detail_page_negative(
        self, new_container_function_and_deploying, get_user_page: Page
    ):
        _, func_vers_id = new_container_function_and_deploying

        # Step 1, Enter [Deployments] Page
        deployments_list_page = DeploymentsListPage(get_user_page)
        deployments_list_page.navigate_to_page()
        deployments_list_page.wait_deployment_to_expected_status(func_vers_id, "Deploying")

        # Step 2, Click 3 dots button for a deploying function, and choose [View Function Details]
        page = deployments_list_page.DeploymentsListPagePaginationTable.action_to(
            uuid=func_vers_id, entry="View Version Details"
        )

        # Step 3, Click 3 dots button for the deploying version and choose [Manage Secrets]
        func_vers_detail_page = FunctionVerDetailPage(page)
        func_vers_detail_page.wait_for_page_ready()
        func_vers_detail_page.ActionMenu.choose(entry_name="Manage Secrets")

        # Step 4, Check if the specified text exists in the dialog
        expected_message = """Secrets cannot be added or updated while a function version is in a deploying state.\n
To manage secrets, please ensure your function version is in an inactive state."""
        assert func_vers_detail_page.page.get_by_text(
            expected_message
        ).is_visible, "The secrets reminder was not found in the dialog."

        # Step 5, Click Okay to close the dialog
        func_vers_detail_page.page.get_by_role(
            "button", name=re.compile("Okay", re.IGNORECASE)
        ).click()

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3712458
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_deploy_helm_chart_function_with_overrides_in_func_list_page(
        self,
        new_helm_chart_function,
        get_user_page: Page,
        session_nvcf_admin_sak,
    ):
        if CURRENT_ENV == "staging":
            deploy_spec = TEST_DATA_CF["deployment_min_stg"]
        else:
            deploy_spec = TEST_DATA_CF["deployment_min"]
        logging.info(f"Deployment spec:\n {deploy_spec}")
        # Step 1, [Function List] page, select an Inactive Helm Chart function, then click [Deploy Function] from its 3 dots menu
        func_id, func_vers_id = new_helm_chart_function
        func_page = FunctionListPage(get_user_page)
        func_page.FunctionPagination.wait_function_list_ready()
        current_page = func_page.FunctionPagination.action_to(
            uuid=func_id, entry="Deploy Version"
        )

        # Step 2, [Deploy Function] modal, fill in all required fields, then expand [Helm Chart Overrides (Optional)]
        # In [Helm Chart Overrides (Optional)] section, input valid JSON content and deploy the function
        deployments_create_page = DeploymentsCreatePage(current_page)
        deployments_create_page.deploy_function_version(
            current_page,
            func_id,
            func_vers_id,
            wait_until_deployed=True,
            helm_chart_overrides_data=TEST_DATA_CF["helm_chart_overrides_data"],
            **deploy_spec,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3877628
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_all_regions_attributes_shown_for_selected_instance_type_without_filter(
        self, new_container_function, get_user_page: Page
    ):
        func_id, func_vers_id = new_container_function
        if CURRENT_ENV == "staging":
            deploy_spec = TEST_DATA_CF["deployment_with_instance_and_deployment_spec_stg"]
        else:
            deploy_spec = TEST_DATA_CF["deployment_with_instance_and_deployment_spec"]
        logging.info(f"Deployment spec:\n {deploy_spec}")

        # Step 1, In [Deployments] page, click [Deploy Function] on the top right side of the page.
        deploy_list_page = DeploymentsListPage(get_user_page)
        deploy_list_page.navigate_to_page()
        deploy_list_page.page.get_by_role("button", name="Deploy Version").click()
        deploy_list_page.page.wait_for_load_state()
        deploy_list_page.page.wait_for_url("https://nvcf.*nvidia.com/deployments/create")

        # Step 2, [Deploy Function] page should show.select Function Name and Function Version under [Function Details].

        deployments_create_page = DeploymentsCreatePage(deploy_list_page.page)
        if CURRENT_ENV == "staging":
            func_name_input_element = "//label[text()='Function Name' or text()='Function ID']/../following-sibling::div/..//input"
            func_vers_input_element = (
                "//label[text()='Function Version']/../following-sibling::div/..//input"
            )
        else:
            func_name_input_element = deployments_create_page.FunctionNameSelector.elements[
                "FuncNameInput"
            ]
            func_vers_input_element = (
                deployments_create_page.FunctionVersionSelector.elements["FuncVersionInput"]
            )
        deployments_create_page.FunctionNameSelector.search_and_select(
            func_name_input_element, func_id
        )

        deployments_create_page.FunctionNameSelector.search_and_select(
            func_vers_input_element, func_vers_id
        )

        # Step 3, Select GPU types
        for item in deploy_spec["gpu_list"]:
            deployments_create_page.page.locator(f"input[value='{item}']").check()

        # Step 4. Click [Instance Type Settings], "Any region" should be shown as the default value for [Region].
        # "Any attribute" should be shown as the default value for [Attributes]. Keep them default.

        # deployments_create_page.page.wait_for_load_state("load")
        # if (
        #     deployments_create_page.page.get_by_role(
        #         "button", name="Instance Type Settings"
        #     ).get_attribute("aria-expanded")
        #     == "false"
        # ):
        #     deployments_create_page.page.get_by_role(
        #         "button", name="Instance Type Settings"
        #     ).click()

        assert (
            deployments_create_page.page.locator("#react-select-4-placeholder").inner_text()
            == "Any region"
        ), 'The default value for Regions is not "Any region".'
        assert (
            deployments_create_page.page.locator("#react-select-6-placeholder").inner_text()
            == "Any attributes"
        ), 'The default value for Attributes is not "Any attributes".'

        # Step 5, Select the wanted instance types
        deployments_create_page.page.get_by_role(
            "row",
            name=re.compile(
                f"{deploy_spec['instance_detail_list'][0]['name']}", re.IGNORECASE
            ),
        ).get_by_test_id("kui-checkbox").check()

        # # Step 6, Click [Deployment Specifications], check that all the regions and attributes of the selected instance type should show in the drop-down list of [Target Regions] and [Attributes].
        # # Check they can be selected.
        # if (
        #     deployments_create_page.page.get_by_role(
        #         "button", name="Deployment Specifications"
        #     ).get_attribute("aria-expanded")
        #     == "false"
        # ):
        #     deployments_create_page.page.get_by_role(
        #         "button", name="Deployment Specifications"
        #     ).click()
        deployments_create_page.page.wait_for_load_state()

        deployments_create_page.page.get_by_text("Target RegionsAny region").nth(0).click()
        expected_regions = deployments_create_page.page.locator(
            "div[role='option']"
        ).all_inner_texts()

        deployments_create_page.page.get_by_text("Target RegionsAny region").click()
        listed_regions = deployments_create_page.page.locator(
            "div[role='option']"
        ).all_inner_texts()
        assert (
            listed_regions == expected_regions
        ), f"Expected regions {expected_regions} but listed regions {listed_regions}"
        for region in listed_regions:
            deployments_create_page.page.get_by_role("option", name=region).click()

        deployments_create_page.page.locator("div").filter(
            has_text=re.compile(r"^Attributes")
        ).nth(0).locator("svg").click()
        expected_attributes = deployments_create_page.page.locator(
            "div[role='option']"
        ).all_inner_texts()
        deployments_create_page.page.locator("label").get_by_text("Attributes").nth(
            1
        ).locator("../..").locator("svg").click()
        listed_attributes = deployments_create_page.page.locator(
            "div[role='option']"
        ).all_inner_texts()
        assert (
            listed_attributes == expected_attributes
        ), f"Expected attributes {expected_attributes} but listed attributes {listed_attributes}"
        for attribute in listed_attributes:
            deployments_create_page.page.get_by_role("option", name=attribute).click()
        deployments_create_page.page.get_by_role("button", name="Cancel")

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T3877627
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    @pytest.mark.skipif(
        CURRENT_ENV == "staging",
        reason="Skip this test case because L40 is not available on staging",
    )
    def test_all_regions_attributes_shown_for_selected_instance_type_with_filter(
        self, new_container_function, get_user_page: Page
    ):
        func_id, func_vers_id = new_container_function
        deploy_spec = TEST_DATA_CF["deployment_L40_with_filter_spec"]
        logging.info(f"Deployment spec:\n {deploy_spec}")

        # Step 1, In [Deployments] page, click [Deploy Function] on the top right side of the page.
        deploy_list_page = DeploymentsListPage(get_user_page)
        deploy_list_page.navigate_to_page()
        deploy_list_page.page.get_by_role("button", name="Deploy Version").click()
        deploy_list_page.page.wait_for_load_state()
        deploy_list_page.page.wait_for_url("https://nvcf.*nvidia.com/deployments/create")

        # Step 2, [Deploy Function] page should show.select Function Name and Function Version under [Function Details].
        deployments_create_page = DeploymentsCreatePage(deploy_list_page.page)
        if CURRENT_ENV == "staging":
            func_name_input_element = "//label[text()='Function Name' or text()='Function ID']/../following-sibling::div/..//input"
            func_vers_input_element = (
                "//label[text()='Function Version']/../following-sibling::div/..//input"
            )
        else:
            func_name_input_element = deployments_create_page.FunctionNameSelector.elements[
                "FuncNameInput"
            ]
            func_vers_input_element = (
                deployments_create_page.FunctionVersionSelector.elements["FuncVersionInput"]
            )
        deployments_create_page.FunctionNameSelector.search_and_select(
            func_name_input_element, func_id
        )

        deployments_create_page.FunctionNameSelector.search_and_select(
            func_vers_input_element, func_vers_id
        )

        # Step 3, Select GPU types
        for item in deploy_spec["gpu_list"]:
            deployments_create_page.page.locator(f"input[value='{item}']").check()

        # Step 4. Click [Instance Type Settings], "Any region" should be shown as the default value for [Region].
        # "Any attribute" should be shown as the default value for [Attributes]. Keep them default.
        deployments_create_page.page.wait_for_load_state("load")
        deployments_create_page.page.wait_for_timeout(2000)

        deployments_create_page.page.get_by_text("Target RegionsAny region").first.click()
        full_regions = deployments_create_page.page.locator(
            "div[role='option']"
        ).all_inner_texts()
        for region in deploy_spec["instance_regions_filter_list"]:
            deployments_create_page.page.get_by_role("option", name=region).click()

        deployments_create_page.page.wait_for_timeout(2000)
        deployments_create_page.page.get_by_text("AttributesAny attributes").first.click()
        deployments_create_page.page.wait_for_load_state()
        full_attributes = deployments_create_page.page.locator(
            "div[role='option']"
        ).all_inner_texts()
        for attribute in deploy_spec["instance_attributes_filter_list"]:
            deployments_create_page.page.get_by_role("option", name=attribute).click()

        # Step 5, Select the wanted instance types
        deployments_create_page.page.get_by_role(
            "row",
            name=re.compile(
                f"{deploy_spec['instance_detail_list'][0]['name']}", re.IGNORECASE
            ),
        ).get_by_test_id("kui-checkbox").check()

        # Step 6, All the regions and attributes of the selected instance type should show in the drop-down list of [Target Regions] and [Attributes]
        # even if they are filtered in Instance Type Settings. Check they can be selected.

        deployments_create_page.page.wait_for_load_state()

        target_regions_container = (
            deployments_create_page.page.get_by_text("Target Regions")
            .nth(1)
            .locator("../..")
        )
        svg_elements = target_regions_container.locator("svg")
        svg_count = svg_elements.count()
        if svg_count == 1:
            svg_elements.click()
        else:
            svg_elements.last.click()
        deployments_create_page.page.wait_for_load_state()
        listed_regions = deployments_create_page.page.locator(
            "div[role='option']"
        ).all_inner_texts()
        # assert listed_regions should be a subset of full_regions
        assert all(
            [region in full_regions for region in listed_regions]
        ), f"{listed_regions} is not a subset of {full_regions}"
        assert (
            len(listed_regions) >= 2
        ), f"the regions of selected instance types {listed_regions} is less than 2"
        for region in listed_regions:
            deployments_create_page.page.get_by_role("option", name=region).click()

        deployments_create_page.page.get_by_text("Target Regions").nth(1).locator(
            "../.."
        ).locator("svg").last.click()
        deployments_create_page.page.get_by_text("Attributes").nth(1).locator(
            "../.."
        ).locator("svg").last.click()
        listed_attributes = deployments_create_page.page.locator(
            "div[role='option']"
        ).all_inner_texts()
        assert (
            listed_attributes == full_attributes
        ), f"Expected attributes {full_attributes} but listed attributes {listed_attributes}"
        for attribute in listed_attributes:
            deployments_create_page.page.get_by_role("option", name=attribute).click()
        deployments_create_page.page.get_by_role("button", name="Cancel")

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5072075
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_deploy_configuration_in_deployments_page(
        self,
        get_user_page: Page,
    ):
        deployments_list_page = DeploymentsListPage(get_user_page)
        deployments_list_page.navigate_to_page()
        deployments_list_page.page.get_by_role("button", name="Deploy Version").click()
        deployments_list_page.page.wait_for_load_state()
        deployments_list_page.page.wait_for_url(
            "https://nvcf.*nvidia.com/deployments/create"
        )

        # Step 2, [Deploy Function] page should show.
        deployments_create_page = DeploymentsCreatePage(deployments_list_page.page)
        deployments_create_page.page.wait_for_timeout(10000)
        assert deployments_create_page.page.locator(
            "xpath=//*[normalize-space(text())='Function Details']"
        ).is_visible(), "Function Details section not found"

        assert deployments_create_page.page.locator(
            "xpath=//*[normalize-space(text())='Deployment Configuration']"
        ).is_visible(), "Deployment Configuration section not found"

        assert deployments_create_page.page.locator(
            "xpath=//*[normalize-space(text())='Function ID']"
        ).is_visible(), "Function ID not found"

        assert deployments_create_page.page.locator(
            "xpath=//*[normalize-space(text())='Function Version']"
        ).is_visible(), "Function Version not found"

        assert deployments_create_page.page.locator(
            "xpath=//*[normalize-space(text())='GPU & Instance Types']"
        ).is_visible(), "GPU&Instance Types not found"

        assert deployments_create_page.page.locator(
            "xpath=//*[normalize-space(text())='Selected Instance Type Settings']"
        ).is_visible(), "Selected Instance Type Settings not found"

        assert deployments_create_page.page.locator(
            "xpath=//*[normalize-space(text())='Select a GPU and one or more instance types to deploy your function version.']"
        ).is_visible(), "GPU&Instance Types description not found"

        assert deployments_create_page.page.locator(
            "xpath=//*[normalize-space(text())='Specify regional restrictions, usage limits, minimum instances, and maximum concurrencies for each instance type selected above.']"
        ).is_visible(), "Selected Instance Type Settings description not found"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5072076
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_instance_type_settings_in_deployments_page(
        self, get_user_page: Page, new_container_function
    ):
        deployments_list_page = DeploymentsListPage(get_user_page)
        deployments_list_page.navigate_to_page()
        deployments_list_page.page.get_by_role("button", name="Deploy Version").click()
        deployments_list_page.page.wait_for_load_state()
        deployments_list_page.page.wait_for_url(
            "https://nvcf.*nvidia.com/deployments/create"
        )

        # Step 2, [Deploy Function] page should show.
        deployments_create_page = DeploymentsCreatePage(deployments_list_page.page)
        deployments_create_page.page.wait_for_timeout(10000)

        func_id, func_vers_id = new_container_function
        if CURRENT_ENV == "staging":
            func_name_input_element = "//label[text()='Function Name' or text()='Function ID']/../following-sibling::div/..//input"
            func_vers_input_element = (
                "//label[text()='Function Version']/../following-sibling::div/..//input"
            )
        else:
            func_name_input_element = deployments_create_page.FunctionNameSelector.elements[
                "FuncNameInput"
            ]
            func_vers_input_element = (
                deployments_create_page.FunctionVersionSelector.elements["FuncVersionInput"]
            )
        deployments_create_page.FunctionNameSelector.search_and_select(
            func_name_input_element, func_id
        )

        deployments_create_page.FunctionNameSelector.search_and_select(
            func_vers_input_element, func_vers_id
        )

        # Step 3, Select GPU types
        child_divs = deployments_create_page.page.locator(
            "xpath=//div[@data-testid='gpu-checkboxes']/div[@data-testid='kui-flex']"
        )
        for i in range(child_divs.count()):
            child_div = child_divs.nth(i)
            child_div.locator("input").check()

        # Step 4, Select regions under [Region] and attributes under [Attributes] to filter the wanted Instance types
        deployments_create_page.page.wait_for_load_state("load")
        deployments_create_page.page.locator("//div[text()='Any region']").first.click()

        deployments_create_page.page.locator("div[role='option']").first.click()
        if CURRENT_ENV != "staging":
            deployments_create_page.page.locator(
                "//div[text()='Any attributes']"
            ).first.click()
            deployments_create_page.page.locator("div[role='option']").first.click()

        # Step 5, multi-select the wanted instance types
        deployments_create_page.page.locator("//button[text()=' Refresh']").click()
        deployments_create_page.page.wait_for_selector(
            "//button[text()=' Refresh']", state="visible"
        )
        deployments_create_page.page.locator(
            "//th[@id='data-view-header-checkbox']"
        ).click()

        # Step 6, Check the selected instances have been shown below the [Selected Instance Type Settings],
        # and each instance has: [Target Regions], [Clusters], [Attributes], [Min Instances], [Max Instances],[Max Concurrency].
        instance_cards = deployments_create_page.page.locator(
            "div[data-testid='kui-flex'].border.rounded-md.p-4.gap-4.flex-1"
        )
        assert instance_cards.count() > 0, "No selected instance cards found!"

        for i in range(instance_cards.count()):
            card = instance_cards.nth(i)

            assert card.locator(
                "label[data-testid='kui-label']", has_text="Target Regions"
            ).is_visible(), f"Target Regions missing in card {i+1}"

            assert card.locator(
                "label[data-testid='kui-label']", has_text="Clusters"
            ).is_visible(), f"Clusters missing in card {i+1}"

            assert card.locator(
                "label[data-testid='kui-label']", has_text="Attributes"
            ).is_visible(), f"Attributes missing in card {i+1}"

            assert card.locator(
                "label[data-testid='kui-label']", has_text="Min Instances"
            ).is_visible(), f"Min Instances missing in card {i+1}"

            assert card.locator(
                "label[data-testid='kui-label']", has_text="Max Instances"
            ).is_visible(), f"Max Instances missing in card {i+1}"

            assert card.locator(
                "label[data-testid='kui-label']", has_text="Max Concurrency"
            ).is_visible(), f"Max Concurrency missing in card {i+1}"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5091256
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_check_the_target_regions_and_clusters_in_edit_deployment_page(
        self,
        new_container_function,
        get_user_page: Page,
    ):
        func_id, func_vers_id = new_container_function

        # Step 1, In [Deployments] page, click [Deploy Function] on the top right side of the page.
        deploy_list_page = DeploymentsListPage(get_user_page)
        deploy_list_page.navigate_to_page()
        deploy_list_page.page.get_by_role("button", name="Deploy Version").click()
        deploy_list_page.page.wait_for_load_state()
        deploy_list_page.page.wait_for_url("https://nvcf.*nvidia.com/deployments/create")

        # Step 2, [Deploy Function] page should show.select Function Name and Function Version under [Function Details].

        deployments_create_page = DeploymentsCreatePage(deploy_list_page.page)
        if CURRENT_ENV == "staging":
            func_name_input_element = "//label[text()='Function Name' or text()='Function ID']/../following-sibling::div/..//input"
            func_vers_input_element = (
                "//label[text()='Function Version']/../following-sibling::div/..//input"
            )
        else:
            func_name_input_element = deployments_create_page.FunctionNameSelector.elements[
                "FuncNameInput"
            ]
            func_vers_input_element = (
                deployments_create_page.FunctionVersionSelector.elements["FuncVersionInput"]
            )
        deployments_create_page.FunctionNameSelector.search_and_select(
            func_name_input_element, func_id
        )

        deployments_create_page.FunctionNameSelector.search_and_select(
            func_vers_input_element, func_vers_id
        )

        # Step 3, Select GPU types
        if CURRENT_ENV == "staging":
            deploy_spec = {
                "gpu_list": ["H100", "L40S"],
                "instance_name": ["OCI.GPU.H100_1x", "gl40s_1.br25_2xlarge"],
            }
        else:
            deploy_spec = {
                "gpu_list": ["H100", "T10"],
                "instance_name": ["OCI.GPU.H100_1x", "g6.full"],
            }
        for item in deploy_spec["gpu_list"]:
            deployments_create_page.page.locator(f"input[value='{item}']").check()

        # Step 4, Select the wanted instance types
        for instance_name in deploy_spec["instance_name"]:
            deployments_create_page.page.get_by_role(
                "row",
                name=re.compile(f"{instance_name}", re.IGNORECASE),
            ).get_by_test_id("kui-checkbox").check()

        # select all options
        settings_block = deployments_create_page.page.locator(
            "//span[contains(text(), 'Selected Instance Type Settings')]/parent::div/parent::div"
        )
        for instance in deploy_spec["instance_name"]:
            instance_block = settings_block.locator(
                f"//span[contains(text(),'{instance}')]/parent::div/parent::div"
            )
            # deployments_create_page.page.wait_for_timeout(5000)
            svg_elements = (
                instance_block.locator("label", has_text="Target Regions")
                .locator("../..")
                .locator("svg")
            )
            svg_count = svg_elements.count()
            if svg_count == 1:
                svg_elements.click()
            else:
                svg_elements.last.click()
            options = instance_block.locator("div[role='option']")
            count = options.count()
            for i in range(count):
                options.nth(i).click()

            if CURRENT_ENV != "staging":
                if instance != "g6.full":
                    svg_elements = (
                        instance_block.locator("label", has_text="Clusters")
                        .locator("../..")
                        .locator("svg")
                    )
                    svg_count = svg_elements.count()
                    if svg_count == 1:
                        svg_elements.click()
                    else:
                        svg_elements.last.click()
                    options = instance_block.locator("div[role='option']")
                    count = options.count()
                    for i in range(count):
                        options.nth(i).click()
            else:
                if instance != "gl40s_1.br25_2xlarge":
                    instance_block.locator("label", has_text="Clusters").locator(
                        "../.."
                    ).locator("svg").click()
                    options = instance_block.locator("div[role='option']")
                    count = options.count()
                    for i in range(count):
                        options.nth(i).click()
        # deploy version
        deployments_create_page.page.wait_for_timeout(5000)
        deployments_create_page.page.get_by_role("button", name="Deploy Version").click()
        deployments_create_page.page.locator(
            "//span[contains(text(), 'is being deployed')]"
        ).wait_for(state="visible")

        function_list_page = FunctionListPage(get_user_page)
        function_list_page.navigate_to_page()
        function_list_page.FunctionPagination.action_to(
            uuid=func_id, entry="Edit Deployment"
        )
        function_list_page.page.wait_for_load_state()

        # check Target Regions
        for instance in deploy_spec["instance_name"]:
            instance_block = function_list_page.page.locator(
                f"//span[contains(text(),'{instance}')]/parent::div/parent::div"
            )
            target_regions_span = instance_block.locator(
                "label", has_text="Target Regions"
            ).locator("xpath=following-sibling::span[1]")
            regions_text = target_regions_span.text_content().strip()
            regions_values = [v.strip() for v in regions_text.split(",")]
            if len(regions_values) > 1:
                assert (
                    "," in regions_text
                ), "Multiple Target Regions should be separated by commas"

            assert not regions_text.endswith("..."), "Target Regions value is truncated"

            scroll_width = target_regions_span.evaluate("el => el.scrollWidth")
            client_width = target_regions_span.evaluate("el => el.clientWidth")
            assert (
                scroll_width == client_width
            ), "Target Regions value is truncated horizontally"

            # check Clusters
            clusters_span = instance_block.locator("label", has_text="Clusters").locator(
                "xpath=following-sibling::span[1]"
            )
            clusters_text = clusters_span.text_content().strip()
            clusters_values = [v.strip() for v in clusters_text.split(",")]
            if len(clusters_values) > 1:
                assert (
                    "," in clusters_text
                ), "Multiple Clusters should be separated by commas"
            assert not clusters_text.endswith("..."), "Clusters value is truncated"
            scroll_width = clusters_span.evaluate("el => el.scrollWidth")
            client_width = clusters_span.evaluate("el => el.clientWidth")
            assert scroll_width == client_width, "Clusters value is truncated horizontally"
