import logging
import pytest
import json
from config.consts import (
    TEST_DATA_CF,
    FUNCTION_INVOCATION_DATA,
    CURRENT_ENV,
)
from playwright.sync_api import Page

from pages.Functions.FunctionVerDetailPage import FunctionVerDetailPage
from pages.CreateFunction.CreateFunctionPage import CreateFunctionPage
from utils.common.tools import add_timestamp
from cloudia.utils.tools import Tools
from cloudia.api.http.api_session import APISession
from cloudia.api.http.api_validate import APIValidation
from cloudia.utils.backend_service.nvcf.nvcf_const import NVCF_DATA
from pages.Settings.SettingsPage import SettingsPage
from pages.serviceKey.ServiceKey import ServiceKeyPage
from pages.Deployments.DeploymentsCreatePage import DeploymentsCreatePage
from datetime import datetime
import base64


class TestCloudFunctions:
    NVCF_ADMIN_USER = ["org_admin-nvcf_admin"]
    NVCF_USER_USESR = ["org_user-nvcf_user"]
    NVCF_VIEWER_USER = ["org_user-nvcf_viewer"]
    NVCF_PROD_ADMIN = ["nvcf_admin"]
    NVCF_VIEWER_USER = ["nvcf_viewer"]
    NVCF_GMAIL_OWNER = ["nvcf_gmail_owner"]

    func_vers_remove_list = list()
    api_keys_remove_list = list()
    cluster_remove_list = list()

    @pytest.fixture()
    def function_version_teardown(self, session_nvcf_admin_sak):
        yield
        session: APISession = session_nvcf_admin_sak["ngc"]
        while self.func_vers_remove_list:
            func_id, func_version_id = self.func_vers_remove_list.pop()
            logging.info(
                f"Deleting function with function {func_id} and ID {func_version_id}"
            )
            test_data_del = Tools.load_case_data(NVCF_DATA, "DELETE_FUNCTION")
            test_data_del["req"]["path"] += f"/{func_id}/versions/{func_version_id}"
            resp_del = session.request(**test_data_del["req"])
            APIValidation(resp_del, test_data_del["exp"]).validate_response()

    @pytest.fixture()
    def cluster_teardown(self, get_user_page):
        yield
        while self.cluster_remove_list:
            cluster_name = self.cluster_remove_list.pop()
            logging.info(f"Deleting cluster with name {cluster_name}")
            settings_page = SettingsPage(get_user_page)
            settings_page.navigate()
            settings_page.delete_cluster(cluster_name)

    @pytest.fixture()
    def function_version_teardown_gmail_org(self, session_gmail_org_nvcf_admin):
        yield
        session: APISession = session_gmail_org_nvcf_admin["ngc"]
        while self.func_vers_remove_list:
            func_id, func_version_id = self.func_vers_remove_list.pop()
            logging.info(
                f"Deleting function with function {func_id} and ID {func_version_id}"
            )
            test_data_del = Tools.load_case_data(NVCF_DATA, "DELETE_FUNCTION")
            test_data_del["req"]["path"] += f"/{func_id}/versions/{func_version_id}"
            resp_del = session.request(**test_data_del["req"])
            APIValidation(resp_del, test_data_del["exp"]).validate_response()

    def compare_datatime(self, rotated_time: str = None, start_time: str = None):
        try:
            current_time = datetime.strptime(rotated_time, "%m/%d/%Y %I:%M %p")
            target_time = datetime.strptime(start_time, "%m/%d/%Y %I:%M %p")

            if current_time >= target_time:
                return True
            else:
                return False

        except ValueError:
            logging.info(
                "Invalid time format! Valid time format is: MM/DD/YYYY HH:MM AM/PM"
            )
            return False

    @pytest.fixture()
    def service_key_teardown(self, get_user_page, delete_service_keys_with_cookies):
        yield
        # ser_key_page = ServiceKeyPage(get_user_page)
        # ser_key_page.navigate_to_page()

        # Make a copy of the list before we start modifying it
        keys_to_remove = self.api_keys_remove_list.copy()
        self.api_keys_remove_list.clear()

        for key_id in keys_to_remove:
            try:
                logging.info(f"Cleaning up API key: {key_id}")
                delete_service_keys_with_cookies(key_id=key_id)
            except Exception as e:
                logging.error(f"Failed to delete API key {key_id}: {str(e)}")

    @pytest.fixture()
    def service_key_teardown_gmail_org(
        self, get_user_page, delete_service_keys_with_cookies_gmail_org
    ):
        yield
        # ser_key_page = ServiceKeyPage(get_user_page)
        # ser_key_page.navigate_to_page()

        # Make a copy of the list before we start modifying it
        keys_to_remove = self.api_keys_remove_list.copy()
        self.api_keys_remove_list.clear()

        for key_id in keys_to_remove:
            try:
                logging.info(f"Cleaning up API key: {key_id}")
                delete_service_keys_with_cookies_gmail_org(key_id=key_id)
            except Exception as e:
                logging.error(f"Failed to delete API key {key_id}: {str(e)}")

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5096067
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_cannot_create_service_key_name_start_with_auto_cf(
        self,
        test_data,
        get_user_page: Page,
        running_config,
    ):
        ser_key = ServiceKeyPage(get_user_page)
        ser_key.navigate_to_page()
        result = ser_key.create_new_service_key_with_illegal_prefix_is_not_allowed(
            service_key_type="auto_key"
        )
        if not result:
            pytest.fail(
                "Create service key name start with __atuo_cf_ pass and failed the case"
            )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5096069
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_cannot_update_authorizations_for_service_key_name_start_with_auto_cf(
        self,
        test_data,
        get_user_page: Page,
    ):
        ser_key = ServiceKeyPage(get_user_page)
        ser_key.navigate_to_page()
        result = (
            ser_key.update_authorizations_service_key_with_illegal_prefix_is_not_allowed(
                service_key_type="auto_key"
            )
        )
        if not result:
            pytest.fail(
                "Update authorizations for service key name start with __atuo_cf_ is allowed and failed the case"
            )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5096068
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_cannot_deactivate_for_service_key_name_start_with_auto_cf(
        self,
        test_data,
        get_user_page: Page,
    ):
        ser_key = ServiceKeyPage(get_user_page)
        ser_key.navigate_to_page()
        result = ser_key.deactivate_auto_cf_within_key_prefix_is_not_allowed()
        if not result:
            pytest.fail(
                "Deactivate for service key name start with __atuo_cf_ pass and failed the case"
            )

    @pytest.mark.CloudFunctions
    @pytest.mark.edgecase
    @pytest.mark.T5096070
    @pytest.mark.skipif(
        CURRENT_ENV != "staging", reason="no specific ORG for testing in other ENVs"
    )
    @pytest.mark.parametrize("get_user_page", NVCF_GMAIL_OWNER, indirect=True)
    def test_cannot_delete_service_key_name_start_with_auto_cf(
        self,
        test_data,
        get_user_page: Page,
        get_service_keys_with_cookies_gmail_org_by_id,
    ):
        ser_key = ServiceKeyPage(get_user_page)
        ser_key.navigate_to_page()
        result = ser_key.delete_auto_cf_within_key_prefix_is_not_allow()
        if not result:
            pytest.fail(
                "Create service key name start with __atuo_cf_ pass and failed the case"
            )
        resp = get_service_keys_with_cookies_gmail_org_by_id(key_id=result[1])
        products = [product["product"] for product in resp["apiKey"]["policies"]]
        assert (
            "private-registry" in products
        ), "Service key does not have private-registry permission"
        assert (
            "artifact-catalog" in products
        ), "Service key does not have artifact-catalog permission"
        for product in resp["apiKey"]["policies"]:
            assert "registry:downloadArtifact" in product["scopes"]
            assert "registry:getArtifact" in product["scopes"]
            assert "registry:downloadContainer" in product["scopes"]
            assert "registry:getContainer" in product["scopes"]
            assert "registry:getContainerList" in product["scopes"]

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5096073
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_user_admin_can_delete_the_service_key_not_start_with_auto_cf_(
        self, test_data, get_user_page: Page, service_key_teardown
    ):
        ser_key = ServiceKeyPage(get_user_page)
        ser_key.navigate_to_page()
        key_name = add_timestamp("auto_limitation_key", "%m%d%H%M%S")
        gener_ser_key_value, key_id = ser_key.generate_service_key(
            key_name=key_name, expiration="1 hour"
        )
        self.api_keys_remove_list.append(key_id)

        assert "nvapi-" in gener_ser_key_value, "Generated new key failed!"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5096072
    @pytest.mark.parametrize("get_user_page", NVCF_VIEWER_USER, indirect=True)
    def test_non_user_admin_user_cannot_access_the_service_key_page(
        self,
        test_data,
        get_user_page: Page,
    ):
        ser_key = ServiceKeyPage(get_user_page)
        result = ser_key.check_if_exist_service_key_entry_for_non_user_admin_user()
        if not result:
            pytest.fail("Has Service Keys entry in organization and fail the case")

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5096320
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_user_admin_can_rotate_the_service_name_without___auto_cf__prefix(
        self,
        test_data,
        get_user_page: Page,
        service_key_teardown,
        get_service_keys_with_cookies,
        delete_service_keys_with_cookies,
        create_service_keys_with_cookies,
    ):
        ser_key = ServiceKeyPage(get_user_page)
        ser_key.navigate_to_page()

        resp = create_service_keys_with_cookies()
        self.api_keys_remove_list.append(resp["keyId"])

        # get service keys number by API before rotate key
        resp_json_before = get_service_keys_with_cookies()
        before_rotate = len(resp_json_before["apiKeys"])
        result = ser_key.rotate_auto_cf_within_input_key_prefix(key_name=resp["name"])
        if not result:
            pytest.fail("Rotate non __auto_cf_ key pass and failed the case")
        # get service keys number by API after rotate key
        resp_json_after = get_service_keys_with_cookies()
        after_rotate = len(resp_json_after["apiKeys"])
        assert after_rotate == int(
            before_rotate + 1
        ), "Not equal number before and after rotating"
        key_before = [key["keyId"] for key in resp_json_before["apiKeys"]]
        key_after = [key["keyId"] for key in resp_json_after["apiKeys"]]
        for key in key_after:
            if key not in key_before:
                new_key_id = key
        # tear down for the new dup one after rotation
        resp_delete = delete_service_keys_with_cookies(key_id=new_key_id)
        assert "SUCCESS" in resp_delete["requestStatus"]["statusCode"]

    @pytest.mark.CloudFunctions
    @pytest.mark.edgecase
    @pytest.mark.T5096071
    @pytest.mark.skipif(
        CURRENT_ENV != "staging", reason="no specific ORG for testing in other ENVs"
    )
    @pytest.mark.parametrize("get_user_page", NVCF_GMAIL_OWNER, indirect=True)
    def test_can_rotate_service_key_name_start_with_auto_cf(
        self,
        test_data,
        get_user_page: Page,
        function_version_teardown_gmail_org,
        session_gmail_org_nvcf_admin,
        func_invoc_data,
        get_service_keys_with_cookies_gmail_org_by_id,
    ):
        ser_key = ServiceKeyPage(get_user_page)
        ser_key.navigate_to_page()
        current_time = datetime.now()
        start_time = current_time.strftime("%m/%d/%Y %I:%M %p")
        result = ser_key.rotate_auto_cf_within_key_prefix()
        if not result:
            pytest.fail("Rotate __atuo_cf_ key pass and failed the case")

        rotated_time = result[2]

        if not self.compare_datatime(rotated_time=rotated_time, start_time=start_time):
            pytest.fail("Rotate __atuo_cf_ key do not generate a new timestamp!")

        resp = get_service_keys_with_cookies_gmail_org_by_id(key_id=result[1])
        products = [product["product"] for product in resp["apiKey"]["policies"]]
        assert (
            "private-registry" in products
        ), "Service key does not have private-registry permission"
        assert (
            "artifact-catalog" in products
        ), "Service key does not have artifact-catalog permission"
        for product in resp["apiKey"]["policies"]:
            assert "registry:downloadArtifact" in product["scopes"]
            assert "registry:getArtifact" in product["scopes"]
            assert "registry:downloadContainer" in product["scopes"]
            assert "registry:getContainer" in product["scopes"]
            assert "registry:getContainerList" in product["scopes"]

        test_data = Tools.load_case_data(func_invoc_data, "CREATE_FUNCTION")

        func_name = add_timestamp(test_data["req"]["json"]["name"], "%m%d%H%M%S")
        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        container = "0737195995831534/fastapi_echo_sample"
        tag = "latest (Latest)"
        create_func_page.Form.create_container_function(
            name=func_name,
            container=container,
            tag=tag,
            inference_protocol="HTTP",
            inference_endpoint="/echo",
            health_endpoint="/health",
            inference_port=8000,
        )

        # Get source function details
        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.BasicDetails.check_func_name(func_name)
        func_id = func_ver_detail_page.OverviewBasicDetails.get_function_id_info()[
            "Function ID"
        ]
        func_ver_id = func_ver_detail_page.OverviewBasicDetails.get_version_id_info()[
            "Version ID"
        ]

        # Add to teardown list
        self.func_vers_remove_list.append([func_id, func_ver_id])

        # Deploy the function
        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deploy_data = TEST_DATA_CF["deployment_with_A100"]
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            **deploy_data,
            wait_until_deployed=True,
            session_nvcf_admin_sak=session_gmail_org_nvcf_admin,
        )

    @pytest.mark.CloudFunctions
    @pytest.mark.edgecase
    @pytest.mark.T5096075
    @pytest.mark.skipif(
        CURRENT_ENV != "staging", reason="no specific ORG for testing in other ENVs"
    )
    @pytest.mark.parametrize("get_user_page", NVCF_GMAIL_OWNER, indirect=True)
    def test_verify_new_key_by_get_nvcf_account_credentials(
        self,
        test_data,
        get_user_page: Page,
        session_service_key_ssa_admin_allscope,
        get_service_keys_with_cookies_gmail_org,
    ):
        test_data = Tools.load_case_data(FUNCTION_INVOCATION_DATA, Tools.get_case_name())
        session_dict = session_service_key_ssa_admin_allscope
        session: APISession = session_dict["ngc"]
        resp_json_before = session.request(**test_data["req"]).json()
        credential_base64_before = resp_json_before["account"]["credentials"]
        decoded_str_before = base64.b64decode(credential_base64_before).decode()

        container_reg_cre_before = json.loads(decoded_str_before)[
            "containerRegistryCredential"
        ]

        resp_json_key_before = get_service_keys_with_cookies_gmail_org()
        before_rotate = len(resp_json_key_before["apiKeys"])

        # Rotate __auto_cf_ key
        ser_key = ServiceKeyPage(get_user_page)
        ser_key.navigate_to_page()
        result = ser_key.rotate_auto_cf_within_key_prefix()
        if not result:
            pytest.fail("Rotate __atuo_cf_ key pass and failed the case")

        # Get credential after rotation
        resp_json_after = session.request(**test_data["req"]).json()
        credential_base64_after = resp_json_after["account"]["credentials"]
        decoded_str_after = base64.b64decode(credential_base64_after).decode()

        container_reg_cre_after = json.loads(decoded_str_after)[
            "containerRegistryCredential"
        ]

        assert container_reg_cre_before != container_reg_cre_after, "Rotation not success!"

        resp_json_key_after = get_service_keys_with_cookies_gmail_org()
        after_rotate = len(resp_json_key_after["apiKeys"])

        for key in resp_json_key_after["apiKeys"]:
            if key["keyId"] == result[1]:
                assert (
                    key["value"][-3:] == container_reg_cre_after[-3:]
                ), "Credential key not the using __auto_cf_ key!"

        assert after_rotate == before_rotate, "Key number not correct!"

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5095876
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_manual_instance_configuration_for_the_not_ready_cluster(
        self,
        test_data,
        get_user_page: Page,
    ):
        settings_page = SettingsPage(get_user_page)
        # Negative to the settings page
        settings_page.navigate()
        result = settings_page.verify_manual_configuration_in_cluster_registration()
        if result is False:
            pytest.fail("Manual instance configuration is not correct!")

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5102620
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_cannot_rotate_the_keys_name_starting_with___byoc__at_service_page_negative(
        self,
        test_data,
        get_user_page: Page,
    ):
        service_key_page = ServiceKeyPage(get_user_page)
        service_key_page.navigate_to_page()
        result = service_key_page.rotate_with_byoc_key_prefix()
        if result[1] is True:
            logging.info(f"Rotating {result[0]} can get warning info")
        else:
            pytest.fail(f"Rotating {result[0]} key can not get warning info")

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5102621
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_cannot_update_authorizations_the_keys_name_starting_with___byoc__at_service_page_negative(
        self,
        test_data,
        get_user_page: Page,
    ):
        service_key_page = ServiceKeyPage(get_user_page)
        service_key_page.navigate_to_page()
        result = service_key_page.update_authorizations_service_key_with_illegal_prefix_is_not_allowed(
            service_key_type="byoc"
        )
        if result is False:
            pytest.fail(
                "Update authorizations for service key name start with __byoc_ is allowed and failed the case"
            )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5102622
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_cannot_create_new_keys_name_starting_with___byoc__at_service_page_negative(
        self,
        test_data,
        get_user_page: Page,
    ):
        service_key_page = ServiceKeyPage(get_user_page)
        service_key_page.navigate_to_page()
        result = service_key_page.create_new_service_key_with_illegal_prefix_is_not_allowed(
            service_key_type="byoc"
        )
        if result is False:
            pytest.fail(
                "Create new service key name starts with __byoc_ is allowed and failed the case"
            )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5102624
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_deactivate_service_key_name_starting_with___byoc__at_service_page_negative(
        self,
        test_data,
        get_user_page: Page,
    ):
        service_key_page = ServiceKeyPage(get_user_page)
        service_key_page.navigate_to_page()
        result = service_key_page.deactivate_normal_service_key(service_key="byoc")
        if result is False:
            pytest.fail(
                "Create new service key name starts with __byoc_ is allowed and failed the case"
            )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5102623
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_delete_service_key_starting_with___byoc__at_service_page(
        self,
        test_data,
        get_user_page: Page,
        cluster_teardown,
    ):
        settings_page = SettingsPage(get_user_page)
        settings_page.navigate()
        timestamp = datetime.now().strftime("%m%d%H%M%S")
        cluster_name = f"automation-for-delete-{timestamp}"
        self.cluster_remove_list.append(cluster_name)
        result = settings_page.resume_and_rotate_not_ready_cluster_to_generate_new_byoc_key(
            cluster_name=cluster_name
        )
        if result is None:
            pytest.fail("Generate cluster service key failed!")
        service_page = ServiceKeyPage(get_user_page)
        service_page.navigate_to_page()
        delete_result = service_page.delete_service_key(key_name=result)
        if delete_result != "Key has been deleted!":
            pytest.fail("Delete cluster service key error!")

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5095874
    @pytest.mark.skipif(CURRENT_ENV == "staging", reason="existing bug: 5371589")
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_key_generation_limits_rotate_cluster_key_for_the_not_ready_cluster_when_the_service_key_already_access_limits_negative(
        self,
        get_user_page: Page,
        running_config,
        get_service_keys_with_cookies,
        delete_service_keys_with_cookies,
        create_service_keys_with_cookies,
        service_key_teardown,
    ):
        settings_page = SettingsPage(get_user_page)
        settings_page.navigate()
        result = settings_page.resume_and_rotate_not_ready_cluster_to_generate_new_byoc_key(
            cluster_name="automation-for-delete-byoc-key"
        )
        if result is None:
            pytest.fail("Generate cluster service key failed!")

        # Get rotate key by get API
        resp = get_service_keys_with_cookies()
        key_id = next(
            (key["keyId"] for key in resp["apiKeys"] if key["name"] == result), ""
        )
        logging.info(f"Current service keys number is {len(resp['apiKeys'])}")

        # Delete the key id just rotated
        delete_service_keys_with_cookies(key_id=key_id)

        # create new service keys to access the limitation of service keys number (64)
        new_key_id = []
        if CURRENT_ENV == "staging":
            max_num = 51
        else:
            max_num = 65
        for _ in range(max_num - len(resp["apiKeys"])):
            res_create = create_service_keys_with_cookies()
            new_key_id.append(res_create["keyId"])
        logging.info(f"Generate {max_num-len(resp['apiKeys'])} service key!")

        # Teardown new generate service keys
        self.api_keys_remove_list.extend(new_key_id)

        settings_page.navigate()
        result_after = settings_page.cannot_resume_and_rotate_not_ready_cluster_to_generate_new_byoc_key_when_access_the_limitation_number(
            cluster_name="automation-for-delete-byoc-key"
        )
        if result_after is None:
            pytest.fail(
                "Fail case due to still can resume and rotate service key when the number access the limitation!"
            )

    @pytest.mark.CloudFunctions
    @pytest.mark.Sanity
    @pytest.mark.prod
    @pytest.mark.canary
    @pytest.mark.T5096074
    @pytest.mark.skipif(
        CURRENT_ENV == "staging", reason="not support on staging, test on other case"
    )
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_service_keys_number_should_not_exceed_64(
        self,
        get_user_page: Page,
        running_config,
        get_service_keys_with_cookies,
        delete_service_keys_with_cookies,
        create_service_keys_with_cookies,
        service_key_teardown,
    ):
        # Get rotate key by get API
        resp = get_service_keys_with_cookies()

        # create new service keys to access the limitation of service keys number (64)
        for _ in range(64 - len(resp["apiKeys"])):
            # Generate new service key
            res_create = create_service_keys_with_cookies()
            # Teardown new service keys
            self.api_keys_remove_list.append(res_create["keyId"])
        logging.info(f"Generate {64-len(resp['apiKeys'])} service key!")

        # Navigate to service key page
        service_key_page = ServiceKeyPage(get_user_page)
        service_key_page.navigate_to_page()
        key_name = add_timestamp("actor_exceed_limitation")
        result_after = (
            service_key_page.actor_exceed_limitation_warning_when_generate_new_key(
                key_name=key_name, expiration="1 hour"
            )
        )

        if result_after is False:
            pytest.fail(
                "No Actor reached maximum allowed number of keys in service info came up"
            )

    @pytest.mark.CloudFunctions
    @pytest.mark.edgecase
    @pytest.mark.T5204906
    @pytest.mark.skipif(
        CURRENT_ENV != "staging", reason="no specific ORG for testing in other ENVs"
    )
    @pytest.mark.parametrize("get_user_page", NVCF_GMAIL_OWNER, indirect=True)
    def test_service_keys_number_should_not_exceed_50_in_staging(
        self,
        get_user_page: Page,
        running_config,
        get_service_keys_with_cookies_gmail_org,
        create_service_keys_with_cookies_gmail_org,
        service_key_teardown_gmail_org,
    ):
        # Get rotate key by get API
        resp = get_service_keys_with_cookies_gmail_org()
        # create new service keys to access the limitation of service keys number (64)
        for _ in range(50 - len(resp["apiKeys"])):
            # Generate new service key
            res_create = create_service_keys_with_cookies_gmail_org()
            # Teardown new service keys
            self.api_keys_remove_list.append(res_create["keyId"])
        logging.info(f"Generate {50 - len(resp['apiKeys'])} service key!")

        # Navigate to service key page
        service_key_page = ServiceKeyPage(get_user_page)
        service_key_page.navigate_to_page()
        key_name = add_timestamp("actor_exceed_limitation")
        result_after = (
            service_key_page.actor_exceed_limitation_warning_when_generate_new_key(
                key_name=key_name, expiration="1 hour"
            )
        )

        if result_after is False:
            pytest.fail(
                "No Actor reached maximum allowed number of keys in service info came up"
            )
