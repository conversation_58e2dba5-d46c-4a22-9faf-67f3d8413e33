import logging
import pytest
import time

from pages.Deployments.DeploymentsCreatePage import DeploymentsCreatePage
from pages.Deployments.DeploymentsEditPage import DeploymentsEditPage
from utils.grpc_client import GRPCClient
from playwright.sync_api import Page
from pages.Functions.FunctionsListPage import FunctionListPage
from pages.Functions.FunctionVerDetailPage import FunctionVerDetailPage
from pages.CreateFunction.CreateFunctionPage import CreateFunctionPage
from utils.common.tools import add_timestamp
from cloudia.utils.tools import Tools
from cloudia.api.http.api_session import APISession
from cloudia.api.http.api_validate import APIValidation
from cloudia.api.utils.api_utils import APITools
from cloudia.utils.backend_service.nvcf.nvcf_const import NVCF_DATA
from google.protobuf.json_format import MessageToDict
from config.consts import CURRENT_ORG, GPU_KATA_SUPPORTED_LIST, GPU_GXCACHE_SUPPORTED_LIST


@pytest.fixture(scope="session")
def conf_nvcf_grpc_endpoint(running_config):
    return {**running_config["nvcf_grpc_endpoint"]}


@pytest.fixture
def test_params(request):
    params = {
        "gpu": request.config.getoption("--gpu"),
        "backend": request.config.getoption("--backend"),
        "instanceType": request.config.getoption("--instanceType"),
        "minInstances": request.config.getoption("--minInstances"),
        "maxInstances": request.config.getoption("--maxInstances"),
        "maxConcurrency": request.config.getoption("--maxConcurrency"),
        "clusters": request.config.getoption("--clusters"),
        "func_name": request.config.getoption("--func_name"),
    }
    return params


class TestCluster:
    NVCF_ADMIN_USER = ["org_admin-nvcf_admin"]
    NVCF_USER_USESR = ["org_user-nvcf_user"]
    NVCF_VIEWER_USER = ["org_user-nvcf_viewer"]
    NVCF_PROD_ADMIN = ["nvcf_admin"]

    func_vers_remove_list = list()

    @pytest.fixture()
    def function_version_teardown(self, session_nvcf_admin_sak):
        yield
        session: APISession = session_nvcf_admin_sak["ngc"]
        while self.func_vers_remove_list:
            func_id, func_version_id = self.func_vers_remove_list.pop()
            logging.info(
                f"Deleting function with function {func_id} and ID {func_version_id}"
            )
            test_data_del = Tools.load_case_data(NVCF_DATA, "DELETE_FUNCTION")
            test_data_del["req"]["path"] += f"/{func_id}/versions/{func_version_id}"
            resp_del = session.request(**test_data_del["req"])
            APIValidation(resp_del, test_data_del["exp"]).validate_response()

    def create_and_deploy_container_function(
        self,
        get_user_page,
        func_info,
        params,
        inference_protocol=None,
        session_nvcf_admin_sak=None,
    ):
        prefix = params["func_name"] or (
            params["clusters"] if params["clusters"] else "cluster"
        )
        func_name = add_timestamp(f"{prefix}_{func_info['name']}_UI", "%m%d%H%M%S")

        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        if inference_protocol == "gRPC":
            create_func_page.Form.create_gRPC_container_function(
                name=func_name,
                container="/".join([CURRENT_ORG] + func_info["container"].split("/")[1:]),
                tag=func_info["tag"],
                inference_protocol=func_info["inference_protocol"],
                inference_port=func_info["inference_port"],
            )
        else:
            create_func_page.Form.create_container_function(
                name=func_name,
                container="/".join([CURRENT_ORG] + func_info["container"].split("/")[1:]),
                tag=func_info["tag"],
                inference_protocol=func_info["inference_protocol"],
                inference_endpoint=func_info["inference_endpoint"],
                health_endpoint=func_info["health_endpoint"],
                secrets=func_info.get("secrets"),
                env=func_info.get("environment_variables"),
            )

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.BasicDetails.check_func_name(func_name)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        self.func_vers_remove_list.append([func_id, func_ver_id])

        func_ver_detail_page.NavigationBar.navigate_to_function_list_page()
        func_page = FunctionListPage(get_user_page)
        func_page.FunctionPagination.wait_function_list_ready()
        assert func_page.FunctionPagination.check_if_function_exists(
            func_name
        ), "The function was not created successfully."

        deployment_parameters = {
            "gpu_list": [params["gpu"]],
            "instance_detail_list": [
                {
                    "name": params["instanceType"],
                    "min_instances": params["minInstances"],
                    "max_instances": params["maxInstances"],
                    "max_concurrency": params["maxConcurrency"],
                }
            ],
        }

        if params["clusters"]:
            deployment_parameters["instance_clusters_filter_list"] = params[
                "clusters"
            ].split(",")

        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            **deployment_parameters,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

        return func_id, func_ver_id, func_name

    def create_and_deploy_helm_chart_function(
        self, get_user_page, test_data, func_info, params, session_nvcf_admin_sak
    ):
        func_name = add_timestamp(func_info["name"], "%m%d%H%M%S")

        create_func_page = CreateFunctionPage(get_user_page)
        create_func_page.navigate_to_page()
        create_func_page.Form.create_helm_chart_function(
            func_name,
            func_info["helm_chart"],
            func_info["helm_chart_version"],
            func_info["helm_chart_service_name"],
            func_info["inference_protocol"],
            func_info["inference_endpoint"],
            func_info["health_endpoint"],
            func_info["inference_port"],
        )

        func_ver_detail_page = FunctionVerDetailPage(get_user_page)
        func_ver_detail_page.BasicDetails.check_func_name(func_name)
        basic_details_data = func_ver_detail_page.BasicDetails.get_all_data()
        func_id = basic_details_data["Function ID"]
        func_ver_id = basic_details_data["Version ID"]
        self.func_vers_remove_list.append([func_id, func_ver_id])

        func_ver_detail_page.NavigationBar.navigate_to_function_list_page()
        func_page = FunctionListPage(get_user_page)
        func_page.FunctionPagination.wait_function_list_ready()
        assert func_page.FunctionPagination.check_if_function_exists(
            func_name
        ), "The function was not created successfully."

        deployments_create_page = DeploymentsCreatePage(get_user_page)
        deployments_create_page.deploy_function_version(
            get_user_page,
            func_id,
            func_ver_id,
            **test_data["deployment_CUSTOM"],
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

        return func_id, func_ver_id, func_name

    def check_instance_count(
        self, page, func_name, func_id, func_ver_id, expected_instance_count=0
    ):
        expected_instance_count_str = (
            "—" if expected_instance_count == 0 else str(expected_instance_count)
        )
        version_detail_page = FunctionVerDetailPage(page, func_name, func_id, func_ver_id)
        version_detail_page.navigate_to_page()
        version_detail_page.switch_tab("metrics")
        version_detail_page.AggreatedInvoc.wait_for_invo_data_visible("func_version_invo")
        version_detail_page.AggreatedInvoc.set_display_span("Past 1 Minute")
        version_detail_page.AggreatedInvoc.wait_for_invo_data_visible("func_version_invo")
        instance_count = version_detail_page.AggreatedInvoc.get_all_invo_data_new("HTTP")[
            "Total Instance Count"
        ]
        logging.info(
            f"Total instance count is {instance_count} on Funtion Version Detail Page"
        )
        assert (
            instance_count == expected_instance_count_str
        ), f"Total instance count {instance_count} is not as expected {expected_instance_count_str}"

    def invoke_function_and_get_result(self, api_data, func_id, session_nvcf_admin_sak):
        """
        Invoke the function and handle the normal case where we get a 200 OK response right away.
        """
        api_data["req"]["path"] += func_id
        session: APISession = session_nvcf_admin_sak["nvcf"]
        resp = session.request(**api_data["req"])
        assert resp.status_code == 200, f"Expected status 200, but got {resp.status_code}"
        APIValidation(resp, api_data["exp"]).validate_response()
        return resp

    def invoke_function_and_poll_result(
        self, first_api_data, poll_api_data, func_id, session_nvcf_admin_sak
    ):
        """
        Invoke the function and handle the case where a 504 code will be recived before worker is running,
        then a 202 code is for pending response,the poll action will received 200 finally.
        """
        first_api_data["req"]["path"] += func_id
        session: APISession = session_nvcf_admin_sak["nvcf"]

        start_time = time.time()
        timeout_seconds = 1800  # 30
        while True:
            resp = session.request(**first_api_data["req"])
            if resp.status_code == 504:
                if time.time() - start_time > timeout_seconds:
                    assert False, "Test failed due to 504 timeout"
                time.sleep(5)
                continue
            break

        assert resp.status_code in [
            200,
            202,
        ], f"Expected status 200 or 202, but got {resp.status_code}"

        if resp.status_code == 202:
            request_id = APITools.get_header_value(resp, "NVCF-REQID")
            poll_api_data["req"]["path"] += f"/{request_id}"
            start_time = time.time()
            timeout_seconds = 1800
            while time.time() - start_time < timeout_seconds:
                resp_poll = session.request(**poll_api_data["req"])
                if resp_poll.status_code == 200:
                    break
                time.sleep(5)
            else:
                assert False, "Test failed due to timeout"
            APIValidation(resp_poll, first_api_data["exp"]).validate_response()
            return resp_poll
        else:
            APIValidation(resp, first_api_data["exp"]).validate_response()
            return resp

    def invoke_grpc_echo_function_and_get_result(
        self, nvcf_grpc_endpoint, func_id, func_ver_id, running_config
    ):
        """
        Invoke the grpc echo function and get the result from validating the reponse and schemas
        """
        sak_token = running_config["nvcf_admin"]["api_key"]
        default_metadata = [
            ("authorization", "Bearer" + " " + sak_token),
            ("function-id", func_id),
            ("function-version-id", func_ver_id),
        ]
        logging.info(f"default_metadata: {default_metadata}")
        client = GRPCClient(nvcf_grpc_endpoint, default_metadata)
        response = client.make_request("EchoMessage", {"message": "message_test"})
        logging.info(f"Response: {response}")
        response_dict = MessageToDict(response)
        response_schema = {
            "type": "object",
            "properties": {"message": {"type": "string"}},
            "required": ["message"],
        }
        return client.validate_response(response_dict, response_schema)

    @pytest.mark.CloudFunctions
    @pytest.mark.T4004847
    @pytest.mark.cluster_validation
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_e2e_ui_1_register_byoc_and_create_functions_with_http(
        self,
        test_data,
        func_invoc_data,
        get_user_page: Page,
        function_version_teardown,
        test_params,
        session_nvcf_admin_sak,
    ):
        func_info = test_data["prod_function_pytriton_echo"]
        func_id, _, _ = self.create_and_deploy_container_function(
            get_user_page,
            func_info,
            test_params,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )
        api_data = Tools.load_case_data(func_invoc_data, "INVOKE_FUNCTION_ID_PYTRITON")
        self.invoke_function_and_get_result(api_data, func_id, session_nvcf_admin_sak)
        logging.info(f"{Tools.get_case_name()} Done.\n")

    @pytest.mark.CloudFunctions
    @pytest.mark.T4004848
    @pytest.mark.cluster_validation
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_e2e_ui_2_create_container_function_with_inference_protocol_of_grpc(
        self,
        test_data,
        func_invoc_data,
        get_user_page: Page,
        function_version_teardown,
        test_params,
        conf_nvcf_grpc_endpoint,
        running_config,
        session_nvcf_admin_sak,
    ):
        func_info = test_data["grpc_echo_sample"]
        func_id, func_ver_id, _ = self.create_and_deploy_container_function(
            get_user_page,
            func_info,
            test_params,
            inference_protocol="gRPC",
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

        nvcf_grpc_endpoint = conf_nvcf_grpc_endpoint["nvcf_grpc_endpoint"]
        valid, error = self.invoke_grpc_echo_function_and_get_result(
            nvcf_grpc_endpoint, func_id, func_ver_id, running_config
        )
        assert valid, f"Validation failed: {error}"

    @pytest.mark.CloudFunctions
    @pytest.mark.T4004849
    @pytest.mark.cluster_validation
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_e2e_ui_3_create_functions_with_ess_http(
        self,
        test_data,
        func_invoc_data,
        get_user_page: Page,
        test_params,
        session_nvcf_admin_sak,
    ):
        func_info = test_data["prod_secrets_sample_function"]
        func_id, _, _ = self.create_and_deploy_container_function(
            get_user_page,
            func_info,
            test_params,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

        api_data = Tools.load_case_data(func_invoc_data, "INVOKE_FUNCTION_ID_SECRET_SAMPLE")
        resp = self.invoke_function_and_get_result(
            api_data, func_id, session_nvcf_admin_sak
        )
        assert (
            resp.json() == func_info["secrets"]
        ), f"Secrets validation failed: expected {func_info['secrets']}, got {resp.json()}"
        logging.info(f"{Tools.get_case_name()} Done.\n")

    @pytest.mark.CloudFunctions
    @pytest.mark.T4004851
    @pytest.mark.cluster_validation
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_e2e_ui_4_scaling_up_instances(
        self,
        test_data,
        func_invoc_data,
        get_user_page: Page,
        function_version_teardown,
        test_params,
        session_nvcf_admin_sak,
    ):
        WAIT_TIME_AFTER_INVOKE = 300
        func_info = test_data["prod_function_pytriton_echo"]
        deployment_setting = test_params.copy()
        deployment_setting["minInstances"] = 0
        func_id, func_ver_id, func_name = self.create_and_deploy_container_function(
            get_user_page,
            func_info,
            deployment_setting,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )
        self.check_instance_count(get_user_page, func_name, func_id, func_ver_id, 0)
        first_api_data = Tools.load_case_data(
            func_invoc_data, "INVOKE_FUNCTION_ID_PYTRITON"
        )
        poll_api_data = Tools.load_case_data(
            func_invoc_data, "POLL_RESULT_OF_FUNCTION_INVOCATION_REQUEST"
        )
        self.invoke_function_and_poll_result(
            first_api_data, poll_api_data, func_id, session_nvcf_admin_sak
        )
        time.sleep(WAIT_TIME_AFTER_INVOKE)
        self.check_instance_count(get_user_page, func_name, func_id, func_ver_id, 1)
        logging.info(f"{Tools.get_case_name()} Done.\n")

    @pytest.mark.CloudFunctions
    @pytest.mark.T4004852
    @pytest.mark.timeout(10000)
    @pytest.mark.cluster_validation
    @pytest.mark.skip(reason="Spend too longer time, skip for case refine")
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_e2e_ui_5_scaling_down_instances(
        self,
        test_data,
        func_invoc_data,
        get_user_page: Page,
        function_version_teardown,
        test_params,
        session_nvcf_admin_sak,
    ):
        # Constants specific to this test case
        WAIT_TIME_SCALE_DOWN = 2000
        WAIT_TIME_AFTER_INVOKE = 120
        WAIT_TIME_FINAL_SCALE_DOWN = 2400

        func_info = test_data["prod_function_pytriton_echo"]
        func_id, func_ver_id, func_name = self.create_and_deploy_container_function(
            get_user_page,
            func_info,
            test_params,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )
        time.sleep(60)
        self.check_instance_count(get_user_page, func_name, func_id, func_ver_id, 1)

        deployment_edit_page = DeploymentsEditPage(get_user_page)
        deployment_edit_page.navigate_to_page("deployments_entry", func_id, func_ver_id)
        deployment_edit_page.set_mininstance_to_zero()

        time.sleep(WAIT_TIME_SCALE_DOWN)
        self.check_instance_count(get_user_page, func_name, func_id, func_ver_id, 0)
        first_api_data = Tools.load_case_data(
            func_invoc_data, "INVOKE_FUNCTION_ID_PYTRITON"
        )
        poll_api_data = Tools.load_case_data(
            func_invoc_data, "POLL_RESULT_OF_FUNCTION_INVOCATION_REQUEST"
        )
        self.invoke_function_and_poll_result(
            first_api_data, poll_api_data, func_id, session_nvcf_admin_sak
        )
        time.sleep(WAIT_TIME_AFTER_INVOKE)
        self.check_instance_count(get_user_page, func_name, func_id, func_ver_id, 1)

        time.sleep(WAIT_TIME_FINAL_SCALE_DOWN)
        self.check_instance_count(get_user_page, func_name, func_id, func_ver_id, 0)
        logging.info(f"{Tools.get_case_name()} Done.\n")

    @pytest.mark.CloudFunctions
    @pytest.mark.T3951797
    @pytest.mark.cluster_validation
    @pytest.mark.skip(reason="Kata support is removed in SBOM 1.7.28")
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_e2e_ui_6_kata_container_validation(
        self,
        test_data,
        func_invoc_data,
        get_user_page: Page,
        function_version_teardown,
        test_params,
        session_nvcf_admin_sak,
    ):
        if test_params.get("gpu") not in GPU_KATA_SUPPORTED_LIST:
            pytest.skip("GPU does not support")

        func_info = test_data["prod_function_pytriton_echo"]
        func_id, _, _ = self.create_and_deploy_container_function(
            get_user_page,
            func_info,
            test_params,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )
        api_data = Tools.load_case_data(func_invoc_data, "INVOKE_FUNCTION_ID_PYTRITON")
        self.invoke_function_and_get_result(api_data, func_id, session_nvcf_admin_sak)

    @pytest.mark.CloudFunctions
    @pytest.mark.T3951798
    @pytest.mark.cluster_validation
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_e2e_ui_7_gxcache_container_validation(
        self,
        test_data,
        func_invoc_data,
        get_user_page: Page,
        function_version_teardown,
        test_params,
        session_nvcf_admin_sak,
    ):
        if test_params.get("gpu") not in GPU_GXCACHE_SUPPORTED_LIST:
            pytest.skip("GPU does not support")

        func_info = test_data["prod_gxcache_validator_function"]
        func_id, _, _ = self.create_and_deploy_container_function(
            get_user_page,
            func_info,
            test_params,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

        hit, miss = 0, 50
        for i in range(3):
            api_data = Tools.load_case_data(func_invoc_data, "INVOKE_FUNCTION_GXCACHE_HIT")
            resp = self.invoke_function_and_get_result(
                api_data, func_id, session_nvcf_admin_sak
            )
            logging.info(f"invoke {i} -> {resp.json()}.\n")
            assert (
                resp.json()["status"] == "OK"
            ), f"GXCACHE expected status not OK, got {resp.json()}"
            output = resp.json()["output"]
            n_hit, n_miss = int(output.split()[3]), int(output.split()[1][:-1])
            assert (
                n_hit >= hit and n_miss <= miss
            ), f"GXCACHE hit check failed: got {resp.json()}"
            hit, miss = n_hit, n_miss

        assert (
            n_hit > 0 and n_miss < 50
        ), f"GXCACHE validation failed: expected hit value increase, got {resp.json()}"
        logging.info(f"{Tools.get_case_name()} Done.\n")

    @pytest.mark.CloudFunctions
    @pytest.mark.T3988884
    @pytest.mark.cluster_validation
    @pytest.mark.skip(reason="Wait for bug 5027148 fixed")
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_e2e_ui_8_create_and_deploy_helm_chart_function(
        self,
        test_data,
        func_invoc_data,
        get_user_page: Page,
        function_version_teardown,
        test_params,
        session_nvcf_admin_sak,
    ):
        func_info = test_data["prod_multi_node_scerets_test_helm_chart_function"]
        func_id, _, _ = self.create_and_deploy_helm_chart_function(
            get_user_page,
            test_data,
            func_info,
            test_params,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )
        api_data = Tools.load_case_data(func_invoc_data, "INVOKE_FUNCTION_ID_SECRET_SAMPLE")
        for i in range(3):
            resp = self.invoke_function_and_get_result(
                api_data, func_id, session_nvcf_admin_sak
            )
            expected_secrets = {"key_1": "value_1"}
            assert (
                resp.json() == expected_secrets
            ), f"Secrets validation failed: expected {expected_secrets}, got {resp.json()}"
            logging.info(f"request {i}: {resp.json()}")
        logging.info(f"{Tools.get_case_name()} Done.\n")

    @pytest.mark.CloudFunctions
    @pytest.mark.T3989525
    @pytest.mark.cluster_validation
    @pytest.mark.parametrize("get_user_page", NVCF_PROD_ADMIN, indirect=True)
    def test_e2e_ui_9_proxycache_container_validation(
        self,
        test_data,
        func_invoc_data,
        get_user_page: Page,
        function_version_teardown,
        test_params,
        session_nvcf_admin_sak,
    ):
        if test_params.get("gpu") not in GPU_GXCACHE_SUPPORTED_LIST:
            pytest.skip("GPU does not support")

        func_info = test_data["prod_proxycache_sample_function"]
        func_id, _, _ = self.create_and_deploy_container_function(
            get_user_page,
            func_info,
            test_params,
            session_nvcf_admin_sak=session_nvcf_admin_sak,
        )

        time.sleep(120)
        for i in range(3):
            api_data = Tools.load_case_data(
                func_invoc_data, "INVOKE_FUNCTION_PROXYCACHE_HIT"
            )
            resp = self.invoke_function_and_get_result(
                api_data, func_id, session_nvcf_admin_sak
            )
            expected_json = [200, "HIT"]
            if not resp.json() == expected_json:
                time.sleep(5)
                resp = self.invoke_function_and_get_result(
                    api_data, func_id, session_nvcf_admin_sak
                )
            assert (
                resp.json() == expected_json
            ), f"Secrets validation failed: expected {expected_json}, got {resp.json()}"
            logging.info(f"request {i}: {resp.json()}")
        logging.info(f"{Tools.get_case_name()} Done.\n")
