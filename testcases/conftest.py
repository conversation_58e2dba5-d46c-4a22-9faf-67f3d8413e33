import pytest
import logging
from datetime import datetime, timezone, timedelta
import re
from cloudia.utils.file_handler import file_handler, YamlHandler
from cloudia.utils.tools import Tools
from cloudia.api.http.api_session import APISession
from cloudia.api.http.api_validate import APIValidation
from config.consts import (
    TEST_DATA_PATH_CF,
    TEST_DATA_PATH_TASKS,
    FUNC_INVOC_DATA_PATH,
    TEST_DATA_PATH_SETTINGS,
    CURRENT_ENV,
    CURRENT_ORG_SECONDARY,
    CURRENT_ORG,
)
from cloudia.utils.backend_service.nvcf.nvcf_utils import NVCFUtils
from cloudia.utils.backend_service.nvcf.nvcf_const import NVCF_DATA
from playwright.sync_api import Page
import copy
from utils.common.tools import add_timestamp


@pytest.fixture()
def test_data():
    test_data = file_handler(TEST_DATA_PATH_CF).read()
    yield test_data


@pytest.fixture()
def test_data_tasks():
    test_data = file_handler(TEST_DATA_PATH_TASKS).read()
    yield test_data


@pytest.fixture()
def test_data_settings():
    test_data = file_handler(TEST_DATA_PATH_SETTINGS).read()
    yield test_data


@pytest.fixture()
def func_invoc_data():
    data = YamlHandler(FUNC_INVOC_DATA_PATH).read()
    yield data


@pytest.fixture(scope="function")
def new_container_function(session_nvcf_admin_sak):
    test_data_add = Tools.load_case_data(NVCF_DATA, "CREATE_FUNCTION")
    session_dict = session_nvcf_admin_sak
    session: APISession = session_dict["ngc"]
    funcs_info = NVCFUtils(session_dict).get_functions_info()
    test_data_add["req"]["json"]["name"] = Tools.add_timestamp(
        test_data_add["req"]["json"]["name"], "%m%d%H%M%S"
    )
    test_data_add["req"]["json"]["containerImage"] = funcs_info["pytriton_echo"][
        "containerImage"
    ]
    test_data_add["req"]["json"]["inferenceUrl"] = funcs_info["pytriton_echo"][
        "inferenceUrl"
    ]
    test_data_add["req"]["json"]["inferencePort"] = funcs_info["pytriton_echo"][
        "inferencePort"
    ]
    resp_add = session.request(**test_data_add["req"])
    APIValidation(resp_add, test_data_add["exp"]).validate_response()
    result_add = resp_add.json()
    func_id = result_add["function"]["id"]
    func_vers_id = result_add["function"]["versionId"]
    yield func_id, func_vers_id
    test_data_del = Tools.load_case_data(NVCF_DATA, "DELETE_FUNCTION")
    logging.info(f"Deleting function with function {func_id} and ID {func_vers_id}")
    test_data_del["req"]["path"] += f"/{func_id}/versions/{func_vers_id}"
    resp_del = session.request(**test_data_del["req"])
    APIValidation(resp_del, test_data_del["exp"]).validate_response()


@pytest.fixture(scope="function")
def new_error_container_function(session_nvcf_admin_sak):
    test_data_add = Tools.load_case_data(NVCF_DATA, "CREATE_FUNCTION")
    session_dict = session_nvcf_admin_sak
    session: APISession = session_dict["ngc"]
    funcs_info = NVCFUtils(session_dict).get_functions_info()
    test_data_add["req"]["json"]["name"] = Tools.add_timestamp(
        test_data_add["req"]["json"]["name"], "%m%d%H%M%S"
    )
    test_data_add["req"]["json"]["containerImage"] = funcs_info["fastapi_echo"][
        "containerImage"
    ]
    test_data_add["req"]["json"]["containerImage"] = re.sub(
        r":[^:]*$", ":crashloop", test_data_add["req"]["json"]["containerImage"]
    )
    test_data_add["req"]["json"]["containerImage"] = str(
        test_data_add["req"]["json"]["containerImage"]
    ).replace("fastapi_echo_sample_prod", "fastapi_echo_sample")
    test_data_add["req"]["json"]["inferenceUrl"] = funcs_info["fastapi_echo"][
        "inferenceUrl"
    ]
    test_data_add["req"]["json"]["inferencePort"] = funcs_info["fastapi_echo"][
        "inferencePort"
    ]
    test_data_add["req"]["json"]["healthUri"] = funcs_info["fastapi_echo"]["healthUri"]
    logging.info(f"test_data_add: {test_data_add}")
    resp_add = session.request(**test_data_add["req"])
    APIValidation(resp_add, test_data_add["exp"]).validate_response()
    result_add = resp_add.json()
    func_id = result_add["function"]["id"]
    func_vers_id = result_add["function"]["versionId"]
    yield func_id, func_vers_id
    test_data_del = Tools.load_case_data(NVCF_DATA, "DELETE_FUNCTION")
    logging.info(f"Deleting function with function {func_id} and ID {func_vers_id}")
    test_data_del["req"]["path"] += f"/{func_id}/versions/{func_vers_id}"
    resp_del = session.request(**test_data_del["req"])
    APIValidation(resp_del, test_data_del["exp"]).validate_response()


@pytest.fixture(scope="function")
def get_deployment_data():
    test_data = Tools.load_case_data(NVCF_DATA, "DEPLOY_FUNCTION_L40S")
    test_data["req"]["json"]["deploymentSpecifications"][0]["gpu"] = "T10"
    test_data["req"]["json"]["deploymentSpecifications"][0]["instanceType"] = "g6.full"
    test_data["req"]["json"]["deploymentSpecifications"][0]["backend"] = "GFN"
    test_data["req"]["json"]["deploymentSpecifications"][0]["minInstances"] = 1
    test_data["req"]["json"]["deploymentSpecifications"][0]["maxInstances"] = 1
    return test_data


@pytest.fixture(scope="function")
def pre_check_available_function_version(
    session_nvcf_admin_sak, request, get_deployment_data
):
    """Check if a function with given name exists and return its details.

    Args:
        request: pytest request object containing the function name parameter
    Returns:
        dict: Function details if found, None otherwise
    """
    nvcfutils = NVCFUtils(session_nvcf_admin_sak)
    func_name = request.param
    func_info = nvcfutils.get_functions_info(expected_status_list=["ACTIVE", "INACTIVE"])
    func_info = func_info[func_name]

    if func_info["status"] == "INACTIVE":
        if nvcfutils.deploy_function_to_active(
            func_info["id"], func_info["versionId"], get_deployment_data
        ):
            logging.info(f"Deployed function {func_name} to active")
            yield func_info
    if func_info["status"] == "ACTIVE":
        yield func_info
    pytest.skip(f"Required function {func_name} not found")


@pytest.fixture(scope="function")
def new_helm_chart_function(session_nvcf_admin_sak):
    session_dict = session_nvcf_admin_sak
    nvcfutils = NVCFUtils(session_dict)
    func_id, func_vers_id = nvcfutils.create_function("inference_helm_chart")
    yield func_id, func_vers_id
    funcs_remove_info = {
        "func_id": func_id,
        "version_id": func_vers_id,
    }
    nvcfutils.delete_function(func_remove_list=[funcs_remove_info])


@pytest.fixture(scope="function")
def new_helm_chart_function_without_teardown(session_nvcf_admin_sak):
    session_dict = session_nvcf_admin_sak
    nvcfutils = NVCFUtils(session_dict)
    func_id, func_vers_id = nvcfutils.create_function("inference_helm_chart")
    func_name = nvcfutils.get_function_name_by_ids(func_id, func_vers_id)
    yield func_id, func_vers_id, func_name


@pytest.fixture(scope="function")
def new_ess_container_function(func_invoc_data, session_nvcf_admin_sak):
    api_template = Tools.load_case_data(func_invoc_data, "CREATE_ESS_FUNCTION")
    session_dict = session_nvcf_admin_sak
    session = session_dict["ngc"]
    api_template["req"]["json"]["name"] = Tools.add_timestamp(
        api_template["req"]["json"]["name"], "%m%d%H%M%S"
    )
    funcs_info = NVCFUtils(session_dict).get_functions_info()
    api_template["req"]["json"]["containerImage"] = funcs_info[
        "secrets-sample-ui-dailyrun"
    ]["containerImage"]
    api_template["req"]["json"]["inferenceUrl"] = funcs_info["secrets-sample-ui-dailyrun"][
        "inferenceUrl"
    ]

    response = session.request(**api_template["req"])

    APIValidation(response, api_template["exp"]).validate_response()
    logging.info(
        f'expected_secrets_name in fixture is {api_template["req"]["json"]["secrets"][0]["name"]}'
    )
    response_content = response.json()
    func_id, func_vers_id = (
        response_content["function"]["id"],
        response_content["function"]["versionId"],
    )
    yield func_id, func_vers_id

    funcs_remove_info = {
        "func_id": func_id,
        "version_id": func_vers_id,
    }
    NVCFUtils(session_dict).delete_function(func_remove_list=[funcs_remove_info])


@pytest.fixture(scope="function")
def new_gpu_comunication_function(session_nvcf_admin_sak):
    test_data_add = Tools.load_case_data(NVCF_DATA, "CREATE_FUNCTION")
    session_dict = session_nvcf_admin_sak
    session: APISession = session_dict["ngc"]
    funcs_info = NVCFUtils(session_dict).get_functions_info()
    test_data_add["req"]["json"]["name"] = Tools.add_timestamp(
        test_data_add["req"]["json"]["name"], "%m%d%H%M%S"
    )
    for item in ["inferenceUrl", "inferencePort", "healthUri", "containerImage"]:
        test_data_add["req"]["json"][item] = funcs_info["gpu_comunication"][item]
    resp_add = session.request(**test_data_add["req"])
    APIValidation(resp_add, test_data_add["exp"]).validate_response()
    result_add = resp_add.json()
    func_id = result_add["function"]["id"]
    func_vers_id = result_add["function"]["versionId"]
    yield func_id, func_vers_id
    test_data_del = Tools.load_case_data(NVCF_DATA, "DELETE_FUNCTION")
    logging.info(f"Deleting function with function {func_id} and ID {func_vers_id}")
    test_data_del["req"]["path"] += f"/{func_id}/versions/{func_vers_id}"
    resp_del = session.request(**test_data_del["req"])
    APIValidation(resp_del, test_data_del["exp"]).validate_response()


@pytest.fixture(scope="function")
def new_gRPC_function(session_nvcf_admin_sak):
    test_data_add = Tools.load_case_data(NVCF_DATA, "CREATE_FUNCTION")
    session_dict = session_nvcf_admin_sak
    session: APISession = session_dict["ngc"]
    funcs_info = NVCFUtils(session_dict).get_functions_info()
    test_data_add["req"]["json"]["name"] = Tools.add_timestamp(
        test_data_add["req"]["json"]["name"], "%m%d%H%M%S"
    )
    test_data_add["req"]["json"]["containerImage"] = funcs_info["grpc_echo"][
        "containerImage"
    ]
    test_data_add["req"]["json"]["inferenceUrl"] = funcs_info["grpc_echo"]["inferenceUrl"]
    test_data_add["req"]["json"]["inferencePort"] = funcs_info["grpc_echo"]["inferencePort"]
    resp_add = session.request(**test_data_add["req"])
    APIValidation(resp_add, test_data_add["exp"]).validate_response()
    result_add = resp_add.json()
    func_id = result_add["function"]["id"]
    func_ver_id = result_add["function"]["versionId"]
    logging.info(f"Create gRPC function with function {func_id} and ID {func_ver_id}")
    yield func_id, func_ver_id
    test_data_del = Tools.load_case_data(NVCF_DATA, "DELETE_FUNCTION")
    logging.info(f"Deleting function with function {func_id} and ID {func_ver_id}")
    test_data_del["req"]["path"] += f"/{func_id}/versions/{func_ver_id}"
    resp_del = session.request(**test_data_del["req"])
    APIValidation(resp_del, test_data_del["exp"]).validate_response()


@pytest.fixture(scope="function")
def new_container_function_and_deploying(func_invoc_data, session_nvcf_admin_sak):
    session_dict = session_nvcf_admin_sak
    nvcfutils = NVCFUtils(session_dict)
    func_id, func_vers_id = nvcfutils.create_function("pytriton_echo")
    logging.info("Start to deploy function...")
    if CURRENT_ENV == "staging":
        deploy_data = func_invoc_data["DEPLOY_FUNCTION_L40S_STG"]
    else:
        deploy_data = func_invoc_data["DEPLOY_FUNCTION_T10"]
    deploy_data["req"]["path"] += f"{func_id}/versions/{func_vers_id}"
    session: APISession = session_dict["ngc"]
    resp = session.request(**deploy_data["req"])
    APIValidation(resp, deploy_data["exp"]).validate_response()

    yield func_id, func_vers_id
    funcs_remove_info = {
        "func_id": func_id,
        "version_id": func_vers_id,
    }
    nvcfutils.delete_function(func_remove_list=[funcs_remove_info])


@pytest.fixture(scope="function")
def new_container_function_and_deploying_on_non_gfn(
    func_invoc_data, session_nvcf_admin_sak
):
    session_dict = session_nvcf_admin_sak
    nvcfutils = NVCFUtils(session_dict)
    func_id, func_vers_id = nvcfutils.create_function("pytriton_echo")
    logging.info("Start to deploy function...")
    if CURRENT_ENV == "staging":
        deploy_data = func_invoc_data["DEPLOY_FUNCTION_H100_NON_GFN_STG"]
    else:
        deploy_data = func_invoc_data["DEPLOY_FUNCTION_H100_NON_GFN"]
    deploy_data["req"]["path"] += f"{func_id}/versions/{func_vers_id}"
    session: APISession = session_dict["ngc"]
    resp = session.request(**deploy_data["req"])
    APIValidation(resp, deploy_data["exp"]).validate_response()

    yield func_id, func_vers_id
    funcs_remove_info = {
        "func_id": func_id,
        "version_id": func_vers_id,
    }
    nvcfutils.delete_function(func_remove_list=[funcs_remove_info])


@pytest.fixture(scope="function")
def new_helm_chart_function_and_deploying(func_invoc_data, session_nvcf_admin_sak):
    session_dict = session_nvcf_admin_sak
    nvcfutils = NVCFUtils(session_dict)
    func_id, func_vers_id = nvcfutils.create_function("inference_helm_chart")
    logging.info("Start to deploy function...")
    if CURRENT_ENV == "staging":
        deploy_data = func_invoc_data["DEPLOY_FUNCTION_L40S_STG"]
    else:
        deploy_data = func_invoc_data["DEPLOY_FUNCTION_T10"]
    deploy_data["req"]["path"] += f"{func_id}/versions/{func_vers_id}"
    session: APISession = session_dict["ngc"]
    resp = session.request(**deploy_data["req"])
    APIValidation(resp, deploy_data["exp"]).validate_response()
    yield func_id, func_vers_id
    funcs_remove_info = {
        "func_id": func_id,
        "version_id": func_vers_id,
    }
    nvcfutils.delete_function(func_remove_list=[funcs_remove_info])


@pytest.fixture(scope="function")
def new_helm_chart_function_and_deploying_to_non_gfn(
    func_invoc_data, session_nvcf_admin_sak
):
    session_dict = session_nvcf_admin_sak
    nvcfutils = NVCFUtils(session_dict)
    func_id, func_vers_id = nvcfutils.create_function("inference_helm_chart")
    logging.info("Start to deploy function...")
    if CURRENT_ENV == "staging":
        deploy_data = func_invoc_data["DEPLOY_FUNCTION_H100_NON_GFN_STG"]
    else:
        deploy_data = func_invoc_data["DEPLOY_FUNCTION_H100_NON_GFN"]
    deploy_data["req"]["path"] += f"{func_id}/versions/{func_vers_id}"
    session: APISession = session_dict["ngc"]
    resp = session.request(**deploy_data["req"])
    APIValidation(resp, deploy_data["exp"]).validate_response()
    yield func_id, func_vers_id
    funcs_remove_info = {
        "func_id": func_id,
        "version_id": func_vers_id,
    }
    nvcfutils.delete_function(func_remove_list=[funcs_remove_info])


@pytest.fixture(scope="session")
def get_funcs_info(session_nvcf_admin_sak):
    NVCF_Util = NVCFUtils(session_nvcf_admin_sak)
    yield NVCF_Util.get_functions_info()


@pytest.fixture(scope="function")
def get_service_keys_with_cookies(
    page: Page, func_invoc_data, session_nvcf_admin_sak, running_config
):
    """
    Function-scoped fixture that returns a callable function to make fresh API calls.
    Can be called multiple times in the same test for fresh data each time.
    """

    def _get_keys():
        test_data_add = func_invoc_data["GET_SERVICE_KEYS"]
        test_data_add["req"]["path"] += "/type/SERVICE_KEY"
        session: APISession = session_nvcf_admin_sak["ngc_org"]
        try:
            browser_cookies = page.context.cookies()
            logging.info(
                f"Successfully extracted {len(browser_cookies)} cookies from browser"
            )
        except Exception as e:
            logging.error(f"Error extracting cookies from browser: {str(e)}")
            return None

        cookie_str = ""
        for cookie in browser_cookies:
            logging.info(cookie)
            if (
                cookie["name"] == "CSID"
                or cookie["name"] == "SID"
                or cookie["name"] == "SSID"
            ):
                cookie_str = f'{cookie["name"]}={cookie["value"]}'
        test_data_add["req"]["headers"]["cookie"] = cookie_str
        resp = session.request(**test_data_add["req"])
        test_data_add["req"]["path"] = "/keys"
        APIValidation(resp, test_data_add["exp"]).validate_response()
        return resp.json()

    return _get_keys


@pytest.fixture(scope="function")
def get_service_keys_with_cookies_gmail_org(page: Page, func_invoc_data, running_config):
    """
    Function-scoped fixture that returns a callable function to make fresh API calls.
    Can be called multiple times in the same test for fresh data each time.
    """

    def _get_keys():
        test_data_add = func_invoc_data["GET_SERVICE_KEYS"]
        test_data_add["req"]["path"] += "/type/SERVICE_KEY"
        session: APISession = APISession(
            api_host=running_config["api_host_ngc_org"].replace(
                CURRENT_ORG, CURRENT_ORG_SECONDARY
            ),
            api_key=running_config["nvcf_gmail_owner"]["api_key"],
        )
        try:
            browser_cookies = page.context.cookies()
            logging.info(
                f"Successfully extracted {len(browser_cookies)} cookies from browser"
            )
        except Exception as e:
            logging.error(f"Error extracting cookies from browser: {str(e)}")
            return None

        cookie_str = ""
        for cookie in browser_cookies:
            logging.info(cookie)
            if (
                cookie["name"] == "CSID"
                or cookie["name"] == "SID"
                or cookie["name"] == "SSID"
            ):
                cookie_str = f'{cookie["name"]}={cookie["value"]}'
        test_data_add["req"]["headers"]["cookie"] = cookie_str
        resp = session.request(**test_data_add["req"])
        test_data_add["req"]["path"] = "/keys"
        APIValidation(resp, test_data_add["exp"]).validate_response()
        return resp.json()

    return _get_keys


@pytest.fixture(scope="function")
def get_service_keys_with_cookies_gmail_org_by_id(
    page: Page,
    func_invoc_data,
    running_config,
    key_id: str = None,
    verify_result: bool = True,
):
    """
    Function-scoped fixture that returns a callable function to make fresh API calls.
    Can be called multiple times in the same test for fresh data each time.
    """

    def _get_keys(key_id: str):
        test_data_add = func_invoc_data["GET_SERVICE_KEYS"]
        test_data_add["req"]["path"] += f"/{key_id}/type/SERVICE_KEY"
        session: APISession = APISession(
            api_host=running_config["api_host_ngc_org"].replace(
                CURRENT_ORG, CURRENT_ORG_SECONDARY
            ),
            api_key=running_config["nvcf_gmail_owner"]["api_key"],
        )
        try:
            browser_cookies = page.context.cookies()
            logging.info(
                f"Successfully extracted {len(browser_cookies)} cookies from browser"
            )
        except Exception as e:
            logging.error(f"Error extracting cookies from browser: {str(e)}")
            return None

        cookie_str = ""
        for cookie in browser_cookies:
            logging.info(cookie)
            if (
                cookie["name"] == "CSID"
                or cookie["name"] == "SID"
                or cookie["name"] == "SSID"
            ):
                cookie_str = f'{cookie["name"]}={cookie["value"]}'
        test_data_add["req"]["headers"]["cookie"] = cookie_str
        resp = session.request(**test_data_add["req"])
        test_data_add["req"]["path"] = "/keys"
        if verify_result is True:
            APIValidation(resp, test_data_add["exp"]).validate_response()
        return resp.json()

    return _get_keys


@pytest.fixture(scope="function")
def delete_service_keys_with_cookies(
    page: Page,
    func_invoc_data,
    session_nvcf_admin_sak,
    running_config,
    key_id: str = None,
    verify_result: bool = True,
):
    """
    Function-scoped fixture that returns a callable function to delete service keys.
    Can be called multiple times in the same test to delete different keys.
    """

    def _delete_key(key_id: str):
        test_data_add = func_invoc_data["DELETE_SERVICE_KEYS"]
        test_data_add["req"]["path"] += f"/{key_id}/type/SERVICE_KEY"
        session: APISession = session_nvcf_admin_sak["ngc_org"]
        try:
            browser_cookies = page.context.cookies()
            logging.info(
                f"Successfully extracted {len(browser_cookies)} cookies from browser"
            )
        except Exception as e:
            logging.error(f"Error extracting cookies from browser: {str(e)}")
            return None

        cookie_str = ""
        for cookie in browser_cookies:
            logging.info(cookie)
            if (
                cookie["name"] == "CSID"
                or cookie["name"] == "SID"
                or cookie["name"] == "SSID"
            ):
                cookie_str = f'{cookie["name"]}={cookie["value"]}'
        test_data_add["req"]["headers"]["cookie"] = cookie_str
        resp = session.request(**test_data_add["req"])
        test_data_add["req"]["path"] = "/keys"
        if verify_result is True:
            APIValidation(resp, test_data_add["exp"]).validate_response()
        return resp.json()

    return _delete_key


@pytest.fixture(scope="function")
def create_service_keys_with_cookies(
    page: Page,
    func_invoc_data,
    session_nvcf_admin_sak,
    running_config,
    verify_result: bool = True,
):
    """
    Function-scoped fixture that returns a callable function to make fresh API calls.
    Can be called multiple times in the same test for fresh data each time.
    """

    def _create_keys():
        test_data_add = func_invoc_data["CREATE_SERVICE_KEYS"]
        test_data_add["req"]["path"] += "/type/SERVICE_KEY"
        session: APISession = session_nvcf_admin_sak["ngc_org"]
        try:
            browser_cookies = page.context.cookies()
            logging.info(
                f"Successfully extracted {len(browser_cookies)} cookies from browser"
            )
        except Exception as e:
            logging.error(f"Error extracting cookies from browser: {str(e)}")
            return None

        cookie_str = ""
        for cookie in browser_cookies:
            logging.info(cookie)
            if (
                cookie["name"] == "CSID"
                or cookie["name"] == "SID"
                or cookie["name"] == "SSID"
            ):
                cookie_str = f'{cookie["name"]}={cookie["value"]}'
        test_data_add["req"]["headers"]["cookie"] = cookie_str
        test_data_add["req"]["json"]["name"] = add_timestamp("auto_limitation_")
        test_data_add["req"]["json"]["expiryDate"] = (
            datetime.now(timezone.utc) + timedelta(seconds=10)
        ).strftime("%Y-%m-%dT%H:%M:%SZ")
        resp = session.request(**test_data_add["req"])
        test_data_add["req"]["path"] = "/keys"
        if verify_result is True:
            APIValidation(resp, test_data_add["exp"]).validate_response()
        return resp.json()["apiKey"]

    return _create_keys


@pytest.fixture(scope="function")
def get_personal_keys_with_cookies(
    page: Page, func_invoc_data, session_nvcf_admin_sak, running_config
):
    """
    Function-scoped fixture that returns a callable function to make fresh API calls.
    Can be called multiple times in the same test for fresh data each time.
    """

    def _get_keys():
        test_data_add = func_invoc_data["GET_SERVICE_KEYS"]
        test_data_add["req"]["path"] += "/type/PERSONAL_KEY"
        session: APISession = session_nvcf_admin_sak["ngc_org"]
        try:
            browser_cookies = page.context.cookies()
            logging.info(
                f"Successfully extracted {len(browser_cookies)} cookies from browser"
            )
        except Exception as e:
            logging.error(f"Error extracting cookies from browser: {str(e)}")
            return None

        cookie_str = ""
        for cookie in browser_cookies:
            logging.info(cookie)
            if (
                cookie["name"] == "CSID"
                or cookie["name"] == "SID"
                or cookie["name"] == "SSID"
            ):
                cookie_str = f'{cookie["name"]}={cookie["value"]}'
        test_data_add["req"]["headers"]["cookie"] = cookie_str
        test_data_add["req"]["headers"]["Accept-Encoding"] = "gzip, deflate, br"
        resp = session.request(**test_data_add["req"])
        test_data_add["req"]["path"] = "/keys"
        APIValidation(resp, test_data_add["exp"]).validate_response()
        return resp.json()

    return _get_keys


@pytest.fixture(scope="function")
def delete_personal_keys_with_cookies(
    page: Page,
    func_invoc_data,
    session_nvcf_admin_sak,
    running_config,
    key_id: str = None,
    verify_result: bool = True,
):
    """
    Function-scoped fixture that returns a callable function to delete service keys.
    Can be called multiple times in the same test to delete different keys.
    """

    def _delete_key(key_id: str):
        test_data_add = func_invoc_data["DELETE_SERVICE_KEYS"]
        test_data_add["req"]["path"] += f"/{key_id}/type/PERSONAL_KEY"
        session: APISession = session_nvcf_admin_sak["ngc_org"]
        try:
            browser_cookies = page.context.cookies()
            logging.info(
                f"Successfully extracted {len(browser_cookies)} cookies from browser"
            )
        except Exception as e:
            logging.error(f"Error extracting cookies from browser: {str(e)}")
            return None

        cookie_str = ""
        for cookie in browser_cookies:
            logging.info(cookie)
            if (
                cookie["name"] == "CSID"
                or cookie["name"] == "SID"
                or cookie["name"] == "SSID"
            ):
                cookie_str = f'{cookie["name"]}={cookie["value"]}'
        test_data_add["req"]["headers"]["cookie"] = cookie_str
        resp = session.request(**test_data_add["req"])
        test_data_add["req"]["path"] = "/keys"
        if verify_result is True:
            APIValidation(resp, test_data_add["exp"]).validate_response()
        return resp.json()

    return _delete_key


@pytest.fixture(scope="function")
def get_container_gpu_types_with_cookies(
    page: Page, func_invoc_data, session_nvcf_admin_sak, running_config
):
    """
    Function-scoped fixture that returns a callable function to make fresh API calls.
    Can be called multiple times in the same test for fresh data each time.
    """

    def _get_gpus():
        test_data_add = copy.deepcopy(func_invoc_data["GET_GPU_TYPES"])
        if CURRENT_ENV == "staging" or CURRENT_ENV == "production":
            test_data_add["req"]["path"] = test_data_add["req"]["path"].replace(
                "/available", ""
            )

        test_data_add["req"]["path"] += "/gpus?instanceTypeUsage=CONTAINER"
        session: APISession = session_nvcf_admin_sak["ngc_gpus"]
        try:
            browser_cookies = page.context.cookies()
            logging.info(
                f"Successfully extracted {len(browser_cookies)} cookies from browser"
            )
        except Exception as e:
            logging.error(f"Error extracting cookies from browser: {str(e)}")
            return None

        cookie_str = ""
        for cookie in browser_cookies:
            logging.info(cookie)
            if (
                cookie["name"] == "CSID"
                or cookie["name"] == "SID"
                or cookie["name"] == "SSID"
            ):
                cookie_str = f'{cookie["name"]}={cookie["value"]}'
        test_data_add["req"]["headers"]["cookie"] = cookie_str
        test_data_add["req"]["headers"]["Accept-Encoding"] = "gzip, deflate, br"
        resp = session.request(**test_data_add["req"])
        APIValidation(resp, test_data_add["exp"]).validate_response()
        return resp.json()

    return _get_gpus


@pytest.fixture(scope="function")
def get_container_instance_types_with_cookies(
    page: Page, func_invoc_data, session_nvcf_admin_sak, running_config
):
    """
    Function-scoped fixture that returns a callable function to make fresh API calls.
    Can be called multiple times in the same test for fresh data each time.
    """

    def _get_instance_types():
        test_data_add = copy.deepcopy(func_invoc_data["GET_GPU_TYPES"])
        if CURRENT_ENV == "staging" or CURRENT_ENV == "production":
            test_data_add["req"]["path"] = test_data_add["req"]["path"].replace(
                "/available", ""
            )
        test_data_add["req"]["path"] += "/instanceTypes?instanceTypeUsage=CONTAINER"
        session: APISession = session_nvcf_admin_sak["ngc_gpus"]
        try:
            browser_cookies = page.context.cookies()
            logging.info(
                f"Successfully extracted {len(browser_cookies)} cookies from browser"
            )
        except Exception as e:
            logging.error(f"Error extracting cookies from browser: {str(e)}")
            return None

        cookie_str = ""
        for cookie in browser_cookies:
            logging.info(cookie)
            if (
                cookie["name"] == "CSID"
                or cookie["name"] == "SID"
                or cookie["name"] == "SSID"
            ):
                cookie_str = f'{cookie["name"]}={cookie["value"]}'
        test_data_add["req"]["headers"]["cookie"] = cookie_str
        test_data_add["req"]["headers"]["Accept-Encoding"] = "gzip, deflate, br"
        resp = session.request(**test_data_add["req"])
        APIValidation(resp, test_data_add["exp"]).validate_response()
        return resp.json()

    return _get_instance_types


@pytest.fixture(scope="function")
def create_service_keys_with_cookies_gmail_org(
    page: Page,
    func_invoc_data,
    running_config,
    verify_result: bool = True,
):
    """
    Function-scoped fixture that returns a callable function to make fresh API calls.
    Can be called multiple times in the same test for fresh data each time.
    """

    def _create_keys():
        test_data_add = func_invoc_data["CREATE_SERVICE_KEYS"]
        test_data_add["req"]["path"] += "/type/SERVICE_KEY"
        session: APISession = APISession(
            api_host=running_config["api_host_ngc_org"].replace(
                CURRENT_ORG, CURRENT_ORG_SECONDARY
            ),
            api_key=running_config["nvcf_gmail_owner"]["api_key"],
        )
        try:
            browser_cookies = page.context.cookies()
            logging.info(
                f"Successfully extracted {len(browser_cookies)} cookies from browser"
            )
        except Exception as e:
            logging.error(f"Error extracting cookies from browser: {str(e)}")
            return None

        cookie_str = ""
        for cookie in browser_cookies:
            logging.info(cookie)
            if (
                cookie["name"] == "CSID"
                or cookie["name"] == "SID"
                or cookie["name"] == "SSID"
            ):
                cookie_str = f'{cookie["name"]}={cookie["value"]}'
        test_data_add["req"]["headers"]["cookie"] = cookie_str
        test_data_add["req"]["json"]["name"] = add_timestamp("auto_limitation_")
        test_data_add["req"]["json"]["expiryDate"] = (
            datetime.now(timezone.utc) + timedelta(seconds=10)
        ).strftime("%Y-%m-%dT%H:%M:%SZ")
        resp = session.request(**test_data_add["req"])
        test_data_add["req"]["path"] = "/keys"
        if verify_result is True:
            APIValidation(resp, test_data_add["exp"]).validate_response()
        return resp.json()["apiKey"]

    return _create_keys


@pytest.fixture(scope="function")
def delete_service_keys_with_cookies_gmail_org(
    page: Page,
    func_invoc_data,
    running_config,
    key_id: str = None,
    verify_result: bool = True,
):
    """
    Function-scoped fixture that returns a callable function to delete service keys.
    Can be called multiple times in the same test to delete different keys.
    """

    def _delete_key(key_id: str):
        test_data_add = func_invoc_data["DELETE_SERVICE_KEYS"]
        test_data_add["req"]["path"] += f"/{key_id}/type/SERVICE_KEY"
        session: APISession = APISession(
            api_host=running_config["api_host_ngc_org"].replace(
                CURRENT_ORG, CURRENT_ORG_SECONDARY
            ),
            api_key=running_config["nvcf_gmail_owner"]["api_key"],
        )
        try:
            browser_cookies = page.context.cookies()
            logging.info(
                f"Successfully extracted {len(browser_cookies)} cookies from browser"
            )
        except Exception as e:
            logging.error(f"Error extracting cookies from browser: {str(e)}")
            return None

        cookie_str = ""
        for cookie in browser_cookies:
            logging.info(cookie)
            if (
                cookie["name"] == "CSID"
                or cookie["name"] == "SID"
                or cookie["name"] == "SSID"
            ):
                cookie_str = f'{cookie["name"]}={cookie["value"]}'
        test_data_add["req"]["headers"]["cookie"] = cookie_str
        resp = session.request(**test_data_add["req"])
        test_data_add["req"]["path"] = "/keys"
        if verify_result is True:
            APIValidation(resp, test_data_add["exp"]).validate_response()
        return resp.json()

    return _delete_key
