# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
default_stages:
  - "pre-commit"
default_install_hook_types:
  - "pre-commit"
  - "commit-msg"

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: check-yaml
      - id: check-toml
      - id: requirements-txt-fixer
      - id: detect-private-key

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.9
    hooks:
      - id: ruff
        args: ["--fix","--show-source"]
      - id: ruff-format

  - repo: https://github.com/commit-check/commit-check
    rev: v0.7.4
    hooks: # support hooks
      - id: check-message  # it requires hook prepare-commit-msg
        stages:
          - commit-msg
      - id: check-author-name
      - id: check-author-email
