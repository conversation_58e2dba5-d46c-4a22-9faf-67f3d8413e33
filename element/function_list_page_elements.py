from element.elements import (
    SideBarElements,
    NavigationBarElements,
    PaginationTableElements,
)
from config.consts import ELE_CONF_PATH_CF


class FunctionListPageSideBarElements(SideBarElements):
    """
    All the xpaths of Side bar elements on Function List Page

    Note:
        prefix = //div[@data-testid='kui-app-shell-sidebar']
        The prefix is the parent path of all the elements
        And the prefix will automatically add to all the elements
    """

    def __init__(self):
        super().__init__(ELE_CONF_PATH_CF, elem_chain=["SideBar"])


class FunctionListPageNavigationBarElements(NavigationBarElements):
    """
    All the xpath of navigation bar elements fon Function List Page

    Note:
        prefix = //div[@class='ngc-layout-header']
        The prefix is the parent path of all the elements
        And the prefix will automatically add to all the elements
    """

    def __init__(self):
        super().__init__(
            ELE_CONF_PATH_CF, elem_chain=["NavigationBar", "Functions", "NavigationBar"]
        )


class FunctionListPageSwitchBarElements(SideBarElements):
    """
    All the xpaths of switch bar elements on Function List Page

    Note:
        prefix = //div[@data-testid='kui-tab-root']
        The prefix is the parent path of all the elements
        And the prefix will automatically add to all the elements
    """

    def __init__(self):
        super().__init__(ELE_CONF_PATH_CF, elem_chain=["Functions", "SwitchBar"])


class FunctionListPageFunctionPaginationElements(PaginationTableElements):
    """All the xpaths of function pagination elements on Function List Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_CF, elem_chain=["Functions", "Pagination"])
