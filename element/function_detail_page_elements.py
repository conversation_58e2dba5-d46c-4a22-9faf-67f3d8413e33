from element.elements import (
    NavigationBarElements,
    PaginationTableElements,
    StatisticalTableElements,
    CollapsedTableElements,
)
from config.consts import ELE_CONF_PATH_CF
from pathlib import Path


class FunctionDetailPageNavigationBarElements(NavigationBarElements):
    """All the xpath of navigation bar elements for Function Detail Page.

    Note:
        prefix = //div[@class='ngc-layout-header']
        The prefix is the parent path of all the elements
        And the prefix will automatically add to all the elements
    """

    def __init__(self):
        super().__init__(
            element_path=ELE_CONF_PATH_CF,
            elem_chain=["Functions", "Detail", "NavigationBar"],
        )


class FunctionDetailPageStatisticsElements(StatisticalTableElements):
    """All the xpath of statistics elements for Function Detail Page."""

    def __init__(
        self,
        element_path: Path = ELE_CONF_PATH_CF,
        elem_chain: list = None,
    ):
        _elem_chain = ["Functions", "Detail", "Statistics"]
        if elem_chain:
            _elem_chain.extend(elem_chain)
        super().__init__(element_path, _elem_chain)


class FunctionDetailPageAgrInvoElements(FunctionDetailPageStatisticsElements):
    """All the xpath of aggregation invocations stastical table's elements for Function Detail Page."""

    def __init__(self):
        super().__init__(elem_chain=["AggregatedInvocations"])


class FunctionDetailPageAgrInsElements(FunctionDetailPageStatisticsElements):
    """All the xpath of aggregation invocations stastical table's elements for Function Detail Page."""

    def __init__(self):
        super().__init__(elem_chain=["AggregatedInstances"])


class FunctionDetailPagePaginationElements(PaginationTableElements):
    """All the xpath of pagination elements for Function Detail Page."""

    def __init__(self):
        super().__init__(
            element_path=ELE_CONF_PATH_CF, elem_chain=["Functions", "Detail", "Pagination"]
        )


class FunctionDetailPageCollapsedTableElements(CollapsedTableElements):
    """All the xpath of collapsed table elements for Function Detail Page."""

    def __init__(
        self,
        elem_chain: list = None,
    ):
        _elem_chain = ["Functions", "Detail", "CollapsedTable"]
        if elem_chain:
            _elem_chain.extend(elem_chain)
        super().__init__(element_path=ELE_CONF_PATH_CF, elem_chain=_elem_chain)


class FunctionsDetailPageBasicDetailsElements(FunctionDetailPageCollapsedTableElements):
    """All the xpath of basic details elements for Function Detail Page."""

    def __init__(self):
        super().__init__(elem_chain=["BasicDetails"])


class FunctionsDetailPageRuntimeDetailsElements(FunctionDetailPageCollapsedTableElements):
    """All the xpath of runtime details elements for Function Detail Page."""

    def __init__(self):
        super().__init__(elem_chain=["RuntimeDetails"])


class FunctionsDetailPageModelDetailsElements(FunctionDetailPageCollapsedTableElements):
    """All the xpath of model details elements for Function Detail Page."""

    def __init__(self):
        super().__init__(elem_chain=["ModelDetails"])


class FunctionsDetailPageEnvironmentVariablesElements(
    FunctionDetailPageCollapsedTableElements
):
    """All the xpath of environment variables elements for Function Detail Page."""

    def __init__(self):
        super().__init__(elem_chain=["EnvironmentVariables"])
