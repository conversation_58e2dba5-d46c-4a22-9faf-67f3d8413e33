from element.elements import (
    FormElements,
    PageMiscElements,
    DropDownSelectorElements,
)
from enum import Enum
from config.consts import ELE_CONF_PATH_TASKS


class TasksCreatePageMiscElements(PageMiscElements):
    """All the xpaths of page MISCs elements on task create Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_TASKS, elem_chain=["Tasks", "CreatePage"])


class CreateTaskFormElements(FormElements):
    """All the xpaths of elements on task create Page."""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_TASKS, elem_chain=["CreateTasks", "Form"])


class TasksCreatePageMaximumRuntimeDurationDropDownSelectorElements(
    DropDownSelectorElements
):
    """All the xpaths of function name selector elements on task create Page"""

    def __init__(self):
        super().__init__(<PERSON><PERSON>_CONF_PATH_TASKS, elem_chain=["CreateTasks", "Form"])


class TasksCreatePageMaximumQueuedDurationDropDownSelectorElements(
    DropDownSelectorElements
):
    """All the xpaths of function name selector elements on task create Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_TASKS, elem_chain=["CreateTasks", "Form"])


class TasksCreatePageTerminationGracePeriodDropDownSelectorElements(
    DropDownSelectorElements
):
    """All the xpaths of function name selector elements on task create Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_TASKS, elem_chain=["CreateTasks", "Form"])


class TasksCreatePageKeyExpirationDropDownSelectorElements(DropDownSelectorElements):
    """All the xpaths of function name selector elements on task create Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_TASKS, elem_chain=["CreateTasks", "Form"])


class TaskCreateOptionENUM(Enum):
    CREATTASKSUCCESS = "CreateTaskSuccessfully"
    NONEFORMAXRUNTIMEDURATIONGFN = "ChooseNoneforMaxruntimedurationonGFN"
    RUNTIMELARGERTHAN8 = "chooselargerthan8hoursformaxRuntimeDurationonGFN"
    GRACELARGERTHANRUNTIME = (
        "SetTerminationGracePeriodDurationlargerthanmaxRuntimeDurationonGFN"
    )
    WITHOUTPR_OR_EXPIRED = "CreateTaskWithoutPROrExpired"
    NEGATIVE_MODEL_NAME = "NegativeModelName"
    HELM_REVAL_SERVICE = "HelmRevalService"
