from element.elements import (
    NavigationBarElements,
    CollapsedChartElements,
    SwitchBarElements,
    TabElements,
    CollapsedTableElements,
    DropDownSelectorElements,
    SearchBarElements,
    PaginationTableElements,
    ActionMenuElements,
)
from config.consts import ELE_CONF_PATH_CF


class FunctionVersionDetailPageNavigationBarElements(NavigationBarElements):
    """All the xpath of navigation bar elements for Function Version Detail Page.

    Note:
        prefix = //div[@class='ngc-layout-header']
        The prefix is the parent path of all the elements
        And the prefix will automatically add to all the elements
    """

    def __init__(self):
        super().__init__(
            element_path=ELE_CONF_PATH_CF,
            elem_chain=["Functions", "VerDetail", "NavigationBar"],
        )


class FunctionVerDetailPageCollapsedChartElements(CollapsedChartElements):
    """All the xpath of collapsed chart elements for Function Version Detail Page."""

    def __init__(
        self,
        elem_chain: list = None,
    ):
        _elem_chain = ["Functions", "VerDetail", "CollapsedChart"]
        if elem_chain:
            _elem_chain.extend(elem_chain)
        super().__init__(element_path=ELE_CONF_PATH_CF, elem_chain=_elem_chain)


class FunctionsVerDetailPageInvocActAndQueDpthElements(
    FunctionVerDetailPageCollapsedChartElements
):
    """All the xpath of Invocation Activity and Queue Depth elements for Function Version Detail Page."""

    def __init__(self):
        super().__init__(elem_chain=["InvocActAndQueDpth"])


class FunctionsVerDetailPageAvgInferTimeElements(
    FunctionVerDetailPageCollapsedChartElements
):
    """All the xpath of Average Inference Time elements for Function Version Detail Page."""

    def __init__(self):
        super().__init__(elem_chain=["AvgInferTime"])


class FunctionsVerDetailPageInstancesElements(FunctionVerDetailPageCollapsedChartElements):
    """All the xpath of Instances elements for Function Version Detail Page."""

    def __init__(self):
        super().__init__(elem_chain=["Instances"])


class FunctionsVerDetailPageSuccessRateElements(
    FunctionVerDetailPageCollapsedChartElements
):
    """All the xpath of Success Rate for Function Version Detail Page."""

    def __init__(self):
        super().__init__(elem_chain=["SuccessRate"])


class FunctionsVerDetailPageSwitchBarElements(SwitchBarElements):
    """All the xpath of switch bar elements for Function Version Detail Page."""

    def __init__(self):
        super().__init__(
            element_path=ELE_CONF_PATH_CF,
            elem_chain=["Functions", "VerDetail", "SwitchBar"],
        )


class FunctionsVerDetailPageLogsTabElements(TabElements):
    """All the xpath of switch bar elements for Function Version Detail Page."""

    def __init__(self):
        super().__init__(
            element_path=ELE_CONF_PATH_CF,
            elem_chain=["Functions", "VerDetail", "LogsTab"],
        )


class FunctionsVerDetailPageDeploymentDetailsTabElements(CollapsedTableElements):
    """All the xpath of basic deployment details elements for Function Version Detail Page."""

    def __init__(
        self,
        elem_chain: list = None,
    ):
        _elem_chain = ["Functions", "VerDetail", "DeploymentDetailsTab"]
        if elem_chain:
            _elem_chain.extend(elem_chain)
        super().__init__(element_path=ELE_CONF_PATH_CF, elem_chain=_elem_chain)


class FunctionsDetailPageDeploymentBasicDetailsElements(
    FunctionsVerDetailPageDeploymentDetailsTabElements
):
    """All the xpath of basic deployment details elements for Function Version Detail Page."""

    def __init__(self):
        super().__init__(elem_chain=["Details"])


class FunctionsVerDetailPageLogsTimeDropDownSelectorElements(DropDownSelectorElements):
    """All the xpath of switch bar elements for Function Version Detail Page."""

    def __init__(self):
        super().__init__(
            element_path=ELE_CONF_PATH_CF,
            elem_chain=["Functions", "VerDetail", "LogsTimeDropDownSelector"],
        )


class FunctionsVerDetailPageLogsCountDropDownSelectorElements(DropDownSelectorElements):
    """All the xpath of switch bar elements for Function Version Detail Page."""

    def __init__(self):
        super().__init__(
            element_path=ELE_CONF_PATH_CF,
            elem_chain=["Functions", "VerDetail", "LogsCountDropDownSelector"],
        )


class FunctionsVerDetailPageLogsSearchBarElements(SearchBarElements):
    """All the xpath of elements for Logs tab search bar."""

    def __init__(self):
        super().__init__(
            element_path=ELE_CONF_PATH_CF,
            elem_chain=["Functions", "VerDetail", "LogsSearchBar"],
        )


class FunctionsVerDetailPageLogsPaginationTableElements(PaginationTableElements):
    """All the xpath of elements for Logs tab pagination table."""

    def __init__(self):
        super().__init__(
            element_path=ELE_CONF_PATH_CF,
            elem_chain=["Functions", "VerDetail", "LogsPaginationTable"],
        )


class FunctionVerDetailPageActionMenuElements(ActionMenuElements):
    """All the xpath of elements for Action menu."""

    def __init__(self):
        super().__init__(
            element_path=ELE_CONF_PATH_CF,
            elem_chain=["Functions", "VerDetail", "ActionMenu"],
        )


class FunctionVerDetailFunctionDetailsElements(CollapsedTableElements):
    """All the xpath of elements for Action menu."""

    def __init__(self):
        super().__init__(
            element_path=ELE_CONF_PATH_CF,
            elem_chain=["Functions", "VerDetail", "FunctionDetails"],
        )


class FunctionVerDetailOverviewElements(CollapsedTableElements):
    """All the xpath of elements for Action menu."""

    def __init__(
        self,
        elem_chain: list = None,
    ):
        _elem_chain = ["Functions", "VerDetail", "Overview"]
        if elem_chain:
            _elem_chain.extend(elem_chain)
        super().__init__(element_path=ELE_CONF_PATH_CF, elem_chain=_elem_chain)


class FunctionVerDetailOverviewBasicDetailElements(FunctionVerDetailOverviewElements):
    """All the xpath of elements for Action menu."""

    def __init__(self):
        super().__init__(elem_chain=["BasicDetail"])


class FunctionVerDetailPageInstanceTypeElements(CollapsedTableElements):
    """All the xpath of elements for Action menu."""

    def __init__(self):
        super().__init__(
            element_path=ELE_CONF_PATH_CF,
            elem_chain=["Functions", "VerDetail", "InstanceType"],
        )
