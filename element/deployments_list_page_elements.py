from element.elements import (
    FormElements,
    PageMiscElements,
    SearchBarElements,
    PaginationElements,
    DropDownSelectorElements,
)

from config.consts import ELE_CONF_PATH_CF, ELE_CONF_PATH_DP


class DeploymentsListPageMiscElements(PageMiscElements):
    """All the xpaths of page MISCs elements on Deployment list Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_DP, elem_chain=["Deployments", "ListPage"])


class DeploymentsListPageKeyExpirationDropDownSelectorElements(DropDownSelectorElements):
    """All the xpaths of function name selector elements on Deployments list Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_DP, elem_chain=["Deployments", "ListPage"])


class DeployVersionPageFormElements(FormElements):
    """All the xpaths of form elements on Deploy Version Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_CF, elem_chain=["DeployVersion", "Form"])


class DeploymentsListPageSearchBarElements(SearchBarElements):
    """All the xpaths of form elements of the search bar in Deployments list Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_DP, elem_chain=["Deployments", "ListPage"])


class DeploymentsListPagePaginationTableElements(PaginationElements):
    """All the xpaths of form elements of the pagination table in Deployments list Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_DP, elem_chain=["Deployments", "ListPage"])
