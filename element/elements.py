from abc import ABCMeta, abstractmethod
from pathlib import Path
from cloudia.utils.file_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Dict
import logging


class Elements:
    """Contains all the elements for a page.

    parameters:
    ----------
    element_path: `pathlib.Path`, optional
        The path to the elements file. Which supports `toml`, `yaml`, `json` file types.

    Usage:
    ------
    Here we will split the elements into two parts, one is optional elements, the other is backup elements.

    `_backup_elements`: The elements that are defined in the elements file.

    `_optional_elements`: Updated by calling `update(page_name)` to read the page elements of elements file.

    >>> elements = Elements(Path("path/to/elements/file"), elem_chain=["page_name"])
    # get elements from optional elements dict
    >>> elements.elemName
    >>> elements["elemName"]
    # get elements from backup elements dict, which is not encouraged to use and just for migration.
    >>> elements.get("page_name")

    **Example 1**

    Common
    >>> elements = Elements(Path("config/elements/elements.toml"), eleme_chain=["PersonalKey"])
    >>> elements.generate_personal_key_entry
    >>> elements["generate_personal_key_entry"]

    **Example 2**

    Inheritence [Recommended]
    >>> class SideBarElements (`Elements`):
    ...     def __init__(self):
    ...         super().__init__(Path("config/elements/elements.toml"), eleme_chain=["SideBar"])

    >>> elements = SideBarElements()
    >>> elements.elemName
    """

    handler_dict = {
        ".toml": TomlHandler,
        ".yaml": YamlHandler,
        ".json": JsonHandler,
    }

    def __init__(self, element_path: Path = None, elem_chain: list = None):
        self._optional_elements = dict()
        self._backup_elements = dict()
        if element_path:
            self.backup_elements_file = element_path
            file_suffix = self.backup_elements_file.suffix
            if file_suffix not in self.handler_dict:
                raise TypeError(
                    f"Unsupported file type: {self.backup_elements_file.suffix}\nSupported types are: {list(self.handler_dict.keys())}"
                )
            element_toml = self.handler_dict[file_suffix](self.backup_elements_file)
            self._backup_elements: Dict = element_toml.read()
        if elem_chain:
            for elem in elem_chain:
                self.update(elem)
        logging.debug(f"{self.__class__}: {self.all()}")

    def __getattr__(self, name):
        return self[name]

    def __getitem__(self, key):
        # find elements from optional elements dict
        if key in self._optional_elements:
            return self._optional_elements[key]
        # find page elements from backup elements dict
        elif key in self._backup_elements:
            page_elements = self._backup_elements.get(key)
            self._optional_elements.update(page_elements)
            return page_elements
        else:
            raise KeyError(
                f"'{self.__class__.__name__}' object has no '{key}', Please check if the elements file \
                has the right element defined\nAll the elements are: {self.all()}"
            )

    def get(self, page_name):
        """Get the elements from the backup elements dict.

        Which is not encouraged to use and just for migration.

        parameters:
        -----------
        page_name : `str`
            The page name to get the elements.

        Returns:
        --------
        page_elements : `dict`
            The elements for the page.
        """
        if self._backup_elements.get(page_name, None):
            page_elements = self._backup_elements.get(page_name)
            return page_elements
        else:
            raise TypeError(f"Elements for {page_name} is not defined")

    def update(self, page_name):
        """Update the optional elements dict with the elements of the page.

        parameters:
        -----------
        page_name : `str`
            The page name to update the elements.

        Returns:
        --------
        None
        """
        page_elements: Dict = self.get(page_name)
        for value in page_elements.values():
            if isinstance(value, dict):
                self._backup_elements.update(page_elements)
                return
        self._optional_elements.update(page_elements)

    def all(self):
        """Get the set of all elements.

        Returns:
        --------
        elements : `set`
            All the elements.
        """
        return set(self._optional_elements.keys())


class LocatorParentXPathMixIn:
    """Automatically adding parent xpath to all the elements.

    Usage:
    ------
    **example**

    class SideBarElements (`Elements`, `LocatorParentXPathMixIn`):
        def _get_parent_xpath(self):
            return "xpath=//div[@data-testid='kui-app-shell-sidebar']"
    """

    def set_parent_xpath(self):
        """Set the parent xpath to all the elements."""
        prefix = self._get_parent_xpath()

        for attr_name in vars(self):
            attr_value = getattr(self, attr_name)
            if isinstance(attr_value, str):
                setattr(self, attr_name, prefix + attr_value)

    @abstractmethod
    def _get_parent_xpath(self):
        pass


class SideBarElements(Elements, LocatorParentXPathMixIn):
    """Base elements for Side Bar.

    All elements will be prefixed with the parent xpath : `//div[@data-testid='kui-app-shell-sidebar']`.
    """

    def __init__(self, element_path: Path = None, elem_chain: list = None):
        self.set_parent_xpath()
        super().__init__(element_path, elem_chain)

    def _get_parent_xpath(self):
        return "xpath=//div[@data-testid='kui-app-shell-sidebar']"


class NavigationBarElements(Elements, LocatorParentXPathMixIn):
    """Base elements for Navigation Bar.

    All elements will be prefixed with the parent xpath : `//div[@class='ngc-layout-header']`.
    """

    def __init__(self, element_path: Path = None, elem_chain: list = None):
        self.set_parent_xpath()
        super().__init__(element_path, elem_chain)

    def _get_parent_xpath(self):
        return "xpath=//div[@class='ngc-layout-header']"


class SwitchBarElements(Elements, LocatorParentXPathMixIn):
    """Base elements for Switch Bar.

    All elements will be prefixed with the parent xpath : `//div[@data-testid='kui-tab-root']`.
    """

    def __init__(self, element_path: Path = None, elem_chain: list = None):
        self.set_parent_xpath()
        super().__init__(element_path, elem_chain)

    def _get_parent_xpath(self):
        return "xpath=//div[@data-testid='kui-tab-root']"


class TableElements(Elements, metaclass=ABCMeta):
    """Base elements for Table."""

    pass


class PaginationElements(Elements):
    """Base elements for Pagination."""

    pass


class PaginationTableElements(PaginationElements, TableElements):
    """Base elements for Pagination Table."""

    pass


class ActionMenuElements(Elements):
    """Base elements for Action Menu."""

    pass


class StatisticalTableElements(TableElements):
    """Base elements for Statistic."""

    pass


class FormElements(Elements, metaclass=ABCMeta):
    """Base elements for Form."""

    pass


class ChartElements(Elements, metaclass=ABCMeta):
    """Base elements for Chart."""

    pass


class TabElements(Elements, metaclass=ABCMeta):
    """Base elements for Tab."""

    pass


class DropDownSelectorElements(Elements, metaclass=ABCMeta):
    """Base elements for drop down selector."""

    pass


class SearchBarElements(Elements, metaclass=ABCMeta):
    """Base elements for Search Bar."""

    pass


class CollapsedChartElements(ChartElements, metaclass=ABCMeta):
    """Base elements for collapsed chart.

    Note:
    -----
    Collapsed chart is a chart that can be expanded to show more details.
    If your class interits this class, you should has these elements:
    - ``ExpandBtn``: The button to expand the chart.
    """

    pass


class CollapsedTableElements(TableElements, metaclass=ABCMeta):
    """Base elements for Collapsed Table.

    Note:
    -----
    Collapsed Table is a table that can be expanded to show more details.
    If your class interits this class, you should has these elements:
    - ``ExpandBtn``: The button to expand the table.
    - ``DetailAttr``: The attribute to show the details.
    - ``DetailVal``: The value of the attribute.
    """

    pass


class PageMiscElements(Elements, metaclass=ABCMeta):
    """Base elements for Page MISCs.

    Note:
    -----
    All the Elements that not proper to be a standalone class in a page defined here.
    """

    pass
