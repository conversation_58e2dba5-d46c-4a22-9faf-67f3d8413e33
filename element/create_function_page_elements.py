from element.elements import NavigationBarElements, FormElements
from config.consts import ELE_CONF_PATH_CF


class CreateFunctionFormElements(FormElements):
    """All the xpaths of elements on Create Function Page."""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_CF, elem_chain=["CreateFunction", "Form"])


class CreateFunctionPageNavigationBarElements(NavigationBarElements):
    """All the xpath of navigation bar elements for Create Function Page.

    Note:
        prefix = //div[@class='ngc-layout-header']
        The prefix is the parent path of all the elements
        And the prefix will automatically add to all the elements
    """

    def __init__(self):
        super().__init__(ELE_CONF_PATH_CF, elem_chain=["NavigationBar"])
