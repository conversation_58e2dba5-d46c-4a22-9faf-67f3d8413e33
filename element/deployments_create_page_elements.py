from element.elements import (
    FormElements,
    PageMiscElements,
    DropDownSelectorElements,
    PaginationTableElements,
)

from config.consts import ELE_CONF_PATH_DP


class DeploymentsCreatePageMiscElements(PageMiscElements):
    """All the xpaths of page MISCs elements on Deployment create Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_DP, elem_chain=["Deployments", "CreatePage"])


class DeploymentsCreatePageFunctionNameSelectorElements(DropDownSelectorElements):
    """All the xpaths of function name selector elements on Deployment create Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_DP, elem_chain=["Deployments", "CreatePage"])


class DeploymentsCreatePageFunctionVersionSelectorElements(DropDownSelectorElements):
    """All the xpaths of function version selector elements on Deployment create Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_DP, elem_chain=["Deployments", "CreatePage"])


class DeploymentsCreatePageInstanceTypePagePaginationElements(PaginationTableElements):
    """All the xpaths of function version selector elements on Deployment create Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_DP, elem_chain=["Deployments", "CreatePage"])


class DeploymentsCreatePageInstanceRegionsFilterDropDownSelectorElements(
    DropDownSelectorElements
):
    """All the xpaths of function version selector elements on Deployment create Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_DP, elem_chain=["Deployments", "CreatePage"])


class DeploymentsCreatePageInstanceClustersFilterDropDownSelectorElements(
    DropDownSelectorElements
):
    """All the xpaths of function version selector elements on Deployment create Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_DP, elem_chain=["Deployments", "CreatePage"])


class DeploymentsCreatePageInstanceAttributesFilterDropDownSelectorElements(
    DropDownSelectorElements
):
    """All the xpaths of function version selector elements on Deployment create Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_DP, elem_chain=["Deployments", "CreatePage"])


class DeploymentsCreatePageDeploymentSpecificationsFormElements(FormElements):
    """All the xpaths of function version selector elements on Deployment create Page"""

    def __init__(self):
        super().__init__(ELE_CONF_PATH_DP, elem_chain=["Deployments", "CreatePage"])
