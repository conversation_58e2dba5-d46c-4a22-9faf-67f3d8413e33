#!/usr/bin/env python3
import subprocess
import re
import os
import json
import argparse
from collections import defaultdict
from tabulate import tabulate
import sys
from datetime import datetime
from colorama import Fore, Style, init
from multiprocessing import Pool, cpu_count
import shutil
import logging
from typing import List
import asyncio
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError

# Initialize colorama
init()

# Set up logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")


def get_timestamped_dir(base_dir, prefix=""):
    """Create a timestamped directory name."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return os.path.join(base_dir, f"{prefix}{timestamp}" if prefix else timestamp)


# Default directories
TRIAGE_DIR = "triage"
ALLURE_BASE_DIR = os.path.join(TRIAGE_DIR, "allure")
DEFAULT_ALLURE_RESULTS = None  # Will be set during runtime with timestamp
DEFAULT_ALLURE_REPORT = None  # Will be set during runtime with timestamp
DEFAULT_OUTPUT_DIR = None  # Will be set during runtime with timestamp


async def extract_failed_tests_from_url_playwright(
    url: str, verbose: bool = False
) -> List[str]:
    """
    Extract failed test cases from a test result HTML page using Playwright.

    Args:
        url (str): The URL of the test result page
        verbose (bool): Whether to print verbose logs

    Returns:
        List[str]: List of failed test case names
    """
    failed_tests = []

    async with async_playwright() as p:
        try:
            if verbose:
                logging.info(f"Launching browser for URL: {url}")

            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()

            # Set longer timeout for page navigation
            page.set_default_timeout(60000)  # 60 seconds

            if verbose:
                logging.info(f"Navigating to: {url}")

            try:
                # Try different wait strategies
                await page.goto(url, wait_until="domcontentloaded", timeout=60000)
                if verbose:
                    logging.info("Page loaded successfully")
            except PlaywrightTimeoutError:
                if verbose:
                    logging.info("Initial load timed out, trying with load event...")
                await page.goto(url, wait_until="load", timeout=60000)

            # Wait a bit for dynamic content to load
            await page.wait_for_timeout(3000)

            if verbose:
                logging.info("Analyzing page for failed tests...")

            # Try to wait for the results table to load with multiple attempts
            table_found = False
            selectors_to_try = [
                "tbody.results-table-row",
                "tbody",
                "table",
                ".results-table-row",
                "[class*='results']",
                "[class*='failed']",
            ]

            for selector in selectors_to_try:
                try:
                    await page.wait_for_selector(selector, timeout=10000)
                    if verbose:
                        logging.info(f"Found page elements with selector: {selector}")
                    table_found = True
                    break
                except PlaywrightTimeoutError:
                    if verbose:
                        logging.info(f"Selector {selector} not found, trying next...")
                    continue

            if not table_found:
                if verbose:
                    logging.info("No table elements found, analyzing page content...")
                # Get page title and some content for debugging
                title = await page.title()
                if verbose:
                    logging.info(f"Page title: {title}")

                # Check if page has any content
                body_text = await page.locator("body").text_content()
                if body_text:
                    if verbose:
                        logging.info(f"Page has {len(body_text)} characters of content")
                else:
                    if verbose:
                        logging.info("Page appears to be empty")
                    await browser.close()
                    return []

            # Try the primary selector for failed tests
            try:
                failed_rows = await page.locator("tbody.results-table-row.failed").all()
                if verbose:
                    logging.info(
                        f"Found {len(failed_rows)} failed test cases with primary selector"
                    )

                for row in failed_rows:
                    # Get the collapsible row that contains the test information
                    test_row = row.locator("tr.collapsible").first

                    # Extract test information
                    test_id_cell = test_row.locator("td.col-testId")

                    # Get test name
                    test_name = (
                        await test_id_cell.text_content()
                        if await test_id_cell.count() > 0
                        else "Unknown"
                    )

                    if test_name and test_name != "Unknown":
                        failed_tests.append(test_name.strip())
                        if verbose:
                            logging.info(f"Found failed test: {test_name.strip()}")

            except Exception as e:
                if verbose:
                    logging.error(f"Error with primary selector: {e}")

            # If no failed tests found with primary selector, try alternatives
            if not failed_tests:
                if verbose:
                    logging.info(
                        "No failed tests found with primary selector, trying alternative methods..."
                    )

                # Try alternative selectors
                alternative_selectors = [
                    "tbody[class*='failed']",
                    "tr[class*='failed']",
                    "tr:has-text('Failed')",
                    "tr:has-text('FAILED')",
                    "tr.failed",
                    "tr.failure",
                    "[class*='failed']",
                ]

                for selector in alternative_selectors:
                    try:
                        alt_rows = await page.locator(selector).all()
                        if alt_rows:
                            if verbose:
                                logging.info(
                                    f"Found {len(alt_rows)} rows with selector: {selector}"
                                )
                            for row in alt_rows:
                                text = await row.text_content()
                                if text and any(
                                    keyword in text.lower()
                                    for keyword in ["failed", "error", "failure"]
                                ):
                                    # Try to extract more structured information
                                    cells = await row.locator("td").all()
                                    if len(cells) >= 2:
                                        test_name = (
                                            await cells[1].text_content()
                                            if len(cells) > 1
                                            else text.strip()
                                        )
                                    else:
                                        test_name = text.strip()

                                    if test_name:
                                        failed_tests.append(test_name.strip())
                                        if verbose:
                                            logging.info(
                                                f"Found failed test: {test_name.strip()}"
                                            )
                            break
                    except Exception as e:
                        if verbose:
                            logging.error(f"Error with selector {selector}: {e}")
                        continue

            await browser.close()

        except Exception as e:
            logging.error(f"Error fetching page with Playwright: {e}")
            return []

    return failed_tests


async def extract_failed_tests_from_url(url: str, verbose: bool = False) -> List[str]:
    """
    Extract failed test cases from a test result HTML page using Playwright method.

    Args:
        url (str): The URL of the test result page
        verbose (bool): Whether to print verbose logs

    Returns:
        List[str]: List of failed test case names
    """
    try:
        # Use Playwright method
        if verbose:
            logging.info("Extracting failed tests using Playwright method...")

        failed_tests = await extract_failed_tests_from_url_playwright(url, verbose)

        if failed_tests:
            if verbose:
                logging.info(
                    f"Playwright method successful: found {len(failed_tests)} failed tests"
                )
            return failed_tests

        # If no tests found
        if verbose:
            logging.info("No failed tests found in the provided URL")

        return []

    except Exception as e:
        logging.error(f"Error in extract_failed_tests_from_url: {str(e)}")
        if verbose:
            logging.info("Failed to extract tests due to error")
        return []


def save_failed_tests(failed_tests, output_file=None):
    """Save the failed test cases to a file

    Args:
        failed_tests: List of failed test cases
        output_file: Optional custom output file path. If not provided, will use default path in triage dir.
    """
    if failed_tests:
        if output_file is None:
            # Use default path if not specified
            output_dir = "triage"
            os.makedirs(output_dir, exist_ok=True)
            current_date = datetime.now().strftime("%m%d")
            output_file = os.path.join(output_dir, current_date)
        else:
            # Ensure the directory exists for custom output path
            output_dir = os.path.dirname(output_file)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)

        # Save the failed tests
        with open(output_file, "w", encoding="utf-8") as f:
            for test in failed_tests:
                f.write(f"{test}\n")

        logging.info(f"Failed test cases have been saved to {output_file}")
        return output_file
    return None


def setup_output_dirs():
    """Setup output directories with timestamps."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_base_dir = os.path.join(ALLURE_BASE_DIR, timestamp)
    results_dir = os.path.join(output_base_dir, "results")
    report_dir = os.path.join(output_base_dir, "report")
    test_output_dir = os.path.join(output_base_dir, "test_output")

    # Create directories
    ensure_directory_exists(results_dir)
    ensure_directory_exists(report_dir)
    ensure_directory_exists(test_output_dir)

    return test_output_dir, results_dir, report_dir


def ensure_directory_exists(directory):
    """Ensure the directory exists, create it if it doesn't."""
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)
    return directory


def get_optimal_worker_count():
    """Get the optimal number of worker processes based on CPU cores."""
    try:
        cpu_cores = cpu_count()
        optimal_workers = max(1, cpu_cores - 1)
        print(
            f"{Fore.BLUE}System has {cpu_cores} CPU cores, using {optimal_workers} workers for parallel execution{Style.RESET_ALL}"
        )
        return optimal_workers
    except Exception as e:
        print(
            f"{Fore.YELLOW}Warning: Could not determine CPU count ({str(e)}), defaulting to 1 worker{Style.RESET_ALL}"
        )
        return 1


def get_latest_triage_file():
    """Get the path of the most recent test case file in the triage directory."""
    try:
        # Ensure triage directory exists
        if not os.path.exists(TRIAGE_DIR):
            os.makedirs(TRIAGE_DIR)
            return None

        # Get all files in triage directory
        files = [
            os.path.join(TRIAGE_DIR, f)
            for f in os.listdir(TRIAGE_DIR)
            if os.path.isfile(os.path.join(TRIAGE_DIR, f)) and not f.startswith(".")
        ]

        if not files:
            return None

        # Sort files by modification time, newest first
        latest_file = max(files, key=os.path.getmtime)

        logging.info(f"Using latest triage file: {latest_file}")
        return latest_file
    except Exception as e:
        logging.warning(f"Error finding latest triage file: {str(e)}")
        return None


def parse_arguments():
    # Setup output directories with timestamps
    global DEFAULT_ALLURE_RESULTS, DEFAULT_ALLURE_REPORT, DEFAULT_OUTPUT_DIR
    DEFAULT_OUTPUT_DIR, DEFAULT_ALLURE_RESULTS, DEFAULT_ALLURE_REPORT = setup_output_dirs()

    # Get the latest triage file as default
    default_test_file = get_latest_triage_file() or "triage/0416"

    parser = argparse.ArgumentParser(
        description="""
Test Execution and Report Generation Tool

This script provides two main functionalities:
1. Extract failed test cases from a test report URL
2. Run test cases and generate detailed reports with Allure integration

Examples:
  # Extract failed tests from URL:
  python run_test.py --extract-only --url https://test-results.example.com/report

  # Run tests from a file:
  python run_test.py -f triage/my_tests

  # Run tests with custom parallel jobs:
  python run_test.py -f triage/my_tests -j 4

  # Extract and run tests from URL:
  python run_test.py --url https://test-results.example.com/report
""",
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )

    # URL argument (required only when -f is not specified or when using --extract-only)
    parser.add_argument(
        "--url",
        type=str,
        help="URL of the test report to extract failed cases from (required when not using -f or when using --extract-only)",
        metavar="URL",
    )

    # Test execution options
    execution_group = parser.add_argument_group("Test Execution Options")
    execution_group.add_argument(
        "-f",
        "--file",
        type=str,
        help="Path to the file containing test cases (default: latest file in triage directory)",
        default=default_test_file,
        metavar="FILE",
    )
    execution_group.add_argument(
        "-j",
        "--jobs",
        type=int,
        help="Number of parallel jobs (default: optimal based on CPU cores)",
        default=None,
        metavar="N",
    )
    execution_group.add_argument(
        "--clean",
        action="store_true",
        help="Clean previous test results before running",
        default=False,
    )

    # Output options
    output_group = parser.add_argument_group("Output Options")
    output_group.add_argument(
        "-o",
        "--output-dir",
        type=str,
        help="Directory to store test results (default: triage/allure/TIMESTAMP/test_output)",
        default=DEFAULT_OUTPUT_DIR,
        metavar="DIR",
    )
    output_group.add_argument(
        "--output-file",
        type=str,
        help="Custom output file path for saving failed test cases (only used with --extract-only)",
        metavar="FILE",
    )
    output_group.add_argument(
        "--allure-dir",
        type=str,
        help=f"Directory to store Allure results (default: {DEFAULT_ALLURE_RESULTS})",
        default=DEFAULT_ALLURE_RESULTS,
        metavar="DIR",
    )

    # Additional options
    additional_group = parser.add_argument_group("Additional Options")
    additional_group.add_argument(
        "-v",
        "--verbose",
        action="store_true",
        help="Show full error messages",
        default=True,
    )
    additional_group.add_argument(
        "--extract-only",
        action="store_true",
        help="Only extract and save failed test cases without running them",
        default=False,
    )

    args = parser.parse_args()

    # Validate arguments
    if args.extract_only and not args.url:
        parser.error("--url is required when using --extract-only")

    if not args.file and not args.url:
        parser.error("either --file or --url must be specified")

    # Validate URL format if provided
    if args.url:
        if not args.url.startswith("http://") and not args.url.startswith("https://"):
            parser.error("--url must be a valid HTTP/HTTPS URL")

    # Ensure triage directory exists
    ensure_directory_exists(TRIAGE_DIR)
    ensure_directory_exists(ALLURE_BASE_DIR)

    return args


def clean_directories(*dirs):
    """Clean directories before test execution."""
    for dir_path in dirs:
        if os.path.exists(dir_path):
            shutil.rmtree(dir_path)
        os.makedirs(dir_path)


def format_error_message(error_message):
    """Format error message for better readability."""
    if not error_message:
        return "No error details available"

    lines = [line for line in error_message.split("\n") if line.strip()]
    if not lines:
        return "No error details available"

    if len(lines) <= 5:
        return "\n".join(lines)

    return "\n".join([*lines[:3], f"... ({len(lines) - 5} more lines) ...", *lines[-2:]])


def extract_error_details(stdout):
    """Extract error details from test output."""
    error_lines = []
    traceback_lines = []
    in_error = False
    in_traceback = False
    collecting_details = False

    for line in stdout.split("\n"):
        line = line.strip()

        if "FAILED" in line and "[100%]" in line:
            collecting_details = True
            continue

        if not collecting_details:
            continue

        if line.startswith("=") and ("failed" in line.lower() or "passed" in line.lower()):
            break

        if line.startswith("E       "):
            in_error = True
            error_lines.append(line.replace("E       ", ""))
        elif line.startswith("_") and "_" in line:
            if not in_traceback:
                in_traceback = True
                error_lines.append("Traceback:")
        elif in_traceback and line:
            error_lines.append(line)
        elif line and (
            in_error or "Error" in line or "Exception" in line or "failed" in line.lower()
        ):
            error_lines.append(line)

    error_message = []
    for line in error_lines:
        line = line.replace("E   ", "").replace("_   ", "")
        if line and line not in error_message:
            error_message.append(line)

    return "\n".join(error_message), "\n".join(traceback_lines)


def run_single_test(args):
    """Run a single test case and return the results."""
    test_case, i, total, output_dir, allure_dir = args

    print(f"{Fore.BLUE}[{i}/{total}] Running test: {test_case}{Style.RESET_ALL}")

    cmd = f"python -m pytest {test_case} -v --alluredir={allure_dir}"
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

    test_name = test_case.split("::")[-1]
    output_file = f"{output_dir}/{test_name}.log"
    with open(output_file, "w") as f:
        f.write(result.stdout)
        f.write("\n\n")
        f.write(result.stderr)

    duration_match = re.search(r"PASSED|FAILED.*?in (\d+\.\d+)s", result.stdout)
    duration = duration_match.group(1) if duration_match else "N/A"

    status = "PASSED" if result.returncode == 0 else "FAILED"
    status_colored = (
        f"{Fore.GREEN}PASSED{Style.RESET_ALL}"
        if status == "PASSED"
        else f"{Fore.RED}FAILED{Style.RESET_ALL}"
    )

    duration_display = f"{duration}s" if duration != "N/A" else "N/A"
    print(f"  {status_colored} in {duration_display}")

    error_message = ""
    category = ""
    if status == "FAILED":
        error_message, traceback = extract_error_details(result.stdout)

        if "DEADLINE_EXCEEDED" in error_message:
            category = "DEADLINE_EXCEEDED"
        elif "PERMISSION_DENIED" in error_message:
            category = "PERMISSION_DENIED"
        elif "INVALID_ARGUMENT" in error_message:
            category = "INVALID_ARGUMENT"
        elif "NOT_FOUND" in error_message:
            category = "NOT_FOUND"
        elif "failed to establish link to worker" in error_message:
            category = "WORKER_CONNECTION_FAILURE"
        elif "UNAVAILABLE" in error_message:
            category = "SERVICE_UNAVAILABLE"
        elif "UNIMPLEMENTED" in error_message:
            category = "UNIMPLEMENTED"
        elif "AssertionError" in error_message:
            category = "ASSERTION_ERROR"
        else:
            category = "OTHER"

    return {
        "test_case": test_case,
        "status": status,
        "duration": duration_display,
        "error_message": error_message,
        "category": category,
        "traceback": traceback if status == "FAILED" else "",
    }


def get_allure_path():
    """Get the path to allure command"""
    return shutil.which("allure") or "/opt/homebrew/bin/allure"


def generate_allure_report(allure_dir, report_dir=None):
    """Generate Allure report from results."""
    if report_dir is None:
        report_dir = DEFAULT_ALLURE_REPORT

    print(f"\n{Fore.CYAN}Generating Allure report...{Style.RESET_ALL}")

    ensure_directory_exists(os.path.dirname(allure_dir))
    ensure_directory_exists(os.path.dirname(report_dir))

    try:
        allure_cmd = get_allure_path()
        cmd = f"{allure_cmd} generate {allure_dir} -o {report_dir} --clean"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        if result.returncode == 0:
            print(
                f"{Fore.GREEN}Allure report generated successfully at {report_dir}{Style.RESET_ALL}"
            )
            try:
                cmd = f"{allure_cmd} open {report_dir}"
                subprocess.Popen(cmd, shell=True)
            except Exception as e:
                print(
                    f"{Fore.YELLOW}Could not open report automatically: {str(e)}{Style.RESET_ALL}"
                )
                print(f"You can view the report by running: {allure_cmd} open {report_dir}")

            latest_link = os.path.join(ALLURE_BASE_DIR, "latest")
            try:
                if os.path.exists(latest_link):
                    os.remove(latest_link)
                os.symlink(os.path.dirname(report_dir), latest_link)
                print(
                    f"{Fore.GREEN}Created symlink to latest report at {latest_link}{Style.RESET_ALL}"
                )
            except Exception as e:
                print(
                    f"{Fore.YELLOW}Could not create symlink to latest report: {str(e)}{Style.RESET_ALL}"
                )
        else:
            print(
                f"{Fore.RED}Failed to generate Allure report: {result.stderr}{Style.RESET_ALL}"
            )
    except Exception as e:
        print(f"{Fore.RED}Error generating Allure report: {str(e)}{Style.RESET_ALL}")
        print("Make sure allure command-line tool is installed and available in PATH")


def run_tests(
    test_cases_file, output_dir, allure_dir, verbose=True, jobs=None, clean=False
):
    if jobs is None:
        jobs = get_optimal_worker_count()
    else:
        cpu_cores = cpu_count()
        if jobs > cpu_cores:
            print(
                f"{Fore.YELLOW}Warning: Specified worker count ({jobs}) exceeds available CPU cores ({cpu_cores}){Style.RESET_ALL}"
            )
        print(f"{Fore.BLUE}Using {jobs} workers for parallel execution{Style.RESET_ALL}")

    if clean:
        clean_directories(output_dir, allure_dir)
    else:
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(allure_dir, exist_ok=True)

    try:
        with open(test_cases_file, "r") as f:
            test_cases = [
                line.strip().split("[")[0] for line in f.readlines() if line.strip()
            ]
    except FileNotFoundError:
        print(
            f"{Fore.RED}Error: Test cases file '{test_cases_file}' not found.{Style.RESET_ALL}"
        )
        print(
            f"{Fore.YELLOW}Please make sure the file exists or use -f option to specify a different file.{Style.RESET_ALL}"
        )
        sys.exit(1)

    if not test_cases:
        print(
            f"{Fore.YELLOW}Warning: No test cases found in '{test_cases_file}'.{Style.RESET_ALL}"
        )
        sys.exit(0)

    print(f"{Fore.BLUE}Using test cases from: {test_cases_file}{Style.RESET_ALL}")
    print(f"{Fore.BLUE}Found {len(test_cases)} test cases to execute{Style.RESET_ALL}")
    print(f"{Fore.BLUE}Test results will be saved to: {output_dir}{Style.RESET_ALL}")
    print(f"{Fore.BLUE}Allure results will be saved to: {allure_dir}{Style.RESET_ALL}")

    results = {"passed": [], "failed": []}
    failure_categories = defaultdict(list)
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    start_time = datetime.now()

    total = len(test_cases)
    test_args = [
        (test_case, i + 1, total, output_dir, allure_dir)
        for i, test_case in enumerate(test_cases)
    ]

    with Pool(processes=jobs) as pool:
        test_results = pool.map(run_single_test, test_args)

    for result in test_results:
        test_case = result["test_case"]
        status = result["status"]
        error_message = result["error_message"]
        category = result["category"]

        if status == "PASSED":
            results["passed"].append(test_case)
        else:
            results["failed"].append(test_case)
            failure_categories[category].append(
                {
                    "test_case": test_case,
                    "error": error_message,
                    "traceback": result["traceback"],
                }
            )

    end_time = datetime.now()
    execution_time = (end_time - start_time).total_seconds()

    print(f"\n{Fore.CYAN}=== TEST EXECUTION SUMMARY ==={Style.RESET_ALL}")

    summary_table = [
        ["Total Tests", len(test_cases)],
        ["Passed", f"{Fore.GREEN}{len(results['passed'])}{Style.RESET_ALL}"],
        ["Failed", f"{Fore.RED}{len(results['failed'])}{Style.RESET_ALL}"],
        ["Success Rate", f"{len(results['passed']) / len(test_cases) * 100:.1f}%"],
        ["Execution Time", f"{execution_time:.2f}s"],
    ]
    print(tabulate(summary_table, tablefmt="pretty"))

    if failure_categories:
        print(f"\n{Fore.CYAN}=== FAILURE CATEGORIES ==={Style.RESET_ALL}")
        category_table = []
        for category, failures in failure_categories.items():
            category_table.append(
                [
                    category,
                    len(failures),
                    f"{len(failures) / len(test_cases) * 100:.1f}%",
                    ", ".join(
                        [f.split("::")[-1] for f in [f["test_case"] for f in failures][:3]]
                    ),
                ]
            )

        category_table.sort(key=lambda x: x[1], reverse=True)

        print(
            tabulate(
                category_table,
                headers=["Category", "Count", "% of Total", "Example Failures"],
                tablefmt="pretty",
            )
        )

    with open(f"{output_dir}/summary.json", "w") as f:
        json.dump(
            {
                "summary": {
                    "total": len(test_cases),
                    "passed": len(results["passed"]),
                    "failed": len(results["failed"]),
                    "execution_time": f"{execution_time:.2f}s",
                    "timestamp": timestamp,
                },
                "passed_tests": results["passed"],
                "failed_tests": results["failed"],
                "failure_categories": {
                    category: [
                        {
                            "test_case": f["test_case"],
                            "error": f["error"],
                            "traceback": f["traceback"],
                        }
                        for f in failures
                    ]
                    for category, failures in failure_categories.items()
                },
            },
            f,
            indent=2,
        )

    with open(f"{output_dir}/report.md", "w") as f:
        f.write("# Test Execution Report\n\n")
        f.write(f"Generated: {timestamp}\n\n")
        f.write("## Summary\n")
        f.write(f"- Total tests: {len(test_cases)}\n")
        f.write(f"- Passed: {len(results['passed'])}\n")
        f.write(f"- Failed: {len(results['failed'])}\n")
        f.write(f"- Success rate: {len(results['passed']) / len(test_cases) * 100:.1f}%\n")
        f.write(f"- Execution time: {execution_time:.2f}s\n\n")

        f.write("## Failure Categories\n\n")
        if not failure_categories:
            f.write("No failures! All tests passed.\n\n")
        else:
            f.write("| Category | Count | % of Total |\n")
            f.write("|----------|-------|------------|\n")
            for category, failures in failure_categories.items():
                f.write(
                    f"| {category} | {len(failures)} | {len(failures) / len(test_cases) * 100:.1f}% |\n"
                )

            f.write("\n## Detailed Failure Information\n\n")
            for category, failures in failure_categories.items():
                f.write(f"### {category} ({len(failures)} tests)\n\n")
                for failure in failures:
                    f.write(f"#### {failure['test_case']}\n")
                    f.write("```\n")
                    f.write("Error Message:\n")
                    f.write(failure["error"])
                    if failure["traceback"]:
                        f.write("\n\nTraceback:\n")
                        f.write(failure["traceback"])
                    f.write("\n```\n\n")

    generate_allure_report(allure_dir)

    print(
        f"\n{Fore.GREEN}Execution completed. Reports saved to '{output_dir}' directory.{Style.RESET_ALL}"
    )
    return results, failure_categories


async def main_async():
    """Main async function to handle URL extraction and test execution."""
    try:
        args = parse_arguments()

        if args.url:
            # Extract failed tests from URL first
            print(
                f"{Fore.BLUE}Extracting failed tests from URL: {args.url}{Style.RESET_ALL}"
            )
            output_file = await extract_failed_tests_async(args.url, args.output_file)
            if not output_file:
                print(
                    f"{Fore.RED}No failed test cases found or error occurred while extracting from URL{Style.RESET_ALL}"
                )
                sys.exit(1)

            if args.extract_only:
                print(
                    f"{Fore.GREEN}Failed test cases have been saved to {output_file}{Style.RESET_ALL}"
                )
                sys.exit(0)

            # Use the newly created file for test execution
            args.file = output_file

        # Run tests and generate reports
        run_tests(
            args.file, args.output_dir, args.allure_dir, args.verbose, args.jobs, args.clean
        )
    except KeyboardInterrupt:
        print(
            f"\n{Fore.YELLOW}Test execution interrupted by user. Saving current progress...{Style.RESET_ALL}"
        )
        sys.exit(1)
    except Exception as e:
        print(f"\n{Fore.RED}An error occurred: {str(e)}{Style.RESET_ALL}")
        sys.exit(1)


async def extract_failed_tests_async(url, output_file=None):
    """Async wrapper for extract_failed_tests function."""
    try:
        print(f"{Fore.BLUE}Fetching test results from URL...{Style.RESET_ALL}")
        # Extract failed tests from URL
        failed_tests = await extract_failed_tests_from_url(url, verbose=True)

        if not failed_tests:
            print(
                f"{Fore.YELLOW}No failed test cases found in the provided URL{Style.RESET_ALL}"
            )
            return None

        print(f"{Fore.GREEN}Found {len(failed_tests)} failed test cases{Style.RESET_ALL}")

        # Save failed tests to file
        saved_file = save_failed_tests(failed_tests, output_file)
        if not saved_file:
            print(f"{Fore.RED}Error: Failed to save test cases to file{Style.RESET_ALL}")
            return None

        print(
            f"{Fore.GREEN}Successfully saved {len(failed_tests)} test cases to {saved_file}{Style.RESET_ALL}"
        )
        return saved_file

    except Exception as e:
        print(f"{Fore.RED}Error extracting failed tests: {str(e)}{Style.RESET_ALL}")
        return None


if __name__ == "__main__":
    asyncio.run(main_async())
