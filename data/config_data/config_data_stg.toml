base_url = "https://nvcf.stg.ngc.nvidia.com/"
api_host_nvcf = "https://stg.api.nvcf.nvidia.com/v2/nvcf"
api_host_ngc = "https://api.stg.ngc.nvidia.com/v2/nvcf"
api_host_ngc_org = "https://api.stg.ngc.nvidia.com/v3/orgs/tadiathdfetp"
api_host_gpus = "https://api.stg.ngc.nvidia.com/v2/orgs/tadiathdfetp"

api_host_nvct = "https://stg.api.nvct.nvidia.com/v1/nvct"
api_host_nvct_admin = "https://stg.api.nvct.nvidia.com/v1/nvct/accounts/taLfaNPSpq7LV7vM1ueLJ2526IsX3be1MAjZ58GCQoc"
[nvcf_admin]
base_url = "https://nvcf.stg.ngc.nvidia.com/"
team = "No Team"
org_id = "NVCF QA Team"

[nvcf_viewer]
base_url = "https://nvcf.stg.ngc.nvidia.com/"
team = "No Team"
org_id = "NVCF QA Team"

[nvcf_admin_pr_user]
base_url = "https://nvcf.stg.ngc.nvidia.com/"
team = "No Team"
org_id = "NVCF QA Team"

[nvcf_gmail_owner]
base_url = "https://nvcf.stg.ngc.nvidia.com/"
team = "No Team"
org_id = "NVCF QA Team - Auto Edge Cases"

[nvcf_grpc_endpoint]
nvcf_grpc_endpoint = "stg.grpc.nvcf.nvidia.com:443"

[streaming_cluster]
gpu = "L40"
instanceType = "DGX-CLOUD.GPU.L40_1x"
backend = "nvcf-dgxc-k8s-forge-az60-ct1"
minInstances = 1
maxInstances = 1
maxRequestConcurrency = 1

[nvct_ssa_info]
url = "https://plykqk1qp504rigm-dor-xdp-hrtypqx6iu9eity3nw.stg.ssa.nvidia.com/token"
content_type = "application/x-www-form-urlencoded"

[telemetry_ssa_info]
url = "https://bvuehlapsvdurm6kziwyjqrvfbmcxe2idnqs61ld1lk.stg.ssa.nvidia.com/token"
content_type = "application/x-www-form-urlencoded"

[grafana_cloud_prometheus]
url = "prometheus-prod-36-prod-us-west-0.grafana.net/api/prom"

[grafana_cloud_loki]
url = "logs-prod-021.grafana.net"

[grafana_cloud_tempo]
url = "tempo-prod-15-prod-us-west-0.grafana.net/tempo"

[servicenow_traces]
url = "api.lightstep.com/public/v0.2/NVIDIA/projects/nvidia-staging"

[kratos_thanos_ssa_info]
url = "https://w6rojyggn16dpp37xuunjjnvczxdhjrkobq393rkkae.ssa.nvidia.com/token"
content_type = "application/x-www-form-urlencoded"
endpoint_url = "https://api4.kratos.nvidia.com"