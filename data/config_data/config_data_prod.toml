base_url = "https://nvcf.ngc.nvidia.com/"
api_host_nvcf = "https://api.nvcf.nvidia.com/v2/nvcf"
api_host_ngc = "https://api.ngc.nvidia.com/v2/nvcf"
api_host_nvct = "https://api.nvct.nvidia.com/v1/nvct"
api_host_nvct_admin = "https://api.nvct.nvidia.com/v1/nvct/accounts/e15g_kOXsdLwXAA4qA4LJ-QCHSv1X0Xozto1VTsaoa4"
api_host_telemetry = "https://api.nvcf.nvidia.com/v2/nvcf"
api_host_telemetry_admin = "https://api.nvcf.nvidia.com/v2/nvcf/accounts/taLfaNPSpq7LV7vM1ueLJ2526IsX3be1MAjZ58GCQoc"
api_host_ngc_org = "https://api.ngc.nvidia.com/v3/orgs/rw983xdqtcdp"
api_host_gpus = "https://api.ngc.nvidia.com/v2/orgs/rw983xdqtcdp"

[nvcf_admin]
base_url = "https://nvcf.ngc.nvidia.com/"
team = "No Team"
org_id = "Enterprise SWQA"
cookies_file = "nvcf_admin_prod_cookies.json"

[nvcf_viewer]
base_url = "https://nvcf.ngc.nvidia.com/"
team = "No Team"
org_id = "Enterprise SWQA"
cookies_file = "nvcf_viewer_prod_cookies.json"

[nvcf_admin_pr_user]
base_url = "https://nvcf.ngc.nvidia.com/"
team = "No Team"
org_id = "Enterprise SWQA"

[nvcf_grpc_endpoint]
nvcf_grpc_endpoint = "grpc.nvcf.nvidia.com:443"

[streaming_cluster]
gpu = "L40"
instanceType = "DGX-CLOUD.GPU.L40_1x"
backend = "nvcf-dgxc-k8s-forge-az22-prd1"
minInstances = 1
maxInstances = 1
maxRequestConcurrency = 1

[nvct_ssa_info]
url = "https://lm3gxhcjngdvbscsvml-qugnsn9jqnfov0kgr1kyype.ssa.nvidia.com/token"
content_type = "application/x-www-form-urlencoded"

[telemetry_ssa_info]
url = "https://tbyyhdy8-opimayg5nq78mx1wblbi8enaifkmlqrm8m.ssa.nvidia.com/token"
content_type = "application/x-www-form-urlencoded"

[grafana_cloud_prometheus]
url = "prometheus-prod-36-prod-us-west-0.grafana.net/api/prom"

[grafana_cloud_loki]
url = "logs-prod-021.grafana.net"

[grafana_cloud_tempo]
url = "tempo-prod-15-prod-us-west-0.grafana.net/tempo"

[servicenow_traces]
url = "api.lightstep.com/public/v0.2/NVIDIA/projects/nvidia-staging"

[kratos_thanos_ssa_info]
url = "https://w6rojyggn16dpp37xuunjjnvczxdhjrkobq393rkkae.ssa.nvidia.com/token"
content_type = "application/x-www-form-urlencoded"
endpoint_url = "https://api4.kratos.nvidia.com"