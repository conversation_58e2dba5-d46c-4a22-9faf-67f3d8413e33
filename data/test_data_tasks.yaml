prod_task_sample_run_command:
  name: "task_sample_ui_auto"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "run_command_containerargs"
  model: [["rw983xdqtcdp/model_nvcf_qa", "0.1", "a"]]
  resource: [["rw983xdqtcdp/resource_nvcf_qa", "0.1", "b"]]
  secrets: {"key": "aaa"}
  run_command: "python3 main.py aaa"
  environment_variables: {"NUM_OF_RESULTS": "3","DELAY_BETWEEN_RESULTS_IN_MINUTES": "1"}
  gpu_type: T10
  instance_type: g6.full
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

prod_container_task_sample_gfn_upload_result_with_secrets:
  name: "task_container_gfn_ui_auto"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "latest"
  gpu_type: T10
  secrets: {"key11": "aaa", "key22": {"key1": 1,"key2": 2}}
  instance_type: g6.full
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_container_task_sample_gfn_upload_result_with_secrets:
  name: "task_container_gfn_ui_auto"
  container: "tadiathdfetp/tasks_sample"
  tag: "latest"
  gpu_type: L40S
  secrets: {"key11": "aaa", "key22": {"key1": 1,"key2": 2}}
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

prod_helm_task_sample_gfn_upload_result_with_secrets:
  name: "task_helm_gfn_ui_auto"
  Helm_Chart: "rw983xdqtcdp/task-test"
  Helm_Chart_Version: "0.4"
  gpu_type: T10
  secrets: {"key11": "aaa", "key22": {"key1": 1,"key2": 2}}
  instance_type: g6.full
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 2400

prod_helm_task_sample_gfn_without_upload_return_error_result:
  name: "task_helm_gfn_ui_auto"
  Helm_Chart: "rw983xdqtcdp/task-test-percentcompleteunchanged"
  Helm_Chart_Version: "0.2"
  gpu_type: T10
  instance_type: g6.full
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  time_out: 2400

stg_helm_task_sample_gfn_upload_result_with_secrets:
  name: "task_helm_gfn_ui_auto"
  Helm_Chart: "tadiathdfetp/task-test"
  Helm_Chart_Version: "0.4"
  gpu_type: L40S
  secrets: {"key11": "aaa", "key22": {"key1": 1,"key2": 2}}
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 2400

stg_helm_task_sample_gfn_without_upload_return_error_result:
  name: "task_helm_gfn_ui_auto"
  Helm_Chart: "tadiathdfetp/task-test-percentcompleteunchanged"
  Helm_Chart_Version: "0.1"
  gpu_type: T10
  instance_type: g6.full
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  time_out: 2400

prod_container_task_sample_gfn_upload_result_with_run_command:
  name: "task_container_gfn_ui_auto"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "run_command_containerargs0.0.15"
  gpu_type: T10
  run_command: "python3 main.py aaa"
  instance_type: g6.full
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_container_task_sample_gfn_upload_result_with_run_command:
  name: "task_container_gfn_ui_auto"
  container: "tadiathdfetp/tasks_sample"
  tag: "run_command_containerargs0.0.15"
  gpu_type: L40S
  run_command: "python3 main.py aaa"
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

prod_container_task_sample_gfn_full_config:
  name: "task_sample_ui_auto"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "latest"
  model: [["rw983xdqtcdp/model_nvcf_qa", "0.1", "a"]]
  resource: [["rw983xdqtcdp/resource_nvcf_qa", "0.1", "b"]]
  secrets: {"key": "aaa"}
  environment_variables: {"NUM_OF_RESULTS": "3","DELAY_BETWEEN_RESULTS_IN_MINUTES": "1"}
  gpu_type: T10
  instance_type: g6.full
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_container_task_sample_gfn_full_config:
  name: "task_sample_ui_auto"
  container: "tadiathdfetp/tasks_sample"
  tag: "latest"
  model: [["tadiathdfetp/model_nvcf_qa", "0.1", "a"]]
  resource: [["tadiathdfetp/resource_nvcf_qa", "0.1", "b"]]
  secrets: {"key": "aaa"}
  environment_variables: {"NUM_OF_RESULTS": "3","DELAY_BETWEEN_RESULTS_IN_MINUTES": "1"}
  gpu_type: L40S
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

prod_container_task_sample_gfn_model_resource:
  name: "task_sample_ui_auto"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "latest"
  model: [["rw983xdqtcdp/model_nvcf_qa", "0.1", "a"]]
  resource: [["rw983xdqtcdp/resource_nvcf_qa", "0.1", "b"]]
  gpu_type: T10
  instance_type: g6.full
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_container_task_sample_gfn_model_resource:
  name: "task_sample_ui_auto"
  container: "tadiathdfetp/tasks_sample"
  tag: "latest"
  model: [["tadiathdfetp/model_nvcf_qa", "0.1", "a"]]
  resource: [["tadiathdfetp/resource_nvcf_qa", "0.1", "b"]]
  gpu_type: L40S
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

prod_container_task_sample_gfn_upload_result:
  name: "task_container_gfn_ui_auto"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "latest"
  gpu_type: T10
  instance_type: g6.full
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_container_task_sample_gfn_upload_result:
  name: "task_container_gfn_ui_auto"
  container: "tadiathdfetp/tasks_sample"
  tag: "latest"
  gpu_type: L40S
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_container_task_sample_gfn_upload_result_with_telemetries:
  name: "task_container_gfn_ui_auto"
  container: "tadiathdfetp/task_byoo_sample"
  tag: "0.0.9 (Latest)"
  gpu_type: L40S
  instance_type: gl40s_1.br25_2xlarge
  environment_variables: {"NUM_OF_RESULTS": "3","DELAY_BETWEEN_RESULTS_IN_MINUTES": "1"}
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 2400

stg_container_task_sample_dgxc_upload_result_with_telemetries:
  name: "task_container_gfn_ui_auto"
  container: "tadiathdfetp/task_byoo_sample"
  tag: "0.0.9 (Latest)"
  gpu_type: H100
  instance_type: GCP.GPU.H100_1x
  clusters: nvcf-dgxc-k8s-gcp-usc1-ct1
  environment_variables: {"NUM_OF_RESULTS": "3","DELAY_BETWEEN_RESULTS_IN_MINUTES": "1"}
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 2400

stg_container_task_sample_non_dgxc_upload_result_with_telemetries:
  name: "task_container_gfn_ui_auto"
  container: "tadiathdfetp/task_byoo_sample"
  tag: "0.0.9 (Latest)"
  gpu_type: H100
  instance_type: GCP.GPU.H100_1x
  clusters: nvcf-qa-cluster-gcp
  environment_variables: {"NUM_OF_RESULTS": "3","DELAY_BETWEEN_RESULTS_IN_MINUTES": "1"}
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "10 minutes"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 2400

prod_helm_task_sample_gfn_upload_result:
  name: "task_helm_gfn_ui_auto"
  Helm_Chart: "rw983xdqtcdp/task-test"
  Helm_Chart_Version: "0.4"
  gpu_type: T10
  model: [["rw983xdqtcdp/model_nvcf_qa", "0.1", "a"]]
  resource: [["rw983xdqtcdp/resource_nvcf_qa", "0.1", "b"]]
  instance_type: g6.full
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  helm_chart_overrides_data:
    "numberOfResults": 2
  ResultsUpload_model_name: uiauto
  time_out: 2400

stg_helm_task_sample_gfn_upload_result:
  name: "task_helm_gfn_ui_auto"
  Helm_Chart: "tadiathdfetp/task-test"
  Helm_Chart_Version: "0.4"
  gpu_type: L40S
  model: [["tadiathdfetp/model_nvcf_qa", "0.1", "a"]]
  resource: [["tadiathdfetp/resource_nvcf_qa", "0.1", "b"]]
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  helm_chart_overrides_data:
    "numberOfResults": 2
  ResultsUpload_model_name: uiauto
  time_out: 2400

stg_helm_task_sample_dgxc_upload_result:
  name: "task_helm_dgxc_ui_auto"
  Helm_Chart: "tadiathdfetp/task-test"
  Helm_Chart_Version: "0.4"
  gpu_type: H100
  model: [["tadiathdfetp/model_nvcf_qa", "0.1", "a"]]
  resource: [["tadiathdfetp/resource_nvcf_qa", "0.1", "b"]]
  instance_type: GCP.GPU.H100_1x
  clusters: nvcf-dgxc-k8s-gcp-usc1-ct1
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  helm_chart_overrides_data:
    "numberOfResults": 2
  ResultsUpload_model_name: uiauto
  time_out: 2400

prod_helm_task_sample_gfn_exceed_max_runtime_duration:
  name: "task_helm_gfn_ui_auto"
  Helm_Chart: "rw983xdqtcdp/task-test"
  Helm_Chart_Version: "0.4"
  gpu_type: T10
  instance_type: g6.full
  maximum_runtime_duration: "10 minutes"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "10 minutes"
  helm_chart_overrides_data:
    "numberOfResults": 10
  ResultsUpload_model_name: uiauto
  time_out: 2400

stg_helm_task_sample_gfn_exceed_max_runtime_duration:
  name: "task_helm_gfn_ui_auto"
  Helm_Chart: "tadiathdfetp/task-test"
  Helm_Chart_Version: "0.4"
  gpu_type: L40S
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "10 minutes"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "10 minutes"
  helm_chart_overrides_data:
    "numberOfResults": 10
  ResultsUpload_model_name: uiauto
  time_out: 2400


prod_container_task_sample_gfn_without_upload_result:
  name: "task_container_gfn_ui_auto"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "latest"
  gpu_type: T10
  instance_type: g6.full
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  time_out: 1200

stg_container_task_sample_gfn_without_upload_result:
  name: "task_container_gfn_ui_auto"
  container: "tadiathdfetp/tasks_sample"
  tag: "latest"
  gpu_type: L40S
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  time_out: 1200

prod_task_sample_NONE_runs_forever_GFN:
  name: "task_sample_ui_auto"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "latest"
  gpu_type: T10
  instance_type: g6.full
  maximum_runtime_duration: "None (runs forever)"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_task_sample_NONE_runs_forever_GFN:
  name: "task_sample_ui_auto"
  container: "tadiathdfetp/tasks_sample"
  tag: "latest"
  gpu_type: L40S
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "None (runs forever)"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

prod_task_sample_maxRuntimeDuration_larger_than_8_GFN:
  name: "task_sample_ui_auto"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "latest"
  gpu_type: T10
  instance_type: g6.full
  maximum_runtime_duration: "12 hours"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_task_sample_maxRuntimeDuration_larger_than_8_GFN:
  name: "task_sample_ui_auto"
  container: "tadiathdfetp/tasks_sample"
  tag: "latest"
  gpu_type: L40S
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "12 hours"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

prod_task_sample_terminationGracePeriodDuration_large_than_maxRuntimeDuration_GFN:
  name: "task_sample_ui_auto"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "latest"
  gpu_type: T10
  instance_type: g6.full
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "8 hours"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_task_sample_terminationGracePeriodDuration_large_than_maxRuntimeDuration_GFN:
  name: "task_sample_ui_auto"
  container: "tadiathdfetp/tasks_sample"
  tag: "latest"
  gpu_type: L40S
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "8 hours"
  ResultsUpload_model_name: uiauto
  time_out: 1200

prod_helm_task_sample_terminationGracePeriodDuration_large_than_maxRuntimeDuration_GFN:
  name: "task_helm_gfn_ui_auto"
  Helm_Chart: "rw983xdqtcdp/task-test"
  Helm_Chart_Version: "0.4"
  gpu_type: T10
  instance_type: g6.full
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "8 hours"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_helm_task_sample_terminationGracePeriodDuration_large_than_maxRuntimeDuration_GFN:
  name: "task_helm_gfn_ui_auto"
  Helm_Chart: "tadiathdfetp/task-test"
  Helm_Chart_Version: "0.4"
  gpu_type: L40S
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "8 hours"
  ResultsUpload_model_name: uiauto
  time_out: 1200

prod_task_sample_terminationGracePeriodDuration_larger_than_maxRuntimeDuration_BYOC_nagative:
  name: "task_sample_ui_auto_terminationGracePeriodDuration_larger_than_maxRuntimeDuration_nagative"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "latest"
  gpu_type: A10
  instance_type: OCI.GPU.A10_1x
  cluster_type: "nvcf-qa-oci-oke "
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "12 hours"
  result_handling: "None"
  time_out: 1200

stg_task_sample_terminationGracePeriodDuration_larger_than_maxRuntimeDuration_BYOC_nagative:
  name: "task_sample_ui_auto_terminationGracePeriodDuration_larger_than_maxRuntimeDuration_nagative"
  container: "tadiathdfetp/tasks_sample"
  tag: "latest"
  gpu_type: H100
  instance_type: GCP.GPU.H100_1x
  cluster_type: "nvcf-qa-cluster-gcp"
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "12 hours"
  result_handling: "None"
  time_out: 1200

clone_task_deployment_config:
  gpu_type: T10
  instance_type: g6.full
  clusters: null
  generate_personal_key: true
  generate_person_key_config:
    key_name: "test_task_key"
    duration: "1 hour"

clone_task_deployment_config_stg:
  gpu_type: H100
  instance_type: GCP.GPU.H100_1x
  clusters: nvcf-qa-cluster-gcp
  generate_personal_key: true
  generate_person_key_config:
    key_name: "test_task_key"
    duration: "1 hour"

clone_task_non_gfn_deployment_config:
  gpu_type: A10
  instance_type: OCI.GPU.A10_1x
  clusters: nvcf-qa-oci-oke
  generate_personal_key: true
  generate_person_key_config:
    key_name: "test_key"
    duration: "1 hour"

TASK_PATH: &TASK_PATH "/tasks"
APPLICATION_JSON: &APPLICATION_JSON "application/json"
CTEATE_TASK_AND_UPLOAD_SCHEMA: &CTEATE_TASK_AND_UPLOAD_SCHEMA
  task:
    id: string
    ncaId: string
    name: string
    status: string
    gpuSpecification:
      gpu: string
      backend: string
      instanceType: string
    containerImage: string
    containerEnvironment:
      - key: string
        value: string
    models:
      - name: string
        version: string
        uri: string
    resources:
      - name: string
        version: string
        uri: string
    resultHandlingStrategy: string
    resultsLocation: string
    maxRuntimeDuration: string
    maxQueuedDuration: string
    terminationGracePeriodDuration: string
    secrets:
      - string
    createdAt: string

CTEATE_TASK_AND_UPLOAD_A10:
  req:
    method: POST
    path: *TASK_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: create-task-and-upload
      containerImage: nvcr.io/rw983xdqtcdp/tasks_sample:latest
      gpuSpecification:
        gpu: "A10"
        backend: "nvcf-qa-oci-oke"
        instanceType: "OCI.GPU.A10_1x"
      secrets:
        - name: NGC_API_KEY
          value: ""
      models:
        - name: a
          version: "0.1"
          uri: /v2/org/rw983xdqtcdp/models/model_nvcf_qa/0.1/files
      resources:
        - name: a
          version: "0.1"
          uri: /v2/org/rw983xdqtcdp/resources/resource_nvcf_qa/0.1/files
      resultsLocation: rw983xdqtcdp/cc
      resultHandlingStrategy: UPLOAD
      maxRuntimeDuration: PT1H
      maxQueuedDuration: PT1H
      terminationGracePeriodDuration: PT1H
  exp:
    description: "Creates and launches a new task API"
    status_code: 200
    schema: *CTEATE_TASK_AND_UPLOAD_SCHEMA
  timeout: 1800

CTEATE_TASK_AND_UPLOAD_H100_STG:
  req:
    method: POST
    path: *TASK_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: create-task-and-upload
      containerImage: stg.nvcr.io/tadiathdfetp/tasks_sample:latest
      gpuSpecification:
        gpu: "H100"
        backend: "nvcf-qa-cluster-gcp"
        instanceType: "GCP.GPU.H100_1x"
      secrets:
        - name: NGC_API_KEY
          value: ""
      models:
        - name: a
          version: "0.1"
          uri: /v2/org/tadiathdfetp/models/model_nvcf_qa/0.1/files
      resources:
        - name: a
          version: "0.1"
          uri: /v2/org/tadiathdfetp/resources/resource_nvcf_qa/0.1/files
      resultsLocation: tadiathdfetp/cc
      resultHandlingStrategy: UPLOAD
      maxRuntimeDuration: PT1H
      maxQueuedDuration: PT1H
      terminationGracePeriodDuration: PT1H
  exp:
    description: "Creates and launches a new task API"
    status_code: 200
    schema: *CTEATE_TASK_AND_UPLOAD_SCHEMA
  timeout: 1800

CTEATE_TASK_WITHOUT_UPLOAD_T10:
  req:
    method: POST
    path: *TASK_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: create-task-and-upload
      containerImage: nvcr.io/rw983xdqtcdp/tasks_sample:latest
      gpuSpecification:
        gpu: "T10"
        backend: "GFN"
        instanceType: "g6.full"
      secrets:
        - name: NGC_API_KEY
          value: ""
      resultHandlingStrategy: NONE
      maxRuntimeDuration: PT1H
      maxQueuedDuration: PT1H
      terminationGracePeriodDuration: PT1H
  exp:
    description: "Creates and launches a new task API"
    status_code: 200
    schema: *CTEATE_TASK_AND_UPLOAD_SCHEMA
  timeout: 1800

CTEATE_TASK_AND_UPLOAD_L40S_STG:
  req:
    method: POST
    path: *TASK_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: create-task-and-upload
      containerImage: stg.nvcr.io/tadiathdfetp/tasks_sample:latest
      containerEnvironment:
        - key: NUM_OF_RESULTS
          value: "5"
        - key: DELAY_BETWEEN_RESULTS_IN_MINUTES
          value: "1"
      gpuSpecification:
        gpu: "L40S"
        backend: "GFN"
        instanceType: "gl40s_1.br25_2xlarge"
      secrets:
        - name: NGC_API_KEY
          value: ""
      models:
        - name: a
          version: "0.1"
          uri: /v2/org/tadiathdfetp/models/model_nvcf_qa/0.1/files
      resources:
        - name: a
          version: "0.1"
          uri: /v2/org/tadiathdfetp/resources/resource_nvcf_qa/0.1/files
      resultsLocation: tadiathdfetp/cc
      resultHandlingStrategy: UPLOAD
      maxRuntimeDuration: PT1H
      maxQueuedDuration: PT1H
      terminationGracePeriodDuration: PT1H
  exp:
    description: "Creates and launches a new task API"
    status_code: 200
    schema: *CTEATE_TASK_AND_UPLOAD_SCHEMA
  timeout: 1800

CTEATE_TASK_WITHOUT_UPLOAD_T10_ERROR:
  req:
    method: POST
    path: *TASK_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: create-task-error
      containerImage: nvcr.io/rw983xdqtcdp/tasks_sample:fakecommand
      gpuSpecification:
        gpu: "T10"
        backend: "GFN"
        instanceType: "g6.full"
      secrets:
        - name: NGC_API_KEY
          value: ""
      resultHandlingStrategy: NONE
      maxRuntimeDuration: PT1H
      maxQueuedDuration: PT1H
      terminationGracePeriodDuration: PT1H
  exp:
    description: "Creates and launches a new task API"
    status_code: 200
    schema: *CTEATE_TASK_AND_UPLOAD_SCHEMA
  timeout: 1800

CTEATE_TASK_WITHOUT_UPLOAD_H100_ERROR_STG:
  req:
    method: POST
    path: *TASK_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: create-task-error
      containerImage: stg.nvcr.io/tadiathdfetp/tasks_sample:fakecommand
      gpuSpecification:
        gpu: "H100"
        backend: "nvcf-qa-cluster-gcp"
        instanceType: "GCP.GPU.H100_1x"
      secrets:
        - name: NGC_API_KEY
          value: ""
      resultHandlingStrategy: NONE
      maxRuntimeDuration: PT1H
      maxQueuedDuration: PT1H
      terminationGracePeriodDuration: PT1H
  exp:
    description: "Creates and launches a new task API"
    status_code: 200
    schema: *CTEATE_TASK_AND_UPLOAD_SCHEMA
  timeout: 1800

CTEATE_TASK_WITHOUT_UPLOAD_NON_GFN_ERROR:
  req:
    method: POST
    path: *TASK_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: create-task-error
      containerImage: nvcr.io/rw983xdqtcdp/tasks_sample:fakecommand
      gpuSpecification:
        gpu: "A10"
        backend: "nvcf-qa-oci-oke"
        instanceType: "OCI.GPU.A10_1x"
      secrets:
        - name: NGC_API_KEY
          value: ""
      resultHandlingStrategy: NONE
      maxRuntimeDuration: PT1H
      maxQueuedDuration: PT1H
      terminationGracePeriodDuration: PT1H
  exp:
    description: "Creates and launches a new task API"
    status_code: 200
    schema: *CTEATE_TASK_AND_UPLOAD_SCHEMA
  timeout: 1800

CTEATE_TASK_WITHOUT_UPLOAD_NON_GFN_ERROR_STG:
  req:
    method: POST
    path: *TASK_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: create-task-error
      containerImage: stg.nvcr.io/tadiathdfetp/tasks_sample:fakecommand
      gpuSpecification:
        gpu: "H100"
        backend: "nvcf-qa-cluster-gcp"
        instanceType: "GCP.GPU.H100_1x"
      secrets:
        - name: NGC_API_KEY
          value: ""
      resultHandlingStrategy: NONE
      maxRuntimeDuration: PT1H
      maxQueuedDuration: PT1H
      terminationGracePeriodDuration: PT1H
  exp:
    description: "Creates and launches a new task API"
    status_code: 200
    schema: *CTEATE_TASK_AND_UPLOAD_SCHEMA
  timeout: 1800

task_sample_on_A10:
  name: "task_sample_on_A10"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "latest"
  gpu_type: A10
  instance_type: OCI.GPU.A10_1x
  cluster_type: "nvcf-qa-oci-oke "
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  result_handling: "None"
  time_out: 1200

task_sample_on_GFN:
  name: "task_sample_on_GFN"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "latest"
  gpu_type: T10
  instance_type: g6.full
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  result_handling: "None"
  time_out: 1200

task_sample_on_GFN_stg:
  name: "task_sample_on_GFN"
  container: "tadiathdfetp/tasks_sample"
  tag: "latest"
  gpu_type: L40S
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  result_handling: "None"
  time_out: 1200

CREATE_HELM_TASK_AND_UPLOAD_RESULT:
  req:
    method: POST
    path: *TASK_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: create-helm-task-and-upload-result
      helmChart: https://helm.ngc.nvidia.com/rw983xdqtcdp/charts/task-test-0.4.tgz
      gpuSpecification:
        gpu: "T10"
        backend: "GFN"
        instanceType: "g6.full"
      secrets:
        - name: NGC_API_KEY
          value: ""
      models:
        - name: a
          version: "0.1"
          uri: /v2/org/rw983xdqtcdp/models/model_nvcf_qa/0.1/files
      resources:
        - name: a
          version: "0.1"
          uri: /v2/org/rw983xdqtcdp/resources/resource_nvcf_qa/0.1/files
      resultsLocation: rw983xdqtcdp/cc
      resultHandlingStrategy: UPLOAD
      maxRuntimeDuration: PT1H
      maxQueuedDuration: PT1H
      terminationGracePeriodDuration: PT1H
  exp:
    description: "Creates and launches a new helm task API with result upload"
    status_code: 200
    schema: *CTEATE_TASK_AND_UPLOAD_SCHEMA
  timeout: 1800

CREATE_HELM_TASK_AND_UPLOAD_RESULT_STG:
  req:
    method: POST
    path: *TASK_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: create-helm-task-and-upload-result
      helmChart: https://helm.stg.ngc.nvidia.com/tadiathdfetp/charts/task-test-0.4.tgz
      gpuSpecification:
        gpu: "H100"
        backend: "nvcf-qa-cluster-gcp"
        instanceType: "GCP.GPU.H100_1x"
      secrets:
        - name: NGC_API_KEY
          value: ""
      models:
        - name: a
          version: "0.1"
          uri: /v2/org/tadiathdfetp/models/model_nvcf_qa/0.1/files
      resources:
        - name: a
          version: "0.1"
          uri: /v2/org/tadiathdfetp/resources/resource_nvcf_qa/0.1/files
      resultsLocation: tadiathdfetp/cc
      resultHandlingStrategy: UPLOAD
      maxRuntimeDuration: PT1H
      maxQueuedDuration: PT1H
      terminationGracePeriodDuration: PT1H
  exp:
    description: "Creates and launches a new helm task API with result upload"
    status_code: 200
    schema: *CTEATE_TASK_AND_UPLOAD_SCHEMA
  timeout: 1800

CREATE_HELM_TASK_WITHOUT_UPLOAD_RESULT:
  req:
    method: POST
    path: *TASK_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: create-helm-task-without-upload-result
      helmChart: https://helm.ngc.nvidia.com/rw983xdqtcdp/charts/task-test-0.4.tgz
      gpuSpecification:
        gpu: "T10"
        backend: "GFN"
        instanceType: "g6.full"
      secrets:
        - name: NGC_API_KEY
          value: ""
      models:
        - name: a
          version: "0.1"
          uri: /v2/org/rw983xdqtcdp/models/model_nvcf_qa/0.1/files
      resources:
        - name: a
          version: "0.1"
          uri: /v2/org/rw983xdqtcdp/resources/resource_nvcf_qa/0.1/files
      resultHandlingStrategy: NONE
      maxRuntimeDuration: PT1H
      maxQueuedDuration: PT1H
      terminationGracePeriodDuration: PT1H
  exp:
    description: "Creates and launches a new helm task API without result upload"
    status_code: 200
    schema: *CTEATE_TASK_AND_UPLOAD_SCHEMA
  timeout: 1800

CREATE_HELM_TASK_WITHOUT_UPLOAD_RESULT_STG:
  req:
    method: POST
    path: *TASK_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: create-helm-task-without-upload-result
      helmChart: https://helm.stg.ngc.nvidia.com/tadiathdfetp/charts/task-test-0.4.tgz
      gpuSpecification:
        gpu: "L40S"
        backend: "GFN"
        instanceType: "gl40s_1.br25_2xlarge"
      secrets:
        - name: NGC_API_KEY
          value: ""
      models:
        - name: a
          version: "0.1"
          uri: /v2/org/tadiathdfetp/models/model_nvcf_qa/0.1/files
      resources:
        - name: a
          version: "0.1"
          uri: /v2/org/tadiathdfetp/resources/resource_nvcf_qa/0.1/files
      resultHandlingStrategy: NONE
      maxRuntimeDuration: PT1H
      maxQueuedDuration: PT1H
      terminationGracePeriodDuration: PT1H
  exp:
    description: "Creates and launches a new helm task API without result upload"
    status_code: 200
    schema: *CTEATE_TASK_AND_UPLOAD_SCHEMA
  timeout: 1800

CREATE_HELM_TASK_WITHOUT_UPLOAD_RESULT_NON_DGXC:
  req:
    method: POST
    path: *TASK_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: create-helm-task-without-upload-result
      helmChart: https://helm.ngc.nvidia.com/rw983xdqtcdp/charts/task-test-0.4.tgz
      gpuSpecification:
        gpu: "A10"
        backend: "nvcf-qa-oci-oke"
        instanceType: "OCI.GPU.A10_1x"
      secrets:
        - name: NGC_API_KEY
          value: ""
      models:
        - name: a
          version: "0.1"
          uri: /v2/org/rw983xdqtcdp/models/model_nvcf_qa/0.1/files
      resources:
        - name: a
          version: "0.1"
          uri: /v2/org/rw983xdqtcdp/resources/resource_nvcf_qa/0.1/files
      resultHandlingStrategy: NONE
      maxRuntimeDuration: PT1H
      maxQueuedDuration: PT1H
      terminationGracePeriodDuration: PT1H
  exp:
    description: "Creates and launches a new helm task API without result upload"
    status_code: 200
    schema: *CTEATE_TASK_AND_UPLOAD_SCHEMA
  timeout: 1800

CREATE_HELM_TASK_WITHOUT_UPLOAD_RESULT_NON_DGXC_STG:
  req:
    method: POST
    path: *TASK_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: create-helm-task-without-upload-result
      helmChart: https://helm.stg.ngc.nvidia.com/tadiathdfetp/charts/task-test-0.4.tgz
      gpuSpecification:
        gpu: "H100"
        backend: "nvcf-qa-cluster-gcp"
        instanceType: "GCP.GPU.H100_1x"
      secrets:
        - name: NGC_API_KEY
          value: ""
      models:
        - name: a
          version: "0.1"
          uri: /v2/org/tadiathdfetp/models/model_nvcf_qa/0.1/files
      resources:
        - name: a
          version: "0.1"
          uri: /v2/org/tadiathdfetp/resources/resource_nvcf_qa/0.1/files
      resultHandlingStrategy: NONE
      maxRuntimeDuration: PT1H
      maxQueuedDuration: PT1H
      terminationGracePeriodDuration: PT1H
  exp:
    description: "Creates and launches a new helm task API without result upload"
    status_code: 200
    schema: *CTEATE_TASK_AND_UPLOAD_SCHEMA
  timeout: 1800

CREATE_HELM_TASK_WITHOUT_UPLOAD_RESULT_DGXC:
  req:
    method: POST
    path: *TASK_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: create-helm-task-without-upload-result
      helmChart: https://helm.ngc.nvidia.com/rw983xdqtcdp/charts/task-test-0.4.tgz
      gpuSpecification:
        gpu: "H100"
        backend: "nvcf-dgxc-k8s-oci-nrt-prd7"
        instanceType: "OCI.GPU.H100_1x"
      secrets:
        - name: NGC_API_KEY
          value: ""
      models:
        - name: a
          version: "0.1"
          uri: /v2/org/rw983xdqtcdp/models/model_nvcf_qa/0.1/files
      resources:
        - name: a
          version: "0.1"
          uri: /v2/org/rw983xdqtcdp/resources/resource_nvcf_qa/0.1/files
      resultHandlingStrategy: NONE
      maxRuntimeDuration: PT1H
      maxQueuedDuration: PT1H
      terminationGracePeriodDuration: PT1H
  exp:
    description: "Creates and launches a new helm task API without result upload"
    status_code: 200
    schema: *CTEATE_TASK_AND_UPLOAD_SCHEMA
  timeout: 1800

CREATE_HELM_TASK_WITHOUT_UPLOAD_RESULT_DGXC_STG:
  req:
    method: POST
    path: *TASK_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: create-helm-task-without-upload-result
      helmChart: https://helm.stg.ngc.nvidia.com/tadiathdfetp/charts/task-test-0.4.tgz
      gpuSpecification:
        gpu: "AD102GL"
        backend: "nvcf-dgxc-k8s-forge-az24-dev6"
        instanceType: "DGX-CLOUD.GPU.AD102GL_1x"
      secrets:
        - name: NGC_API_KEY
          value: ""
      models:
        - name: a
          version: "0.1"
          uri: /v2/org/tadiathdfetp/models/model_nvcf_qa/0.1/files
      resources:
        - name: a
          version: "0.1"
          uri: /v2/org/tadiathdfetp/resources/resource_nvcf_qa/0.1/files
      resultHandlingStrategy: NONE
      maxRuntimeDuration: PT1H
      maxQueuedDuration: PT1H
      terminationGracePeriodDuration: PT1H
  exp:
    description: "Creates and launches a new helm task API without result upload"
    status_code: 200
    schema: *CTEATE_TASK_AND_UPLOAD_SCHEMA
  timeout: 1800

prod_container_task_sample_gfn_exceed_max_runtime_duration:
  name: "task_helm_gfn_ui_auto"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "latest"
  gpu_type: T10
  instance_type: g6.full
  maximum_runtime_duration: "10 minutes"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "10 minutes"
  environment_variables: {"NUM_OF_RESULTS": "5","DELAY_BETWEEN_RESULTS_IN_MINUTES": "3"}
  ResultsUpload_model_name: uiauto
  time_out: 2400

stg_container_task_sample_gfn_exceed_max_runtime_duration:
  name: "task_helm_gfn_ui_auto"
  container: "tadiathdfetp/tasks_sample"
  tag: "latest"
  gpu_type: L40S
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "10 minutes"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "10 minutes"
  environment_variables: {"NUM_OF_RESULTS": "5","DELAY_BETWEEN_RESULTS_IN_MINUTES": "3"}
  ResultsUpload_model_name: uiauto
  time_out: 2400

prod_helm_task_NONE_runs_forever_GFN:
  name: "task_sample_ui_auto"
  Helm_Chart: "rw983xdqtcdp/task-test"
  Helm_Chart_Version: "0.4"
  gpu_type: T10
  instance_type: g6.full
  maximum_runtime_duration: "None (runs forever)"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_helm_task_NONE_runs_forever_GFN:
  name: "task_sample_ui_auto"
  Helm_Chart: "tadiathdfetp/task-test"
  Helm_Chart_Version: "0.4"
  gpu_type: L40S
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "None (runs forever)"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

prod_helm_task_maxRuntimeDuration_larger_than_8_GFN:
  name: "task_sample_ui_auto"
  Helm_Chart: "rw983xdqtcdp/task-test"
  Helm_Chart_Version: "0.4"
  gpu_type: T10
  instance_type: g6.full
  maximum_runtime_duration: "12 hours"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_helm_task_maxRuntimeDuration_larger_than_8_GFN:
  name: "task_sample_ui_auto"
  Helm_Chart: "tadiathdfetp/task-test"
  Helm_Chart_Version: "0.4"
  gpu_type: L40S
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "12 hours"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_helm_task_sample_gfn_upload_result_with_telemetries:
  name: "task_helm_gfn_ui_auto"
  Helm_Chart: "tadiathdfetp/task-helmchart-byoo"
  Helm_Chart_Version: "0.1"
  gpu_type: L40S
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 2400

stg_helm_task_sample_dgxc_upload_result_with_telemetries:
  name: "task_helm_gfn_ui_auto"
  Helm_Chart: "tadiathdfetp/task-helmchart-byoo"
  Helm_Chart_Version: "0.2"
  gpu_type: H100
  instance_type: GCP.GPU.H100_1x
  clusters: nvcf-dgxc-k8s-gcp-usc1-ct1
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "10 minutes"
  ResultsUpload_model_name: uiauto
  time_out: 2400

prod_container_task_sample_byoc_upload_result:
  name: "task_container_byoc_ui_auto"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "latest"
  gpu_type: A10
  instance_type: OCI.GPU.A10_1x
  cluster_type: "nvcf-qa-oci-oke "
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_container_task_sample_byoc_upload_result:
  name: "task_container_byoc_ui_auto"
  container: "tadiathdfetp/tasks_sample"
  tag: "latest"
  gpu_type: H100
  instance_type: GCP.GPU.H100_1x
  cluster_type: "nvcf-qa-cluster-gcp "
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

prod_container_task_sample_byoc_model_resource:
  name: "task_sample_ui_auto"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "latest"
  model: [["rw983xdqtcdp/model_nvcf_qa", "0.1", "a"]]
  resource: [["rw983xdqtcdp/resource_nvcf_qa", "0.1", "b"]]
  gpu_type: A10
  instance_type: OCI.GPU.A10_1x
  cluster_type: "nvcf-qa-oci-oke "
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_container_task_sample_byoc_model_resource:
  name: "task_sample_ui_auto"
  container: "tadiathdfetp/tasks_sample"
  tag: "latest"
  model: [["tadiathdfetp/model_nvcf_qa", "0.1", "a"]]
  resource: [["tadiathdfetp/resource_nvcf_qa", "0.1", "b"]]
  gpu_type: H100
  instance_type: GCP.GPU.H100_1x
  cluster_type: "nvcf-qa-cluster-gcp "
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

prod_container_task_sample_non_gfn_non_byoc_upload_result:
  name: "task_container_non_gfn_non_byoc_ui_auto"
  container: "rw983xdqtcdp/tasks_sample"
  tag: "latest"
  gpu_type: H100
  instance_type: OCI.GPU.H100_1x
  cluster_type: "nvcf-dgxc-k8s-oci-nrt-prd7 "
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_container_task_sample_non_gfn_non_byoc_upload_result:
  name: "task_container_non_gfn_non_byoc_ui_auto"
  container: "tadiathdfetp/tasks_sample"
  tag: "latest"
  gpu_type: AD102GL
  instance_type: DGX-CLOUD.GPU.AD102GL_1x
  cluster_type: "nvcf-dgxc-k8s-forge-az24-dev6 "
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  ResultsUpload_model_name: uiauto
  time_out: 1200

stg_helm_task_helm_reval_service:
  name: "task_helm_reval_service"
  Helm_Chart: "tadiathdfetp/nvcf-test-func"
  Helm_Chart_Version: "0.0.2"
  gpu_type: L40S
  instance_type: gl40s_1.br25_2xlarge
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  helm_chart_overrides_data:
    "serviceAccount": {"create":true}
  ResultsUpload_model_name: uiauto
  time_out: 2400

stg_helm_task_sample_non_dgxc_upload_result:
  name: "task_helm_non_dgxc_ui_auto"
  Helm_Chart: "tadiathdfetp/task-test"
  Helm_Chart_Version: "0.4"
  gpu_type: H100
  model: [["tadiathdfetp/model_nvcf_qa", "0.1", "a"]]
  resource: [["tadiathdfetp/resource_nvcf_qa", "0.1", "b"]]
  instance_type: GCP.GPU.H100_1x
  clusters: nvcf-qa-cluster-gcp
  maximum_runtime_duration: "1 hour"
  maximum_queued_duration: "1 hour"
  termination_grace_period_duration: "1 hour"
  helm_chart_overrides_data:
    "numberOfResults": 2
  ResultsUpload_model_name: uiauto
  time_out: 2400

