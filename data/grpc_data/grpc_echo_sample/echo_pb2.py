# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: data/grpc_data/grpc_echo_sample/echo.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n*data/grpc_data/grpc_echo_sample/echo.proto\"^\n\x0b\x45\x63hoRequest\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x14\n\x07repeats\x18\x02 \x01(\x05H\x00\x88\x01\x01\x12\x12\n\x05\x64\x65lay\x18\x03 \x01(\x02H\x01\x88\x01\x01\x42\n\n\x08_repeatsB\x08\n\x06_delay\"\\\n\tEchoReply\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x14\n\x07repeats\x18\x02 \x01(\x05H\x00\x88\x01\x01\x12\x12\n\x05\x64\x65lay\x18\x03 \x01(\x02H\x01\x88\x01\x01\x42\n\n\x08_repeatsB\x08\n\x06_delay2i\n\x04\x45\x63ho\x12)\n\x0b\x45\x63hoMessage\x12\x0c.EchoRequest\x1a\n.EchoReply\"\x00\x12\x36\n\x14\x45\x63hoMessageStreaming\x12\x0c.EchoRequest\x1a\n.EchoReply\"\x00(\x01\x30\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'data.grpc_data.grpc_echo_sample.echo_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_ECHOREQUEST']._serialized_start=46
  _globals['_ECHOREQUEST']._serialized_end=140
  _globals['_ECHOREPLY']._serialized_start=142
  _globals['_ECHOREPLY']._serialized_end=234
  _globals['_ECHO']._serialized_start=236
  _globals['_ECHO']._serialized_end=341
# @@protoc_insertion_point(module_scope)
