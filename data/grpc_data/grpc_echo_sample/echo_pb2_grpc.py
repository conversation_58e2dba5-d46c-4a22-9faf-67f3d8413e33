# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from data.grpc_data.grpc_echo_sample import echo_pb2 as data_dot_grpc__data_dot_grpc__echo__sample_dot_echo__pb2


class EchoStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.EchoMessage = channel.unary_unary(
                '/Echo/EchoMessage',
                request_serializer=data_dot_grpc__data_dot_grpc__echo__sample_dot_echo__pb2.EchoRequest.SerializeToString,
                response_deserializer=data_dot_grpc__data_dot_grpc__echo__sample_dot_echo__pb2.EchoReply.FromString,
                )
        self.EchoMessageStreaming = channel.stream_stream(
                '/Echo/EchoMessageStreaming',
                request_serializer=data_dot_grpc__data_dot_grpc__echo__sample_dot_echo__pb2.EchoRequest.SerializeToString,
                response_deserializer=data_dot_grpc__data_dot_grpc__echo__sample_dot_echo__pb2.EchoReply.FromString,
                )


class EchoServicer(object):
    """Missing associated documentation comment in .proto file."""

    def EchoMessage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def EchoMessageStreaming(self, request_iterator, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_EchoServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'EchoMessage': grpc.unary_unary_rpc_method_handler(
                    servicer.EchoMessage,
                    request_deserializer=data_dot_grpc__data_dot_grpc__echo__sample_dot_echo__pb2.EchoRequest.FromString,
                    response_serializer=data_dot_grpc__data_dot_grpc__echo__sample_dot_echo__pb2.EchoReply.SerializeToString,
            ),
            'EchoMessageStreaming': grpc.stream_stream_rpc_method_handler(
                    servicer.EchoMessageStreaming,
                    request_deserializer=data_dot_grpc__data_dot_grpc__echo__sample_dot_echo__pb2.EchoRequest.FromString,
                    response_serializer=data_dot_grpc__data_dot_grpc__echo__sample_dot_echo__pb2.EchoReply.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'Echo', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Echo(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def EchoMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/Echo/EchoMessage',
            data_dot_grpc__data_dot_grpc__echo__sample_dot_echo__pb2.EchoRequest.SerializeToString,
            data_dot_grpc__data_dot_grpc__echo__sample_dot_echo__pb2.EchoReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def EchoMessageStreaming(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(request_iterator, target, '/Echo/EchoMessageStreaming',
            data_dot_grpc__data_dot_grpc__echo__sample_dot_echo__pb2.EchoRequest.SerializeToString,
            data_dot_grpc__data_dot_grpc__echo__sample_dot_echo__pb2.EchoReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
