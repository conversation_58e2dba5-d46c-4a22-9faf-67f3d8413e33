prod_function_pytriton_echo:
  name: "pytriton_echo_ui_auto"
  container: "rw983xdqtcdp/pytriton_echo_sample"
  tag: "1.0 (Latest)"
  inference_protocol: "HTTP"
  inference_endpoint: "/v2/models/echo/infer"
  health_endpoint: "/v2/health/ready"
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

stg_function_pytriton_echo:
  name: "pytriton_echo_ui_auto"
  container: "tadiathdfetp/pytriton_echo_sample"
  tag: "1.0 (Latest)"
  inference_protocol: "HTTP"
  inference_endpoint: "/v2/models/echo/infer"
  health_endpoint: "/v2/health/ready"
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

fastapi_echo_function:
  name: "fastapi_echo_dedicated_for_dailyrun"
  func_id: "4767c2a2-2c3f-4277-b1b3-7cce7025f246"
  func_vers_id: "2dcbb38a-acd0-4a9d-bc2c-63f6b7d77d38"
  func_desc: "fastapi_echo_dedicated_for_dailyrun description"
  func_type: "Container"
  tags: "tag1"
  container: "rw983xdqtcdp/fastapi_echo_sample_prod"
  tag: "latest"
  models: "—"
  resources: "—"
  inference_protocol: "HTTP"
  inference_port: "8000"
  health_port: "8000 (Default)"
  inference_endpoint: "/echo"
  health_endpoint: "/health"
  run_command_overrides: "—"
  testenv: "testenv: testenv_value"
  total_instance_count: "1"
  instance_type: "gl40_1.br20_2xlarge"
  gpu: "L40"
  min_max_instances: "1 / 1"
  minimum_instances: "1"
  maximum_instances: "1"
  max_concurrency: "1"
  instance_count: "1"

fastapi_echo_function_stg:
  name: "fastapi_echo_dedicated_for_dailyrun"
  func_id: "36e69c78-928a-48f0-9100-651544d87c4f"
  func_vers_id: "4d2994ba-0beb-4b66-bf9b-2033a130713c"
  func_desc: "fastapi_echo_dedicated_for_dailyrun"
  func_type: "Container"
  tags: "—"
  container: "tadiathdfetp/fastapi_echo_sample"
  tag: "latest"
  models: "—"
  resources: "—"
  inference_protocol: "HTTP"
  inference_port: "8000"
  health_port: "8000 (Default)"
  inference_endpoint: "/echo"
  health_endpoint: "/health"
  run_command_overrides: "—"
  testenv: "—"
  total_instance_count: "1"
  instance_type: "Azure.GPU.A100_1x"
  gpu: "A100"
  min_max_instances: "1 / 1"
  minimum_instances: "1"
  maximum_instances: "1"
  max_concurrency: "1"
  instance_count: "1"

prod_function_version_test:
  func_id: "c92b9e2c-5663-47a1-8899-e68cf8e8c76d"

stg_function_version_test:
  func_id: "9c8d2bb2-ccd4-40ff-95ef-ea44b2c04de4"

prod_function_version_test_helm_chart:
  name: "inference_hc_dedicated_for_dailyrun"
  func_id: "6d60e8a4-0ea7-4a5c-8193-a568420ab0e2"
  func_vers_id: "15e6a616-b162-451b-9e1d-ef02b5ad55ca"
  func_type: "Helm Chart"
  func_desc: "inference_hc_dedicated_for_dailyrun"
  helm_chart: "https://helm.ngc.nvidia.com/rw983xdqtcdp/charts/inference-helm-chart-prod-0.1.tgz"
  helm_chart_service_name: "entrypoint"
  inference_protocol: "HTTP"
  inference_port: "8001"
  inference_endpoint: "/echo"
  health_endpoint: "/health"
  run_command_overrides: "—"
  instance_type: "gl40_1.br20_2xlarge"
  gpu: "L40"

stg_function_version_test_helm_chart:
  name: "inference_hc_dedicated_for_dailyrun"
  func_id: "d3b22fca-b7a7-4bd7-9f9c-dff599d30146"
  func_vers_id: "ccfc9555-533c-423a-893c-7b4335e3efcb"
  func_type: "Helm Chart"
  func_desc: "inference_hc_dedicated_for_dailyrun"
  helm_chart: "https://helm.stg.ngc.nvidia.com/tadiathdfetp/charts/inference-helm-chart-0.3.tgz"
  helm_chart_service_name: "entrypoint"
  inference_protocol: "HTTP"
  inference_port: "8001"
  inference_endpoint: "/echo"
  health_endpoint: "/health"
  run_command_overrides: "—"
  instance_type: "DGX-CLOUD.GPU.AD102GL_1x"
  gpu: "AD102GL"

function_clone_single_version:
  func_container: true
  func_name: "fastapi_echo_dedicated_for_regression_test_single-version"
  func_desc: "Dedicated for QA regression test"
  tags: "Regression_Test, Canary_Test"
  container: "rw983xdqtcdp/fastapi_echo_sample"
  tag: "latest"
  inference_protocol: "HTTP"
  inference_port: 8000
  inference_endpoint: "/echo"
  health_endpoint: "/health"
  health_port: 8000
  model: [["rw983xdqtcdp/llama3-8b-instruct-hf", "0.10.0+cbc614f5-4100z1-fp16-lora", "a"]]

function_clone_single_version_stg:
  func_container: true
  func_name: "fastapi_echo_dedicated_for_regression_test_single-version_stg"
  func_desc: "Dedicated for QA regression test"
  tags: "Regression_Test, Canary_Test"
  container: "tadiathdfetp/fastapi_echo_sample"
  tag: "latest"
  inference_protocol: "HTTP"
  inference_port: 8000
  inference_endpoint: "/echo"
  health_endpoint: "/health"
  health_port: 8000
  model: [["tadiathdfetp/model_nvcf_qa", "0.1", "a"]]

grpc_echo_sample:
  name: "grpc_echo_sample_ui_auto"
  container: "rw983xdqtcdp/grpc_echo_sample"
  tag: "v1.0 (Latest)"
  inference_protocol: "gRPC"
  inference_port: "8001"
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

grpc_echo_sample_stg:
  name: "grpc_echo_sample_ui_auto"
  container: "tadiathdfetp/grpc_echo_sample"
  tag: "v1.0 (Latest)"
  inference_protocol: "gRPC"
  inference_port: "8001"
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

grpc_echo_helm_inference_chart:
  name: "helm_chart_http_ui_auto"
  Helm_Chart: "rw983xdqtcdp/inference-test-grpc"
  Helm_Chart_Version: "2.3"
  Helm_Chart_Service_Name: "entrypoint"
  inference_protocol: "gRPC"
  inference_port: 8001
  inference_endpoint: "/grpc"
  health_endpoint: "/v2/health/ready"
  health_port: 8001
  time_out: 1200

grpc_echo_helm_inference_chart_stg:
  name: "helm_chart_http_ui_auto"
  Helm_Chart: "tadiathdfetp/inference-test-grpc"
  Helm_Chart_Version: "3.0"
  Helm_Chart_Service_Name: "entrypoint"
  inference_protocol: "gRPC"
  inference_port: 8001
  inference_endpoint: "/grpc"
  health_endpoint: "/v2/health/ready"
  health_port: 8001
  time_out: 1200

deployment_min:
  gpu_list:
    - 'T10'

deployment_min_stg:
  gpu_list:
    - 'T10'
  instance_detail_list:
    - name: g6.full

deployment_byoc:
  gpu_list:
    - 'A10'
  cluster_list:
    - 'nvcf-qa-oci-oke'

deployment_byoc_stg:
  gpu_list:
    - 'H100'
  cluster_list:
    - 'nvcf-qa-cluster-gcp'

deployment_with_L40G_br25:
  gpu_list:
    - L40G
  instance_detail_list:
    - name: gl40g_1.br25_2xlarge

deployment_with_L40S_br25_rate_limit:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
  rate_limit:
    rate_limit_value: 10/hour
    exclued_nca_ids:
      - e15g_kOXsdLwXAA4qA4LJ-QCHSv1X0Xozto1VTsaoa4
    helm_chart_override:
      test: value

deployment_with_L40S_br25_rate_limit_5rps:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
  rate_limit:
    rate_limit_value: 5/s
    exclued_nca_ids:
      - None
    helm_chart_override:
      test: value

deployment_container_with_L40S_br25_rate_limit_5rps:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
  rate_limit:
    rate_limit_value: 5/s
    exclued_nca_ids:
      - None

deployment_container_with_L40S_br25_rate_limit_5_requests_per_minute:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
  rate_limit:
    rate_limit_value: 5/minute
    exclued_nca_ids:
      - None


deployment_container_with_L40S_br25_rate_limit_5_requests_per_minute_excluded_test_nca_ids:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
  rate_limit:
    rate_limit_value: 5/minute
    exclued_nca_ids:
      - e15g_kOXsdLwXAA4qA4LJ-QCHSv1X0Xozto1VTsaoa4
      - nac_id_invalid

deployment_container_with_L40S_br25_rate_limit_5rps_excluded_invalid_nca_ids:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
  rate_limit:
    rate_limit_value: 5/s
    exclued_nca_ids:
      - nac_id_invalid_1
      - nac_id_invalid_2

deployment_container_with_L40S_br25_rate_limit_negative:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
  rate_limit:
    rate_limit_value: -1/s
    exclued_nca_ids:
      - None

deployment_container_with_L40S_br25_rate_limit_zero:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
  rate_limit:
    rate_limit_value: 0/s
    exclued_nca_ids:
      - None

deployment_container_with_L40S_br25_rate_limit_float:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
  rate_limit:
    rate_limit_value: 3.5/s
    exclued_nca_ids:
      - None


deployment_container_with_L40S_br25_rate_limit_100request_min:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
  rate_limit:
    rate_limit_value: 100/minute
    exclued_nca_ids:
      - None

deployment_container_with_L40S_br25_rate_limit_1000request_hour:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
  rate_limit:
    rate_limit_value: 1000/hour
    exclued_nca_ids:
      - None

deployment_container_with_L40S_br25_rate_limit_10000request_day:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
  rate_limit:
    rate_limit_value: 10000/day
    exclued_nca_ids:
      - None

deployment_with_L40S_br25_default_rate_limit:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
  rate_limit: ~

deployment_with_L40S_br25_without_rate_limit_and_with_override:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
  rate_limit:
    rate_limit_value: ~
    exclued_nca_ids: ~
    helm_chart_override:
      test: value


deployment_with_L40S_br25_with_duplicated_invalid_exclued_nca_ids:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
  rate_limit:
    rate_limit_value: 100/hour
    exclued_nca_ids:
      - e15g_invliad_excluded_nca_id
      - e15g_invliad_excluded_nca_id
    helm_chart_override: ~

deployment_with_L40S_br25:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge

deployment_with_A100:
  gpu_list:
    - A100
  instance_regions_filter_list:
    - us-east-1
  instance_detail_list:
    - name: AZURE.GPU.A100_1x
      cluster_list:
        - nvcf-dgxc-k8s-azr-scus-dev1
      min_instances: 1
      max_instances: 1
      max_concurrency: 2

deployment_with_H100:
  gpu_list:
    - H100
  instance_regions_filter_list:
    - ap-east-1
  instance_detail_list:
    - name: OCI.GPU.H100_1x
      cluster_list:
        - nvcf-dgxc-k8s-oci-nrt-prd7
      target_region_list:
        - ap-east-1
      min_instances: 1
      max_instances: 1
      max_concurrency: 2

deployment_with_dgxc_stg:
  gpu_list:
    - H100
  instance_detail_list:
    - name: GCP.GPU.H100_1x
      cluster_list:
        - nvcf-dgxc-k8s-gcp-usc1-ct1
      min_instances: 1
      max_instances: 1
      max_concurrency: 2

deployment_with_dgxc_forge_stg:
  gpu_list:
    - L40
  instance_detail_list:
    - name: DGX-CLOUD.GPU.L40_1x
      cluster_list:
        - nvcf-dgxc-k8s-forge-az60-ct1
      min_instances: 1
      max_instances: 1
      max_concurrency: 1

deployment_with_H100_2instances:
  gpu_list:
    - H100
  instance_regions_filter_list:
    - ap-east-1
  instance_detail_list:
    - name: OCI.GPU.H100_1x
      cluster_list:
        - nvcf-dgxc-k8s-oci-nrt-prd7
      target_region_list:
        - ap-east-1
      min_instances: 1
      max_instances: 2
      max_concurrency: 100

deployment_with_H100_2instances_stg:
  gpu_list:
    - H100
  instance_detail_list:
    - name: GCP.GPU.H100_1x
      cluster_list:
        - nvcf-qa-cluster-gcp
      min_instances: 1
      max_instances: 2
      max_concurrency: 100

deployment_with_instance:
  gpu_list:
    - T10
  instance_regions_filter_list:
    - eu-north-1
    - eu-south-1
    - us-east-1
    - us-west-1
  instance_attributes_filter_list:
    - Graphics Optimized
    - Streaming Optimized

deployment_with_instance_stg:
  gpu_list:
    - L40S
  instance_regions_filter_list:
    - us-west-1
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
  instance_attributes_filter_list:
    - SOC2 Compliant

deployment_with_instance_and_deployment_spec:
  gpu_list:
    - T10
  instance_regions_filter_list:
    - eu-north-1
    - eu-south-1
  instance_attributes_filter_list:
    - Graphics Optimized
    - Streaming Optimized
  instance_detail_list:
    - name: g6.full
      target_region_list:
        - eu-north-1
        - eu-south-1
      min_instances: 1
      max_instances: 1
      max_concurrency: 2
      target_attributes_list:
        - Graphics Optimized
        - Streaming Optimized

deployment_with_instance_and_deployment_spec_stg:
  gpu_list:
    - L40S
  instance_regions_filter_list:
    - us-west-1
  instance_attributes_filter_list:
    - SOC2 Compliant
  instance_detail_list:
    - name: gl40s_1.br25_2xlarge
      target_region_list:
        - us-west-1
      min_instances: 1
      max_instances: 1
      max_concurrency: 2
      target_attributes_list:
        - SOC2 Compliant

deployment_with_instance_and_deployment_spec_byoc:
  gpu_list:
    - A10
  instance_regions_filter_list:
    - us-west-2
  instance_detail_list:
    - name: OCI.GPU.A10_2x
      cluster_list:
        - nvcf-qa-oci-oke
      target_region_list:
        - us-west-2
      min_instances: 1
      max_instances: 1
      max_concurrency: 2

deployment_with_instance_and_deployment_spec_byoc_stg:
  gpu_list:
    - H100
  instance_regions_filter_list:
    - us-west-1
  instance_detail_list:
    - name: GCP.GPU.H100_1x
      cluster_list:
        - nvcf-qa-cluster-gcp
      target_region_list:
        - us-west-1
      min_instances: 1
      max_instances: 1
      max_concurrency: 2

deployment_L40_with_filter_spec:
  gpu_list:
    - L40
  instance_regions_filter_list:
    - eu-north-1
  instance_attributes_filter_list:
    - Graphics Optimized
  instance_detail_list:
    - name: gl40_1.br20_2xlarge
      target_region_list:
        - eu-north-1
      min_instances: 1
      max_instances: 1
      max_concurrency: 2
      target_attributes_list:
        - Graphics Optimized

echo_function_with_2_instances:
  name: "grpc_echo_dedicated_for_dailyrun"
  func_id: "220241c3-2fd8-4968-b6b7-fd8d676f2af9"
  func_vers_id: "019b039c-26aa-4b9e-85e8-82c37bdcaaab"

echo_function_with_2_instances_stg:
  name: "grpc_echo_dedicated_for_dailyrun"
  func_id: "90211d04-0ee7-4976-aaf8-50738cabad7a"
  func_vers_id: "70b4503d-5ca4-4052-aba2-a3dc57dc45f0"

deployment_with_dual_L40S:
  gpu_list:
    - L40S
  instance_detail_list:
    - name: gl40s_1x2.br25_4xlarge

deployment_with_dual_L40:
  gpu_list:
    - L40
  instance_detail_list:
    - name: gl40_1x2.br20_4xlarge

deployment_with_L40:
  gpu_list:
    - L40
  instance_detail_list:
    - name: gl40_1.br20_2xlarge

deployment_with_dual_L40G_br20:
  gpu_list:
    - L40G
  instance_detail_list:
    - name: gl40g_1x2.br20_4xlarge

deployment_with_dual_L40G_br25:
  gpu_list:
    - L40G
  instance_detail_list:
    - name: gl40g_1x2.br25_4xlarge

deployment_with_multiple_instances:
  gpu_list:
    - L40
    - L40S
  instance_detail_list:
    - name: gl40_1.br20_2xlarge
    - name: gl40s_1x2.br25_4xlarge

deployment_with_multiple_instances_stg:
  gpu_list:
    - T10
    - L40S
  instance_detail_list:
    - name: g6.full
    - name: gl40s_1.br25_2xlarge

helm_chart_overrides_data:
  configuration:
    key_one: ''
    key_two:
      key_two_subkey_one: ''
      key_two_subkey_two: ''

prod_function_helm_chart_HTTP:
  name: "helm_chart_http_ui_auto"
  Helm_Chart: "rw983xdqtcdp/inference-helm-chart-prod"
  Helm_Chart_Version: "0.1"
  Helm_Chart_Service_Name: "entrypoint"
  inference_protocol: "HTTP"
  health_port: 8001
  inference_port: 8001
  inference_endpoint: "/echo"
  health_endpoint: "/health"
  model: [["rw983xdqtcdp/model_nvcf_qa", "0.1", "a"]]
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

stg_function_helm_chart_HTTP:
  name: "helm_chart_http_ui_auto"
  Helm_Chart: "tadiathdfetp/inference-helm-chart"
  Helm_Chart_Version: "0.3"
  Helm_Chart_Service_Name: "entrypoint"
  inference_protocol: "HTTP"
  health_port: 8001
  inference_port: 8001
  inference_endpoint: "/echo"
  health_endpoint: "/health"
  model: [["tadiathdfetp/model_nvcf_qa", "0.1", "a"]]
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

prod_function_helm_chart_gRPC:
  name: "helm_chart_grpc_ui_auto"
  Helm_Chart: "rw983xdqtcdp/inference-test-grpc"
  Helm_Chart_Version: "2.3"
  Helm_Chart_Service_Name: "entrypoint"
  inference_protocol: "gRPC"
  inference_port: 8001
  model: [["rw983xdqtcdp/model_nvcf_qa", "0.1", "a"]]
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

stg_function_helm_chart_gRPC:
  name: "helm_chart_grpc_ui_auto"
  Helm_Chart: "tadiathdfetp/inference-test-grpc"
  Helm_Chart_Version: "3.0"
  Helm_Chart_Service_Name: "entrypoint"
  inference_protocol: "gRPC"
  inference_port: 8001
  model: [["tadiathdfetp/model_nvcf_qa", "0.1", "a"]]
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

prod_fastapi_echo_support_all_fields:
  name: "fastapi_echo_support_all_fields_ui_auto"
  container: "rw983xdqtcdp/fastapi_echo_model_resource"
  tag: "latest (Latest)"
  model: [["rw983xdqtcdp/model_nvcf_qa", "0.1", "a"]]
  resource: [["rw983xdqtcdp/resource_nvcf_qa", "0.1", "a"]]
  inference_protocol: "HTTP"
  inference_endpoint: "/echo"
  health_endpoint: "/health"
  inference_port: 8000
  health_port: 8000
  inference_port_run_command_overrides: 9000
  secrets: {"key": "aaa"}
  environment_variables: {"WORKER_COUNT": "20"}
  run_command_overrides: "uvicorn http_echo_server:app --host=0.0.0.0 --port=9000"
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

stg_fastapi_echo_support_all_fields:
  name: "fastapi_echo_support_all_fields_ui_auto"
  container: "tadiathdfetp/fastapi_echo_model_resource"
  tag: "latest (Latest)"
  model: [["tadiathdfetp/model_nvcf_qa", "0.1", "a"]]
  resource: [["tadiathdfetp/resource_nvcf_qa", "0.1", "a"]]
  inference_protocol: "HTTP"
  inference_endpoint: "/echo"
  health_endpoint: "/health"
  inference_port: 8000
  health_port: 8000
  inference_port_run_command_overrides: 8000
  secrets: {"key": "aaa"}
  environment_variables: {"WORKER_COUNT": "20"}
  run_command_overrides: "uvicorn http_echo_server:app --host=0.0.0.0 --port=8000"
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

prod_secrets_sample_function:
  name: "secrets_sample"
  container: "rw983xdqtcdp/secrets-sample"
  tag: "latest"
  inference_protocol: "HTTP"
  inference_endpoint: "/test"
  health_endpoint: "/health"
  secrets: {"key_1": "value_1"}
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

stg_secrets_sample_function:
  name: "secrets_sample"
  container: "tadiathdfetp/secrets-sample"
  tag: "latest (Latest)"
  inference_protocol: "HTTP"
  inference_endpoint: "/test"
  health_endpoint: "/health"
  secrets: {"key_1": "value_1"}
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

prod_gxcache_validator_function:
  name: "gxcache-validator"
  container: "rw983xdqtcdp/nvcf-gxcache-validator"
  tag: "1.1.0 (Latest)"
  inference_protocol: "HTTP"
  inference_endpoint: "/inference"
  health_endpoint: "/health"
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

prod_proxycache_sample_function:
  name: "proxycache"
  container: "rw983xdqtcdp/proxycache_sample"
  tag: "1.0.1 (Latest)"
  inference_protocol: "HTTP"
  inference_endpoint: "/test"
  health_endpoint: "/health"
  min_instances: "1"
  max_instances: "1"
  environment_variables: {"REQUESTS_CA_BUNDLE": "/etc/ssl/certs/ca-certificates.crt","PROXY_URL": "https://proxy-cache-validate-2.s3.us-west-2.amazonaws.com/mug.jpg"}
  time_out: 1200

prod_multi_node_scerets_test_helm_chart_function:
  name: "multi-node-secrets-test"
  helm_chart: "rw983xdqtcdp/multi-node-secrets-test"
  helm_chart_version: "0.1.3"
  helm_chart_service_name: "nccl-multi-node-test-secret"
  inference_protocol: "HTTP"
  inference_port: 8000
  inference_endpoint: "/test"
  health_endpoint: "/health"
  secrets: {"key_1": "value_1"}
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

prod_function_helm_chart_HTTP_invalid:
  name: "helm_chart_http_ui_auto_invalid"
  Helm_Chart: "rw983xdqtcdp/nvcf-test-func"
  Helm_Chart_Version: "0.0.2"
  Helm_Chart_Service_Name: "entrypoint"
  inference_protocol: "HTTP"
  inference_port: 8001
  inference_endpoint: "test"
  health_endpoint: "/health"
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

stg_function_helm_chart_HTTP_invalid:
  name: "helm_chart_http_ui_auto_invalid"
  Helm_Chart: "tadiathdfetp/nvcf-test-func"
  Helm_Chart_Version: "0.0.2"
  Helm_Chart_Service_Name: "entrypoint"
  inference_protocol: "HTTP"
  inference_port: 8001
  inference_endpoint: "test"
  health_endpoint: "/health"
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

nim_function_sample_on_L40:
  name: "nim_function_sample_on_L40"
  nim: "nim/meta/llama-3.1-8b-instruct"
  tag: "1.3.3"
  tags: "ui-auto"
  model_configuration: "0.12+2333135a3-l40sx1-bf16-throughput.1.3.121240"
  environment_variables: {"key_1": "value_1"}
  function_name_prefix: "nim_function_sample_on_L40"
  description: "NIM function sample on L40"
  time_out: 1200

stg_function_helm_reval_service:
  name: "helm_reval_service"
  Helm_Chart: "tadiathdfetp/nvcf-test-func"
  Helm_Chart_Version: "0.0.2"
  Helm_Chart_Service_Name: "entrypoint"
  inference_protocol: "HTTP"
  health_port: 8000
  inference_port: 8000
  inference_endpoint: "/echo"
  health_endpoint: "/health"
  min_instances: "1"
  max_instances: "1"
  time_out: 1200

deployment_with_T10_helm_chart_override:
  gpu_list:
    - T10
  instance_detail_list:
    - name: g6.full
  helm_chart_overrides_data:
    serviceAccount: {"create":true}