INVOKE_FUNCTION_PATH: &INVOKE_FUNCTION_PATH "/pexec/functions/"
INVOKE_FUNCTION_ID_PATH: &INVOKE_FUNCTION_ID_PATH "/pexec/functions/"
POLL_RESULT_PATH: &POLL_RESULT_PATH "/pexec/status"
APPLICATION_JSON: &APPLICATION_JSON "application/json"
FUNCTION_PATH: &FUNCTION_PATH "/functions"
DEPLOYMENT_PATH: &DEPLOYMENT_PATH "/deployments/functions/"
ESS_FUNCTIONS_NAME_PREFIX: &ESS_FUNCTIONS_NAME_PREFIX "ess_function_api_auto"
FUNCTIONS_NAME_PREFIX: &FUNCTIONS_NAME_PREFIX "function_api_auto"
QUEUE_DETAILS_PATH: &QUEUE_DETAILS_PATH "/queues/functions/"
ORG_CREDENTIAL_ADMIN_PATH: &ORG_CREDENTIAL_ADMIN_PATH "/accounts/juVK24w11tT-d4RbZmb6BohUbv4564z4njKG2OLYec8"
SERVICE_KEYT_PATH: &SERVICE_KEYT_PATH "/keys"
GPU_TYPES_PATH: &GPU_TYPES_PATH "/ngc/nvcf/deployments/available"


# Schemas
QUEUE_DETAILS_SCHEMA: &QUEUE_DETAILS_SCHEMA
  type: object
  properties:
      functionVersionId:
          type: string
      functionId:
          type: string
  required:
   - functionId

INVOKE_FUNCTION_PYTRITON_SCHEMA: &INVOKE_FUNCTION_PYTRITON_SCHEMA
  type: object
  properties:
    model_name:
      type: string
    model_version:
      type: string
    outputs:
      type: array
      items:
        type: object
        properties:
          name:
            type: string
          datatype:
            type: string
          shape:
            type: array
            items:
              type: integer
          data:
            type: array
            items:
              type: string
        required:
          - name
          - datatype
          - shape
          - data
  required:
    - model_name
    - model_version
    - outputs

INVOKE_FUNCTION_ID_PYTRITON: &INVOKE_FUNCTION_ID_PYTRITON
  req: &INVOKE_FUNCTION_ID_PYTRITON_REQ
    method: POST
    path: *INVOKE_FUNCTION_PATH
    json:
      inputs:
        - name: message
          shape:
            - 1
          datatype: BYTES
          data:
            - Hello
      outputs:
        - name: echo
          datatype: BYTES
          shape:
            - 1
  exp:
    description: "NVCF Invoke Pytriton Echo API"
    status_code: 200
    schema: *INVOKE_FUNCTION_PYTRITON_SCHEMA

POLL_RESULT_OF_FUNCTION_INVOCATION_REQUEST: &POLL_RESULT_OF_FUNCTION_INVOCATION_REQUEST
  req: &POLL_RESULT_REQ
    method: GET
    path: *POLL_RESULT_PATH
  exp: &POLL_RESULT_EXP
    description: "NVCF Poll Result of Function Invocation Request API"
    status_code: 200

GET_FUNCTION_LIST: &GET_FUNCTION_LIST
  req: &GET_FUNCTION_LIST_REQ
    method: GET
    path: *FUNCTION_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
  exp:
    description: "NVCF All List Function Versions API"
    status_code: 200

QUEUE_DETAILS: &QUEUE_DETAILS
  req: &QUEUE_DETAILS_REQ
    method: GET
    path: *QUEUE_DETAILS_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
  exp:
    description: "NVCF All List Function Versions API"
    status_code: 200
    schema: *QUEUE_DETAILS_SCHEMA

INVOKE_FUNCTION_ID_SECRET_SAMPLE: &INVOKE_FUNCTION_ID_SECRET_SAMPLE
  req: &INVOKE_FUNCTION_ID_SECRET_SAMPLE_REQ
    method: POST
    path: *INVOKE_FUNCTION_PATH
    json:
      key: "key_1"
  exp:
    description: "NVCF Invoke Secret Sample API"
    status_code: 200

INVOKE_FUNCTION_ID_WITH_SPECIFIC_SECRET_SAMPLE: &INVOKE_FUNCTION_ID_WITH_SPECIFIC_SECRET_SAMPLE
  req: &INVOKE_FUNCTION_ID_WITH_SPECIFIC_SECRET_SAMPLE_REQ
    method: POST
    path: *INVOKE_FUNCTION_PATH
    json:
      lab: "labuser"
  exp:
    description: "NVCF Invoke Secret Sample API"
    status_code: 200


INVOKE_FUNCTION_GXCACHE_HIT: &INVOKE_FUNCTION_GXCACHE_HIT
  req: &INVOKE_FUNCTION_GXCACHE_HIT_REQ
    method: POST
    path: *INVOKE_FUNCTION_PATH
    json:
      "reps": "50"
      "seed": "42"
  exp:
    description: "NVCF Invoke GXCache Hit API"
    status_code: 200


INVOKE_FUNCTION_PROXYCACHE_HIT: &INVOKE_FUNCTION_PROXYCACHE_HIT
  req: &INVOKE_FUNCTION_PROXYCACHE_HIT_REQ
    method: POST
    path: *INVOKE_FUNCTION_PATH
    json:
      "message": "Hello"

  exp:
    description: "NVCF Invoke ProxyCache Hit API"
    status_code: 200

INVOKE_NIM_FUNCTION: &INVOKE_NIM_FUNCTION
  req: &INVOKE_NIM_FUNCTION_REQ
    method: POST
    path: *INVOKE_FUNCTION_PATH
    json:
      "model": "meta/llama-3.1-8b-instruct"
      "messages": [
        {
          "role": "user",
          "content": "What is machine learning?"
        }
      ]
      "temperature": 0.7
      "max_tokens": 100
      "stream": false

  exp:
    description: "NVCF Invoke NIM Function API"
    status_code: 200

GET_ORG_CREDENTIAL_INFO: &GET_ORG_CREDENTIAL_INFO
  req: &GET_ORG_CREDENTIAL_INFO_REQ
    method: GET
    path: *ORG_CREDENTIAL_ADMIN_PATH
  exp:
    description: "Get Org Credential Info"
    status_code: 200

CREATE_FUNCTION_VERSION_SCHEMA: &CREATE_FUNCTION_VERSION_SCHEMA
  type: object
  properties:
    function:
      type: object
      properties:
        id:
          type: string
          pattern: "^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"
        ncaId:
          type: string
        versionId:
          type: string
          pattern: "^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"
        name:
          type: string
        status:
          type: string
          enum:
            - ACTIVE
            - ERROR
            - INACTIVE
            - DEPLOYING
        inferenceUrl:
          type: string
        containerImage:
          type: string
        apiBodyFormat:
          type: string
        healthUri:
          type: string
        createdAt:
          type: string
        activeInstances:
          type: array
      required:
        - id
        - ncaId
        - versionId
        - name
        - status
        - healthUri
        - createdAt

CREATE_ESS_FUNCTION: &CREATE_ESS_FUNCTION
  req: &CREATE_ESS_FUNCTION_REQ
    method: POST
    path: *FUNCTION_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      "name": *ESS_FUNCTIONS_NAME_PREFIX
      inferencePort: 8000
      health:
        protocol: "HTTP"
        uri: "/health"
        port: 8000
        timeout: "PT10S"
        expectedStatusCode: 0
      apiBodyFormat: "PREDICT_V2"
      description: "string"
      secrets:
        - name: "NVCF_secret"
          value: "This is a secret"
      functionType: "DEFAULT"
  exp: &CREATE_ESS_FUNCTION_EXP
    description: "NVCF Create Function API with ESS"
    status_code: 200
    schema: *CREATE_FUNCTION_VERSION_SCHEMA

CREATE_FUNCTION: &CREATE_FUNCTION
  req: &CREATE_FUNCTION_REQ
    method: POST
    path: *FUNCTION_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      "name": *FUNCTIONS_NAME_PREFIX
  exp: &CREATE_FUNCTION_EXP
    description: "NVCF Create Function API"
    status_code: 200
    schema: *CREATE_FUNCTION_VERSION_SCHEMA

DEPLOY_FUNCTION_SCHEMA: &DEPLOY_FUNCTION_SCHEMA
  type: object
  properties:
    deployment:
      type: object
      description: Function deployment response
      required:
        - functionId
        - functionVersionId
        - ncaId
        - functionStatus
        - requestQueueUrl
        - deploymentSpecifications
      properties:
        functionId:
          type: string
          description: Function id
        functionVersionId:
          type: string
          description: Function version id
        ncaId:
          type: string
          description: NVIDIA Cloud Account Id
        functionStatus:
          type: string
          description: Function status
          enum:
            - ACTIVE
            - DEPLOYING
            - ERROR
            - INACTIVE
            - DELETED
        requestQueueUrl:
          type: string
          description: SQS Request Queue URL
        functionName:
          type: string
          description: function name
        createdAt:
          type: string
          description: creat time
        healthInfo:
          type: array
          description: Health info for a deployment specification is included only if there are any issues/errors.
          items:
            type: object
            required:
              - sisRequestId
              - gpu
              - backend
              - error
            properties:
              sisRequestId:
                type: string
                description: SIS Request ID
              gpu:
                type: string
                description: GPU Type as per SDD
              backend:
                type: string
                description: Backend/CSP where the GPU powered instance will be launched
              instanceType:
                type: string
                description: Instance type
              error:
                type: string
                description: Deployment error
        deploymentSpecifications:
          type: array
          description: Function deployment details
          items:
            type: object
            required:
              - gpu
              - instanceType
              - maxInstances
              - minInstances
            properties:
              gpu:
                type: string
                description: GPU name from the cluster
              backend:
                type: string
                description: Backend/CSP where the GPU powered instance will be launched
              maxInstances:
                type: integer
                format: int32
                description: Maximum number of spot instances for the deployment
              minInstances:
                type: integer
                format: int32
                description: Minimum number of spot instances for the deployment
              instanceType:
                type: string
                description: Instance type, based on GPU, assigned to a Worker
              availabilityZones:
                type: array
                items:
                  type: string
                description: List of availability-zones(or clusters) in the cluster group
              maxRequestConcurrency:
                type: integer
                format: int32
                minimum: 1
                maximum: 1024
                description: Max request concurrency between 1 (default) and 1024.
              configuration:
                type: object
                additionalProperties: true
                description: Configuration object that may contain additional fields.

DEPLOY_FUNCTION_A10G: &DEPLOY_FUNCTION_A10G
  req:
    method: POST
    path: *DEPLOYMENT_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      deploymentSpecifications:
        - gpu: "A10G"
          backend: "GFN"
          instanceType: "ga10g_1.br20_2xlarge"
          maxInstances: 1
          minInstances: 1
  exp:
    description: "NVCF Deploy A10G Functions API"
    status_code: 200
    schema: *DEPLOY_FUNCTION_SCHEMA
  timeout: 1800

DEPLOY_FUNCTION_L40S: &DEPLOY_FUNCTION_L40S
  req:
    method: POST
    path: *DEPLOYMENT_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      deploymentSpecifications:
        - gpu: "L40S"
          backend: "GFN"
          instanceType: "gl40s_1.br25_2xlarge"
          maxInstances: 1
          minInstances: 1
  exp:
    description: "NVCF Deploy L40S Functions API"
    status_code: 200
    schema: *DEPLOY_FUNCTION_SCHEMA
  timeout: 1800

DEPLOY_FUNCTION_H100_NON_GFN: &DEPLOY_FUNCTION_H100_NON_GFN
  req:
    method: POST
    path: *DEPLOYMENT_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      deploymentSpecifications:
        - gpu: "H100"
          backend: "nvcf-dgxc-k8s-oci-nrt-prd5"
          instanceType: "OCI.GPU.H100_1x"
          maxInstances: 1
          minInstances: 1
  exp:
    description: "NVCF Deploy H100 non-GFN Functions API"
    status_code: 200
    schema: *DEPLOY_FUNCTION_SCHEMA
  timeout: 1800

DEPLOY_FUNCTION_H100_NON_GFN_STG: &DEPLOY_FUNCTION_H100_NON_GFN_STG
  req:
    method: POST
    path: *DEPLOYMENT_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      deploymentSpecifications:
        - gpu: "H100"
          backend: "nvcf-dgxc-k8s-oci-lhr-ct1"
          instanceType: "OCI.GPU.H100_1x"
          maxInstances: 1
          minInstances: 1
  exp:
    description: "NVCF Deploy H100 non-GFN Functions API"
    status_code: 200
    schema: *DEPLOY_FUNCTION_SCHEMA
  timeout: 1800

DEPLOY_FUNCTION_AD102GL_NON_GFN_STG: &DEPLOY_FUNCTION_AD102GL_NON_GFN_STG
  req:
    method: POST
    path: *DEPLOYMENT_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      deploymentSpecifications:
        - gpu: "AD102GL"
          backend: "nvcf-dgxc-k8s-forge-az24-dev6"
          instanceType: "DGX-CLOUD.GPU.AD102GL_1x"
          maxInstances: 1
          minInstances: 1
  exp:
    description: "NVCF Deploy AD102GL non-GFN Functions API"
    status_code: 200
    schema: *DEPLOY_FUNCTION_SCHEMA
  timeout: 1800

DEPLOY_FUNCTION_A10_BYOC: &DEPLOY_FUNCTION_A10_BYOC
  req:
    method: POST
    path: *DEPLOYMENT_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      deploymentSpecifications:
        - gpu: "A10"
          backend: "nvcf-qa-oci-oke"
          instanceType: "OCI.GPU.A10_1x"
          maxInstances: 1
          minInstances: 1
  exp:
    description: "NVCF Deploy A10 BYOC Functions API"
    status_code: 200
    schema: *DEPLOY_FUNCTION_SCHEMA
  timeout: 1800

DEPLOY_FUNCTION_A100_BYOC_STG: &DEPLOY_FUNCTION_A100_BYOC_STG
  req:
    method: POST
    path: *DEPLOYMENT_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      deploymentSpecifications:
        - gpu: "A100"
          backend: "AZURE"
          instanceType: "AZURE.GPU.A100_1x"
          maxInstances: 1
          minInstances: 1
  exp:
    description: "NVCF Deploy A10 BYOC Functions API"
    status_code: 200
    schema: *DEPLOY_FUNCTION_SCHEMA
  timeout: 1800


DEPLOY_FUNCTION_T10: &DEPLOY_FUNCTION_T10
  req:
    method: POST
    path: *DEPLOYMENT_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      deploymentSpecifications:
        - gpu: "T10"
          backend: "GFN"
          instanceType: "g6.full"
          maxInstances: 1
          minInstances: 1
  exp:
    description: "NVCF Deploy T10 Functions API"
    status_code: 200
    schema: *DEPLOY_FUNCTION_SCHEMA
  timeout: 1800

DEPLOY_FUNCTION_L40S_STG: &DEPLOY_FUNCTION_L40S_STG
  req:
    method: POST
    path: *DEPLOYMENT_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      deploymentSpecifications:
        - gpu: "L40S"
          backend: "GFN"
          instanceType: "gl40s_1.br25_2xlarge"
          maxInstances: 1
          minInstances: 1
  exp:
    description: "NVCF Deploy L40S Functions API"
    status_code: 200
    schema: *DEPLOY_FUNCTION_SCHEMA
  timeout: 1800

UPDATE_FUNCTION_T10: &UPDATE_FUNCTION_T10
  req:
    method: PUT
    path: *DEPLOYMENT_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      deploymentSpecifications:
        - gpu: "T10"
          backend: "GFN"
          instanceType: "g6.full"
          maxInstances: 1
          minInstances: 1
          maxConcurrency: 2
  exp:
    description: "NVCF Deploy T10 Functions API"
    status_code: 200
    schema: *DEPLOY_FUNCTION_SCHEMA

UPDATE_FUNCTION_L40S_STG: &UPDATE_FUNCTION_L40S_STG
  req:
    method: PUT
    path: *DEPLOYMENT_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      deploymentSpecifications:
        - gpu: "L40S"
          backend: "GFN"
          instanceType: "gl40s_1.br25_2xlarge"
          maxInstances: 1
          minInstances: 1
          maxConcurrency: 2
  exp:
    description: "NVCF Deploy L40S Functions API"
    status_code: 200
    schema: *DEPLOY_FUNCTION_SCHEMA

CREATE_INFERENCE_HELM_CHART: &CREATE_INFERENCE_HELM_CHART
  req: &CREATE_FUNCTION_HELM_CHART_REQ
    method: POST
    path: *FUNCTION_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      "name": *FUNCTIONS_NAME_PREFIX
  exp:
    description: "NVCF Create Inference Helm Chart Function API"
    status_code: 200
    schema: *CREATE_FUNCTION_VERSION_SCHEMA

INVOKE_FUNCTION_PENDING_RESULT: &INVOKE_FUNCTION_PENDING_RESULT
  req:
    method: POST
    path: *INVOKE_FUNCTION_ID_PATH
    headers:
      NVCF-POLL-SECONDS: "1"
    json:
      inputs:
        - name: message
          shape:
            - 1
          datatype: BYTES
          data:
            - Hello
        - name: response_delay_in_seconds
          shape:
            - 1
          datatype: FP32
          data:
            - 20
      outputs:
        - name: echo
          datatype: BYTES
          shape:
            - 1
  exp:
    description: "NVCF Invoke Function Pending Result API"
    status_code: 202

INVOKE_FUNCTION_PENDING_RESULT_RESPONSE_DELAY: &INVOKE_FUNCTION_PENDING_RESULT_RESPONSE_DELAY
  req:
    method: POST
    path: *INVOKE_FUNCTION_PATH
    headers:
      NVCF-POLL-SECONDS: "1"
    json:
      inputs:
        - name: message
          shape:
            - 1
          datatype: BYTES
          data:
            - Hello
        - name: response_delay_in_seconds
          shape:
            - 1
          datatype: FP32
          data:
            - 90
      outputs:
        - name: echo
          datatype: BYTES
          shape:
            - 1
  exp:
    description: "NVCF Invoke Pending Result Response Delay API"
    status_code: 202

DEPLOY_FUNCTION_CLUSTER:
  req:
    method: POST
    path: "/deployments/functions/"
    headers:
      accept: "application/json"
      content-type: "application/json"
    json:
      deploymentSpecifications:
        - gpu: "L40"
          clusters: ""
          instanceType: ""
          maxInstances: 1
          minInstances: 1
  exp:
    description: "NVCF Deploy Functions API on CLUSTER"
    status_code: 200
    schema: *DEPLOY_FUNCTION_SCHEMA
  timeout: 1800

GET_SERVICE_KEYS:
  req:
    method: GET
    path: *SERVICE_KEYT_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
  exp:
    description: "Get Service Key"
    status_code: 200

DELETE_SERVICE_KEYS:
  req:
    method: DELETE
    path: *SERVICE_KEYT_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
  exp:
    description: "Get Service Key"
    status_code: 200

CREATE_SERVICE_KEYS:
  req:
    method: POST
    path: *SERVICE_KEYT_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
    json:
      name: temp_limitation
      type: SERVICE_KEY
      expiryDate: time_to_expire
      policies:
        - product: nv-cloud-functions
          scopes:
            - invoke_function
          resources:
            - id: "*"
              type: account-functions
  exp:
    description: "Get Service Key"
    status_code: 200

test_invoke_an_active_function_with_multiple_times_and_check_the_Total_Invocations_in_Metrics: *CREATE_FUNCTION
test_verify_new_key_by_get_nvcf_account_credentials: *GET_ORG_CREDENTIAL_INFO

GET_GPU_TYPES:
  req:
    method: GET
    path: *GPU_TYPES_PATH
    headers:
      accept: *APPLICATION_JSON
      content-type: *APPLICATION_JSON
  exp:
    description: "Get GPU Types"
    status_code: 200

INVOKE_FUNCTION_PROXYCACHE_HIT_FAILURE: &INVOKE_FUNCTION_PROXYCACHE_HIT_FAILURE
  req: &INVOKE_FUNCTION_PROXYCACHE_HIT_FAILURE_REQ
    method: POST
    path: *INVOKE_FUNCTION_PATH
    json:
      "message": 1
  exp:
    description: "NVCF Invoke ProxyCache Hit API with failure"
    status_code: 422

INVOKE_BYOO_FUNCTION: &INVOKE_BYOO_FUNCTION
  req: &INVOKE_BYOO_FUNCTION_REQ
    method: POST
    path: *INVOKE_FUNCTION_PATH
    json:
      "name": "foo"
  exp:
    description: "NVCF Invoke BYOO Function API"
    status_code: 200

