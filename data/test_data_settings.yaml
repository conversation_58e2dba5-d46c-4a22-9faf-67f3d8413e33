# Settings Page Test Data Configuration

defaults:
  skip_if_no_clusters: true
  skip_if_no_endpoints: true
  environment: "development"


clusters:
  test_cluster:
    cluster_name: "test-cluster-98765"
    expected_status: "NOT READY"
    expect_cluster_to_exist: true


telemetry:
  endpoints:
    kratos_thanos:
      name: "byoo_auto_test_kratos-thanos"
      provider: "Kratos Thanos"
      endpoint_url: "https://nvcf-receivers.thanos.nvidiangn.net/api/v1/receive"
      private_key: "test11"
      client_certificate: "test11"
      telemetry_types:
        metrics: true
      protocol: "HTTP"

    grafana:
      name: "byoo_auto-test-endpoint"
      provider: "Grafana Cloud"
      instance_id: "919931"
      endpoint_url: "https://otlp-gateway-prod-us-west-0.grafana.net/otlp"
      telemetry_types:
        logs: true
        metrics: true
      protocol: "HTTP"

    datadog:
      name: "byoo_auto_datadog-test-endpoint"
      provider: "Datadog"
      endpoint_url: "datadoghq.com"
      key: "********************************"
      protocol: "GRPC"
      telemetry_types:
        logs: true
        metrics: true

    azure_monitor:
      name: "byoo_auto_test_azure_monitor"
      provider: "Azure Monitor"
      endpoint_url: "https://uksouth-1.in.applicationinsights.azure.com/"
      instrumentation_key: "test11"
      live_endpoint: "https://uksouth.livediagnostics.monitor.azure.com/"
      application_id: "test11"
      telemetry_types:
        logs: true
        metrics: true
        traces: true
      protocol: "HTTP"

    prometheus_remote_write:
      name: "byoo_auto_test_prometheus"
      provider: "Prometheus Remote Write"
      endpoint_url: "https://nvcf-receivers.thanos.nvidiangn.net/api/v1/receive"
      private_key: "test private key"
      client_certificate: "test client certificate"
      cafile: "test cafile"
      telemetry_types:
        metrics: true
      protocol: "HTTP"

    service_now:
      name: "byoo_auto_test_service_now"
      provider: "ServiceNow"
      endpoint_url: "https://sandbox-receivers.thanos.nvidiangn.net/api/v1/receive"
      key: "test private key"
      telemetry_types:
        traces: true
      protocol: "HTTP"

    # Production endpoints used in tests
    byoo_auto_telemetry_dailyrun:
      name: "byoo_auto_telemetry_dailyrun"
      provider: "Grafana Cloud"
      telemetry_types:
        logs: true
        metrics: true
      protocol: "HTTP"

    mb_datadog_test:
      name: "mb-datadog-test"
      provider: "Datadog"
      telemetry_types:
        logs: true
        metrics: false
      protocol: "HTTP"

    telemetry_ui_automation_inactive_func:
      name: "telemetry-ui-automation-inactive-func-dailyrun"
      provider: "Grafana Cloud"
      telemetry_types:
        logs: true
        metrics: true
      protocol: "HTTP"

  # Invalid names for testing validation
  invalid_names:
    - "-example"
    - ".example"
    - "-.-"
    - "this-name-is-extremely-long-and-exceeds-48-characters-limit"

  filters:
    datadog_logs:
      id: "datadog_logs"
      providers:
        - "Datadog"
      telemetry_types:
        - "Logs"

    grafana_metrics:
      id: "grafana_metrics"
      providers:
        - "Grafana Cloud"
      telemetry_types:
        - "Metrics"

    all_providers_all_types:
      id: "all_providers_all_types"
      providers:
        - "Datadog"
        - "Grafana Cloud"
      telemetry_types:
        - "Logs"
        - "Metrics"

  # Test data for deletion tests
  deletion_tests:
    active_function_endpoint: "byoo_auto_telemetry_dailyrun"
    inactive_function_endpoint: "telemetry-ui-automation-inactive-func-dailyrun"
    expected_error_message: "Cannot be deleted as it is in use by Function"

# Function Version Details Test Data
active_function_version_with_telemetry_endpoint:
  telemetry_export_test:
    function_id: "a9967cff-e947-4587-962a-5565a41695f5"
    version_id: "2edb5c3e-60b7-4b99-ac30-2fc58e22b371"
    version_name: "grafana_active_for_dailyrun"
    function_name: "grafana_active_for_dailyrun"
    filter_tab: "Function Details"
    expected_logs_endpoint: "byoo_auto_telemetry_dailyrun (Grafana Cloud)"
    expected_metrics_endpoint: "byoo_auto_telemetry_dailyrun (Grafana Cloud)"

active_function_version_with_telemetry_endpoint_stg:
  telemetry_export_test:
    function_id: "589795c1-58fd-4ebb-8a7f-b828f804887d"
    version_id: "72faa716-98d6-415b-b23d-8087ba3744bb"
    version_name: "grafana_active_for_dailyrun"
    function_name: "grafana_active_for_dailyrun"
    filter_tab: "Function Details"
    expected_logs_endpoint: "byoo_auto_telemetry_dailyrun (Grafana Cloud)"
    expected_metrics_endpoint: "byoo_auto_telemetry_dailyrun (Grafana Cloud)"

# Deploying Function Version Test Data for 4093814
deploying_function_version_with_telemetry_endpoint:
  telemetry_export_test:
    function_id: "87ff1546-d4d0-41e8-85bb-d119716e6a9b"
    version_id: "9681e580-f704-4c7d-9974-cfb60704001c"
    version_name: "fastapi_echo_dedicated_grafana_for_dailyrun"
    function_name: "fastapi_echo_dedicated_grafana_deploying_for_dailyrun"
    filter_tab: "Function Details"
    expected_logs_endpoint: "telemetry-ui-automation-deploying (Grafana Cloud)"
    expected_metrics_endpoint: "telemetry-ui-automation-deploying (Grafana Cloud)"

deploying_function_version_with_telemetry_endpoint_stg:
  telemetry_export_test:
    function_id: "62f078aa-951f-47e1-b780-b94e8e229197"
    version_id: "0c6d1472-c463-421e-96b4-0f2fc2ffcd3e"
    version_name: "fastapi_echo_dedicated_grafana_for_dailyrun"
    function_name: "fastapi_echo_dedicated_grafana_deploying_for_dailyrun"
    filter_tab: "Function Details"
    expected_logs_endpoint: "telemetry-ui-automation-deploying (Grafana Cloud)"
    expected_metrics_endpoint: "telemetry-ui-automation-deploying (Grafana Cloud)"

# Inactive Function Version Test Data for 4093861
inactive_function_version_with_telemetry_endpoint:
  telemetry_export_test:
    function_id: "5c5de273-5a11-4248-8811-6067fee024ef"
    function_name: "fastapi_echo_telemetry_inactive_func_for_dailyrun"
    version_id: "4b7d5636-2c0d-4af3-8882-f3d8c9621d4d"
    version_name: "fastapi_echo_telemetry_inactive_func_for_dailyrun"
    filter_tab: "Function Details"
    expected_logs_endpoint: "telemetry-ui-automation-inactive-func-dailyrun (Grafana Cloud)"
    expected_metrics_endpoint: "telemetry-ui-automation-inactive-func-dailyrun (Grafana Cloud)"

inactive_function_version_with_telemetry_endpoint_stg:
  telemetry_export_test:
    function_id: "c6b8b6a3-a2c0-4fbd-ab25-f2cf6b17b7a2"
    function_name: "fastapi_echo_telemetry_inactive_func_for_dailyrun"
    version_id: "2be30505-1d51-44d5-84a3-60ceb09e921d"
    version_name: "fastapi_echo_telemetry_inactive_func_for_dailyrun"
    filter_tab: "Function Details"
    expected_logs_endpoint: "telemetry-ui-automation-inactive-func-dailyrun (Grafana Cloud)"
    expected_metrics_endpoint: "telemetry-ui-automation-inactive-func-dailyrun (Grafana Cloud)"

container_function_telemetry_endpoint_test:
  func_name: "byoo_function_auto_telemetry_endpoint_test"
  func_desc: "Dedicated for UI automation test"
  tags: "byoo_function_auto_telemetry_endpoint_test"
  container: "rw983xdqtcdp/fastapi_echo_sample"
  tag: "latest"
  inference_protocol: "HTTP"
  inference_port: 8000
  inference_endpoint: "/echo"
  health_endpoint: "/health"
  health_port: 8000
  telemetry_logs: "byoo_auto_datadog_dailyrun (Datadog)"
  telemetry_metrics: "byoo_auto_datadog_dailyrun (Datadog)"

container_function_telemetry_endpoint_test_stg:
  func_name: "byoo_function_auto_telemetry_endpoint_test"
  func_desc: "Dedicated for UI automation test"
  tags: "byoo_function_auto_telemetry_endpoint_test"
  container: "tadiathdfetp/fastapi_echo_sample"
  tag: "latest"
  inference_protocol: "HTTP"
  inference_port: 8000
  inference_endpoint: "/echo"
  health_endpoint: "/health"
  health_port: 8000
  telemetry_logs: "byoo_auto_datadog_dailyrun (Datadog)"
  telemetry_metrics: "byoo_auto_datadog_dailyrun (Datadog)"

helm_chart_function_telemetry_endpoint_test:
  name: "byoo_function_auto_helm_chart_telemetry_endpoint_test"
  Helm_Chart: "rw983xdqtcdp/byoo-validation"
  Helm_Chart_Version: "0.11"
  Helm_Chart_Service_Name: "entrypoint"
  inference_protocol: "HTTP"
  inference_port: 8000
  inference_endpoint: "/byoo"
  health_endpoint: "/health"
  health_port: 8000
  min_instances: "1"
  max_instances: "1"
  time_out: 1200
  telemetry_logs: "byoo_auto_datadog_dailyrun (Datadog)"
  telemetry_metrics: "byoo_auto_datadog_dailyrun (Datadog)"

helm_chart_function_telemetry_endpoint_test_stg:
  name: "byoo_function_auto_helm_chart_telemetry_endpoint_test"
  Helm_Chart: "tadiathdfetp/byoo-validation"
  Helm_Chart_Version: "0.11"
  Helm_Chart_Service_Name: "entrypoint"
  inference_protocol: "HTTP"
  inference_port: 8000
  inference_endpoint: "/byoo"
  health_endpoint: "/health"
  health_port: 8000
  min_instances: "1"
  max_instances: "1"
  time_out: 1200
  telemetry_logs: "byoo_auto_datadog_dailyrun (Datadog)"
  telemetry_metrics: "byoo_auto_datadog_dailyrun (Datadog)"
# Function Version Without Telemetry Test Data for 4108256
function_version_without_telemetry:
  telemetry_export_test:
    function_id: "5d477422-edfa-4a7e-8c64-4d9500831e62"
    version_id: "478a6005-982c-482c-a123-5dd3f462a013"
    function_name: "fastapi_no_telemetry_for_dailyrun"
    version_name: "fastapi_no_telemetry_for_dailyrun"
    filter_tab: "Function Details"
    expected_logs_endpoint: "—"
    expected_metrics_endpoint: "—"

function_version_without_telemetry_stg:
  telemetry_export_test:
    function_id: "68ef736e-0209-4d36-b762-2b91594a9de4"
    version_id: "b2dfc05e-c03a-4a8a-a52a-de92e18818a0"
    function_name: "fastapi_no_telemetry_for_dailyrun"
    version_name: "fastapi_no_telemetry_for_dailyrun"
    filter_tab: "Function Details"
    expected_logs_endpoint: "—"
    expected_metrics_endpoint: "—"

container_function_with_invalid_telemetry_endpoint_test:
  func_name: "byoo_function_auto_with_invalid_telemetry-endpoint-test"
  func_desc: "Dedicated for UI automation test"
  tags: "byoo_function_auto_with_invalid_telemetry-endpoint-test"
  container: "rw983xdqtcdp/fastapi_echo_sample"
  tag: "latest"
  inference_protocol: "HTTP"
  inference_port: 8000
  inference_endpoint: "/echo"
  health_endpoint: "/health"
  health_port: 8000
  invalid_telemetry_logs: "byoo_auto_grafana_invaild_dailyrun (Grafana Cloud)"
  invalid_telemetry_metrics: "byoo_auto_grafana_invaild_dailyrun (Grafana Cloud)"

container_function_with_invalid_telemetry_endpoint_test_stg:
  func_name: "byoo_function_auto_with_invalid_telemetry-endpoint-test"
  func_desc: "Dedicated for UI automation test"
  tags: "byoo_function_auto_with_invalid_telemetry-endpoint-test"
  container: "tadiathdfetp/fastapi_echo_sample"
  tag: "latest"
  inference_protocol: "HTTP"
  inference_port: 8000
  inference_endpoint: "/echo"
  health_endpoint: "/health"
  health_port: 8000
  invalid_telemetry_logs: "byoo_auto_grafana_invaild_dailyrun (Grafana Cloud)"
  invalid_telemetry_metrics: "byoo_auto_grafana_invaild_dailyrun (Grafana Cloud)"

active_function_version_with_only_logs_telemetry_endpoint:
  telemetry_export_test:
    function_id: "d1af008c-08b9-4272-aa68-f83b8317a61f"
    version_id: "4eb2215f-4dc4-4e7c-9a9c-c07228bf9409"
    version_name: "only_logs_telemetry_active_for_dailyrun"
    function_name: "only_logs_telemetry_active_for_dailyrun"
    filter_tab: "Function Details"
    expected_logs_endpoint: "byoo_auto_telemetry_dailyrun (Grafana Cloud)"

active_function_version_with_only_logs_telemetry_endpoint_stg:
  telemetry_export_test:
    function_id: "52dbcbca-a227-4e65-8ceb-b7699603e323"
    version_id: "c2a313ba-9da7-43e4-837e-79de1f6f4b9c"
    version_name: "only_logs_telemetry_active_for_dailyrun"
    function_name: "only_logs_telemetry_active_for_dailyrun"
    filter_tab: "Function Details"
    expected_logs_endpoint: "byoo_auto_telemetry_dailyrun (Grafana Cloud)"

active_function_version_with_only_metrics_telemetry_endpoint:
  telemetry_export_test:
    function_id: "8e3dc082-5a7f-440e-afa9-49266c28876a"
    version_id: "cf2f6ea8-0e9a-4c32-879b-11893cbbb6b8"
    version_name: "only_metrics_telemetry_active_for_dailyrun"
    function_name: "only_metrics_telemetry_active_for_dailyrun"
    filter_tab: "Function Details"
    expected_metrics_endpoint: "byoo_auto_telemetry_dailyrun (Grafana Cloud)"

active_function_version_with_only_metrics_telemetry_endpoint_stg:
  telemetry_export_test:
    function_id: "8c454305-5a12-491c-83e8-ecd40d7d171b"
    version_id: "99f984cf-af7d-4d8d-a666-95cbf1b998fc"
    version_name: "only_metrics_telemetry_active_for_dailyrun"
    function_name: "only_metrics_telemetry_active_for_dailyrun"
    filter_tab: "Function Details"
    expected_metrics_endpoint: "byoo_auto_telemetry_dailyrun (Grafana Cloud)"

byoo_container_function_telemetry_endpoint_test:
  func_name: "byoo_function_auto_telemetry_endpoint_test"
  func_desc: "Dedicated for UI automation test"
  tags: "byoo_function_auto_telemetry_endpoint_test"
  container: "rw983xdqtcdp/byoo-test"
  tag: "0.11 (Latest)"
  inference_protocol: "HTTP"
  inference_port: 8000
  inference_endpoint: "/byoo"
  health_endpoint: "/health"
  health_port: 8000
  telemetry_logs: "byoo_auto_datadog_dailyrun (Datadog)"
  telemetry_metrics: "byoo_auto_datadog_dailyrun (Datadog)"
  telemetry_traces: "byoo_auto_datadog_dailyrun (Datadog)"

byoo_container_function_telemetry_endpoint_test_stg:
  func_name: "byoo_function_auto_telemetry_endpoint_test"
  func_desc: "Dedicated for UI automation test"
  tags: "byoo_function_auto_telemetry_endpoint_test"
  container: "tadiathdfetp/byoo-test"
  tag: "0.11"
  inference_protocol: "HTTP"
  inference_port: 8000
  inference_endpoint: "/byoo"
  health_endpoint: "/health"
  health_port: 8000
  telemetry_logs: "byoo_auto_datadog_dailyrun (Datadog)"
  telemetry_metrics: "byoo_auto_datadog_dailyrun (Datadog)"
  telemetry_traces: "byoo_auto_datadog_dailyrun (Datadog)"

byoo_helm_chart_function_telemetry_endpoint_test:
  func_name: "byoo_function_auto_helm_chart_telemetry_endpoint_test"
  helm_chart: "rw983xdqtcdp/byoo-validation"
  helm_chart_version: "0.11"
  helm_chart_service_name: "entrypoint"
  inference_protocol: "HTTP"
  inference_port: 8000
  inference_endpoint: "/byoo"
  health_endpoint: "/health"
  health_port: 8000
  min_instances: "1"
  max_instances: "1"
  time_out: 1200
  telemetry_logs: "byoo_auto_datadog_dailyrun (Datadog)"
  telemetry_metrics: "byoo_auto_datadog_dailyrun (Datadog)"
  telemetry_traces: "byoo_auto_datadog_dailyrun (Datadog)"

byoo_helm_chart_function_telemetry_endpoint_test_stg:
  func_name: "byoo_function_auto_helm_chart_telemetry_endpoint_test"
  helm_chart: "tadiathdfetp/byoo-validation"
  helm_chart_version: "0.11"
  helm_chart_service_name: "entrypoint"
  inference_protocol: "HTTP"
  inference_port: 8000
  inference_endpoint: "/byoo"
  health_endpoint: "/health"
  health_port: 8000
  min_instances: "1"
  max_instances: "1"
  time_out: 1200
  telemetry_logs: "byoo_auto_datadog_dailyrun (Datadog)"
  telemetry_metrics: "byoo_auto_datadog_dailyrun (Datadog)"
  telemetry_traces: "byoo_auto_datadog_dailyrun (Datadog)"