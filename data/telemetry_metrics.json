{"helm_metrics": ["container_cpu_usage_seconds_total", "container_memory_cache", "container_memory_rss", "container_memory_swap", "container_memory_usage_bytes", "container_memory_working_set_bytes", "container_network_receive_bytes_total", "container_network_receive_errors_total", "container_network_receive_packets_dropped_total", "container_network_receive_packets_total", "container_network_transmit_bytes_total", "container_network_transmit_errors_total", "container_network_transmit_packets_dropped_total", "container_network_transmit_packets_total", "kube_deployment_status_condition", "kube_deployment_status_replicas", "kube_deployment_status_replicas_available", "kube_deployment_status_replicas_ready", "kube_deployment_status_replicas_unavailable", "kube_deployment_status_replicas_updated", "kube_service_created", "kube_replicaset_status_replicas", "kube_replicaset_status_ready_replicas", "kube_pod_container_info", "kube_pod_container_resource_limits", "kube_pod_container_resource_requests", "kube_pod_container_status_restarts_total", "kube_pod_container_status_running", "kube_pod_container_status_terminated", "kube_pod_container_status_waiting", "kube_pod_container_status_ready", "kube_pod_info", "kube_pod_status_reason", "DCGM_FI_DEV_GPU_UTIL", "otelcol_exporter_sent_metric_points_total", "otelcol_exporter_sent_spans_total", "otelcol_exporter_sent_log_records_total", "otelcol_exporter_send_failed_log_records_total", "otelcol_exporter_send_failed_spans_total", "otelcol_exporter_send_failed_metric_points_total", "otelcol_processor_incoming_items_total", "otelcol_processor_outgoing_items_total", "otelcol_receiver_accepted_log_records_total", "otelcol_receiver_accepted_metric_points_total", "otelcol_receiver_accepted_spans_total", "otelcol_receiver_refused_log_records_total", "otelcol_receiver_refused_spans_total", "otelcol_receiver_refused_metric_points_total", "nvcf_worker_service_response_total", "nvcf_worker_service_worker_thread_busy_seconds_total", "nvcf_worker_service_worker_thread_count_total", "nvcf_worker_service_inference_request_time_seconds_total", "nvcf_worker_service_request_total", "nvcf_worker_service_request_latency_seconds_bucket", "nvcf_worker_service_request_latency_seconds_sum", "nvcf_worker_service_request_latency_seconds_count", "nvcf_worker_service_stream_latency_seconds_bucket", "nvcf_worker_service_stream_latency_seconds_sum", "nvcf_worker_service_stream_latency_seconds_count"], "container_metrics": ["container_cpu_usage_seconds_total", "container_memory_cache", "container_memory_rss", "container_memory_swap", "container_memory_usage_bytes", "container_memory_working_set_bytes", "container_network_receive_bytes_total", "container_network_receive_errors_total", "container_network_receive_packets_dropped_total", "container_network_receive_packets_total", "container_network_transmit_bytes_total", "container_network_transmit_errors_total", "container_network_transmit_packets_dropped_total", "container_network_transmit_packets_total", "kube_pod_container_info", "kube_pod_container_resource_limits", "kube_pod_container_resource_requests", "kube_pod_container_status_ready", "kube_pod_container_status_restarts_total", "kube_pod_container_status_running", "kube_pod_container_status_terminated", "kube_pod_container_status_waiting", "DCGM_FI_DEV_GPU_UTIL", "otelcol_receiver_refused_metric_points_total", "otelcol_receiver_refused_spans_total", "otelcol_receiver_refused_log_records_total", "otelcol_receiver_accepted_metric_points_total", "otelcol_receiver_accepted_spans_total", "otelcol_receiver_accepted_log_records_total", "otelcol_exporter_sent_metric_points_total", "otelcol_exporter_sent_spans_total", "otelcol_exporter_sent_log_records_total", "otelcol_exporter_send_failed_metric_points_total", "otelcol_exporter_send_failed_spans_total", "otelcol_exporter_send_failed_log_records_total", "otelcol_processor_incoming_items_total", "otelcol_processor_outgoing_items_total", "nvcf_worker_service_response_total", "nvcf_worker_service_worker_thread_busy_seconds_total", "nvcf_worker_service_worker_thread_count_total", "nvcf_worker_service_inference_request_time_seconds_total", "nvcf_worker_service_request_total", "nvcf_worker_service_request_latency_seconds_bucket", "nvcf_worker_service_request_latency_seconds_sum", "nvcf_worker_service_request_latency_seconds_count", "nvcf_worker_service_stream_latency_seconds_bucket", "nvcf_worker_service_stream_latency_seconds_sum", "nvcf_worker_service_stream_latency_seconds_count"]}