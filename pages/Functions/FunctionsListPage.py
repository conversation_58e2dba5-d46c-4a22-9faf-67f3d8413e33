from cloudia.ui.base_page import BasePage
from component.function_list_page_components import (
    FunctionListPageSideBar,
    FunctionListPageSwitchBar,
    FunctionListPageNavigationBar,
    FunctionListPagePagination,
)
import logging

from config.consts import (
    SIDEBAR_CLOUDFUNCTIONS_ENTRY_FUNCTIONS,
    SIDEBAR_APP_CLOUDFUNCTIONS,
)
from playwright.sync_api import Page
from component.deployments_list_page_components import (
    DeploymentsListPageKeyExpirationDropDownSelector,
)


class FunctionListPage(BasePage):
    """Contains all the elements and functionalities of the Functions List Page

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object of the Playwright.
    """

    KeyNameInput = "//label[text()='Key Name']/../..//input[@name='name']"
    GenerateKeyConfirmButton = "//button[text()='Generate Personal API Key' and @form = 'generate-personal-key-form']"
    CopyKeyAndClose = "//button[text()='Copy Key and Close']"
    KeyExpirationBox = (
        "//label[text()='Expiration']/../..//span[contains(text(), '12 months')]"
    )
    ServiceIncludedBtn = "//label[normalize-space(text())='Services Included']/parent::div/following-sibling::div[1]//input"
    Warningbar = "//div[contains(normalize-space(.), 'This is the only time your key will be displayed. If you lose this key') and not(div)]"
    Results = "//span[contains(normalize-space(), 'Results') or contains(normalize-space(), 'Result')]"
    status_list = [
        "Active",
        "Deploying",
        "Error",
        "Inactive",
    ]
    FilterBtn = (
        "//button[@data-testid='kui-menu-trigger' and text()[contains(., 'Filter')]]"
    )
    ClearFilterBtn = "//button[text()[contains(., 'Clear Filters')]]"
    StatusFilter = "//div[@data-testid='kui-menu-sub-trigger']//div[text()='Status']"
    ApplyFilterBtn = "//button[text()[contains(., 'Apply Filter')]]"
    ShowItems = "//span[text()='Show']/following-sibling::div//button"
    SelectEntrybyName = "//div[@data-testid='kui-select-item']/span[text()='{0}']"
    PrevPageButton = (
        "button[data-testid='kui-button']:has(svg[data-icon-name='shapes-chevron-left'])"
    )
    NextPageButton = (
        "button[data-testid='kui-button']:has(svg[data-icon-name='shapes-chevron-right'])"
    )

    def __init__(self, page: Page):
        super().__init__(
            page, SIDEBAR_APP_CLOUDFUNCTIONS, SIDEBAR_CLOUDFUNCTIONS_ENTRY_FUNCTIONS
        )
        self.SideBar = FunctionListPageSideBar(page)
        self.SwitchBar = FunctionListPageSwitchBar(page)
        self.NavigationBar = FunctionListPageNavigationBar(page)
        self.FunctionPagination = FunctionListPagePagination(page)
        self.KeyExpirationDropDownSelector = (
            DeploymentsListPageKeyExpirationDropDownSelector(page)
        )
        self.ServicesIncludedBtn = "//label[normalize-space(text())='Services Included']/parent::div/following-sibling::div[1]//input"

    def generate_key_in_function_list_page(self, key_name: str, key_expiration: str) -> str:
        """Generate a personal API key in the function list page.

        parameters:
        -----------
        key_name: str
            The name of the key to generate
        key_expiration: str
            The expiration time of the key

        Returns:
        --------
        str: The personal API key
        """
        # check the button is [generate personal api key]
        if self.page.get_by_role("button", name="Generate Personal API Key").is_visible():
            self.page.get_by_role("button", name="Generate Personal API Key").click()
        else:
            logging.error("Failed to find the button [generate personal api key]")
            return None

        self.page.locator(self.KeyNameInput).fill(key_name)
        KeyExpirationBox_element = self.KeyExpirationBox
        self.KeyExpirationDropDownSelector.select(KeyExpirationBox_element, key_expiration)

        self.page.locator(self.GenerateKeyConfirmButton).click()
        self.page.wait_for_load_state("load")
        self.page.wait_for_timeout(1000)
        self.page.wait_for_load_state("load")
        warning_message = self.page.locator(
            "//span[text()='Actor reached maximum allowed number of keys in service']"
        )
        if warning_message.is_visible():
            raise Exception(
                "Actor reached maximum allowed number of keys in service, please delete some keys first"
            )
        if not self.page.locator(self.Warningbar).is_visible():
            logging.error("Failed to find the warning bar")
            return None
        self.page.wait_for_selector(self.CopyKeyAndClose, state="visible", timeout=5000)
        self.page.locator(self.CopyKeyAndClose).click()
        copied_key = self.page.evaluate("navigator.clipboard.readText()")
        return copied_key

    def manage_key_in_function_list_page(self, key_name: str):
        # check the button is [manage personal api key]
        if not self.page.get_by_role(
            "button", name="Manage Personal API Keys"
        ).is_visible():
            logging.error("Failed to find the button [manage personal api key]")
            return False
        with self.page.context.expect_page() as new_page_info:
            self.page.get_by_role("button", name="Manage Personal API Keys").click()
        new_page = new_page_info.value
        new_page.wait_for_load_state("networkidle")
        new_page.wait_for_load_state("domcontentloaded")
        new_page.bring_to_front()

        new_url = new_page.url
        logging.info(f"New page URL: {new_url}")
        expected_path = "/setup/api-keys"
        if expected_path not in new_url:
            logging.error(f"Expected URL path '{expected_path}' not found in '{new_url}'")
            return False
        else:
            # check the key name is in the page
            new_page.wait_for_selector("//h3[text()='Personal Keys']", state="visible")
            key_name_locator = new_page.locator(
                f"//tr[td[normalize-space(.)='{key_name}']]"
            )
            logging.info(f"Key name locator: {key_name_locator}")
            if not key_name_locator.is_visible():
                logging.error(f"Key name '{key_name}' not found in the page")
                return False
        return True

    def generate_key_in_personal_api_key_management_page(
        self,
        key_name: str,
        key_expiration: str,
        service_included: list = [
            "Secrets Manager",
            "NGC Catalog",
            "Cloud Functions",
            "Private Registry",
        ],
    ):
        # check the button is [generate personal api key]
        if not self.page.get_by_role(
            "button", name="Manage Personal API Keys"
        ).is_visible():
            logging.error("Failed to find the button [Manage personal api key]")
            return False
        with self.page.context.expect_page() as new_page_info:
            self.page.get_by_role("button", name="Manage Personal API Keys").click()
        new_page = new_page_info.value
        new_page.wait_for_load_state("networkidle")
        new_page.wait_for_load_state("domcontentloaded")
        new_page.bring_to_front()

        new_url = new_page.url
        logging.info(f"New page URL: {new_url}")
        expected_path = "/setup/api-keys"
        if expected_path not in new_url:
            logging.error(f"Expected URL path '{expected_path}' not found in '{new_url}'")
            return False
        else:
            new_page.wait_for_selector("//h3[text()='Personal Keys']", state="visible")
            if new_page.get_by_role("button", name="Generate Personal Key").is_visible():
                new_page.get_by_role("button", name="Generate Personal Key").click()
            else:
                logging.error("Failed to find the button [generate personal key]")
                return None
            new_page.locator(self.KeyNameInput).fill(key_name)

            for service in service_included:
                new_page.locator(self.ServicesIncludedBtn).click()
                new_page.locator(
                    f"//div[@role='option' and normalize-space(text())='{service}']"
                ).click()

            new_page.locator("//button[text()='Generate Personal Key']").click()
            new_page.wait_for_load_state("load")
            new_page.wait_for_timeout(1000)
            new_page.wait_for_load_state("load")
            warning_message = new_page.locator(
                "//span[text()='Actor reached maximum allowed number of keys in service']"
            )
            if warning_message.is_visible():
                raise Exception(
                    "Actor reached maximum allowed number of keys in service, please delete some keys first"
                )

            new_page.locator("//button[text()='Copy Personal Key']").click()
            new_page.locator("//button[text()='Close']").click()

            key_name_locator = new_page.locator(
                f"//tr[td[normalize-space(.)='{key_name}']]"
            )
            logging.info(f"Key name locator: {key_name_locator}")
            if not key_name_locator.is_visible():
                logging.error(f"Key name '{key_name}' not found in the page")
                return False
        return True

    def click_tab(self, tab_name: str) -> bool:
        """Click on a tab in the function details page.

        Parameters:
        -----------
        tab_name: str
            The name of the tab to click, e.g., "Function Details", "Logs", etc.

        Returns:
        --------
        bool:
            True if the tab was clicked successfully, False otherwise
        """
        # Look for the tab button by its name
        self.page.wait_for_load_state("networkidle")
        tab_button = self.page.get_by_role("tab", name=tab_name)

        tab_button.click()

        # Wait for the tab content to be loaded
        self.page.wait_for_load_state("networkidle")

        # Check if the tab is selected
        is_selected = tab_button.get_attribute("aria-selected") == "true"

        return is_selected

    def navigate_to_function_version_detail(
        self,
        function_id: str,
        version_name: str,
        tab_name: str,
    ):
        """Navigate directly to a function version detail page.

        Parameters:
        -----------
        function_id: str
            The UUID of the function
        version_name: str
            The name of the function version

        Returns:
        --------
        bool:
            True if navigation was successful
        """
        logging.info(
            f"Navigating to function version detail: {version_name} (ID: {function_id})"
        )

        try:
            search_box = self.page.get_by_placeholder(
                "Search name, description, or a string"
            )
            search_box.wait_for(state="visible", timeout=10000)
            search_box.fill("")
            search_box.fill(function_id)
            search_box.press("Enter")

            self.page.wait_for_load_state("networkidle", timeout=10000)

            self.page.locator("table").wait_for(state="visible", timeout=10000)

            version_link = self.page.locator(
                f"span[data-testid='kui-text']:has-text('{version_name}')"
            ).first

            version_link.wait_for(state="visible", timeout=10000)

            parent_link = version_link.locator("xpath=../..")
            parent_link.click()

            self.page.wait_for_load_state("networkidle", timeout=60000)

            if tab_name:
                self.click_tab(tab_name)

            logging.info("Successfully navigated to function version detail page")
            return True

        except Exception as e:
            logging.error(f"Failed to navigate to function version detail: {str(e)}")
            logging.debug(f"Current URL: {self.page.url}")
            raise e

    def get_total_function_count(self) -> int:
        """Get the total functions count.

        Returns:
        --------
        `int`
            The total function count.
        """
        self.page.wait_for_timeout(2000)
        text = self.page.locator(self.Results).inner_text()
        return int(text.split()[0])

    def set_filter_by_status(self, status):
        """Set the filter by status.

        Parameters:
        -----------
        status: `str`
            The status to filter by.

        Returns:
        --------
        `int`
            The number of filtered results.
        """
        if status in self.status_list:
            self.page.locator(self.FilterBtn).click()
            self.page.locator(self.StatusFilter).click()
            status_elements = f"//span[@class='c-dpLRfC' and text()='{status}']"
            self.page.locator(status_elements).click()
            self.page.locator(self.ApplyFilterBtn).click()
            self.page.wait_for_load_state("load")
            self.page.wait_for_timeout(3000)
            results = self.page.locator(self.Results).inner_text()
            if self.check_status_filter_by_status(status, int(results.split()[0])):
                self.page.locator(self.ClearFilterBtn).click()
                return int(results.split()[0]), True
            else:
                logging.info(f"Failed to filter by status: {status}")
                self.page.locator(self.ClearFilterBtn).click()
                return int(results.split()[0]), False
        else:
            raise ValueError(f"Invalid status: {status}")

    def check_status_filter_by_status(self, status, results):
        """Check if the status filter is working.

        Returns:
        --------
        `bool`
        """
        if status in self.status_list:
            if results == 0:
                return True

            if results > 20:
                self.set_show_items(100)

            total_pages = (results + 99) // 100
            current_page = 1
            logging.info(f"total_pages: {total_pages}")
            logging.info(f"results: {results}")
            while current_page <= total_pages:
                items_on_current_page = min(100, results - (current_page - 1) * 100)
                for i in range(items_on_current_page):
                    status_element = self.page.locator(
                        "//td[@headers='data-view-header-status']"
                    ).nth(i)
                    status_text = status_element.text_content().upper()
                    if status_text != status.upper():
                        return False
                if current_page < total_pages:
                    logging.info(f"current_page: {current_page}")
                    self.page.locator(self.NextPageButton).click()
                    self.page.wait_for_load_state("load")
                    self.page.wait_for_timeout(3000)
                current_page += 1
            return True
        else:
            raise ValueError(f"Invalid status: {status}")

    def set_show_items(self, expected_show_items):
        """Set the number of items to show.

        Returns:
        --------
        `bool`
        """
        self.page.locator(self.ShowItems).click()
        self.page.locator(self.SelectEntrybyName.format(expected_show_items)).click()
        self.page.wait_for_load_state("load")
        self.page.wait_for_timeout(3000)
