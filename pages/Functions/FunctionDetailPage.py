from cloudia.ui.base_page import BasePage
from component.function_detail_page_components import (
    FunctionDetailPageNavigationBar,
    FunctionDetailPageAgrInvoStatics,
    FunctionDetailPageAgrInsStatics,
    FunctionDetailPagePagination,
    FunctionDetailPageBasicDetails,
    FunctionDetailPageRuntimeDetails,
    FunctionDetailPageConfigurationDetails,
    FunctionDetailPageModelDetails,
    FunctionDetailPageEnvironmentVariables,
)
from component.function_list_page_components import (
    FunctionListPageSideBar as FunctionDetailPageSideBar,
)
from pages.Functions.FunctionsListPage import FunctionListPage

from config.consts import (
    SIDEBAR_CLOUDFUNCTIONS_ENTRY_FUNCTIONS,
    SIDEBAR_APP_CLOUDFUNCTIONS,
)
from playwright.sync_api import Page


class FunctionDetailPage(BasePage):
    """Contains all the elements and functionalities of the Functions Detail Page

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object of the Playwright.

    func_id: `str`
        The uuid of the function.
    func_name: `str`
        The name of the function.
    """

    def __init__(self, page: Page, func_id: str = None, func_name: str = None):
        super().__init__(
            page, SIDEBAR_APP_CLOUDFUNCTIONS, SIDEBAR_CLOUDFUNCTIONS_ENTRY_FUNCTIONS
        )
        self.funcId = func_id
        self.funcName = func_name
        self.SideBar = FunctionDetailPageSideBar(page)
        self.NavigationBar = FunctionDetailPageNavigationBar(page)
        self.FunctionPagination = FunctionDetailPagePagination(page)
        self.AgrInvoStatics = FunctionDetailPageAgrInvoStatics(page)
        self.AgrInsStatics = FunctionDetailPageAgrInsStatics(page)
        self.BasicDetails = FunctionDetailPageBasicDetails(page)
        self.RuntimeDetails = FunctionDetailPageRuntimeDetails(page)
        self.ConfigurationDetails = FunctionDetailPageConfigurationDetails(page)
        self.ModelDetails = FunctionDetailPageModelDetails(page)
        self.EnvironmentVariables = FunctionDetailPageEnvironmentVariables(page)

    def navigate_to_page(self, func_id: str = None, func_name: str = None):
        """Navigate to the function detail page of the given func.

        parameters:
        -----------
        func_id: `str`
            the uuid of the function
        func_name: `str`
            the name of the function

        Returns:
        --------
        None
        """

        if func_id:
            self.funcId = func_id
        if func_name:
            self.funcName = func_name
        fl_pag = FunctionListPage(self.page)
        fl_pag.navigate_to_page()
        fl_pag.FunctionPagination.navigate_to_function_details_page(
            self.funcId, self.funcName
        )

    def wait_for_page_ready(self):
        # Wait for the Function Overview section to load
        self.page.get_by_test_id("kui-text").filter(
            has_text="Function Overview"
        ).first.wait_for()

        # Wait for the table to be visible
        self.page.locator("table").wait_for()

        # Wait for table data to be loaded (check for presence of rows)
        self.page.locator("tbody tr").first.wait_for()

        # Wait for the Actions button to be interactive
        action_button = self.page.locator(
            'td[headers="data-view-header-row-actions"] button[data-testid="kui-menu-trigger"]'
        ).first
        action_button.wait_for(state="visible")
