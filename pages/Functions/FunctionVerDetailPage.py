from cloudia.ui.base_page import BasePage
from component.function_version_detail_page_components import (
    FunctionVerDetailPageNavigationBar,
    FunctionVerDetailSwitchBar,
    FunctionVerDetailPageInvocActAndQueDpth,
    FunctionVerDetailPageAvgInferTime,
    FunctionVerDetailPageInstances,
    FunctionVerDetailPageSuccessRate,
    FunctionVerDetailPageLogsTab,
    FunctionsDetailPageDeploymentBasicDetails,
    FunctionVerDetailPageLogsTimeDropDownSelector,
    FunctionVerDetailPageLogsSearchBar,
    FunctionVerDetailPageLogsPaginationTable,
    FunctionVerDetailPageLogsCountDropDownSelector,
    FunctionVerDetailPageActionMenu,
    FunctionVerDetailPageInstanceType,
    FunctionVerDetailPageOverviewBasicDetails,
    FunctionVerDetailPageFunctionDetails,
)
from component.function_list_page_components import (
    FunctionListPageSideBar as FunctionVerDetailPageSideBar,
)

from pages.Functions.FunctionsListPage import FunctionListPage

from pages.Functions.FunctionDetailPage import FunctionDetailPage

from component.function_detail_page_components import (
    FunctionDetailPageAgrInvoStatics as FunctionVerDetailPageAgrInvoStatics,
    FunctionDetailPageBasicDetails as FunctionVerDetailPageBasicDetails,
    FunctionDetailPageRuntimeDetails as FunctionVerDetailPageRuntimeDetails,
    FunctionDetailPageModelDetails as FunctionVerDetailPageModelDetails,
    FunctionDetailPageEnvironmentVariables as FunctionVerDetailPageEnvironmentVariables,
    FunctionDetailPageConfigurationDetails as FunctionVerDetailPageConfigurationDetails,
    FunctionDetailPageInstanceDetails as FunctionVerDetailPageInstanceDetails,
)

from config.consts import (
    SIDEBAR_CLOUDFUNCTIONS_ENTRY_FUNCTIONS,
    SIDEBAR_APP_CLOUDFUNCTIONS,
)
from playwright.sync_api import Page
import logging


class FunctionVerDetailPage(BasePage):
    """Contains all the elements and functionalities of the Functions Detail Page

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object of the Playwright.
    func_name: `str`, optional
        The name of the function.
    func_id: `str`, optional
        The uuid of the function.
    vers_id: `str`, optional
        The uuid of the function version.
    """

    def __init__(
        self, page: Page, func_name: str = None, func_id: str = None, vers_id: str = None
    ):
        super().__init__(
            page, SIDEBAR_APP_CLOUDFUNCTIONS, SIDEBAR_CLOUDFUNCTIONS_ENTRY_FUNCTIONS
        )
        self.func_name = func_name
        self.func_id = func_id
        self.vers_id = vers_id
        self.SideBar = FunctionVerDetailPageSideBar(page)
        self.NavigationBar = FunctionVerDetailPageNavigationBar(page)
        self.SwitchBar = FunctionVerDetailSwitchBar(page)
        self.AggreatedInvoc = FunctionVerDetailPageAgrInvoStatics(page)
        self.InvocActAndQueDpth = FunctionVerDetailPageInvocActAndQueDpth(page)
        self.AvgInferTime = FunctionVerDetailPageAvgInferTime(page)
        self.OverviewBasicDetails = FunctionVerDetailPageOverviewBasicDetails(page)
        self.FunctionDetails = FunctionVerDetailPageFunctionDetails(page)
        self.InstancesType = FunctionVerDetailPageInstanceType(page)
        self.Instances = FunctionVerDetailPageInstances(page)
        self.SuccessRate = FunctionVerDetailPageSuccessRate(page)
        self.BasicDetails = FunctionVerDetailPageBasicDetails(page)
        self.RuntimeDetails = FunctionVerDetailPageRuntimeDetails(page)
        self.ModelDetails = FunctionVerDetailPageModelDetails(page)
        self.EnvironmentVariables = FunctionVerDetailPageEnvironmentVariables(page)
        self.ConfigurationDetails = FunctionVerDetailPageConfigurationDetails(page)
        self.InstanceDetails = FunctionVerDetailPageInstanceDetails(page)
        self.LogsTab = FunctionVerDetailPageLogsTab(page)
        self.LogsTimeDropDownSelector = FunctionVerDetailPageLogsTimeDropDownSelector(page)
        self.LogsSearchBar = FunctionVerDetailPageLogsSearchBar(page)
        self.LogsPaginationTable = FunctionVerDetailPageLogsPaginationTable(page)
        self.DeploymentBasicDetails = FunctionsDetailPageDeploymentBasicDetails(page)
        self.LogsCountDropDownSelector = FunctionVerDetailPageLogsCountDropDownSelector(
            page
        )
        self.ActionMenu = FunctionVerDetailPageActionMenu(page)

    def get_instance_id_with_retry(self, max_retries=3, timeout=10000):
        self.page.wait_for_selector(
            "//button[text()='Overview']", state="visible", timeout=10000
        )

        self.page.locator("//button[contains(text(), 'Switch To ')]").click()

        """Get instance ID with retry mechanism"""
        selector = "//td[@headers='data-view-header-instanceId']"

        for attempt in range(max_retries):
            try:
                self.page.wait_for_selector(selector, state="visible", timeout=timeout)

                element = self.page.locator(selector).first

                element.wait_for(state="visible", timeout=5000)

                instance_id = element.text_content()

                if instance_id and instance_id.strip():
                    return instance_id.strip()

                if attempt < max_retries - 1:
                    self.page.wait_for_timeout(1000)

            except Exception as e:
                if attempt < max_retries - 1:
                    logging.warning(f"Attempt {attempt + 1} failed: {e}")
                    self.page.wait_for_timeout(1000)
                else:
                    raise ValueError(
                        f"Failed to retrieve instance ID after {max_retries} attempts: {e}"
                    )

        raise ValueError("Instance ID is empty after all retries")

    def get_max_concurrency(self):
        """Get the total max concurrency value from the function version detail page.

        Returns:
        --------
        int:
            The total max concurrency value.
        """
        logging.info("Getting total max concurrency value")
        # Find the label element with text "Max Concurrency"
        label = self.page.get_by_test_id("kui-label").filter(has_text="Max Concurrency")
        label.wait_for(state="visible")

        # Get the parent flex container
        flex_container = label.locator("xpath=..")

        # Get the sibling span element that contains the value
        value_span = flex_container.get_by_test_id("kui-text")
        value_text = value_span.text_content()

        logging.info(f"Found concurrency value: {value_text}")
        return int(value_text)

    def switch_tab(
        self,
        tab_name: str = None,
        func_name: str = None,
        func_id: str = None,
        vers_id: str = None,
        vers_name: str = None,
    ):
        prod_url = (
            f"https://nvcf.ngc.nvidia.com/functions/{func_id}/{vers_id}?filter=Overview"
        )
        canary_url = f"https://nvcf.canary.ngc.nvidia.com/functions/{func_id}/{vers_id}?filter=Overview"
        stg_url = (
            f"https://nvcf.stg.ngc.nvidia.com/functions/{func_id}/{vers_id}?filter=Overview"
        )

        tab_name = tab_name.lower()
        if self.page.url not in [prod_url, canary_url, stg_url]:
            self.navigate_to_page(func_name, func_id, vers_id, vers_name)
        if tab_name == "function details":
            self.page.locator(
                FunctionVerDetailPageNavigationBar.function_detail_model_name_link
            ).click()
            self.page.wait_for_load_state("load")
            self.page.locator(
                FunctionVerDetailPageNavigationBar.function_configuration_title
            ).wait_for(state="visible")
            assert (
                self.page.locator(
                    FunctionVerDetailPageNavigationBar.function_configuration_title
                ).text_content()
                == "Function Configuration"
            )
        elif tab_name == "deployment details":
            self.page.locator(
                FunctionVerDetailPageNavigationBar.deployments_details_model_name_link
            ).click()
            self.page.wait_for_load_state("load")
            self.page.wait_for_timeout(2000)
            if (
                self.page.locator(FunctionVerDetailPageNavigationBar.items_no_exist).count()
                > 0
            ):
                assert (
                    self.page.locator(
                        FunctionVerDetailPageNavigationBar.items_no_exist
                    ).text_content()
                    == "No Instance Types Yet"
                )
            else:
                assert (
                    self.page.locator(
                        FunctionVerDetailPageNavigationBar.instance_type_exist
                    ).text_content()
                    == "Max Concurrency"
                ), "Instance Types part not displayed"
        elif tab_name == "metrics":
            metrics_table = self.page.get_by_role("tablist").get_by_role(
                "tab", name="Metrics", exact=False
            )
            metrics_table.wait_for(state="visible")
            metrics_table.click()
            self.page.wait_for_load_state("load")
            self.page.wait_for_timeout(2000)
            if (
                self.page.locator(FunctionVerDetailPageNavigationBar.items_no_exist).count()
                > 0
            ):
                assert (
                    self.page.locator(
                        FunctionVerDetailPageNavigationBar.items_no_exist
                    ).text_content()
                    == "No Metrics Data Yet"
                ), "Metrics Types part not displayed"
            else:
                assert (
                    self.page.locator(
                        FunctionVerDetailPageNavigationBar.metrics_exist
                    ).text_content()
                    == "Total Invocations"
                ), "Metrics Types part not displayed"
        elif tab_name == "logs":
            self.page.locator(
                FunctionVerDetailPageNavigationBar.logs_model_name_link
            ).click()
            self.page.wait_for_load_state("load")
        else:
            self.page.locator(
                FunctionVerDetailPageNavigationBar.overview_model_name_link
            ).click()
            self.page.wait_for_load_state("load")
            assert self.page.locator(
                FunctionVerDetailPageNavigationBar.active_instance_in_overview
            ).text_content() in [
                "Instances",
                "Active Instances",
            ], "Overview part not displayed"

    def wait_for_page_ready(self):
        self.page.locator(FunctionVerDetailSwitchBar.overview_button).wait_for(
            state="visible"
        )

    def navigate_to_page(
        self,
        func_name: str = None,
        func_id: str = None,
        vers_id: str = None,
        vers_name: str = None,
    ):
        """Navigate to the function detail page of the given func.

        parameters:
        -----------
        func_name: `str`
            The name of the function.
        func_id: `str`
            The uuid of the function.
        vers_id: `str`
            The uuid of the function version.
        vers_name: `str`
            The name of the function version.

        Returns:
        --------
        None
        """
        if func_id:
            self.func_id = func_id
        if func_name:
            self.func_name = func_name
        if vers_id:
            self.vers_id = vers_id

        fl_page = FunctionListPage(self.page)
        fl_page.navigate_to_page()
        fl_page.FunctionPagination.navigate_to_function_details_page(
            func_name=self.func_name, func_id=self.func_id
        )

        if vers_name:
            FunctionDetailPage(self.page).FunctionPagination.navigate_to_vers_detail_page(
                vers_name=vers_name
            )
            return
        if vers_id:
            logging.info(f"Navigating to version detail page with vers_id: {vers_id}")
            FunctionDetailPage(self.page).FunctionPagination.navigate_to_vers_detail_page(
                vers_id=vers_id
            )
            return

    def has_cancel_deployment_button(self) -> bool:
        """Check if the Cancel Deployment button exists on the page.

        Returns:
        --------
        bool:
            True if the Cancel Deployment button exists, False otherwise
        """
        # Check for the Cancel Deployment button
        cancel_button = self.page.get_by_role("button", name="Cancel Deployment")

        # Check if the button exists and is visible
        if cancel_button.count() > 0 and cancel_button.is_visible():
            return True

        # Try alternative selectors
        alternative_buttons = [
            self.page.locator("button:has-text('Cancel Deployment')"),
            self.page.get_by_role("button").filter(has_text="Cancel Deployment"),
        ]

        for button in alternative_buttons:
            if button.count() > 0 and button.is_visible():
                return True

        return False

    def cancel_deployment(self) -> bool:
        """Cancel the deployment of a function from the function version details page.
        Handles the confirmation dialog properly.

        Returns:
        --------
        bool:
            True if the cancellation was successful, False otherwise
        """
        import logging

        try:
            # First click Cancel Deployment button
            cancel_button = self.page.get_by_role("button", name="Cancel Deployment")
            if not cancel_button.is_visible():
                logging.error("Cancel Deployment button not found on the page")
                return False

            cancel_button.click()
            logging.info("Clicked initial Cancel Deployment button")

            # Wait for dialog to appear
            self.page.wait_for_timeout(1000)

            # Find and click the confirmation button in the dialog
            dialog = self.page.locator('[role="dialog"]')
            if dialog.count() > 0:
                logging.info('Found dialog with selector: [role="dialog"]')
                confirm_button = dialog.get_by_role("button", name="Cancel Deployment")
                if confirm_button.count() > 0 and confirm_button.is_visible():
                    logging.info(f"Found confirm button: {confirm_button}")
                    confirm_button.click()

                    # Wait for network activity to complete after confirmation
                    self.page.wait_for_load_state("networkidle", timeout=10000)

                    # Give time for UI to update
                    self.page.wait_for_timeout(3000)

                    logging.info("Successfully cancelled the deployment")
                    return True
                else:
                    logging.warning("Could not find confirmation button in dialog")
                    return False
            else:
                logging.warning("Could not find confirmation dialog")
                return False

        except Exception as e:
            logging.error(f"Error during deployment cancellation: {str(e)}")
            return False

    def verify_endpoint_value(self, endpoint_type: str, expected_value: str) -> bool:
        """Verify the value of an endpoint (Logs or Metrics) in function version details.

        Parameters:
        -----------
        endpoint_type: str
            The type of endpoint to verify, e.g., "Logs Endpoint" or "Metrics Endpoint"
        expected_value: str
            The expected value for the endpoint

        Returns:
        --------
        bool:
            True if verification is successful, False otherwise

        Raises:
        -------
        AssertionError:
            If the expected value is not found in the endpoint text
        """

        # Wait for the endpoint element to be visible
        self.page.get_by_test_id("kui-label").filter(has_text=endpoint_type).wait_for(
            state="visible", timeout=10000
        )
        endpoint_text = self.page.get_by_text(endpoint_type).locator("..").text_content()

        # Verify the expected value is in the text content
        assert (
            expected_value in endpoint_text
        ), f"Expected {endpoint_type} '{expected_value}' not found in '{endpoint_text}'"
        return True

    def _get_label_value(self, label_text: str, use_code_mirror: bool = False) -> str:
        """Helper method to get value associated with a label in the deployment details.

        Parameters:
        -----------
        label_text: str
            The text of the label to find
        use_code_mirror: bool
            Whether to get value from CodeMirror editor instead of kui-text

        Returns:
        --------
        str or None:
            The value associated with the label, or None if not found
        """
        try:
            # Find the label element
            label = self.page.get_by_test_id("kui-label").filter(has_text=label_text)
            if not label.is_visible():
                logging.error(f"Label {label_text} not found on the page")
                return None

            # Get its parent container
            container = label.locator("xpath=..")
            if not container.is_visible():
                logging.error(f"Container for label {label_text} not found on the page")
                return None

            # Get the value based on type
            if use_code_mirror:
                value_elem = container.locator(".cm-content")
            else:
                value_elem = container.get_by_test_id("kui-text")

            if value_elem.is_visible():
                return value_elem.text_content().strip()
            return None
        except Exception as e:
            logging.error(f"Error getting {label_text} value: {str(e)}")
            return None

    def get_rate_limit_value(self):
        """Get the rate limit value from the function version details page."""
        return self._get_label_value("Rate Limit")

    def get_sync_check_value(self):
        """Get the sync check value from the function version details page."""
        return self._get_label_value("Sync Check")

    def get_excluded_nca_ids_value(self):
        """Get the excluded NCA IDs value from the function version details page."""
        excluded_nca_ids_value = self._get_label_value("Excluded NCA IDs")
        if excluded_nca_ids_value == "—":
            return ["-"]
        if excluded_nca_ids_value:
            # Handle both newline-separated and comma-separated values
            if "\n" in excluded_nca_ids_value:
                return [x.strip() for x in excluded_nca_ids_value.split("\n")]
            if "," in excluded_nca_ids_value:
                return [x.strip() for x in excluded_nca_ids_value.split(",")]
            else:
                return [excluded_nca_ids_value]
        return None

    def get_helm_chart_override_value(self):
        """Get the helm chart override value from the function version details page."""
        return self._get_label_value("Helm Chart Overrides", use_code_mirror=True)
