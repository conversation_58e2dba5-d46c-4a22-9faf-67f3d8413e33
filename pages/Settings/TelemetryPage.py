"""Telemetry page object for the NVCF Settings > Telemetry page."""
from datetime import datetime
from playwright.sync_api import Page
from locator.telemetry_locator import TelemetryLocator
from pages.Settings.SettingsPage import SettingsPage
import logging


class TelemetryPage(SettingsPage):
    """Contains all the elements and functionalities related to Telemetry Endpoints.

    Parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object of the Playwright.
    """

    def __init__(self, page: Page):
        super().__init__(page)
        self.locator = TelemetryLocator()
        self.logger = logging.getLogger("root")

    def navigate_to_settings(self):
        """Navigate to the settings page."""
        return self.navigate()

    def verify_on_settings_page(self):
        """Verify we are on the settings page."""
        # Wait for settings heading to appear
        self.page.wait_for_selector(
            self.locators.SETTINGS_HEADING, timeout=5000, state="visible"
        )
        return True

    def verify_telemetry_section_visible(self):
        """Verify the Telemetry Endpoints section is visible"""
        # First wait for any page loading to complete
        self.page.wait_for_load_state("networkidle")
        self.page.wait_for_load_state("domcontentloaded")

        # Use a more general selector for the heading that matches what's on the page
        telemetry_heading = self.page.get_by_role("heading", name="Telemetry Endpoints")
        # Wait for it to be visible with a longer timeout
        telemetry_heading.wait_for(state="visible", timeout=30000)
        assert telemetry_heading.is_visible(), "Telemetry section is not visible"

    def click_add_endpoint(self):
        """
        Click the Add Endpoint button on the Settings page.

        This navigates from the Settings page to the Add Endpoint page.
        """
        add_endpoint_link = self.page.get_by_role("link", name="Add Endpoint")
        add_endpoint_link.click()
        logging.info("Clicked Add Endpoint button to navigate to Add Endpoint page")

    def create_telemetry_endpoint(
        self,
        name=None,
        provider="Grafana Cloud",
        instance_id=None,
        endpoint_url=None,
        client_certificate=None,
        cafile=None,
        protocol="HTTP",
        key=None,
        telemetry_type={"logs": True, "metrics": True},
        expect_error=False,
        live_endpoint=None,
        application_id=None,
        instrumentation_key=None,
    ):
        """
        Create a new telemetry endpoint with the specified provider.

        This method should be called when on the Add Endpoint page.

        Parameters:
        -----------
        name: str, optional
            Name for the telemetry endpoint. If None, generates a unique name with timestamp.
        provider: str, optional
            Provider for the telemetry endpoint. Default is "Grafana Cloud".
            Supported providers include "Grafana Cloud", "Datadog", etc.
        instance_id: str, optional
            Grafana Cloud instance ID.
        endpoint_url: str, optional
            Grafana Cloud endpoint URL.
        key: str, optional
            Grafana Cloud API key.
        telemetry_type: str, optional
            The telemetry type(s) to select, comma-separated. Defaults to "Logs,Metrics".
        expect_error: bool, optional
            Whether an error is expected during creation (e.g., for negative test cases)

        Returns:
        --------
        tuple:
            If expect_error is False:
                str: The name of the created endpoint
            If expect_error is True:
                (str, bool, str): Tuple containing (endpoint_name, error_found, error_message)
        """
        # Generate unique name if none provided
        if name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            name = f"ai-test-endpoint-{timestamp}"

        # Fill the name field
        name_input = self.page.get_by_role("textbox", name="Enter a name")
        name_input.fill(name)

        # Select provider from dropdown
        # provider_dropdown = self.page.get_by_role("combobox").filter(
        #     has_text="Select a provider"
        # )
        provider_dropdown = self.page.locator(
            "//label[text()='Provider']/../following-sibling::button[@role='combobox']"
        )
        provider_dropdown.click()
        provider_option = self.page.get_by_role("option", name=provider)
        provider_option.click()
        logging.info(f"Selected provider: {provider}")

        # Handle provider-specific fields
        # Currently customized for Grafana Cloud but can be extended for other providers
        if provider == "Grafana Cloud":
            # Fill the form fields
            instance_id_input = self.page.get_by_role("textbox", name="Enter instance ID")
            instance_id_input.fill(instance_id)

        # Only fill endpoint_url if it's provided
        if endpoint_url:
            endpoint_input = self.page.get_by_role("textbox", name="Enter endpoint")
            endpoint_input.fill(endpoint_url)

        # Only fill key if it's provided
        if key:
            key_input = self.page.get_by_role("textbox", name="Enter key")
            key_input.fill(key)

        if provider == "Kratos Thanos" or provider == "Prometheus Remote Write":
            # Fill the form fields
            client_certificate_input = self.page.get_by_role(
                "textbox", name="Enter client cert"
            )
            client_certificate_input.fill(client_certificate)
            if cafile:  # This is optional
                cafile_input = self.page.get_by_role("textbox", name="Enter self cert")
                cafile_input.fill(cafile)

        if provider == "Azure Monitor":
            # Fill the form fields
            live_endpoint_input = self.page.get_by_role(
                "textbox", name="Enter live endpoint"
            )
            live_endpoint_input.fill(live_endpoint)
            application_id_input = self.page.get_by_role(
                "textbox", name="Enter application ID"
            )
            application_id_input.fill(application_id)
            instrumentation_key_input = self.page.get_by_role("textbox", name="Enter key")
            instrumentation_key_input.fill(instrumentation_key)
        # Handle telemetry type selection
        has_logs = telemetry_type.get("logs")
        has_metrics = telemetry_type.get("metrics")
        has_traces = telemetry_type.get("traces")
        if has_logs:
            self.page.get_by_role("checkbox", name="Logs").check()
        if has_metrics:
            self.page.get_by_role("checkbox", name="Metrics").check()
        if has_traces:
            self.page.get_by_role("checkbox", name="Traces").check()

        # Select HTTP protocol
        protocol_dropdown = self.page.get_by_role("combobox").filter(
            has_text="Select a communication protocol"
        )
        protocol_dropdown.click()
        if protocol == "HTTP":
            http_option = self.page.get_by_role("option", name="HTTP")
            http_option.click()
        elif protocol == "GRPC":
            grpc_option = self.page.get_by_role("option", name="GRPC")
            grpc_option.click()

        # Save the configuration
        save_button = self.page.get_by_role("button", name="Save Configuration")
        save_button.click()

        # If expecting an error, check for error indicators
        if expect_error:
            error_found = False
            error_message = ""

            try:
                # Wait for a moment to ensure error has time to appear
                self.page.wait_for_timeout(2000)

                # Check for error SVG icon - using a more robust selector
                error_svg_selectors = [
                    "[data-icon-name='common-error']",
                    "svg[data-icon-name='common-error']",
                    ".MuiSvgIcon-root[data-testid='ErrorIcon']",
                    "[aria-label='Error']",
                    ".Mui-error svg",
                ]

                for selector in error_svg_selectors:
                    error_icon = self.page.locator(selector)
                    if error_icon.count() > 0 and error_icon.first.is_visible():
                        error_found = True
                        logging.info(f"Error icon found with selector: {selector}")
                        break

                # Take a screenshot regardless of whether error is found
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                screenshot_path = f"screenshots/endpoint_error_check_{timestamp}.png"
                self.page.screenshot(path=screenshot_path)
                logging.info(f"Screenshot saved to {screenshot_path}")

                # Check for error messages in form validation and text content
                error_text_selectors = [
                    ".MuiFormHelperText-root.Mui-error",
                    ".Mui-error",
                    "[role='alert']",
                    ".Toastify__toast--error",
                    "form .MuiAlert-root",
                ]

                for selector in error_text_selectors:
                    error_elements = self.page.locator(selector).all()
                    for element in error_elements:
                        if element.is_visible():
                            try:
                                error_text = element.text_content()
                                if error_text and any(
                                    keyword in error_text.lower()
                                    for keyword in [
                                        "already exists",
                                        "unique",
                                        "duplicate",
                                        "in use",
                                    ]
                                ):
                                    error_found = True
                                    error_message = error_text
                                    logging.info(
                                        f"Error message found with selector {selector}: {error_message}"
                                    )
                                    break
                            except Exception as e:
                                logging.warning(
                                    f"Error getting text from element: {str(e)}"
                                )

                # Look for any text on the page containing error keywords
                if not error_found:
                    error_keywords = [
                        "already exists",
                        "must be unique",
                        "duplicate",
                        "already in use",
                        "name already exists",
                        "already exists in the account",
                    ]

                    for keyword in error_keywords:
                        try:
                            text_elements = self.page.get_by_text(
                                keyword, exact=False
                            ).all()
                            for element in text_elements:
                                if element.is_visible():
                                    error_found = True
                                    error_message = element.text_content()
                                    logging.info(
                                        f"Found error text with keyword '{keyword}': {error_message}"
                                    )
                                    break
                            if error_found:
                                break
                        except Exception as e:
                            logging.warning(
                                f"Error searching for keyword '{keyword}': {str(e)}"
                            )

                # Verify we're still on the form page (creation failed)
                try:
                    is_still_on_form = self.page.get_by_role(
                        "button", name="Save Configuration"
                    ).is_visible()
                    if is_still_on_form:
                        logging.info(
                            "Still on the form page, which indicates error (form not submitted successfully)"
                        )
                        # If we're still on the form and filled everything out but no explicit error is found,
                        # we'll assume something failed
                        if not error_found:
                            error_found = True
                            error_message = (
                                "Form submission failed without explicit error message"
                            )
                            logging.info(
                                "No explicit error found, but form submission didn't proceed"
                            )
                except Exception as e:
                    logging.warning(f"Error checking if still on form: {str(e)}")

                return (name, error_found, error_message)

            except Exception as e:
                logging.error(f"Error while checking for expected errors: {str(e)}")
                return (name, False, f"Exception occurred: {str(e)}")

        # Regular flow - not expecting errors
        try:
            # Verify the "Endpoint is being created..." message appears
            creating_message = self.page.get_by_text("Endpoint is being created...")
            assert creating_message.is_visible(), "Creation message not displayed"
            return name
        except Exception as e:
            logging.error(f"Error verifying endpoint creation: {str(e)}")
            # Unexpected error occurred - capture screenshot
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.page.screenshot(
                path=f"screenshots/endpoint_creation_error_{timestamp}.png"
            )
            raise

    def search_endpoint(self, endpoint_name):
        """
        Search for the specified endpoint in the telemetry endpoint table.

        Args:
            endpoint_name: The name of the endpoint to search for.
        """
        logging.info(f"Searching for endpoint: {endpoint_name}")

        # Ensure the table has finished loading
        self.wait_for_table_finished_loading()

        try:
            # Wait for page to finish loading
            self.page.wait_for_load_state("networkidle", timeout=15000)

            # Target the second search box - using the search box under the section
            search_input = self.page.locator("section").get_by_role(
                "textbox", name="Search table"
            )

            # Confirm search box is visible
            if not search_input.is_visible():
                logging.warning(
                    "Search box not found under section, trying alternative location methods"
                )
                # Backup: Try to find search box near the filter button
                filter_button = self.page.get_by_role("button", name="Filter")
                if filter_button.is_visible():
                    logging.info(
                        "Found Filter button, trying to find the search box nearby"
                    )
                    filter_button.click()
                    self.page.wait_for_timeout(1000)
                    search_input = self.page.locator("section").get_by_role(
                        "textbox", name="Search table"
                    )

            # If still not found, try other locating methods
            if not search_input.is_visible():
                logging.warning(
                    "Cannot find search box under section, trying second search box"
                )
                all_search_boxes = self.page.get_by_role(
                    "textbox", name="Search table"
                ).all()
                if len(all_search_boxes) >= 2:
                    search_input = all_search_boxes[1]  # Use the second search box
                else:
                    logging.warning(
                        "No two search boxes found, using the first available search box"
                    )
                    search_input = self.page.get_by_role(
                        "textbox", name="Search table"
                    ).first

            logging.info("Found search box, clearing and entering search term")
            # Clear search box and enter the endpoint name to search for
            search_input.click()
            search_input.fill("")
            self.page.wait_for_timeout(500)
            search_input.fill(endpoint_name)

            # Wait for search results to load
            self.page.wait_for_timeout(2000)

            logging.info(f"Used search box to search for '{endpoint_name}'")

        except Exception as e:
            logging.warning(f"Error occurred while searching for endpoint: {str(e)}")
            # Take screenshot to record error state
            self.page.screenshot(
                path=f"search_endpoint_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            )

    def verify_endpoint_exists(self, endpoint_name, max_attempts=10):
        """
        Verify if the endpoint exists in the table

        Args:
            endpoint_name: The name of the endpoint
            max_attempts: Maximum number of attempts

        Returns:
            True if endpoint exists, False otherwise
        """
        self.wait_for_table_finished_loading()
        self.search_for_endpoint(endpoint_name)
        endpoints_count = self.get_telemetry_endpoints_count()
        return endpoints_count > 0

    def wait_for_table_finished_loading(self):
        """Wait for the table to finish loading"""
        try:
            # Check if loading indicator has disappeared
            loading_indicator = self.page.locator(
                "//div[contains(@class, 'MuiCircularProgress-root')]"
            )
            if loading_indicator.count() > 0:
                loading_indicator.first.wait_for(state="hidden", timeout=10000)

            # Ensure data grid exists and is visible
            self.page.get_by_test_id("kui-grid").wait_for(state="visible", timeout=10000)

            # Wait for network requests to complete
            self.page.wait_for_load_state("networkidle", timeout=10000)

            logging.info("Telemetry endpoints table finished loading")
            return True
        except Exception as e:
            logging.warning(f"Error waiting for table to finish loading: {str(e)}")
            return False

    def validate_endpoint_name_requirements(self, invalid_names=None):
        """
        Validates the telemetry endpoint name field requirements.

        This method handles the complete flow from Settings page to Add Endpoint page:
        1. Navigates to Settings page if needed
        2. Clicks Add Endpoint button to go to Add Endpoint page
        3. Tests name validation rules on the Add Endpoint page
        4. Returns to Settings page by canceling

        Parameters:
        -----------
        invalid_names: list, optional
            List of invalid name formats to test. Defaults to ["-1example", ".example", "-.-"]

        Returns:
        --------
        dict:
            Dictionary with validation results for each test case
        """
        if invalid_names is None:
            invalid_names = ["-1example", ".example", "-.-"]

        results = {"length_validation": False, "format_validation": {}}

        try:
            # Step 1: Navigate to settings if not already there
            if "settings" not in self.page.url:
                self.navigate_to_settings()
                self.verify_on_settings_page()
                self.verify_telemetry_section_visible()

            # Step 2: Click Add Endpoint button
            self.click_add_endpoint()

            # Step 3: Test name length validation
            name_input = self.page.get_by_placeholder("Enter a name")
            long_name = "a" * 55  # 55 characters (exceeding the 48 char limit)
            name_input.fill(long_name)

            # Check for length error
            length_error = self.page.locator("text=Name cannot exceed 48 characters")
            results["length_validation"] = length_error.is_visible()

            # Test various invalid name formats
            for invalid_name in invalid_names:
                name_input.fill("")
                name_input.fill(invalid_name)

                # Check for format error
                format_error = self.page.locator(
                    "text=Name must start with a lowercase letter"
                )
                results["format_validation"][invalid_name] = format_error.is_visible(
                    timeout=5000
                )

            # Clean up: Close the form and return to Settings page
            cancel_button = self.page.get_by_role("button", name="Cancel")
            cancel_button.click()

        except Exception as e:
            logging.error(f"Error during endpoint name validation: {str(e)}")
            # Take screenshot for debugging
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.page.screenshot(
                path=f"screenshots/endpoint_validation_error_{timestamp}.png"
            )

        return results

    def delete_endpoint(self, endpoint_name):
        """
        Delete the specified endpoint from the telemetry endpoint table.

        Args:
            endpoint_name: The name of the endpoint to delete.
        """
        self.wait_for_table_finished_loading()
        self.search_for_endpoint(endpoint_name)
        refresh_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//button[text()=' Refresh']"
        self.page.locator(refresh_button).click()
        self.page.wait_for_selector(refresh_button, state="visible")
        action_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//td[@headers='data-view-header-row-actions']"
        self.page.locator(action_button).click()
        delete_button = "//div[@role='menuitem' and text()='Delete Endpoint']"
        self.page.locator(delete_button).click()
        self.page.wait_for_selector(
            "//div[contains(normalize-space(), 'Delete Endpoint') and @role='dialog']",
            state="visible",
        )
        confirm_button = "//button[contains(normalize-space(), 'Delete Endpoint') and @data-testid='kui-button']"
        self.page.locator(confirm_button).click()
        delete_msg = "//div[contains(normalize-space(), 'successfully deleted')]"
        self.page.wait_for_selector(delete_msg, state="visible")

    def update_endpoint(self, endpoint_name, update_data):
        """
        Update the specified endpoint with the provided data.

        Args:
            endpoint_name: The name of the endpoint to update.
            update_data: Dictionary containing the fields to update.
        """
        self.wait_for_table_finished_loading()
        self.search_for_endpoint(endpoint_name)
        refresh_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//button[text()=' Refresh']"
        self.page.locator(refresh_button).click()
        self.page.wait_for_selector(refresh_button, state="visible")
        action_button = "//h4[text()='Telemetry Endpoints']/../following-sibling::div//td[@headers='data-view-header-row-actions']"
        self.page.locator(action_button).click()
        update_button = "//div[@role='menuitem' and text()='Update Key']"
        self.page.locator(update_button).click()
        self.page.wait_for_selector(
            "//div[contains(normalize-space(), 'Update Key') and @role='dialog']",
            state="visible",
        )
        self.page.locator(
            "//p[text()='Update the existing key for this telemetry endpoint configuration. Ensure all fields are completed to save your changes.']"
        ).is_visible()
        private_key_input = self.page.locator("//textarea[@name='secret.value.clientKey']")
        client_certificate_input = self.page.locator(
            "//textarea[@name='secret.value.clientCert']"
        )
        cafile_input = self.page.locator("//textarea[@name='secret.value.caFile']")
        key_input = self.page.locator("//textarea[@name='secret.value']")
        instance_id_input = self.page.locator("//input[@name='secret.value.instanceId']")
        api_key_input = self.page.locator("//textarea[@name='secret.value.apiKey']")
        instrumentation_key_input = self.page.locator(
            "//textarea[@name='secret.value.instrumentationKey']"
        )
        live_endpoint_input = self.page.locator(
            "//textarea[@name='secret.value.liveEndpoint']"
        )
        application_id_input = self.page.locator(
            "//textarea[@name='secret.value.applicationId']"
        )
        if "private_key" in update_data:
            private_key_input.fill(update_data["private_key"])
        if "client_certificate" in update_data:
            client_certificate_input.fill(update_data["client_certificate"])
        if "cafile" in update_data:
            cafile_input.fill(update_data["cafile"])
        if "key" in update_data:
            key_input.fill(update_data["key"])
        if "instance_id" in update_data:
            instance_id_input.fill(update_data["instance_id"])
        if "api_key" in update_data:
            api_key_input.fill(update_data["api_key"])
        if "instrumentation_key" in update_data:
            instrumentation_key_input.fill(update_data["instrumentation_key"])
        if "live_endpoint" in update_data:
            live_endpoint_input.fill(update_data["live_endpoint"])
        if "application_id" in update_data:
            application_id_input.fill(update_data["application_id"])
        save_button = "//button[text()='Save Changes']"
        self.page.locator(save_button).click()
        try:
            self.page.wait_for_selector(
                "//span[contains(normalize-space(), 'Secrets succesfully updated')]",
                state="visible",
            )
            return True
        except Exception as e:
            logging.error(f"Error waiting for secrets update: {str(e)}")
            return False
