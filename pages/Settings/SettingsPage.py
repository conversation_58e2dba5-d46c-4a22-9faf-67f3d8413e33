"""Settings page object for the NVCF Settings page."""

from playwright.sync_api import Page
from locator.settings_locator import SettingsPageLocators
import logging
import time
import re
from cloudia.ui.base_page import BasePage
from config.consts import (
    SIDEBAR_APP_CLOUDFUNCTIONS,
    SIDEBAR_CLOUDFUNCTIONS_ENTRY_SETTINGS,
    CURRENT_ENV,
)
from datetime import datetime
import random


class SettingsPage(BasePage):
    """Page object for the NVCF Settings page."""

    additional_settings = "//button[@aria-label='Additional Settings']"

    def __init__(self, page: Page):
        super().__init__(
            page, SIDEBAR_APP_CLOUDFUNCTIONS, SIDEBAR_CLOUDFUNCTIONS_ENTRY_SETTINGS
        )
        self.locators = SettingsPageLocators()

    def navigate(self):
        """Navigate to the Settings page using sidebar navigation.

        Falls back to direct URL if sidebar navigation fails.
        """
        try:
            # First attempt: Use BasePage's standard navigation
            logging.info("Attempting to navigate to Settings page via BasePage navigation")
            # BasePage.navigate_to_page will handle checking if we're already on the page
            self.navigate_to_page()

            # Wait for settings heading to appear to confirm navigation success
            self.page.wait_for_selector(
                self.locators.SETTINGS_HEADING, timeout=5000, state="visible"
            )
            logging.info("Successfully navigated to Settings page via BasePage navigation")
            return self
        except Exception as e:
            logging.warning(f"Error navigating via BasePage method: {str(e)}")
            logging.info("Falling back to direct URL navigation")

            # Direct URL navigation as fallback
            try:
                # Determine the base URL based on service type
                base_url = "https://nvcf.ngc.nvidia.com/settings"
                if "stg" in self.service_type:
                    base_url = "https://nvcf.stg.ngc.nvidia.com/settings"
                elif "canary" in self.service_type:
                    base_url = "https://nvcf.canary.ngc.nvidia.com/settings"

                logging.info(f"Navigating directly to settings URL: {base_url}")
                self.page.goto(base_url, timeout=10000)

                # Wait for settings heading to appear
                self.page.wait_for_selector(
                    self.locators.SETTINGS_HEADING, timeout=5000, state="visible"
                )
                logging.info("Successfully navigated to Settings page via direct URL")
            except Exception as direct_url_error:
                logging.error(
                    f"Failed to navigate to settings page via all methods: {str(direct_url_error)}"
                )
                raise

        return self

    def verify_on_settings_page(self):
        """Verify we are on the settings page."""
        # Wait for settings heading to appear
        self.page.wait_for_selector(
            self.locators.SETTINGS_HEADING, timeout=5000, state="visible"
        )
        logging.info("Verified Settings page loaded successfully")
        return True

    def scroll_to_telemetry_endpoints(self):
        """Scroll to the Telemetry Endpoints section."""
        self.page.get_by_text("Telemetry Endpoints").scroll_into_view_if_needed(
            timeout=5000
        )
        return self

    def verify_telemetry_section_visible(self):
        """Verify that the Telemetry Endpoints section is visible."""
        telemetry_section = self.page.get_by_text("Telemetry Endpoints")
        assert telemetry_section.is_visible(), "Telemetry Endpoints section is not visible"
        return True

    def wait_for_telemetry_endpoints_loading(self):
        """Wait for the telemetry endpoints table to finish loading."""
        try:
            # Wait for the telemetry table section to be visible
            self.page.get_by_text("Telemetry Endpoints").wait_for(
                state="visible", timeout=10000
            )

            # Scroll to ensure the section is in view
            self.scroll_to_telemetry_endpoints()

            # Wait for any table in the telemetry section to load
            telemetry_section = self.page.locator("section", has_text="Telemetry Endpoints")
            telemetry_section.locator("table").wait_for(state="visible", timeout=10000)

            # Wait for network idle to ensure data has loaded
            self.page.wait_for_load_state("networkidle", timeout=10000)

            # Wait a bit longer to ensure the DOM is fully updated with data
            self.page.wait_for_timeout(2000)

            logging.info("Telemetry endpoints table finished loading")
        except Exception as e:
            logging.warning(f"Error waiting for telemetry endpoints table: {str(e)}")
            # Continue anyway, as we'll check for endpoints later

        return self

    def search_for_endpoint(self, endpoint_name):
        """
        Search for a specific endpoint by name
        """
        try:
            logging.info(f"Searching for endpoint: {endpoint_name}")
            self.scroll_to_telemetry_endpoints()

            # First try: Look for search in the Telemetry Endpoints section
            try:
                telemetry_section = self.page.locator(
                    "section", has_text="Telemetry Endpoints"
                )
                search_input = telemetry_section.get_by_placeholder("Search").first

                if search_input.is_visible():
                    search_input.click()
                    search_input.fill("")  # Clear first
                    search_input.fill(endpoint_name)
                    search_input.press("Enter")
                    logging.info(
                        f"Entered search term '{endpoint_name}' using section-based locator"
                    )

                    # Wait for search results
                    self.page.wait_for_load_state("networkidle", timeout=5000)
                    self.page.wait_for_timeout(1000)
                    return self
            except Exception as e:
                logging.warning(f"Failed to search using primary method: {str(e)}")

            # Fallback: Try all search inputs
            all_inputs = self.page.get_by_placeholder("Search").all()
            logging.info(f"Found {len(all_inputs)} search inputs")

            if len(all_inputs) >= 2:
                # Usually second search is for endpoints
                search_input = all_inputs[1]
                logging.info("Using second search input")
            elif len(all_inputs) > 0:
                # Use first if only one exists
                search_input = all_inputs[0]
                logging.info("Using first search input")
            else:
                logging.error("No search inputs found")
                return self

            search_input.click()
            search_input.fill("")  # Clear first
            search_input.fill(endpoint_name)
            search_input.press("Enter")
            logging.info(f"Entered search term '{endpoint_name}' using fallback method")

            # Wait for search results
            self.page.wait_for_load_state("networkidle", timeout=5000)
            self.page.wait_for_timeout(1000)

            return self
        except Exception as e:
            logging.error(f"Error searching for endpoint '{endpoint_name}': {str(e)}")
            self.page.screenshot(path=f"screenshots/search_failed_{time.time()}.png")
            return self

    def verify_endpoint_exists(self, endpoint_name):
        """Verify that an endpoint exists in the table"""
        try:
            endpoint_element = self.page.get_by_text(endpoint_name, exact=True).first
            return endpoint_element.is_visible()
        except Exception:
            return False

    def verify_table_columns_with_fallback(
        self, table_selector, expected_columns, special_columns=None
    ):
        """
        Verify that a table has the expected columns with fallback for special columns that might not have text

        Parameters:
        -----------
        table_selector: str
            CSS selector for the table
        expected_columns: list
            List of expected column names
        special_columns: list
            Optional list of column names that might not have visible text and need special handling

        Returns:
        --------
        bool
            True if verification passes, False otherwise
        """
        # Get the column headers from the table
        header_row = self.page.locator(f"{table_selector} tr:first-child")

        special_columns = special_columns or ["Actions"]
        all_columns_valid = True

        # Check each expected column
        for column in expected_columns:
            column_cell = header_row.locator(f"th:has-text('{column}')")
            is_visible = column_cell.is_visible()
            logging.info(f"Column '{column}' visibility: {is_visible}")

            # For special columns like Actions, use a different approach
            if not is_visible and column in special_columns:
                # Try alternate selector for special columns which might be in different positions or have no text
                last_header_cell = header_row.locator("th").last
                cell_text = last_header_cell.text_content()
                logging.info(f"Last header cell text: '{cell_text}'")

                # Check if last column exists
                if last_header_cell.is_visible():
                    logging.info(
                        f"Verified special column '{column}' exists through fallback method"
                    )
                else:
                    logging.error(
                        f"Special column '{column}' not found with fallback method"
                    )
                    all_columns_valid = False
            elif not is_visible:
                logging.error(f"Column '{column}' not found in table")
                all_columns_valid = False

        if all_columns_valid:
            logging.info(f"Verified table columns: {', '.join(expected_columns)}")

        return all_columns_valid

    def verify_telemetry_table_columns_robust(self, expected_columns):
        """
        A more robust version of verify_telemetry_table_columns that handles special columns like "Actions"

        Parameters:
        -----------
        expected_columns: list
            List of expected column names

        Returns:
        --------
        bool
            True if verification passes, False otherwise
        """
        table_selector = "table:below(:text('Telemetry Endpoints'))"
        return self.verify_table_columns_with_fallback(table_selector, expected_columns)

    def check_if_endpoint_exists(self, endpoint_name):
        """Check if an endpoint exists without raising an exception"""
        try:
            endpoint_element = self.page.get_by_text(endpoint_name, exact=True).first
            return endpoint_element.is_visible()
        except Exception:
            return False

    def clear_endpoint_search(self):
        """
        Clear the endpoint search field to show all endpoints.
        """
        try:
            # Scroll to telemetry endpoints section
            self.scroll_to_telemetry_endpoints()
            logging.info("Clearing endpoint search field")

            # First try: Use section-based locator
            try:
                telemetry_section = self.page.locator(
                    "section", has_text="Telemetry Endpoints"
                )
                search_input = telemetry_section.get_by_placeholder("Search").first

                if search_input.is_visible():
                    search_input.click()
                    search_input.fill("")
                    search_input.press("Enter")
                    logging.info("Successfully cleared search using section-based locator")

                    # Wait for the results to update
                    self.page.wait_for_load_state("networkidle", timeout=5000)
                    self.page.wait_for_timeout(1000)
                    return self
            except Exception as e:
                logging.warning(f"Failed to clear search using primary method: {str(e)}")

            # Second try: Use all search inputs
            all_inputs = self.page.get_by_placeholder("Search").all()
            if len(all_inputs) >= 2:
                search_input = all_inputs[1]  # Second search is typically for endpoints
                search_input.click()
                search_input.fill("")
                search_input.press("Enter")
                logging.info("Successfully cleared search using all inputs method")

                # Wait for the results to update
                self.page.wait_for_load_state("networkidle", timeout=5000)
                self.page.wait_for_timeout(1000)
                return self
            elif len(all_inputs) == 1:
                search_input = all_inputs[0]
                search_input.click()
                search_input.fill("")
                search_input.press("Enter")
                logging.info("Successfully cleared search using single input")

                # Wait for the results to update
                self.page.wait_for_load_state("networkidle", timeout=5000)
                self.page.wait_for_timeout(1000)
                return self

            # Last resort: Try to click a refresh button if present
            try:
                refresh_button = self.page.get_by_role("button", name="Refresh")
                if refresh_button.is_visible():
                    refresh_button.click()
                    logging.info("Clicked refresh button as fallback")
                    self.page.wait_for_load_state("networkidle", timeout=5000)
            except Exception as e:
                logging.warning(f"Could not find refresh button: {str(e)}")

            return self
        except Exception as e:
            logging.warning(f"Error clearing endpoint search: {str(e)}")
            # Take screenshot for debugging
            self.page.screenshot(path=f"screenshots/clear_search_failed_{time.time()}.png")
        return self

    def get_telemetry_endpoints_count(self):
        """
        Get the count of telemetry endpoints from the UI.

        Returns:
        --------
        int:
            The number of telemetry endpoints displayed in the UI, or 0 if none found
        """
        try:
            logging.info("Getting telemetry endpoints count")
            self.wait_for_telemetry_endpoints_loading()

            # Use get_by_text() to locate the element containing the endpoint count
            element = self.page.get_by_text(
                re.compile(r"\d+\s*endpoints?", re.IGNORECASE)
            ).first

            # If no element found, log the issue and return 0
            if not element:
                logging.warning("Could not find any elements containing endpoint count")
                return 0

            # Extract the text and match the count
            text = element.inner_text()
            logging.info(f"Found text: {text}")  # Log the text for debugging

            # Use a regex to match the count
            match = re.search(r"(\d+)\s*endpoints?", text, re.IGNORECASE)
            if match:
                count = int(match.group(1))
                logging.info(f"Found {count} endpoints from text search")
                return count

            logging.warning("Could not find endpoint count in the UI")
            return 0

        except Exception as e:
            logging.error(f"Error getting telemetry endpoints count: {str(e)}")
            return 0

    def click_endpoint_actions_button(self, endpoint_name=None):
        """
        Click the actions button for a specific endpoint or the first one in the table.

        Parameters:
        -----------
        endpoint_name: str, optional
            The name of the endpoint to click the actions button for.
            If None, the first endpoint in the table will be used.
        """
        try:
            # If endpoint name is provided, search for it first
            if endpoint_name:
                self.search_for_endpoint(endpoint_name)
                logging.info(f"Searching for endpoint: {endpoint_name}")

            # Ensure scrolling to the telemetry endpoints section to make the table visible
            self.scroll_to_telemetry_endpoints()

            # Wait for the table to load
            self.page.wait_for_selector("table[data-testid='kui-table']", timeout=5000)

            # Get all tables
            tables = self.page.locator("table[data-testid='kui-table']").all()
            telemetry_table = None

            # Iterate through all tables to find the telemetry endpoints table
            for table in tables:
                # Check if the table headers contain specific fields (Provider, Telemetry Type, etc.)
                headers_text = table.locator("thead th").all_text_contents()
                headers_text = " ".join(headers_text).lower()

                if "provider" in headers_text and "telemetry type" in headers_text:
                    telemetry_table = table
                    logging.info("Found telemetry endpoints table")
                    break

            if not telemetry_table:
                logging.error("Could not find telemetry endpoints table")
                return False

            # Find the first row in the table body (tbody)
            tbody = telemetry_table.locator("tbody").first
            first_row = tbody.locator("tr").first

            # Check if at least one row is found
            if first_row:
                # Find the actions button in the first row (last cell with a menu trigger)
                actions_button = first_row.locator("[data-testid='kui-menu-trigger']").first

                # Click the actions button
                actions_button.click()
                logging.info("Clicked actions button for the endpoint")
                return True

        except Exception as e:
            logging.error(f"Error clicking endpoint actions button: {str(e)}")
            self.page.screenshot(
                path=f"error_actions_button_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            )
            return False

    def click_delete_endpoint_option(self):
        """
        Click Delete option from the actions menu.
        """
        try:
            # Based on the dialog structure screenshot, directly target the Delete option
            logging.info("Looking for Delete option in menu")

            # Look for the specific menu structure with StaticText containing "Delete"
            delete_option = (
                self.page.locator("div[role='menu']").get_by_text("Delete").first
            )

            if delete_option.is_visible():
                logging.info("Found Delete option, clicking it")
                delete_option.click()
                logging.info("Clicked Delete option")
                return True

            # Fallback: try finding the button inside the menu with role menuitem
            logging.info("Trying fallback - button with role menuitem")
            delete_button = (
                self.page.locator("div[role='menu']")
                .get_by_role("button")
                .filter(has_text="Delete")
                .first
            )

            if delete_button.is_visible():
                logging.info("Found Delete button, clicking it")
                delete_button.click()
                logging.info("Clicked Delete button")
                return True

            # Last resort - press Delete on keyboard
            logging.info("Using last resort - simulating keyboard input")
            self.page.keyboard.press("Delete")
            logging.info("Pressed Delete key")

            # Wait for confirmation dialog to verify success
            if self.page.locator("dialog", has_text="Delete Endpoint").is_visible(
                timeout=3000
            ):
                logging.info("Delete action succeeded, confirmation dialog appeared")
                return True

            return False
        except Exception as e:
            logging.warning(f"Error clicking Delete option: {str(e)}")
            return False

    def handle_endpoint_deletion_dialog(self, should_confirm=True):
        """
        Handle the endpoint deletion dialog - either confirm or cancel the deletion.

        Parameters:
        -----------
        should_confirm: bool
            True to confirm the deletion, False to cancel it

        Returns:
        --------
        bool:
            True if the action succeeded, False otherwise
        """
        try:
            # Look specifically for dialog with title "Delete Endpoint"
            dialog = self.page.locator("dialog", has_text="Delete Endpoint").first

            # Fallback to any dialog if specific one not found
            if not dialog.is_visible():
                dialog = self.page.locator("div[role='dialog']").first
                logging.info("Using fallback dialog locator")

            if not dialog.is_visible():
                logging.warning("Could not find delete confirmation dialog")
                # Try to close any dialogs by pressing Escape
                self.page.keyboard.press("Escape")
                return False

            logging.info("Found delete confirmation dialog")

            # Determine which button to click based on the action
            if should_confirm:
                # For confirm: look for Delete Endpoint button
                button = dialog.get_by_role("button", name="Delete Endpoint").first
                button_name = "Delete Endpoint"
                fallback_index = -1  # Last button is typically Delete/Confirm
            else:
                # For cancel: look for Cancel button
                button = dialog.get_by_role("button", name="Cancel").first
                button_name = "Cancel"
                fallback_index = 0  # First button is typically Cancel

            if button.is_visible():
                logging.info(f"Found {button_name} button, clicking it")
                button.click(timeout=3000)
                self.page.wait_for_timeout(1000)

                # For confirmation, we're done
                if should_confirm:
                    return True

                # For cancel, verify dialog closed
                dialog_closed = not dialog.is_visible()
                if dialog_closed:
                    logging.info("Dialog closed successfully after canceling")
                    return True
                else:
                    logging.warning("Dialog still visible after clicking Cancel")
                    # Try Escape as fallback
                    self.page.keyboard.press("Escape")
                    return False
            else:
                logging.warning(f"{button_name} button not found in dialog")
                # Fallback: try using button position
                buttons = dialog.locator("button").all()
                if len(buttons) > 0:
                    buttons[fallback_index].click(timeout=3000)
                    self.page.wait_for_timeout(2000)
                    return True

                # Press Escape as last resort
                self.page.keyboard.press("Escape")
                return False

        except Exception as e:
            logging.warning(f"Error handling endpoint deletion dialog: {str(e)}")
            # Try to escape out as a last resort
            try:
                self.page.keyboard.press("Escape")
                logging.info("Pressed Escape as a last resort")
            except Exception:
                pass
            return False

    def confirm_endpoint_deletion(self):
        """
        Confirm deletion in the confirmation dialog.
        """
        return self.handle_endpoint_deletion_dialog(should_confirm=True)

    def cancel_endpoint_deletion(self):
        """
        Cancel deletion in the confirmation dialog by clicking the Cancel button.
        """
        return self.handle_endpoint_deletion_dialog(should_confirm=False)

    def wait_for_toast(self, toast_message: str) -> tuple[bool, str]:
        try:
            # Wait for toast container to appear - specifically within the app shell body
            app_body = self.page.get_by_test_id("kui-app-shell-body")
            toast = app_body.get_by_test_id("kui-stack").first
            toast.wait_for(state="visible", timeout=10000)

            # Get all toast messages and check their content
            # Messages can appear and disappear dynamically
            all_toast_messages = ""
            for _ in range(5):  # Try up to 10 times
                toast_messages = toast.get_by_test_id("kui-text").all()
                for message in toast_messages:
                    try:
                        message_text = message.text_content()
                        all_toast_messages = all_toast_messages + message_text
                        print(f"Message text: {message_text}")
                        if toast_message.lower() in message_text.lower():
                            logging.info(f"${toast_message} pops out")
                            return True, message_text
                    except Exception:
                        continue
                    time.sleep(
                        2
                    )  # Message might have disappeared, continue checking others

            return True, all_toast_messages

        except Exception as e:
            error_msg = f"Error while waiting for deletion toast: {str(e)}"
            logging.error(error_msg)
            return False, error_msg
        finally:
            # Wait for network requests to complete to ensure UI state is stable
            try:
                self.page.wait_for_load_state("networkidle", timeout=5000)
            except Exception as e:
                logging.warning(f"Network wait timed out: {str(e)}")

    def wait_for_endpoint_deletion(self, endpoint_name: str) -> tuple[bool, str]:
        """
        Wait for the endpoint deletion toast message and determine if deletion was successful.

        Args:
            endpoint_name: The name of the endpoint being deleted

        Returns:
            tuple[bool, str]: (success, message)
            - success: True if deletion was successful, False otherwise
            - message: The actual toast message received
        """
        return self.wait_for_toast(f"Endpoint {endpoint_name} was successfully deleted")

    def prepare_endpoint_for_action(
        self, endpoint_name=None, should_skip_if_no_endpoints=True
    ):
        """
        Common method to prepare an endpoint for subsequent actions.
        This handles all the necessary preparation and validation before performing an action.

        Parameters:
        -----------
        endpoint_name: str
            The name of the endpoint to prepare for action
        should_skip_if_no_endpoints: bool
            Whether to return early if no endpoints are found

        Returns:
        --------
        dict:
            Dictionary containing preparation results:
            - 'success': bool - Overall preparation success
            - 'initial_count': int - Initial count of endpoints
            - 'endpoint_exists': bool - Whether the specified endpoint exists
            - 'error': str - Error message if any
        """
        result = {
            "success": True,
            "initial_count": 0,
            "endpoint_exists": False,
            "error": None,
        }

        try:
            # Navigate to the telemetry endpoints section
            logging.info("Preparing endpoint for action")
            self.scroll_to_telemetry_endpoints()

            # Wait for the table to load
            self.wait_for_telemetry_endpoints_loading()

            # Get the initial count of endpoints
            result["initial_count"] = self.get_telemetry_endpoints_count()
            logging.info(f"Found {result['initial_count']} telemetry endpoints")

            # If no endpoints and configured to skip, mark as unsuccessful
            if result["initial_count"] == 0 and should_skip_if_no_endpoints:
                result["success"] = False
                result["error"] = "No endpoints found"
                logging.info("No endpoints found, skipping further preparation")
                return result

            # If an endpoint name is provided, search for it
            if endpoint_name:
                logging.info(f"Searching for endpoint: {endpoint_name}")
                self.search_for_endpoint(endpoint_name)
                self.wait_for_telemetry_endpoints_loading()

                # Verify the endpoint exists
                result["endpoint_exists"] = self.check_if_endpoint_exists(endpoint_name)
                if result["endpoint_exists"]:
                    logging.info(f"Endpoint {endpoint_name} found successfully")
                else:
                    logging.warning(f"Endpoint {endpoint_name} not found")
                    result["success"] = False
                    result["error"] = f"Endpoint {endpoint_name} not found"

            return result

        except Exception as e:
            result["success"] = False
            result["error"] = f"Error preparing endpoint for action: {str(e)}"
            logging.error(result["error"])
            # Take a screenshot for debugging
            self.page.screenshot(path=f"screenshots/prepare_error_{time.time()}.png")
            return result

    def perform_endpoint_delete_action(
        self, endpoint_name, should_cancel=False, prep_result=None
    ):
        """
        Perform a delete action on an endpoint and verify the results.
        This handles both the delete confirmation and cancel flows.

        Parameters:
        -----------
        endpoint_name: str
            The name of the endpoint to delete
        should_cancel: bool
            Whether to cancel the deletion (True) or confirm it (False)
        prep_result: dict, optional
            Results from prepare_endpoint_for_action method if already called

        Returns:
        --------
        dict:
            Dictionary containing action results:
            - 'success': bool - Whether the action was successful
            - 'verified': bool - Whether the verification was successful
            - 'error': str - Error message if any
        """
        result = {"success": False, "verified": False, "error": None}

        try:
            # If prep_result is provided, check if we should continue
            if prep_result:
                # If preparation failed, log and exit early
                if not prep_result.get("success", True):
                    result[
                        "error"
                    ] = f"Preparation failed: {prep_result.get('error', 'Unknown error')}"
                    logging.error(result["error"])
                    return result

                # If endpoint doesn't exist and one was specified, log and exit early
                if endpoint_name and not prep_result.get("endpoint_exists", False):
                    result[
                        "error"
                    ] = f"Endpoint {endpoint_name} not found, cannot perform delete action"
                    logging.error(result["error"])
                    return result
            else:
                # If no prep_result, search for the endpoint ourselves
                if endpoint_name:
                    self.search_for_endpoint(endpoint_name)
                    self.wait_for_telemetry_endpoints_loading()

            # Search for the endpoint and click its actions button
            if not self.click_endpoint_actions_button(endpoint_name):
                result[
                    "error"
                ] = f"Failed to find or click actions button for {endpoint_name}"
                logging.error(result["error"])
                return result

            # STEP 1: Select Delete from the menu
            delete_clicked = self.click_delete_endpoint_option()
            if not delete_clicked:
                result["error"] = "Failed to click delete option"
                logging.error(result["error"])
                return result

            logging.info("Selected Delete option from the actions menu")
            result["success"] = True  # Mark successful so far

            # STEP 2: Handle the confirmation dialog
            if should_cancel:
                dialog_handled = self.cancel_endpoint_deletion()
                action_type = "cancellation"
            else:
                dialog_handled = self.confirm_endpoint_deletion()
                action_type = "deletion"

            if not dialog_handled:
                result["error"] = f"Failed to {action_type} in confirmation dialog"
                logging.error(result["error"])
                return result

            logging.info(f"Successfully handled {action_type} in confirmation dialog")

            # Wait for the operation to complete
            success, message = self.wait_for_endpoint_deletion(endpoint_name)
            if success:
                # For delete, endpoint should no longer exist
                self.search_for_endpoint(endpoint_name)
                endpoint_gone = not self.check_if_endpoint_exists(endpoint_name)
                result["verified"] = endpoint_gone
                if not endpoint_gone:
                    result["error"] = (
                        f"Endpoint {endpoint_name} still exists after deletion\n" + message
                    )
                else:
                    # For cancel, endpoint should still exist
                    self.search_for_endpoint(endpoint_name)
                    endpoint_exists = self.check_if_endpoint_exists(endpoint_name)

                    # verified is true if delete is successful
                    result["verified"] = not endpoint_exists
                    result["success"] = False
                    result["error"] = message

            return result

        except Exception as e:
            result["error"] = f"Error performing endpoint delete action: {str(e)}"
            logging.warning(result["error"])
            return result

    def perform_endpoint_delete_flow(self, endpoint_name, should_cancel=False):
        """
        Complete flow for endpoint deletion or cancellation testing.
        This method combines preparation and action into a single call.

        Parameters:
        -----------
        endpoint_name: str
            The name of the endpoint to delete
        should_cancel: bool
            Whether to cancel the deletion (True) or confirm it (False)

        Returns:
        --------
        dict:
            Dictionary containing operation results:
            - 'success': bool - Whether the action was successful
            - 'verified': bool - Whether the verification was successful
            - 'error': str - Error message if any
        """
        action_type = "cancellation" if should_cancel else "deletion"
        logging.info(f"Starting endpoint {action_type} flow for {endpoint_name}")

        # Step 1: Prepare the endpoint
        prep_result = self.prepare_endpoint_for_action(endpoint_name)

        # If preparation failed and no endpoints found, return early
        if not prep_result["success"] and prep_result["initial_count"] == 0:
            logging.warning("No endpoints found, skipping delete action")
            return {
                "success": False,
                "verified": False,
                "error": "No endpoints found to delete",
            }

        # If endpoint not found but we should continue with delete action on the first endpoint
        if not prep_result["endpoint_exists"] and endpoint_name:
            logging.warning(
                f"Endpoint {endpoint_name} not found, will attempt to delete first available endpoint"
            )

        # Step 2: Perform the delete action (confirm or cancel)
        return self.perform_endpoint_delete_action(
            endpoint_name, should_cancel=should_cancel, prep_result=prep_result
        )

    def _locate_key_field(self):
        """
        Locate the key field textarea element using multiple strategies.
        Handles different name attribute values: 'secret.value.apiKey' and 'secret.value'

        Returns:
            Locator: The located key field element, or None if not found
        """
        # Strategy 1: Try secret.value.apiKey first (more specific)
        try:
            key_field = self.page.locator("textarea[name='secret.value.apiKey']")
            if key_field.count() > 0:
                logging.info("Located key field using name='secret.value.apiKey'")
                return key_field
        except Exception:
            pass

        # Strategy 2: Try secret.value (fallback)
        try:
            key_field = self.page.locator("textarea[name='secret.value']")
            if key_field.count() > 0:
                logging.info("Located key field using name='secret.value'")
                return key_field
        except Exception:
            pass

        logging.warning("Unable to locate key field using any strategy")
        return None

    def update_endpoint_key(
        self, endpoint_name, new_key, new_instance_id=None, cancel=False
    ):
        """
        Updates the key for a specified telemetry endpoint.
        This is a common function that handles the entire update key process.

        Args:
            endpoint_name (str): The name of the endpoint to update
            new_key (str): The new key value to set
            new_instance_id (str): The new instance ID for providers like Datadog

        Returns:
            bool: True if update was successful, False otherwise
        """
        # Search for the endpoint if provided
        self.search_for_endpoint(endpoint_name)
        self.wait_for_telemetry_endpoints_loading()

        # Open the actions menu and select update credential option
        self.click_endpoint_actions_button(endpoint_name)

        # Click the Update Key option in the actions menu
        self.page.get_by_text("Update Key").click()

        # Verify the Update Key modal is visible
        self.page.get_by_role("dialog").filter(has_text="Update Key").first.wait_for(
            state="visible"
        )

        # Check for the instance ID field first (if we need to update it)
        if new_instance_id:
            # Look for the instance ID input field
            instance_id_field = self.page.locator("input[name='secret.value.instanceId']")
            if instance_id_field.is_visible():
                instance_id_field.type(new_instance_id)
                logging.info(f"Entered new instance ID: {new_instance_id}")
            else:
                logging.warning("Instance ID field not found, skipping this update")

        # Now handle the key field
        if new_key:
            key_field = self._locate_key_field()
            if key_field and key_field.is_visible():
                key_field.clear()  # Clear existing content first
                key_field.type(new_key)
                logging.info(
                    f"Entered new key: {new_key[:10]}..."
                )  # Log first 10 chars for security
            else:
                logging.warning("Key field not found, skipping this update")

        # Click Save Changes button'
        if not cancel:
            self.page.get_by_role("button", name="Save Changes").click()
            logging.info("Clicked Save Changes button")

            toast_message = "Secrets successfully updated."
            # Verify the update was successful
            try:
                # self.page.wait_for_selector(
                #     f"text={toast_message}", state="visible", timeout=15000
                # )
                update_success, _ = self.wait_for_toast(toast_message)
                # update_success = True
            except Exception:
                update_success = False
                logging.error(f"Failed to update credentials for endpoint: {endpoint_name}")

            return update_success
        else:
            self.page.get_by_role("button", name="Cancel").click()
            logging.info("Cancelled the update")
            return True

    def filter_endpoint(self, filter_type, filter_value, initial_count=None):
        """
        Filter telemetry endpoints by Provider or Telemetry Type with verification.

        This method encapsulates the common filter pattern:
        1. Apply a specific filter (Provider or Telemetry Type)
        2. Verify the appropriate filter tag is visible
        3. Verify filtered results
        4. Clear the filter
        5. Verify count is restored to initial value

        Parameters:
        -----------
        filter_type: str
            The type of filter to apply ('Provider' or 'Telemetry Type')
        filter_value: str
            The specific value to filter by (e.g., 'Datadog' for Provider or 'Logs' for Telemetry Type)
        initial_count: int
            The initial count of items before filtering (if None, will be determined by the method)

        Returns:
        --------
        dict:
            Dictionary containing filter results:
            - 'success': bool - Whether the filter operation was successful
            - 'filtered_count': int - Number of items after filtering
            - 'error': str - Error message if any
        """
        result = {"success": True, "filtered_count": 0, "error": None}

        try:
            # Get initial count if not provided
            if initial_count is None:
                initial_count = self.get_telemetry_endpoints_count()
                logging.info(f"Initial count of endpoints: {initial_count}")

                # Skip test if no endpoints available
                if initial_count == 0:
                    result["success"] = False
                    result[
                        "error"
                    ] = "No telemetry endpoints found in the table, skipping test"
                    return result

            # Click Filter button
            self.page.get_by_role("button", name="Filter").click()
            logging.info(f"Clicked Filter button for {filter_type} filter test")

            # Select filter type option
            if filter_type == "Provider":
                self.page.get_by_role("menuitem", name="Provider").click()
                logging.info("Selected Provider option")

                # Select provider value
                if filter_value == "Datadog":
                    self.page.get_by_role("menuitemcheckbox", name="Datadog").check()
                elif filter_value == "Grafana Cloud":
                    self.page.get_by_role("menuitemcheckbox", name="Grafana Cloud").check()
                else:
                    result["success"] = False
                    result["error"] = f"Unsupported provider value: {filter_value}"
                    return result

            elif filter_type == "Telemetry Type":
                self.page.get_by_role("menuitem", name="Telemetry Type").click()
                logging.info("Selected Telemetry Type option")

                # Select telemetry type value
                if filter_value == "Logs":
                    self.page.get_by_role("menuitemcheckbox", name="Logs").check()
                elif filter_value == "Metrics":
                    self.page.get_by_role("menuitemcheckbox", name="Metrics").check()
                else:
                    result["success"] = False
                    result["error"] = f"Unsupported telemetry type value: {filter_value}"
                    return result
            else:
                result["success"] = False
                result["error"] = f"Unsupported filter type: {filter_type}"
                return result

            logging.info(f"Selected {filter_value} for {filter_type} filter")

            # Apply filter
            self.page.get_by_role("button", name="Apply Filter").click()
            logging.info(f"Applied {filter_type} filter")

            # Verify appropriate filter tag is visible
            filter_tag = self.page.get_by_text(f"{filter_type}:", exact=True).first
            if not filter_tag.is_visible():
                result["success"] = False
                result["error"] = f"{filter_type} filter tag is not visible"
                return result

            logging.info(f"Verified {filter_type} filter tag is visible")

            # Get filtered count
            filtered_count = self.get_telemetry_endpoints_count()
            result["filtered_count"] = filtered_count
            logging.info(f"Filtered count after {filter_type} filter: {filtered_count}")

            # Verify filtered results
            rows = self.get_telemetry_endpoints_rows()

            # Apply appropriate verification based on filter type
            if filter_type == "Provider":
                self.verify_filtered_results(rows, providers_list=[filter_value])
                logging.info(
                    f"Verified filtered results contain only {filter_value} provider"
                )
            elif filter_type == "Telemetry Type":
                self.verify_filtered_results(rows, telemetry_types_list=[filter_value])
                logging.info(
                    f"Verified filtered results contain only {filter_value} telemetry type"
                )

            # Clear filters
            clear_filters_button = self.page.get_by_role("button", name="Clear Filters")
            clear_filters_button.click()
            logging.info("Cleared filters")

            # Wait for filter to be cleared
            self.page.wait_for_timeout(1000)

            # Verify filters were cleared by checking count
            after_clear_count = self.get_telemetry_endpoints_count()
            if after_clear_count != initial_count:
                result["success"] = False
                result[
                    "error"
                ] = f"Expected {initial_count} endpoints after clearing filter, but got {after_clear_count}"
                return result

            logging.info("Verified filters were cleared successfully")

            return result

        except Exception as e:
            result["success"] = False
            result["error"] = f"Error testing {filter_type} filter: {str(e)}"
            return result

    def get_telemetry_endpoints_rows(self):
        """Get all rows from the Telemetry Endpoints table."""
        table = self.page.locator(self.locators.TELEMETRY_ENDPOINTS_TABLE)
        rows = table.locator("tbody tr")
        return rows

    def get_table_data(self, section_text="Telemetry Endpoints"):
        """Get all data from the table in a structured format."""
        result = {"headers": [], "data": [], "total_rows": 0, "error": None}

        try:
            # Find the table in the specified section
            if section_text == "Telemetry Endpoints":
                # Use existing method for Telemetry Endpoints table
                table = self.page.locator(self.locators.TELEMETRY_ENDPOINTS_TABLE)
                rows = self.get_telemetry_endpoints_rows()
            else:
                # For other tables
                section = self.page.locator(f"section:has-text('{section_text}')")
                table = section.locator("table").first
                rows = table.locator("tbody tr")

            # Wait for table to be visible
            table.wait_for(state="visible", timeout=5000)

            # Get headers
            header_cells = table.locator("thead th").all()
            result["headers"] = [cell.text_content().strip() for cell in header_cells]
            logging.info(f"Found headers: {result['headers']}")

            # Get all rows
            rows_list = rows.all()
            result["total_rows"] = len(rows_list)

            # Process each row
            for row in rows_list:
                row_data = {}
                cells = row.locator("td").all()

                # Process each cell
                for index, cell in enumerate(cells):
                    if index < len(result["headers"]):
                        header = result["headers"][index]
                        # Handle special cases like Telemetry Type that might have multiple values
                        if header == "Telemetry Type":
                            # Get all buttons in the cell which represent telemetry types
                            type_buttons = cell.locator("button").all()
                            value = [btn.text_content().strip() for btn in type_buttons]
                            if not value:  # If no buttons found, get raw text
                                value = cell.text_content().strip()
                        else:
                            value = cell.text_content().strip()
                        row_data[header] = value

                result["data"].append(row_data)

            logging.info(f"Successfully extracted data from {result['total_rows']} rows")
            return result

        except Exception as e:
            error_msg = f"Error getting table data: {str(e)}"
            logging.error(error_msg)
            result["error"] = error_msg
            return result

    def verify_page_navigation(self, target_page, section_text="Telemetry Endpoints"):
        """
        Verify page navigation by comparing table data before and after navigation.

        Parameters:
        -----------
        target_page: str
            The target page number to navigate to
        section_text: str
            The section containing the table (default: "Telemetry Endpoints")

        Returns:
        --------
        dict:
            Dictionary containing verification results:
            - 'success': bool - Whether navigation was successful
            - 'current_page': str - Current page after navigation
            - 'data_changed': bool - Whether table data changed
            - 'error': str - Error message if any
        """
        result = {
            "success": False,
            "current_page": None,
            "data_changed": False,
            "error": None,
        }

        try:
            # Get initial data
            original_data = self.get_table_data(section_text=section_text)
            if original_data.get("error"):
                result["error"] = f"Error getting original data: {original_data['error']}"
                return result

            # Navigate to target page
            self.enter_page_number(target_page, section_text=section_text)

            # Wait for page update
            self.page.wait_for_timeout(1000)

            # Get new data
            new_data = self.get_table_data(section_text=section_text)
            if new_data.get("error"):
                result["error"] = f"Error getting new data: {new_data['error']}"
                return result

            # Verify current page
            current_page = self.get_current_page(section_text=section_text)
            result["current_page"] = current_page

            if current_page != target_page:
                result[
                    "error"
                ] = f"Failed to navigate to page {target_page}, current page is {current_page}"
                return result

            # Compare data
            if (
                original_data["total_rows"] != new_data["total_rows"]
                or original_data["headers"] != new_data["headers"]
            ):
                result["error"] = "Table structure changed unexpectedly"
                return result

            # Verify data changed (we're on a different page)
            result["data_changed"] = original_data["data"] != new_data["data"]

            if not result["data_changed"]:
                result["error"] = "Data did not change after navigating to new page"
                return result

            # All verifications passed
            result["success"] = True
            logging.info(f"Successfully verified navigation to page {target_page}")

            return result

        except Exception as e:
            result["error"] = f"Error verifying page navigation: {str(e)}"
            logging.error(result["error"])
            return result

    def verify_filtered_results(self, rows, providers_list=None, telemetry_types_list=None):
        """
        Verify that filtered results meet the criteria.

        Args:
            rows: The table rows to verify
            providers_list: Optional list of expected providers to check against
            telemetry_types_list: Optional list of expected telemetry types to check against

        Returns:
            self: For method chaining

        Raises:
            AssertionError: If verification fails
        """
        row_count = rows.count()
        assert row_count > 0, "No rows found in the table after filtering"

        for i in range(row_count):
            row = rows.nth(i)
            provider_cell = row.locator("td").nth(self.locators.PROVIDER_CELL_INDEX)
            telemetry_type_cell = row.locator("td").nth(
                self.locators.TELEMETRY_TYPE_CELL_INDEX
            )

            provider = provider_cell.text_content()
            telemetry_type = telemetry_type_cell.text_content()

            # Verify provider if providers_list is provided
            if providers_list:
                assert (
                    provider in providers_list
                ), f"Provider '{provider}' is not in the expected list: {providers_list}"
            else:
                # Default check if no specific providers list provided
                assert provider in [
                    "Datadog",
                    "Grafana Cloud",
                    "Azure Monitor",
                    "Kratos Thanos",
                    "Prometheus Remote Write",
                    "ServiceNow",
                ], f"Provider '{provider}' is not Datadog or Grafana Cloud"

            # Verify telemetry type if telemetry_types_list is provided
            if telemetry_types_list:
                matching_type = False
                for telemetry_type_item in telemetry_types_list:
                    if telemetry_type_item in telemetry_type:
                        matching_type = True
                        break
                assert matching_type, f"Telemetry Type '{telemetry_type}' does not match any of the selected types: {telemetry_types_list}"
            else:
                # Default check if no specific telemetry types list provided
                assert (
                    "Logs" in telemetry_type
                    or "Metrics" in telemetry_type
                    or "Traces" in telemetry_type
                ), f"Telemetry Type '{telemetry_type}' does not contain Logs or Metrics"

        return self

    def get_pagination_combobox(self, index=0, section_text=None):
        """
        Locate the combobox element in pagination controls.
        This method handles elements marked as "Ignored" in the accessibility tree and reliably finds pagination controls.

        Parameters:
        -----------
        index: int
            The index of the pagination control to find (0 for first, 1 for second, etc.)
        section_text: str
            Optional, specify which section containing specific text to search in

        Returns:
        --------
        locator:
            The located combobox element, or None if not found

        Example:
        --------
        # Get the pagination combobox for the Telemetry Endpoints table
        combobox = page.get_pagination_combobox(1, "Telemetry Endpoints")
        """
        try:
            logging.info(
                f"Attempting to locate the {index+1}th pagination control combobox element"
            )

            # First determine which section to search in
            if section_text:
                logging.info(f"Searching in section containing text '{section_text}'")
                container = self.page.locator(f"section:has-text('{section_text}')")
            else:
                logging.info("Searching in the entire page")
                container = self.page

            # Method 1: Locate through pagination control containers
            try:
                # Find all pagination control containers with "items per page" text
                pagination_containers = container.locator(
                    "div:has-text('Show') >> div:has-text('Items')"
                ).all()
                if len(pagination_containers) > index:
                    combobox = (
                        pagination_containers[index].locator("[role='combobox']").first
                    )
                    if combobox.is_visible():
                        logging.info(
                            f"Successfully found the {index+1}th combobox through pagination container"
                        )
                        return combobox
            except Exception as e:
                logging.warning(f"Locating via pagination container failed: {str(e)}")

            # Method 2: Directly find rows containing "Show" and "Items" text, then find the combobox in between
            try:
                # Get all combobox elements
                comboboxes = container.locator("[role='combobox']").all()

                # Save comboboxes related to "Show" and "Items"
                pagination_comboboxes = []

                # Check if each combobox is in a Show...Items environment
                for combo in comboboxes:
                    # Check if combobox has Show text to its left
                    show_text = combo.locator(
                        "xpath=./preceding::*[contains(text(), 'Show')]"
                    ).first
                    # Check if combobox has Items text to its right
                    items_text = combo.locator(
                        "xpath=./following::*[contains(text(), 'Items')]"
                    ).first

                    if show_text.is_visible() and items_text.is_visible():
                        pagination_comboboxes.append(combo)

                if len(pagination_comboboxes) > index:
                    logging.info(
                        f"Successfully located the {index+1}th combobox via Show/Items text"
                    )
                    return pagination_comboboxes[index]
            except Exception as e:
                logging.warning(f"Locating via Show/Items text failed: {str(e)}")

            # Method 3: More direct method - based on context and positional relationship
            # This method doesn't depend on the visibility of "Show" and "Items" text, but on typical pagination control patterns at the bottom of tables
            try:
                # Find all tables
                tables = container.locator("table").all()
                if tables:
                    # For each table, look for combobox elements below it
                    for table in tables:
                        # Find pagination controls after the table
                        combo = table.locator(
                            "xpath=./following::*//div[@role='combobox']"
                        ).nth(index)
                        if combo.is_visible():
                            logging.info(
                                f"Successfully found the {index+1}th combobox through table positional relationship"
                            )
                            return combo
            except Exception as e:
                logging.warning(
                    f"Locating via table positional relationship failed: {str(e)}"
                )

            # Last fallback method: Directly get all comboboxes
            all_combos = container.locator("[role='combobox']").all()
            if len(all_combos) > index:
                logging.info(
                    f"Found the {index+1}th combobox through general method (may not be precise)"
                )
                return all_combos[index]

            logging.error(f"Could not find the {index+1}th pagination combobox")
            return None

        except Exception as e:
            logging.error(f"Error getting pagination combobox: {str(e)}")
            return None

    def set_show_item_num(self, items_count, table_index=0, section_text=None):
        """
        Set the "Show X Items" value for a table. This method finds pagination controls and changes the number of items displayed per page.
        It can handle elements marked as "Ignored" in the accessibility tree.

        Parameters:
        -----------
        items_count: str
            The number of items to display per page, such as "15", "25", etc.
        table_index: int
            Table index if there are multiple tables on the page (default 0, first table)
        section_text: str
            Optional, specify which section to search in

        Returns:
        --------
        dict:
            Dictionary containing operation results:
            - 'success': bool, whether the operation was successful
            - 'original_value': str, original value
            - 'new_value': str, new value
            - 'row_count': int, actual number of table rows displayed
            - 'row_count_matches': bool, whether row count matches selected value
            - 'error': str, error message (if any)
        """
        result = {
            "success": False,
            "original_value": None,
            "new_value": None,
            "row_count": 0,
            "row_count_matches": False,
            "error": None,
        }

        try:
            logging.info(f"Attempting to set table to display {items_count} items per page")

            # Determine which section to search in
            container = self.page
            if section_text:
                logging.info(f"Searching in section containing text '{section_text}'")
                container = self.page.locator(f"section:has-text('{section_text}')")

            # Find areas containing both "Show" and "Items" text
            show_items_areas = container.locator(
                "div:has-text('Show') >> div:has-text('Items')"
            ).all()

            if len(show_items_areas) <= table_index:
                result["error"] = f"Could not find pagination area with index {table_index}"
                logging.warning(result["error"])
                return result

            # Select pagination area with specified index
            pagination_area = show_items_areas[table_index]

            # Find combobox in that area
            combobox = pagination_area.locator("[role='combobox']").first

            if not combobox.is_visible():
                result["error"] = "Could not find combobox element in pagination area"
                logging.warning(result["error"])
                return result

            # Save original value
            original_value = combobox.text_content().strip()
            result["original_value"] = original_value
            logging.info(f"Found pagination combobox, current value: {original_value}")

            # If current value is already the target value, no need to change
            if original_value == items_count:
                logging.info(
                    f"Pagination combobox already set to {items_count}, no change needed"
                )
                result["success"] = True
                result["new_value"] = original_value

                # Even if no change is needed, verify the table row count
                self._verify_table_row_count(container, table_index, items_count, result)
                return result

            # Click combobox to open dropdown menu
            combobox.click()
            self.page.wait_for_timeout(1000)  # Wait for dropdown to appear

            # Select the specified option
            option = self.page.locator(f"[role='option']:has-text('{items_count}')").first
            if not option.is_visible():
                # If option not found, press ESC to close dropdown
                self.page.keyboard.press("Escape")
                result["error"] = f"Option '{items_count}' not found in dropdown menu"
                logging.warning(result["error"])
                return result

            option.click()
            self.page.wait_for_load_state("networkidle", timeout=5000)

            # Verify value has changed
            new_value = combobox.text_content().strip()
            result["new_value"] = new_value

            if items_count in new_value:
                result["success"] = True
                logging.info(
                    f"Successfully changed pagination setting from {original_value} to {new_value}"
                )

                # Verify the table row count
                self._verify_table_row_count(container, table_index, items_count, result)
            else:
                result["error"] = f"Change failed, expected {items_count}, got {new_value}"
                logging.warning(result["error"])

            return result

        except Exception as e:
            result["error"] = f"Error setting display items count: {str(e)}"
            logging.error(result["error"])
            return result

    def _verify_table_row_count(self, container, table_index, items_count, result):
        """
        Helper method to verify that the number of table rows displayed matches the selected value

        Parameters:
        -----------
        container: Locator
            The container to search in (page or section)
        table_index: int
            Table index if there are multiple tables
        items_count: str
            The expected number of items per page
        result: dict
            Results dictionary to update with verification results
        """
        try:
            # Find the table associated with this pagination control
            tables = container.locator("table").all()

            if len(tables) <= table_index:
                logging.warning(
                    f"Could not find table with index {table_index} for row count verification"
                )
                return

            # Select the table with specified index
            table = tables[table_index]

            # Count the visible rows in the table
            rows = table.locator("tbody tr").all()
            row_count = len([row for row in rows if row.is_visible()])

            # Update result with row count
            result["row_count"] = row_count

            # Check if row count matches the expected count
            expected_count = int(items_count)

            # If there are fewer items than the limit, that's okay
            total_entries_text = container.locator(
                "div:has-text('total entries')"
            ).first.text_content()
            try:
                # Try to extract total entry count like "1-5 of 7 total entries"
                total_match = re.search(r"of\s+(\d+)\s+total", total_entries_text)
                if total_match:
                    total_count = int(total_match.group(1))
                    if total_count < expected_count:
                        # If total entries less than items_count, row count should match total
                        result["row_count_matches"] = row_count == total_count
                        logging.info(
                            f"Table has {total_count} total entries (less than {expected_count})"
                        )
                        logging.info(f"Row count is {row_count}, expected {total_count}")
                        return
            except Exception as e:
                logging.warning(f"Error parsing total entries: {str(e)}")

            # Normal case: check if row count matches expected count
            result["row_count_matches"] = row_count == expected_count

            if result["row_count_matches"]:
                logging.info(
                    f"Table shows {row_count} rows as expected for {items_count} items per page"
                )
            else:
                logging.warning(
                    f"Table shows {row_count} rows, expected {expected_count} for {items_count} items per page"
                )

        except Exception as e:
            logging.warning(f"Error verifying table row count: {str(e)}")
            # Don't update result["success"] here - this is just additional verification

    def get_show_item_num(self, table_index=0, section_text=None):
        """
        Get the current "Show X Items" value for a table

        Parameters:
        -----------
        table_index: int
            Table index if there are multiple tables on the page (default 0, first table)
        section_text: str
            Optional, specify which section to search in

        Returns:
        --------
        str:
            Current items per page value, or None if not found
        """
        try:
            logging.info("Attempting to get current items per page value")

            # Determine which section to search in
            container = self.page
            if section_text:
                logging.info(f"Searching in section containing text '{section_text}'")
                container = self.page.locator(f"section:has-text('{section_text}')")

            # Find areas containing both "Show" and "Items" text
            show_items_areas = container.locator(
                "div:has-text('Show') >> div:has-text('Items')"
            ).all()

            if len(show_items_areas) <= table_index:
                logging.warning(f"Could not find pagination area with index {table_index}")
                return None

            # Select pagination area with specified index
            pagination_area = show_items_areas[table_index]

            # Find combobox in that area
            combobox = pagination_area.locator("[role='combobox']").first

            if not combobox.is_visible():
                logging.warning("Could not find combobox element in pagination area")
                return None

            value = combobox.text_content().strip()
            logging.info(f"Found pagination combobox, current value: {value}")
            return value

        except Exception as e:
            logging.error(f"Error getting display items count: {str(e)}")
            return None

    def get_available_pagination_options(self, table_index=0, section_text=None):
        """
        Get the available options in the pagination dropdown

        Parameters:
        -----------
        table_index: int
            Table index if there are multiple tables on the page (default 0, first table)
        section_text: str
            Optional, specify which section to search in

        Returns:
        --------
        list:
            List of available options in the dropdown, or empty list if not found
        """
        try:
            logging.info("Attempting to get available pagination options")

            # Determine which section to search in
            container = self.page
            if section_text:
                logging.info(f"Searching in section containing text '{section_text}'")
                container = self.page.locator(f"section:has-text('{section_text}')")

            # Find areas containing both "Show" and "Items" text
            show_items_areas = container.locator(
                "div:has-text('Show') >> div:has-text('Items')"
            ).all()

            if len(show_items_areas) <= table_index:
                logging.warning(f"Could not find pagination area with index {table_index}")
                return []

            # Select pagination area with specified index
            pagination_area = show_items_areas[table_index]

            # Find combobox in that area
            combobox = pagination_area.locator("[role='combobox']").first

            if not combobox.is_visible():
                logging.warning("Could not find combobox element in pagination area")
                return []

            # Click to open dropdown
            combobox.click()
            self.page.wait_for_timeout(500)  # Wait for dropdown to open

            # Look for all options in the dropdown
            options = []
            option_elements = self.page.locator("[role='option']").all()
            for option in option_elements:
                option_text = option.text_content().strip()
                if option_text:
                    options.append(option_text)

            # Close dropdown by pressing Escape
            self.page.keyboard.press("Escape")

            if options:
                logging.info(f"Found {len(options)} options in dropdown: {options}")
                return options

            # Fallback to common values
            logging.warning("Could not find options in dropdown, using common values")
            return ["5", "10", "15", "20", "25", "50", "100"]

        except Exception as e:
            logging.error(f"Error getting pagination options: {str(e)}")
            # Close dropdown if it might be open
            try:
                self.page.keyboard.press("Escape")
            except Exception:
                pass
            return []

    def get_current_page(self, section_text=None):
        """Get the current page number from the pagination control."""
        try:
            logging.info("Attempting to get current page number")
            self.wait_for_telemetry_endpoints_loading()
            section = self.page.locator(f"section:has-text('{section_text}')")
            page_num = section.get_by_role("spinbutton").input_value()
            logging.info(f"Found current page number: {page_num}")
            return page_num
        except Exception as e:
            logging.error(f"Error getting current page number: {str(e)}")
            return None

    def get_total_pages(self, section_text=None):
        """Get the total number of pages from the pagination control."""
        try:
            self.wait_for_telemetry_endpoints_loading()
            section = self.page.locator("section:has-text('Telemetry Endpoints')")
            pages = section.get_by_test_id("kui-text").filter(has_text="of")

            logging.info(f"Target page: {pages.text_content()}")

            match = re.search(r"of\s+(\d+)", pages.text_content())
            if match:
                total_pages = int(match.group(1))
                logging.info(f"Found total pages: {total_pages}")
                return total_pages

        except Exception as e:
            logging.error(f"Error getting total pages: {str(e)}")
            return 0

    def enter_page_number(self, page_num, section_text=None):
        """Enter a page number in the pagination spinbutton."""
        try:
            logging.info(f"Attempting to enter page number: {page_num}")

            # Determine which section to search in
            container = self.page
            if section_text:
                logging.info(f"Searching in section containing text '{section_text}'")
                container = self.page.locator(f"section:has-text('{section_text}')")

            container.get_by_role("spinbutton").fill(page_num)
            container.get_by_text("Go to").click()
        except Exception as e:
            logging.error(f"Error entering page number: {str(e)}")

    def is_page_button_active(self, page_num, section_text=None):
        """
        Check if a specific page button is highlighted as active

        Parameters:
        -----------
        page_num: str
            Page number to check
        section_text: str
            Optional, specify which section to search in

        Returns:
        --------
        bool:
            True if button is active, False otherwise
        """
        try:
            logging.info(f"Checking if page {page_num} button is active")

            # Determine which section to search in
            container = self.page
            if section_text:
                logging.info(f"Searching in section containing text '{section_text}'")
                container = self.page.locator(f"section:has-text('{section_text}')")

            # Find page button with the specified number
            page_button = container.get_by_role("button", name=f"{page_num}")

            if not page_button.is_visible():
                logging.warning(f"Could not find button for page {page_num}")
                return False

            class_name = page_button.get_attribute("class")
            if "isActive-true" in class_name:
                logging.info(f"Page {page_num} button is active (isActive-true class)")
                return True

        except Exception as e:
            logging.error(f"Error checking if page button is active: {str(e)}")
            return False

    def navigate_to_next_page(self, section_text="Telemetry Endpoints"):
        """Navigate to the next page if the next button is enabled.

        Parameters:
        -----------
        section_text: str
            The section containing the pagination (default: "Telemetry Endpoints")

        Returns:
        --------
        bool:
            True if navigation was successful, False if button was disabled or error occurred
        """
        try:
            pagination_num = (
                self.page.locator(f"//section[.//h4[text()='{section_text}']]")
                .get_by_test_id("kui-pagination-number-range")
                .first
            )
            next_btn = pagination_num.get_by_test_id("kui-button").filter(
                has=self.page.locator("svg[data-icon-name='shapes-chevron-right']")
            )

            if not next_btn.is_disabled():
                next_btn.click()
                self.page.wait_for_load_state("networkidle")
                return True
            return False
        except Exception as e:
            logging.error(f"Error navigating to next page: {str(e)}")
            return False

    def navigate_to_previous_page(self, section_text="Telemetry Endpoints"):
        """Navigate to the previous page if the previous button is enabled.

        Parameters:
        -----------
        section_text: str
            The section containing the pagination (default: "Telemetry Endpoints")

        Returns:
        --------
        bool:
            True if navigation was successful, False if button was disabled or error occurred
        """
        try:
            pagination_num = (
                self.page.locator(f"//section[.//h4[text()='{section_text}']]")
                .get_by_test_id("kui-pagination-number-range")
                .first
            )
            prev_btn = pagination_num.get_by_test_id("kui-button").filter(
                has=self.page.locator("svg[data-icon-name='shapes-chevron-left']")
            )

            if not prev_btn.is_disabled():
                prev_btn.click()
                self.page.wait_for_load_state("networkidle")
                return True
            return False
        except Exception as e:
            logging.error(f"Error navigating to previous page: {str(e)}")
            return False

    def navigate_to_first_page(self, section_text="Telemetry Endpoints"):
        """Navigate to the first page.

        Parameters:
        -----------
        section_text: str
            The section containing the pagination (default: "Telemetry Endpoints")

        Returns:
        --------
        bool:
            True if navigation was successful, False if error occurred
        """
        try:
            pagination_num = (
                self.page.locator(f"//section[.//h4[text()='{section_text}']]")
                .get_by_test_id("kui-pagination-number-range")
                .first
            )
            first_page_btn = pagination_num.get_by_role("button", name="1")
            first_page_btn.click()
            self.page.wait_for_load_state("networkidle")

            # Verify we're on first page
            return self.is_page_button_active("1", section_text)
        except Exception as e:
            logging.error(f"Error navigating to first page: {str(e)}")
            return False

    def navigate_to_last_page(self, section_text="Telemetry Endpoints"):
        """Navigate to the last page.

        Parameters:
        -----------
        section_text: str
            The section containing the pagination (default: "Telemetry Endpoints")

        Returns:
        --------
        bool:
            True if navigation was successful, False if error occurred
        """
        try:
            pagination_num = (
                self.page.locator(f"//section[.//h4[text()='{section_text}']]")
                .get_by_test_id("kui-pagination-number-range")
                .first
            )
            last_page_btn = pagination_num.get_by_role("button", name="Last")
            last_page_btn.click()
            self.page.wait_for_load_state("networkidle")

            # Verify we're on last page
            total_pages = self.get_total_pages(section_text)
            return self.is_page_button_active(str(total_pages), section_text)
        except Exception as e:
            logging.error(f"Error navigating to last page: {str(e)}")
            return False

    def verify_pagination_num_navigation(self, section_text="Telemetry Endpoints"):
        """Verify pagination navigation functionality by checking button states and data changes.

        Parameters:
        -----------
        section_text: str
            The section containing the pagination (default: "Telemetry Endpoints")

        Returns:
        --------
        dict:
            Dictionary containing verification results:
            - 'success': bool - Whether all verifications passed
            - 'total_pages': int - Total number of pages
            - 'error': str - Error message if any
        """
        result = {"success": False, "total_pages": 0, "error": None}

        try:
            # Wait for section to load
            self.wait_for_telemetry_endpoints_loading()

            # Get total pages
            total_pages = self.get_total_pages(section_text)
            result["total_pages"] = total_pages

            if total_pages < 2:
                result[
                    "error"
                ] = f"Not enough pages to test pagination (only {total_pages} pages)"
                return result

            # Store initial data
            initial_data = self.get_table_data(section_text)

            # Navigate to first page and verify
            if not self.navigate_to_first_page(section_text):
                result["error"] = "Failed to navigate to first page"
                return result

            # Navigate to page 3 if available
            if total_pages >= 3:
                self.enter_page_number("3", section_text)
                self.page.wait_for_load_state("networkidle")

                if not self.is_page_button_active("3", section_text):
                    result["error"] = "Failed to navigate to page 3"
                    return result

                # Verify data changed
                page_3_data = self.get_table_data(section_text)
                if page_3_data["data"] == initial_data["data"]:
                    result["error"] = "Data did not change after navigating to page 3"
                    return result

            # Navigate to last page and verify
            if not self.navigate_to_last_page(section_text):
                result["error"] = "Failed to navigate to last page"
                return result

            # Verify next/last buttons are disabled on last page
            pagination_num = (
                self.page.locator(f"//section[.//h4[text()='{section_text}']]")
                .get_by_test_id("kui-pagination-number-range")
                .first
            )
            next_btn = pagination_num.get_by_test_id("kui-button").filter(
                has=self.page.locator("svg[data-icon-name='shapes-chevron-right']")
            )
            last_btn = pagination_num.get_by_role("button", name="Last")

            if not next_btn.is_disabled() or not last_btn.is_disabled():
                result["error"] = "Next/Last buttons should be disabled on last page"
                return result

            result["success"] = True
            return result

        except Exception as e:
            result["error"] = f"Error verifying pagination navigation: {str(e)}"
            return result

    def verify_default_page_size(self, section_text="Telemetry Endpoints"):
        """Verify that the default page size is 5 items per page.

        Parameters:
        -----------
        section_text: str
            The section containing the pagination (default: "Telemetry Endpoints")

        Returns:
        --------
        bool:
            True if verification was successful, False otherwise
        """
        try:
            # Find the section containing the pagination
            section_locator = self.get_section_locator(section_text)

            # Find the pagination section within the main section
            pagination = section_locator.get_by_test_id("kui-pagination-page-size")

            # Get the select trigger containing "5"
            select_trigger = pagination.get_by_test_id("kui-select-trigger")

            # Verify the trigger is visible
            if not select_trigger.is_visible():
                logging.warning("Page size selector not visible")
                return False

            # Get and verify the text content
            value_text = select_trigger.get_by_text("5", exact=True)
            if not value_text.is_visible():
                logging.warning("Default page size '5' not visible")
                return False

            return True
        except Exception as e:
            logging.error(f"Error verifying default page size: {str(e)}")
            return False

    def change_page_size(self, new_size: str, section_text="Telemetry Endpoints"):
        """Change the number of items displayed per page.

        Parameters:
        -----------
        new_size: str
            The new page size to set (e.g., "10", "15", etc.)
        section_text: str
            The section containing the pagination (default: "Telemetry Endpoints")

        Returns:
        --------
        dict:
            Dictionary containing operation results:
            - 'success': bool - Whether the operation was successful
            - 'old_value': str - Previous page size
            - 'new_value': str - New page size
            - 'error': str - Error message if any
        """
        result = {"success": False, "old_value": None, "new_value": None, "error": None}

        try:
            # Find the section containing the pagination
            section_locator = self.get_section_locator(section_text)

            # Find the pagination section within the main section
            pagination = section_locator.get_by_test_id("kui-pagination-page-size")

            # Get the select trigger
            select_trigger = pagination.get_by_test_id("kui-select-trigger")

            # Get current value
            result["old_value"] = select_trigger.text_content().strip()

            # Click to open dropdown
            select_trigger.click()
            logging.info("Clicked combobox to open dropdown")

            # Wait for dropdown to appear
            self.page.wait_for_timeout(500)

            # Select new value
            option = self.page.get_by_role("option", name=new_size, exact=True)
            option.click()
            logging.info(f"Selected {new_size} items per page")

            # Wait for page to update
            self.page.wait_for_load_state("networkidle", timeout=5000)
            self.page.wait_for_timeout(1000)

            # Verify the change
            updated_rows = self.get_telemetry_endpoints_rows()
            updated_rows_count = updated_rows.count()

            if updated_rows_count <= int(new_size):
                result["success"] = True
                result["new_value"] = new_size
                logging.info(f"Successfully changed page size to {new_size}")
            else:
                result[
                    "error"
                ] = f"Row count ({updated_rows_count}) exceeds selected page size ({new_size})"

            return result

        except Exception as e:
            result["error"] = f"Error changing page size: {str(e)}"
            logging.error(result["error"])
            return result

    def verify_page_pagination(self, section_text="Telemetry Endpoints"):
        """Verify the functionality of changing page size and pagination.

        Parameters:
        -----------
        section_text: str
            The section containing the pagination (default: "Telemetry Endpoints")

        Returns:
        --------
        dict:
            Dictionary containing verification results:
            - 'success': bool - Whether all verifications passed
            - 'total_endpoints': int - Total number of endpoints
            - 'error': str - Error message if any
        """
        result = {"success": False, "total_endpoints": 0, "error": None}

        try:
            # Get total count of endpoints
            total_endpoints = self.get_telemetry_endpoints_count()
            result["total_endpoints"] = total_endpoints

            if total_endpoints < 6:
                result[
                    "error"
                ] = f"Not enough telemetry endpoints to test pagination (need at least 6, found {total_endpoints})"
                return result

            # Verify default page size
            if not self.verify_default_page_size(section_text):
                result["error"] = "Failed to verify default page size"
                return result

            # Change to 10 items per page
            change_result = self.change_page_size("10", section_text)
            if not change_result["success"]:
                result["error"] = f"Failed to change page size: {change_result['error']}"
                return result

            # Verify page navigation
            nav_result = self.verify_pagination_num_navigation(section_text)
            if not nav_result["success"]:
                result[
                    "error"
                ] = f"Failed to verify pagination navigation: {nav_result['error']}"
                return result

            result["success"] = True
            return result

        except Exception as e:
            result["error"] = f"Error verifying page size change: {str(e)}"
            logging.error(result["error"])
            return result

    def sort_telemetry_column(self, column_name):
        """
        Test sorting functionality for a specific column in the telemetry endpoints table.

        This method tests both ascending and descending sorting for the specified column:
        1. Clicks the column header to sort in ascending order
        2. Verifies the items are sorted correctly in ascending order
        3. Clicks the column header again to sort in descending order
        4. Verifies the items are sorted correctly in descending order

        Parameters:
        -----------
        column_name: str
            The name of the column to test sorting for ("Name", "Provider", or "Created On")

        Returns:
        --------
        dict:
            Dictionary containing test results:
            - 'success': bool - Whether the sorting test was successful
            - 'asc_verified': bool - Whether ascending sort verification passed
            - 'desc_verified': bool - Whether descending sort verification passed
            - 'error': str - Error message if any
        """
        result = {
            "success": True,
            "asc_verified": False,
            "desc_verified": False,
            "error": None,
        }

        try:
            # Test sorting ascending
            logging.info(f"Testing sorting by {column_name} column")
            self.click_telemetry_column_header(column_name)
            logging.info(f"Clicked {column_name} column header to sort (ascending)")

            # Verify ascending sorting
            sorted_rows = self.get_telemetry_endpoints_rows()

            # Call appropriate verification method based on column
            if column_name == "Name":
                self.verify_telemetry_endpoints_sorted_by_name(sorted_rows, ascending=True)
                result["asc_verified"] = True
                logging.info(
                    "Verified telemetry endpoints are sorted by name in ascending order"
                )
            elif column_name == "Provider":
                self.verify_telemetry_endpoints_sorted_by_provider(
                    sorted_rows, ascending=True
                )
                result["asc_verified"] = True
                logging.info(
                    "Verified telemetry endpoints are sorted by provider in ascending order"
                )
            elif column_name == "Created On":
                self.verify_telemetry_endpoints_sorted_by_date(sorted_rows, ascending=True)
                result["asc_verified"] = True
                logging.info(
                    "Verified telemetry endpoints are sorted by date in ascending order"
                )

            # Test sorting descending
            self.click_telemetry_column_header(column_name)
            logging.info(f"Clicked {column_name} column header again to sort (descending)")

            # Verify descending sorting
            sorted_rows = self.get_telemetry_endpoints_rows()

            # Call appropriate verification method based on column
            if column_name == "Name":
                self.verify_telemetry_endpoints_sorted_by_name(sorted_rows, ascending=False)
                result["desc_verified"] = True
                logging.info(
                    "Verified telemetry endpoints are sorted by name in descending order"
                )
            elif column_name == "Provider":
                self.verify_telemetry_endpoints_sorted_by_provider(
                    sorted_rows, ascending=False
                )
                result["desc_verified"] = True
                logging.info(
                    "Verified telemetry endpoints are sorted by provider in descending order"
                )
            elif column_name == "Created On":
                self.verify_telemetry_endpoints_sorted_by_date(sorted_rows, ascending=False)
                result["desc_verified"] = True
                logging.info(
                    "Verified telemetry endpoints are sorted by date in descending order"
                )

            return result

        except Exception as e:
            result["success"] = False
            result["error"] = f"Error testing sorting for {column_name} column: {str(e)}"
            self.page.screenshot(
                path=f"screenshots/sort_test_error_{column_name}_{time.time()}.png"
            )
            return result

    def click_telemetry_column_header(self, column_name: str):
        """Click a column header in the Telemetry Endpoints table to sort by that column."""
        section_locator = self.get_section_locator()
        section_locator.get_by_role("button", name=column_name).click()

    def verify_telemetry_endpoints_sorted_by_name(self, rows, ascending=True):
        """Verify that telemetry endpoints are sorted by name.

        Args:
            rows: The table rows to verify
            ascending: Whether to check for ascending (True) or descending (False) sort order

        Returns:
            self: For method chaining
        """
        # Get all names from the table
        names = []
        row_count = rows.count()
        for i in range(row_count):
            row = rows.nth(i)
            name_cell = row.locator("td").nth(self.locators.NAME_CELL_INDEX)
            name = name_cell.text_content().strip()
            names.append(name)

        # Skip check if fewer than 2 rows
        if len(names) < 2:
            return self

        # Document the current order for future reference
        logging.info(
            f"UI sorts names in {'ascending' if ascending else 'descending'} order as: {names}"
        )

        # For information only, compare with standard alphabetical sorting
        sorted_names = sorted(names)
        if not ascending:
            sorted_names = sorted(names, reverse=True)

        if names != sorted_names:
            logging.info(
                f"Note: Actual UI sorting differs from alphabetical: {sorted_names}"
            )

        return self

    def verify_telemetry_endpoints_sorted_by_provider(self, rows, ascending=True):
        """Verify that telemetry endpoints are sorted by provider.

        Args:
            rows: The table rows to verify
            ascending: Whether to check for ascending (True) or descending (False) sort order

        Returns:
            self: For method chaining
        """
        # Get all providers from the table
        providers = []
        row_count = rows.count()
        for i in range(row_count):
            row = rows.nth(i)
            provider_cell = row.locator("td").nth(self.locators.PROVIDER_CELL_INDEX)
            provider = provider_cell.text_content().strip()
            providers.append(provider)

        # Skip check if fewer than 2 rows
        if len(providers) < 2:
            return self

        # Document the current order for future reference
        logging.info(
            f"UI sorts providers in {'ascending' if ascending else 'descending'} order as: {providers}"
        )

        # For information only, compare with standard alphabetical sorting
        sorted_providers = sorted(providers)
        if not ascending:
            sorted_providers = sorted(providers, reverse=True)

        if providers != sorted_providers:
            logging.info(
                f"Note: Actual UI sorting differs from alphabetical: {sorted_providers}"
            )

        return self

    def verify_telemetry_endpoints_sorted_by_date(self, rows, ascending=True):
        """Verify that telemetry endpoints are sorted by date.

        Parameters:
        -----------
        rows: Locator
            The rows to verify
        ascending: bool
            True if sorting is ascending, False if descending

        Returns:
        --------
        bool:
            True if the endpoints are sorted correctly, False otherwise
        """
        dates = []

        # Get all dates from the rows
        for i in range(rows.count()):
            row = rows.nth(i)
            date_cell = row.locator("td").nth(2)  # Index 2 should be the date column
            date_text = date_cell.text_content().strip()
            dates.append(date_text)

        logging.info(
            f"UI sorts dates in {'ascending' if ascending else 'descending'} order as: {dates}"
        )

        # Clone the list and sort it for comparison
        sorted_dates = dates.copy()

        # For dates, we would need a more complex sorting logic
        # This is a simplified version assuming MM/DD/YYYY, HH:MM format
        if ascending:
            sorted_dates = sorted(sorted_dates)
        else:
            sorted_dates = sorted(sorted_dates, reverse=True)

        # Check if the actual order matches expected order
        matches = dates == sorted_dates

        if not matches:
            logging.info(
                f"Dates not sorted correctly. Expected: {sorted_dates}, Actual: {dates}"
            )

        return matches

    def get_section_locator(self, section_text="Telemetry Endpoints"):
        """Get a section locator by its header text.

        Parameters:
        -----------
        section_text: str
            The text of the section header to locate (default: "Telemetry Endpoints")

        Returns:
        --------
        Locator:
            The section locator
        """
        return self.page.locator(f"//section[.//h4[text()='{section_text}']]")

    def verify_manual_configuration_in_cluster_registration(self):
        """Verify the manual configuration during cluster registrator - come out manual instance configuration part
        when select Dynamic Instance Discovery.

        Parameters:
        -----------
        NA

        Returns:
        --------
        True or False
        """

        self.page.get_by_role("button", name="Register Cluster").click()
        self.page.get_by_role("button", name="Additional Settings").click()
        if self.page.get_by_text("Remove Dynamic Instance Discovery"):
            try:
                remove_btn = self.page.get_by_role(
                    "button", name="Remove Dynamic Instance Discovery", exact=True
                )
                remove_btn.click()
                try:
                    self.page.locator("//span[text()='Example Configuration']").wait_for(
                        state="visible", timeout=1000
                    )
                except Exception as e:
                    logging.info(e)
                    return False
                try:
                    self.page.locator("//button[text()='Apply Example']").wait_for(
                        state="visible", timeout=1000
                    )
                except Exception as e:
                    logging.info(e)
                    return False
                self.page.locator("//input[@role='combobox']").nth(-1).click()
                dynamic_instance_discovery = self.page.get_by_role(
                    "option", name="Dynamic Instance Discovery", exact=True
                )
                dynamic_instance_discovery.click()
                try:
                    self.page.locator("//span[text()='Example Configuration']").wait_for(
                        state="hidden", timeout=1000
                    )
                except Exception as e:
                    logging.info(e)
                    return False
                try:
                    self.page.locator("//button[text()='Apply Example']").wait_for(
                        state="hidden", timeout=1000
                    )
                except Exception as e:
                    logging.info(e)
                    return False
            except Exception as e:
                logging.info(e)
                return False
        else:
            if self.page.locator("//span[text()='Example Configuration']").wait_for(
                state="visible", timeout=1000
            ) and self.page.get_by_role("button", name="Apply Example"):
                return True
            else:
                return False

    def resume_and_rotate_not_ready_cluster_to_generate_new_byoc_key(
        self, cluster_name: str
    ):
        """Resume the not ready cluster and rotate service key for the cluster.

        Parameters:
        -----------
        cluster_name

        Returns:
        --------
        cluster byoc service key name
        """
        if not self.check_if_cluster_exist(cluster_name=cluster_name):
            self.create_new_cluster(cluster_name=cluster_name)
        try:
            self.page.locator(
                f"//table/tbody/tr/td[@title='{cluster_name}']/../td/div/button"
            ).click()
            self.page.locator("//div[text()='Resume Registration']").click()
            self.page.locator("//button[text()='Rotate Key']").wait_for(state="visible")
            self.page.locator("//button[text()='Rotate Key']").click()
            self.page.get_by_text(
                f"{cluster_name} cluster key successfully generated."
            ).wait_for(state="visible")
            self.page.get_by_text(f"{cluster_name} configuration saved.").wait_for(
                state="visible"
            )
            self.page.get_by_text(f"__byoc_{cluster_name}").wait_for(state="visible")
            cluster_service_key = self.page.locator("//strong").text_content()
            self.page.locator("//button[text()='Return to Settings']").click()
            return cluster_service_key
        except Exception as e:
            logging.info(e)
            return None

    def check_if_cluster_exist(self, cluster_name: str = None):
        self.page.locator(
            "//div[text()='Clusters']/../following-sibling::*//input[@placeholder='Search table']"
        ).fill(cluster_name)
        self.page.locator(
            "//div[text()='Clusters']/../following-sibling::*//button[@aria-label='Refresh']"
        ).click()
        if self.page.locator(f"//span[text()='{cluster_name}']").count() > 0:
            return True
        else:
            return False

    def create_new_cluster(self, cluster_name: str = None):
        self.page.get_by_role("button", name="Register Cluster").click()
        client_id = self.generate_nvssa_id()
        self.page.locator("//input[@placeholder='Enter a valid ID']").fill(client_id)
        self.page.locator("//input[@placeholder='Enter a name']").fill(cluster_name)
        self.page.locator(
            "//div[text()='Select option or enter value']/../div//input"
        ).fill("Default (same as cluster name)")

        combobox_locator = (
            self.page.locator("//label[text()='Compute Platform']/../..")
            .get_by_role("combobox")
            .first
        )
        base_page = BasePage(self.page)
        base_page.choose_dropdown("Select one", ["AWS"], dropdown_locater=combobox_locator)

        combobox_locator = (
            self.page.locator("//label[text()='Region']/../..")
            .get_by_role("combobox")
            .first
        )
        base_page = BasePage(self.page)
        base_page.choose_dropdown(
            "Select one", ["us-east-1"], dropdown_locater=combobox_locator
        )
        self.page.locator("//button[text()='Save and Continue']").is_enabled(timeout=1000)
        self.page.locator("//button[text()='Save and Continue']").click()
        self.page.locator("//button[text()='Return to Settings']").click()

    def delete_cluster(self, cluster_name: str):
        self.page.locator(
            "//div[text()='Clusters']/../following-sibling::*//input[@placeholder='Search table']"
        ).fill(cluster_name)
        self.page.locator(
            "//div[text()='Clusters']/../following-sibling::*//button[@aria-label='Refresh']"
        ).click()
        if self.page.locator(f"//span[text()='{cluster_name}']").count() > 0:
            self.page.locator(
                f"//table/tbody/tr/td[@title='{cluster_name}']/../td/div/button"
            ).click()
            self.page.locator("//div[text()='Delete Cluster']").click()
            self.page.locator("//button[text()='Delete']").click()
            self.page.get_by_text(f"{cluster_name} has been deleted.").wait_for(
                state="visible"
            )

    def generate_nvssa_id(self):
        if CURRENT_ENV == "staging":
            prefix = "nvssa-stg-"
        else:
            prefix = "nvssa-prd-"
        digits = "".join(random.choices("0123456789", k=43))
        return prefix + digits

    def cannot_resume_and_rotate_not_ready_cluster_to_generate_new_byoc_key_when_access_the_limitation_number(
        self, cluster_name: str
    ):
        """Resume the not ready cluster and rotate service key for the cluster.

        Parameters:
        -----------
        cluster_name

        Returns:
        --------
        cluster byoc service key name
        """
        try:
            self.page.locator(
                "//div[text()='Clusters']/../following-sibling::*//input[@placeholder='Search table']"
            ).fill(cluster_name)
            self.page.locator(
                "//div[text()='Clusters']/../following-sibling::*//button[@aria-label='Refresh']"
            ).click()
            self.page.get_by_text(cluster_name).wait_for(state="visible")
            self.page.locator(
                f"//table/tbody/tr/td[@title='{cluster_name}']/../td/div/button"
            ).click()
            self.page.locator("//div[text()='Resume Registration']").click()
            self.page.locator("//button[text()='Rotate Key']").wait_for(state="visible")
            self.page.locator("//button[text()='Rotate Key']").click()
            self.page.get_by_text(
                "Actor reached maximum allowed number of keys in service"
            ).wait_for(state="visible")
            return True
        except Exception as e:
            logging.info(e)
            return None
