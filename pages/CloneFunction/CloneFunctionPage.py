from cloudia.ui.base_page import BasePage
from component.clone_function_page_components import (
    CloneFunctionPageNavigationBar,
    CloneFunctionPageForm,
)

from playwright.sync_api import Page


class CloneFunctionPage(BasePage):
    """
    Contains all the elements and functionalities of the Functions List Page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object of the Playwright.
    """

    def __init__(self, page: Page):
        super().__init__(page)
        self.NavigationBar = CloneFunctionPageNavigationBar(page)
        self.Form = CloneFunctionPageForm(page)
