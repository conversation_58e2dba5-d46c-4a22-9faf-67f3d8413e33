from cloudia.ui.base_page import BasePage
from component.create_function_page_components import (
    CreateFunctionPageNavigationBar,
    CreateFunctionPageForm,
)
from component.function_list_page_components import (
    FunctionListPageSideBar as CreateFunctionPageSideBar,
)
from pages.Functions.FunctionsListPage import FunctionListPage

from config.consts import (
    SIDEBAR_CLOUDFUNCTIONS_ENTRY_CREATE_FUNCTION,
    SIDEBAR_APP_CLOUDFUNCTIONS,
)
from playwright.sync_api import Page


class CreateFunctionPage(BasePage):
    """
    Contains all the elements and functionalities of the Functions List Page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object of the Playwright.
    """

    def __init__(self, page: Page):
        super().__init__(
            page, SIDEBAR_APP_CLOUDFUNCTIONS, SIDEBAR_CLOUDFUNCTIONS_ENTRY_CREATE_FUNCTION
        )
        self.SideBar = CreateFunctionPageSideBar(page)
        self.NavigationBar = CreateFunctionPageNavigationBar(page)
        self.Form = CreateFunctionPageForm(page)

    def navigate_to_page(self):
        """Navigate to the create function page from function list page.

        parameters:
        -----------

        Returns:
        --------
        None
        """
        function_list_page = FunctionListPage(self.page)
        function_list_page.navigate_to_page()
        self.page.wait_for_timeout(3000)
        self.page.locator(function_list_page.SwitchBar.elements["MyFunctions"]).wait_for()
        self.page.locator(
            function_list_page.NavigationBar.elements["CreateFunction"]
        ).click()
        self.page.locator(self.Form.elements["CreateDeployFunctionBtn"]).wait_for()
