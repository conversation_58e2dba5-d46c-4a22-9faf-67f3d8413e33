import logging

from cloudia.ui.base_page import BasePage
from playwright.sync_api import Page
from config.elements import elements as total_elements
from config.consts import (
    SIDEBAR_APP_ORGANIZATION,
    SIDEBAR_ORGANIZATION_ENTRY_SERVICE_KEYS,
)


class ServiceKeyPage(BasePage):
    """The CoreService page with common method."""

    def __init__(self, page: Page):
        super().__init__(page, SIDEBAR_APP_ORGANIZATION)
        self.elements = total_elements.get("ServiceKey")

    serActionBtn = "//div[contains(@title, '__auto_cf_')]/following-sibling::div[contains(@class, '')]//button"
    serActionNormalBtn = "//div[@title='{0}']/following-sibling::div[contains(@class, '')]//button[@data-testid='kui-button']"
    deactivateBtn = "//div[contains(@title,'__auto_cf_')]/following-sibling::div//div[@data-testid='kui-switch']//button"
    rotateBtn = "//span[text()='Rotate Service Key']"
    deleteBtn = "//span[text()='Delete Service Key']/.."
    deleteSerKeyCon = "//button[normalize-space()='Delete Service Key']"
    updAuthorizationsBtn = "//span[text()='Update Authorizations']"
    proRotateKeyBtn = "//button[text()='Proceed With Key Rotation']"
    autoKeyName = "//*[starts-with(@title,  '__auto_cf_')]"
    autoKeyId = "//*[starts-with(@title,  '__auto_cf_')]/../div"
    byocKeyName = "//*[starts-with(@title,  '__byoc_')]"
    byocKeyBtn = "//div[contains(@title, '__byoc_')]/following-sibling::div[contains(@class, '')]//button[@data-testid='kui-button']"
    byocKeyDeactivateBtn = "//div[contains(@title, '__byoc_')]/following-sibling::div[contains(@class, '')]//button[@data-testid='kui-switch-root']"
    byocKeyDeactivateBtnafter = "//div[contains(@title, '__byoc_')]/following-sibling::div[contains(@class, '')]//button[@data-testid='kui-switch-root' and @data-state='unchecked']"
    normalKeyDeactivateBtn = "//div[@title='{0}']/following-sibling::div[contains(@class, '')]//button[@data-testid='kui-switch-root']"
    ormalKeyDeactivateBtnafter = "//div[@title='{0}']/following-sibling::div[contains(@class, '')]//button[@data-testid='kui-switch-root' and @data-state='unchecked']"
    winrotateBtn = "//button[text()='Rotate Service Key']"
    inputSerKey = "//div[text()='Select service']/..//input"
    service_key_scope_fillbox = (
        "//div[text()='Cloud Functions']/../../../../..//label[text()='Scope']/../..//input"
    )
    service_key_entity_type_fillbox = "//div[text()='Select entity type']/..//input"
    service_key_copy_button = "//button/*[@data-icon-name='common-copy-generic']"
    closePage = "//button[text()='Close']"
    next_page = "//div[@data-testid='paginationNavigation']/button[3]"
    last_page = "//div[@data-testid='paginationNavigation']/button[4]"
    contextMenu = "//div[@data-testid='kui-logo']/../..//div[@data-testid='kui-avatar'] | //span[text()='Welcome Guest']"
    setup = "//div[@role='menu']//div[text()='Setup']"
    key_id = "//div[@title='auto_service_key_0512023744']/following-sibling::div[contains(@class, '')]/../div"
    next_step = "//button [text()='Next Step']"
    confirmBtn = "//button [text()='Confirm']"
    closePage = "//button[text()='Close']"

    def navigate_to_page(self):
        logging.info("[STEP] Enter to Organization page...")
        self.navigation_to_organization()
        self._close_cookies_policy_dialog()
        self.page.wait_for_load_state()
        # base_page._close_notification_modal()
        logging.info("[STEP] Enter service keys entry...")
        super().navigate_to_page(
            SIDEBAR_APP_ORGANIZATION, SIDEBAR_ORGANIZATION_ENTRY_SERVICE_KEYS
        )
        self.page.wait_for_load_state()
        self.page.locator(self.elements["create_service_key_button"]).wait_for(
            state="visible"
        )

    def check_if_exist_service_key_entry_for_non_user_admin_user(self):
        logging.info("Step 1: Accessing Setup menu")
        self.page.locator(self.contextMenu).click()
        self.page.locator(self.setup).click()
        self.page.wait_for_load_state("load")
        if self.page.locator(self.elements["service_key_entry"]).count() > 0:
            return False
        else:
            return True

    def create_new_service_key_with_illegal_prefix_is_not_allowed(
        self, service_key_type: str
    ):
        logging.info(f"Creating a {service_key_type} ......")
        self.page.locator(self.elements["create_service_key_button"]).click()
        self.page.wait_for_load_state("load")

        # Fill key name start with input service key type
        logging.info(f"Filling key name: {service_key_type}")
        service_key_name_fillbox = "//label[text()='Name']/../..//input"
        if service_key_type == "auto_key":
            self.page.locator(service_key_name_fillbox).fill("__auto_cf_")
        elif service_key_type == "byoc":
            self.page.locator(service_key_name_fillbox).fill("__byoc_")
        else:
            logging.info("Not support in this method")
            return False
        try:
            self.page.locator(self.elements["invalid_key_name_warning"]).wait_for(
                state="visible"
            )
            return True
        except Exception as e:
            logging.info(e)
            return False

    def update_authorizations_service_key_with_illegal_prefix_is_not_allowed(
        self, service_key_type: str
    ):
        logging.info(f"Update authorizations for a {service_key_type} key......")
        if service_key_type == "auto_key":
            self.page.locator(self.serActionBtn).nth(-1).click()
        elif service_key_type == "byoc":
            self.page.locator(self.byocKeyBtn).nth(-1).click()
        else:
            logging.info("Not support in this method")
            return False
        self.page.locator(self.updAuthorizationsBtn).click()
        self.page.wait_for_timeout(1000)
        try:
            self.page.locator(
                self.elements["invalid_update_authorizations_warning"]
            ).wait_for(state="visible")
            return True
        except Exception as e:
            logging.info(e)
            return False

    def deactivate_auto_cf_within_key_prefix_is_not_allowed(self):
        logging.info("Deactivate for a __auto_cf key......")
        self.page.locator(self.deactivateBtn).hover()
        self.page.wait_for_timeout(1000)
        try:
            self.page.get_by_text(
                "This key is used to authenticate Cloud Functions and cannot be deactivated"
            ).wait_for(state="visible")
            return True
        except Exception as e:
            logging.info(e)
            return False

    def deactivate_normal_service_key(self, service_key: str):
        logging.info(f"Deactivate for {service_key} key......")
        if service_key == "byoc":
            try:
                self.page.locator(self.byocKeyDeactivateBtn).nth(0).click()
                self.page.locator(self.byocKeyDeactivateBtnafter).nth(0).wait_for(
                    state="visible"
                )
                self.page.locator(self.byocKeyDeactivateBtnafter).nth(0).click()
                self.page.locator(self.byocKeyDeactivateBtnafter).nth(0).wait_for(
                    state="hidden"
                )
                return True
            except Exception as e:
                logging.info(e)
                return False
        else:
            try:
                self.page.locator(self.normalKeyDeactivateBtn.format(service_key)).click()
                self.page.locator(self.ormalKeyDeactivateBtnafter).wait_for(state="visible")
                return True
            except Exception as e:
                logging.info(e)
                return False

    def rotate_auto_cf_within_key_prefix(self):
        logging.info("Rotate __auto_cf key......")
        key_name = self.page.locator(self.autoKeyName).get_attribute("title")
        self.page.locator(self.serActionBtn).nth(-1).click()
        self.page.locator(self.rotateBtn).click()
        self.page.wait_for_timeout(1000)
        try:
            self.page.locator(self.elements["rotate_service_key_warning"]).wait_for(
                state="visible"
            )
            self.page.locator(self.proRotateKeyBtn).click()
            self.page.get_by_text(f"{key_name} successfully rotated").wait_for(
                state="visible"
            )
            self.page.wait_for_timeout(1000)
            key_id = self.page.locator(self.autoKeyId).nth(0).get_attribute("title")
            key_rotated_time = self.page.locator(self.autoKeyId).nth(3).text_content()
            return [key_name, key_id, key_rotated_time]
        except Exception:
            return None

    def rotate_with_byoc_key_prefix(self):
        logging.info("Rotate __byoc_ key......")
        key_name = self.page.locator(self.byocKeyName).nth(0).get_attribute("title")
        try:
            self.page.locator(self.byocKeyBtn).nth(-1).click()
            self.page.locator(self.rotateBtn).click()
            self.page.wait_for_timeout(1000)
            self.page.locator(self.elements["rotate_byoc_service_key_warning"]).wait_for(
                state="visible"
            )
            return key_name, True
        except Exception as e:
            logging.info(e)
            return key_name, False

    def rotate_auto_cf_within_input_key_prefix(self, key_name: str):
        self.navigate_to_page()
        logging.info("Rotate input key......")
        try:
            if self.page.locator(self.last_page).is_enabled():
                self.page.locator(self.last_page).click()
        except Exception:
            logging.info("Cannot navigate to lst page")
        try:
            self.page.locator(self.serActionNormalBtn.format(key_name)).nth(-1).click()
            self.page.locator(self.rotateBtn).click()
            self.page.wait_for_timeout(1000)
            self.page.locator(self.next_step).click()
            self.page.wait_for_timeout(1000)
            self.page.locator(self.confirmBtn).click()
            self.page.wait_for_timeout(1000)
            self.page.get_by_text(f"{key_name} successfully rotated").wait_for(
                state="visible"
            )
            self.page.locator(self.closePage).click()
            self.page.wait_for_timeout(1000)
            return True
        except Exception:
            return None

    def delete_auto_cf_within_key_prefix_is_not_allow(self):
        logging.info("Delete__auto_cf key......")
        key_name = self.page.locator(self.autoKeyName).get_attribute("title")
        self.page.locator(self.serActionBtn).nth(-1).click()
        self.page.locator(self.deleteBtn).click()
        self.page.wait_for_timeout(1000)
        try:
            self.page.locator(self.elements["delete_service_key_warning"]).wait_for(
                state="visible"
            )
            self.page.locator(self.winrotateBtn).click()
            self.page.locator(self.elements["rotate_service_key_warning"]).wait_for(
                state="visible"
            )
            self.page.locator(self.proRotateKeyBtn).click()
            self.page.get_by_text(f"{key_name} successfully rotated").wait_for(
                state="visible"
            )
            self.page.wait_for_timeout(1000)
            key_rotated_time = self.page.locator(self.autoKeyId).nth(3).text_content()
            key_id = self.page.locator(self.autoKeyId).nth(0).get_attribute("title")
            return [key_name, key_id, key_rotated_time]
        except Exception:
            return None

    def generate_service_key(self, key_name: str, expiration: str):
        """Generate a service key in the deployments list page with predefined scopes and settings.

        This function:
        1. Fills key name and selects expiration time
        2. Selects service -> Cloud Functions
        3. Selects scope -> invoke function, list functions and queue details
        4. Selects entity type -> All Functions
        5. Completes the key creation process

        Parameters:
        -----------
        key_name: str
            Name for the service key
        expiration: str
            Expiration period for the key, e.g. "1 hour"

        Returns:
        --------
        api key: `str`
            The generated service API key
        """
        # Step 1: Create service key
        logging.info("Creating a service key by input service name ......")
        self.page.locator(self.elements["create_service_key_button"]).click()
        self.page.wait_for_load_state("load")

        # Step 2: Fill key name start with
        logging.info("Step 4: Filling key name......")
        service_key_name_fillbox = "//label[text()='Name']/../..//input"
        self.page.locator(service_key_name_fillbox).fill(key_name)

        # Step 3: Select expiration
        if expiration:
            modal_loactor = self.page.get_by_text("Create Service Key")
            combobox_locator = modal_loactor.locator("..").get_by_role("combobox").first
            base_page = BasePage(self.page)
            base_page.choose_dropdown("", [expiration], dropdown_locater=combobox_locator)

        # Step 4 Select Service -> Cloud Functions
        logging.info("Step 6: Selecting Service -> Cloud Functions")
        self.page.locator(self.inputSerKey).click()
        self.page.get_by_role("option", name="Cloud Functions", exact=True).click()

        # Step 5: Select scopes: invoke function, list functions, queue details
        logging.info("Step 7: Selecting scopes")
        scope_list = ["Invoke Function", "List Functions", "Queue Details"]
        for scope_value in scope_list:
            logging.info(f"Choose scope value as {scope_value}")
            self.page.locator(self.service_key_scope_fillbox).click()
            self.page.get_by_role("option", name=scope_value, exact=True).click()

        # Step 6: Select Entity Type -> All Functions
        logging.info("Step 8: Selecting Entity Type -> All Functions")
        self.page.locator(self.service_key_entity_type_fillbox).click()
        self.page.get_by_role("option", name="All Functions", exact=True).click()

        # Step 7: Click Next Step
        logging.info("Step 9: Clicking Next Step button")
        service_key_next_step_button = "//button[text()='Next Step']"
        self.page.locator(service_key_next_step_button).click()
        self.page.wait_for_load_state("load")

        # Step 8: Click Confirm
        logging.info("Step 10: Clicking Confirm button")
        service_key_confirm_button = "//button[text()='Confirm']"
        self.page.locator(service_key_confirm_button).click()
        self.page.wait_for_load_state("load")

        # Step 9: Copy the generated API key
        logging.info("Step 11: Copying the generated API key")
        self.page.wait_for_timeout(1000)
        key_id = self.page.locator(
            "//label[text()='ID']/following-sibling::p"
        ).text_content()
        self.page.locator(self.service_key_copy_button).click()
        copied_service_key_value = self.page.evaluate("navigator.clipboard.readText()")
        self.page.locator(self.closePage).click()
        return copied_service_key_value, key_id

    def delete_service_key(self, key_name: str) -> str:
        """Delete a key in the deployments list page.

        Returns: a service key
        --------
        None
        """
        # Step 1: Delete Service Keys
        try:
            if self.page.locator(self.last_page).is_enabled():
                self.page.locator(self.last_page).click()
            self.page.locator(self.serActionNormalBtn.format(key_name)).wait_for(
                state="visible"
            )
            self.page.locator(self.serActionNormalBtn.format(key_name)).click()
            self.page.locator(self.deleteBtn).click()
            self.page.wait_for_timeout(1000)
            self.page.locator(self.deleteSerKeyCon).click()
            self.page.wait_for_load_state("load")
            self.page.get_by_text(f"{key_name} successfully deleted").wait_for(
                state="visible"
            )
            return "Key has been deleted!"
        except Exception:
            logging.info("Delete key failed")

    def next_page(self) -> bool:
        """Go to the next page if available.

        Returns:
        --------
        `bool`
            True if the next page is available, otherwise False.
        """
        # if not self.page.locator(self.next_page).get_attribute("disabled"):
        if not self.page.get_by_text("caret-right disabled icon").wait_for(state="visible"):
            self.page.locator(self.next_page).click()
            self.page.wait_for_load_state()
            return True
        return False

    def actor_exceed_limitation_warning_when_generate_new_key(
        self, key_name: str, expiration: str
    ):
        try:
            # Step 1: Create service key
            logging.info("Creating a service key by input service name ......")
            self.page.locator(self.elements["create_service_key_button"]).click()
            self.page.wait_for_load_state("load")

            # Step 2: Fill key name start with
            logging.info("Step 4: Filling key name......")
            service_key_name_fillbox = "//label[text()='Name']/../..//input"
            self.page.locator(service_key_name_fillbox).fill(key_name)

            # Step 3: Select expiration
            if expiration:
                modal_loactor = self.page.get_by_text("Create Service Key")
                combobox_locator = modal_loactor.locator("..").get_by_role("combobox").first
                base_page = BasePage(self.page)
                base_page.choose_dropdown(
                    "", [expiration], dropdown_locater=combobox_locator
                )

            # Step 4 Select Service -> Cloud Functions
            logging.info("Step 6: Selecting Service -> Cloud Functions")
            self.page.locator(self.inputSerKey).click()
            self.page.get_by_role("option", name="Cloud Functions", exact=True).click()

            # Step 5: Select scopes: invoke function, list functions, queue details
            logging.info("Step 7: Selecting scopes")
            scope_list = ["Invoke Function", "List Functions", "Queue Details"]
            for scope_value in scope_list:
                logging.info(f"Choose scope value as {scope_value}")
                self.page.locator(self.service_key_scope_fillbox).click()
                self.page.get_by_role("option", name=scope_value, exact=True).click()

            # Step 6: Select Entity Type -> All Functions
            logging.info("Step 8: Selecting Entity Type -> All Functions")
            self.page.locator(self.service_key_entity_type_fillbox).click()
            self.page.get_by_role("option", name="All Functions", exact=True).click()

            # Step 7: Click Next Step
            logging.info("Step 9: Clicking Next Step button")
            service_key_next_step_button = "//button[text()='Next Step']"
            self.page.locator(service_key_next_step_button).click()
            self.page.wait_for_load_state("load")

            # Step 8: Click Confirm
            logging.info("Step 10: Clicking Confirm button")
            service_key_confirm_button = "//button[text()='Confirm']"
            self.page.locator(service_key_confirm_button).click()
            self.page.get_by_text(
                "Actor reached maximum allowed number of keys in service"
            ).wait_for(state="visible")
            return True
        except Exception as e:
            logging.info(e)
            return False
