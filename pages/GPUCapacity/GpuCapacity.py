import logging
import time
import re
import csv
import os
from cloudia.ui.base_page import BasePage
from playwright.sync_api import Page
from config.consts import (
    SIDEBAR_APP_CLOUDFUNCTIONS,
    SIDEBAR_CLOUDFUNCTIONS_ENTRY_GPU_CAPACITY,
    CURRENT_ENV,
)


class GpuCapacityPage(BasePage):
    """The CoreService page with common method."""

    GPU_PAGE_TITLE = "//h2[text()='GPU Capacity']"
    CHART_VIEW_BUTTON = "//button[text()='Chart View']"
    TABLE_VIEW_BUTTON = "//button[text()='Table View']"
    GPU_ALLOCATION = "(//*[local-name()='svg' and @class='recharts-surface'])[1]//*[local-name()='g' and contains(@class, 'xAxis')]//*[local-name()='g' and @class='recharts-layer recharts-cartesian-axis-tick']"
    GPU_ALLOCATION_CHART = "(//*[local-name()='svg' and @class='recharts-surface'])[1]"
    TOOLTIP_CONTENT = "//div[contains(@class, 'recharts-tooltip-wrapper')]"
    GPU_ALLOCATION_TOOLTIP_CONTENT = (
        "//div[contains(@class, 'recharts-tooltip-wrapper')]/div/span"
    )
    MIN_NUM = "//div[contains(@class, 'recharts-tooltip-wrapper')]//span[text()='Min']/../following-sibling::span"
    MAX_NUM = "//div[contains(@class, 'recharts-tooltip-wrapper')]//span[text()='Max']/../following-sibling::span"
    ACTIVE_NUM = "//div[contains(@class, 'recharts-tooltip-wrapper')]//span[text()='Active']/../following-sibling::span"
    ALLOCATED_NUM = "//div[contains(@class, 'recharts-tooltip-wrapper')]//span[text()='Allocated']/../following-sibling::span"
    AVAILABLE_NUM = "//div[contains(@class, 'recharts-tooltip-wrapper')]//span[text()='Available']/../following-sibling::span"
    CHART_TABLE = "//table//tbody"
    SEARCH_INPUT = "//input[@placeholder='Search function version table']"
    INSTANCE_NUM = "//button[text()='Filter']/following-sibling::span"
    FILTER_BUTTON = "//button[text()='Filter']"
    FILTER_GPU_TYPE = "//div[text()='GPU Type']"
    FILTER_INSTANCE_NAME = "//div[text()='Instance Name']"
    APPLY_FILTER_BUTTON = "//button[text()='Apply Filter']"
    FILTER_LIST = "//div[contains(@class,'overflow-y-auto')]"
    CLEAR_FILTER_BUTTON = "//button[text()='Clear Filters']"
    GPU_TYPE_SORT_BUTTON = "//button[text()='GPU Type']"
    REGION_SORT_BUTTON = "//button[text()='Region']"
    INSTANCE_NAME_SORT_BUTTON = "//button[text()='Instance Name']"
    FUNCTION_NAME_SORT_BUTTON = "//button[text()='Function Name']"
    ACTIVE_INSTANCES = "(//*[local-name()='svg' and @class='recharts-surface'])[last()]//*[local-name()='g' and contains(@class, 'xAxis')]//*[local-name()='g' and @class='recharts-layer recharts-cartesian-axis-tick']"
    ACTIVE_INSTANCES_CHART = (
        "(//*[local-name()='svg' and @class='recharts-surface'])[last()]"
    )
    ACTIVE_INSTANCES_TOOLTIP_CONTENT = (
        "//div[contains(@class, 'recharts-tooltip-wrapper')]//span[@align='right']"
    )
    SELECT_REGION_BUTTON = (
        "(//button//*[local-name()='svg' and @data-icon-name='shapes-chevron-down'])[1]"
    )
    REGION_OPTION = "//div[@role='presentation']//div[@role='option']"
    ACTIVE_INSTANCES_REGION_OPTION = "(//button//*[local-name()='svg' and @data-icon-name='shapes-chevron-down'])[last()]"
    DOWNLOAD_CSV_BUTTON = "//button[text()=' Download as CSV']"

    def __init__(self, page: Page):
        super().__init__(
            page, SIDEBAR_APP_CLOUDFUNCTIONS, SIDEBAR_CLOUDFUNCTIONS_ENTRY_GPU_CAPACITY
        )

    def navigate_to_gpu_capacity_page(self):
        logging.info("Enter to GPU Capacity page...")
        self.navigate_to_page()
        self.page.wait_for_selector(self.GPU_PAGE_TITLE, timeout=5000, state="visible")
        logging.info("Successfully navigated to GPU Capacity page")
        time.sleep(5)

    def get_gpu_allocation_num(self):
        return self.page.locator(self.GPU_ALLOCATION).count()

    def get_gpu_allocation_num_table_view(self):
        return self.page.locator(self.GPU_ALLOCATION).count()

    def check_tooltip_on_each_gpu_allocation(self):
        total_gpu_allocation_num = self.get_gpu_allocation_num()
        bounding_box = self.page.locator(self.GPU_ALLOCATION_CHART).bounding_box()
        svg_left = bounding_box["x"]
        svg_top = bounding_box["y"]
        svg_width = bounding_box["width"]
        svg_height = bounding_box["height"]

        viewbox = self.page.locator(self.GPU_ALLOCATION_CHART).get_attribute("viewBox")
        min_x, min_y, viewbox_width, viewbox_height = map(float, viewbox.split())
        y_center_svg = float(min_y) + float(viewbox_height) / 2
        y_center = svg_top + ((y_center_svg - min_y) / float(viewbox_height)) * float(
            svg_height
        )
        for i in range(total_gpu_allocation_num):
            x_location = (
                self.page.locator(self.GPU_ALLOCATION)
                .nth(i)
                .locator("line")
                .first.get_attribute("x1")
            )
            gpu_type = (
                self.page.locator(self.GPU_ALLOCATION)
                .nth(i)
                .locator("tspan")
                .all_text_contents()
            )
            gpu_type = " ".join(gpu_type)
            logging.info(f"GPU allocation {i} type: {gpu_type}")
            x_location = svg_left + (
                (float(x_location) - min_x) / float(viewbox_width)
            ) * float(svg_width)
            self.page.mouse.click(float(x_location), y_center)
            time.sleep(2)
            tooltip_gpu = self.page.locator(
                self.GPU_ALLOCATION_TOOLTIP_CONTENT
            ).text_content()
            logging.info(f"GPU allocation {i} tooltip: {tooltip_gpu}")
            if tooltip_gpu.replace(" ", "") != gpu_type.replace(" ", ""):
                logging.info(f"GPU allocation {i} tooltip type is not correct")
                return False
            if (
                not self.page.locator(self.TOOLTIP_CONTENT)
                .locator("span:text('Min')")
                .is_visible()
            ):
                logging.info(f"GPU allocation {i} tooltip min is not correct")
                return False
            if (
                not self.page.locator(self.TOOLTIP_CONTENT)
                .locator("span:text('Max')")
                .is_visible()
            ):
                logging.info(f"GPU allocation {i} tooltip max is not correct")
                return False
            if (
                not self.page.locator(self.TOOLTIP_CONTENT)
                .locator("span:text('Active')")
                .is_visible()
            ):
                logging.info(f"GPU allocation {i} tooltip active is not correct")
                return False
            if (
                not self.page.locator(self.TOOLTIP_CONTENT)
                .locator("span:text('Allocated')")
                .is_visible()
            ):
                logging.info(f"GPU allocation {i} tooltip allocated is not correct")
                return False
            if (
                not self.page.locator(self.TOOLTIP_CONTENT)
                .locator("span:text('Available')")
                .is_visible()
            ):
                logging.info(f"GPU allocation {i} tooltip available is not correct")
                return False
        return True

    def get_gpu_allocation_in_table_view(self):
        if not self.page.locator(self.TABLE_VIEW_BUTTON).is_visible():
            self.page.locator(self.TABLE_VIEW_BUTTON).click()
            time.sleep(2)
        res = []
        total_gpu_allocation_num = self.get_gpu_allocation_num()
        bounding_box = self.page.locator(self.GPU_ALLOCATION_CHART).bounding_box()
        svg_left = bounding_box["x"]
        svg_top = bounding_box["y"]
        svg_width = bounding_box["width"]
        svg_height = bounding_box["height"]

        viewbox = self.page.locator(self.GPU_ALLOCATION_CHART).get_attribute("viewBox")
        min_x, min_y, viewbox_width, viewbox_height = map(float, viewbox.split())
        y_center_svg = float(min_y) + float(viewbox_height) / 2
        y_center = svg_top + ((y_center_svg - min_y) / float(viewbox_height)) * float(
            svg_height
        )
        for i in range(total_gpu_allocation_num):
            x_location = (
                self.page.locator(self.GPU_ALLOCATION)
                .nth(i)
                .locator("line")
                .first.get_attribute("x1")
            )
            gpu_type = (
                self.page.locator(self.GPU_ALLOCATION)
                .nth(i)
                .locator("tspan")
                .all_text_contents()
            )
            gpu_type = " ".join(gpu_type)
            logging.info(f"GPU allocation {i} type: {gpu_type}")
            x_location = svg_left + (
                (float(x_location) - min_x) / float(viewbox_width)
            ) * float(svg_width)
            self.page.mouse.click(float(x_location), y_center)
            time.sleep(2)
            min_num = self.page.locator(self.MIN_NUM).text_content()
            max_num = self.page.locator(self.MAX_NUM).text_content()
            active_num = self.page.locator(self.ACTIVE_NUM).text_content()
            allocated_num = self.page.locator(self.ALLOCATED_NUM).text_content()
            available_num = self.page.locator(self.AVAILABLE_NUM).text_content()
            res.append(
                {
                    "Name": gpu_type,
                    "Min": min_num,
                    "Max": max_num,
                    "Active": active_num,
                    "Available": available_num,
                    "TotalAllocated": allocated_num,
                }
            )
        return res

    def get_gpu_allocation_in_chart_view(self):
        if not self.page.locator(self.CHART_VIEW_BUTTON).is_visible():
            self.page.locator(self.CHART_VIEW_BUTTON).click()
            time.sleep(2)
        res = []
        for row in self.page.locator(self.CHART_TABLE).first.locator("tr").all():
            gpu_type = row.locator("//td[@headers='data-view-header-gpu']").text_content()
            min_num = row.locator(
                "//td[@headers='data-view-header-minInstances']"
            ).text_content()
            max_num = row.locator(
                "//td[@headers='data-view-header-maxInstances']"
            ).text_content()
            active_num = row.locator(
                "//td[@headers='data-view-header-activeInstances']"
            ).text_content()
            allocated_num = row.locator(
                "//td[@headers='data-view-header-totalAllocated']"
            ).text_content()
            available_num = row.locator(
                "//td[@headers='data-view-header-availableInstances']"
            ).text_content()
            res.append(
                {
                    "Name": gpu_type,
                    "Min": min_num,
                    "Max": max_num,
                    "Active": active_num,
                    "Available": available_num,
                    "TotalAllocated": allocated_num,
                }
            )
        return res

    def search_instance_table(self, search_text: str):
        self.page.locator(self.SEARCH_INPUT).fill(search_text)
        time.sleep(2)
        full_text = self.page.locator(self.INSTANCE_NUM).text_content()
        instance_num = re.search(r"\d+", full_text)
        total_instance_num = int(instance_num.group())
        return total_instance_num

    def get_certain_function_version_data(self, func_version: str):
        res = []
        self.search_instance_table(func_version)
        full_text = self.page.locator(self.INSTANCE_NUM).text_content()
        instance_num = re.search(r"\d+", full_text)
        logging.info(f"Instance number: {instance_num}")
        for row in self.page.locator(self.CHART_TABLE).last.locator("tr").all():
            gpu_type = row.locator("//td[@headers='data-view-header-gpu']").text_content()
            region = row.locator("//td[@headers='data-view-header-regions']").text_content()
            instance_name = row.locator(
                "//td[@headers='data-view-header-instanceType']"
            ).text_content()
            res.append(
                {
                    "GPU Type": gpu_type,
                    "Region": region,
                    "Instance Name": instance_name,
                }
            )
        return res

    def check_filter(self, type="gpu_type"):
        full_text = self.page.locator(self.INSTANCE_NUM).text_content()
        instance_num = re.search(r"\d+", full_text)
        total_instance_num = int(instance_num.group())
        self.page.locator(self.FILTER_BUTTON).click()
        if type == "gpu_type":
            filter_type = self.FILTER_GPU_TYPE
        elif type == "instance_name":
            filter_type = self.FILTER_INSTANCE_NAME
        self.page.wait_for_selector(filter_type, state="visible")
        self.page.locator(filter_type).click()
        for row in (
            self.page.locator(self.FILTER_LIST)
            .locator("//div[@role='menuitemcheckbox']")
            .all()
        ):
            row.click()
            self.page.locator(self.APPLY_FILTER_BUTTON).click()
            full_text = self.page.locator(self.INSTANCE_NUM).text_content()
            instance_num = re.search(r"\d+", full_text)
            instance_num = int(instance_num.group())
            total_instance_num -= instance_num
            self.page.locator(self.CLEAR_FILTER_BUTTON).click()
            self.page.locator(self.FILTER_BUTTON).click()
            self.page.wait_for_selector(filter_type, state="visible")
            self.page.locator(filter_type).click()
        self.page.locator(self.APPLY_FILTER_BUTTON).click()
        if total_instance_num != 0:
            logging.info("Total instance number is not correct")
            return False
        return True

    def check_search(self, search_text: str, type="instance_name"):
        nums = self.search_instance_table(search_text)
        if nums <= 0:
            return False
        else:
            if type == "instance_name":
                check_locator = "//td[@headers='data-view-header-instanceType']"
            elif type == "function_name":
                check_locator = "//td[@headers='data-view-header-functionName']"
            elif type == "function_version":
                check_locator = "//td[@headers='data-view-header-functionVersionId']"
            for row in self.page.locator(self.CHART_TABLE).last.locator("tr").all():
                check_text = row.locator(check_locator).text_content()
                logging.info(f"Check text: {check_text}")
                if search_text not in check_text:
                    return False
            return True

    def check_sort(self, type="gpu_type"):
        if type == "gpu_type":
            sort_button = self.GPU_TYPE_SORT_BUTTON
            check_locator = "//td[@headers='data-view-header-gpu']"
        elif type == "instance_name":
            sort_button = self.INSTANCE_NAME_SORT_BUTTON
            check_locator = "//td[@headers='data-view-header-instanceType']"
        elif type == "function_name":
            sort_button = self.FUNCTION_NAME_SORT_BUTTON
            check_locator = "//td[@headers='data-view-header-functionName']"
        elif type == "region":
            sort_button = self.REGION_SORT_BUTTON
            check_locator = "//td[@headers='data-view-header-regions']"

        if (
            self.page.locator(sort_button)
            .locator("//*[local-name()='svg']")
            .get_attribute("data-icon-name")
            == "arrow-up-down"
        ):
            self.page.locator(sort_button).click()
        elif (
            self.page.locator(sort_button)
            .locator("//*[local-name()='svg']")
            .get_attribute("data-icon-name")
            == "arrow-down"
        ):
            self.page.locator(sort_button).click()
            self.page.locator(sort_button).click()
        check_row = self.page.locator(self.CHART_TABLE).last.locator("tr").all()
        check_list = []
        for row in check_row:
            check_list.append(row.locator(check_locator).text_content())

        def cleaned(s):
            return s.replace("_", "").replace(".", "")

        def natural_keys_for_num_and_char(text):
            text = cleaned(text)
            return [
                int(s) if s.isdigit() else s.lower()
                for s in re.split(r"(\d+)", text.strip())
                if s
            ]

        special_char = ["-", "_", "."]

        def natural_keys_for_special_char(text):
            pattern = "[" + re.escape("".join(special_char)) + "]"
            return [substr for substr in re.split(pattern, text) if substr]

        if type == "gpu_type":

            def location_key(x):
                val = x.strip()
                if val == "—":
                    return (2, "—")
                else:
                    return (0, natural_keys_for_num_and_char(val.lower()))
        elif type == "instance_name":

            def location_key(x):
                val = x.strip()
                if val == "—":
                    return (2, "—")
                else:
                    return (0, natural_keys_for_special_char(val.lower()))
        else:

            def location_key(x):
                val = x.strip()
                if val == "—":
                    return (2, "—")
                else:
                    return (0, cleaned(val.lower()))

        logging.info(f"expect ascending order, check list: {check_list}")

        # check it is in ascending order
        def is_ascending_with_dash_at_end(check_list):
            keys = [location_key(x) for x in check_list]
            logging.info(f"ascending keys: {keys}")
            return all(a <= b for a, b in zip(keys, keys[1:]))

        if not is_ascending_with_dash_at_end(check_list):
            return False

        self.page.locator(sort_button).click()
        if type == "region" and CURRENT_ENV == "staging":
            self.page.locator(sort_button).click()
        check_row = self.page.locator(self.CHART_TABLE).last.locator("tr").all()
        check_list = []
        for row in check_row:
            check_list.append(row.locator(check_locator).text_content())
        logging.info(f"expect descending order, check list: {check_list}")

        # check it is in descending order
        def is_descending_with_dash_at_end(check_list):
            keys = [location_key(x) for x in check_list]
            logging.info(f"descending keys: {keys}")
            return all(a >= b for a, b in zip(keys, keys[1:]))

        if not is_descending_with_dash_at_end(check_list):
            return False

        return True

    def get_active_instances_in_table_view(self):
        self.page.locator(self.ACTIVE_INSTANCES_CHART).scroll_into_view_if_needed()
        res = {}
        total_active_instances_num = self.page.locator(self.ACTIVE_INSTANCES).count()
        bounding_box = self.page.locator(self.ACTIVE_INSTANCES_CHART).bounding_box()
        svg_left = bounding_box["x"]
        svg_top = bounding_box["y"]
        svg_width = bounding_box["width"]
        svg_height = bounding_box["height"]

        viewbox = self.page.locator(self.ACTIVE_INSTANCES_CHART).get_attribute("viewBox")
        min_x, min_y, viewbox_width, viewbox_height = map(float, viewbox.split())
        y_center_svg = float(min_y) + float(viewbox_height) / 2
        y_center = svg_top + ((y_center_svg - min_y) / float(viewbox_height)) * float(
            svg_height
        )
        for i in range(total_active_instances_num):
            x_location = (
                self.page.locator(self.ACTIVE_INSTANCES)
                .nth(i)
                .locator("line")
                .first.get_attribute("x1")
            )
            gpu_type = (
                self.page.locator(self.ACTIVE_INSTANCES)
                .nth(i)
                .locator("tspan")
                .all_text_contents()
            )
            gpu_type = " ".join(gpu_type)
            logging.info(f"GPU allocation {i} type: {gpu_type}")
            x_location = svg_left + (
                (float(x_location) - min_x) / float(viewbox_width)
            ) * float(svg_width)
            self.page.mouse.click(float(x_location), y_center)
            time.sleep(2)
            instance_num = 0
            active_num = self.page.locator(self.ACTIVE_INSTANCES_TOOLTIP_CONTENT).all()
            logging.info(f"Active num: {active_num}")
            for num in active_num:
                num_text = num.text_content().strip()
                instance_num += int(num_text)
            res[gpu_type] = instance_num
        return res

    def check_gpu_allocation_regions(self, view="chart"):
        if view == "chart":
            total_gpu_allocation_num = self.get_gpu_allocation_num()
        elif view == "table":
            total_gpu_allocation_num = len(self.get_gpu_allocation_in_table_view())

        self.page.locator(self.SELECT_REGION_BUTTON).click()
        for row in self.page.locator(self.REGION_OPTION).all():
            row.click()
            time.sleep(2)
            if view == "chart":
                current_gpu_allocation_num = self.get_gpu_allocation_num()
            elif view == "table":
                current_gpu_allocation_num = len(self.get_gpu_allocation_in_table_view())
            if current_gpu_allocation_num > total_gpu_allocation_num:
                return False
            self.page.locator(self.SELECT_REGION_BUTTON).click()
        return True

    def check_active_instances_regions(self):
        total_gpu_allocation_num = len(self.get_active_instances_in_table_view())

        self.page.locator(self.ACTIVE_INSTANCES_REGION_OPTION).click()
        for row in self.page.locator(self.REGION_OPTION).all():
            row.click()
            time.sleep(2)
            current_gpu_allocation_num = len(self.get_active_instances_in_table_view())
            if current_gpu_allocation_num > total_gpu_allocation_num:
                return False
            self.page.locator(self.ACTIVE_INSTANCES_REGION_OPTION).click()
        return True

    def get_gpu_allocation_in_download_csv(self):
        with self.page.expect_download() as download_info:
            self.page.locator(self.DOWNLOAD_CSV_BUTTON).click()
        download = download_info.value
        file_name = download.suggested_filename
        directory = "/tmp"
        os.makedirs(directory, exist_ok=True)
        download_path = os.path.join(directory, file_name)
        download.save_as(download_path)
        logging.info(f"File name: {file_name}")
        data = []
        with open(download_path, "r", encoding="utf-8-sig") as f:
            reader = csv.reader(f)
            for row in reader:
                data.append(row)
        if os.path.exists(download_path):
            os.remove(download_path)
        download_dic = []
        for row in data[1:]:
            download_dic.append(
                {
                    "Name": row[0],
                    "Min": row[1],
                    "Max": row[2],
                    "Active": row[3],
                    "Available": row[4],
                    "TotalAllocated": row[5],
                }
            )

        return download_dic
