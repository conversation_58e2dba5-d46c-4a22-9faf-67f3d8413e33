from cloudia.ui.base_page import BasePage
from component.task_create_page_components import (
    TasksCreateNavigationBar,
    TasksCreatePageForm,
    TasksCreatePageMisc,
)
from pages.Tasks.TasksListPage import TasksListPage
from playwright.sync_api import Page


class TasksCreatePage(BasePage):
    """
    Contains all the elements and functionalities of the Task Create Page.

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object of the Playwright.
    """

    def __init__(self, page: Page):
        super().__init__(page)
        self.NavigationBar = TasksCreateNavigationBar(page)
        self.Form = TasksCreatePageForm(page)
        self.TasksCreatePageMisc = TasksCreatePageMisc(page)

    def navigate_to_page(self):
        """Navigate to the task create page from Tasks list page.

        parameters:
        -----------

        Returns:
        --------
        None
        """
        tasks_list_page = TasksListPage(self.page)
        tasks_list_page.navigate_to_page()
        self.page.wait_for_timeout(3000)
        self.page.locator(
            tasks_list_page.TasksListPageMisc.elements["TasksTitle"]
        ).wait_for()
        self.page.locator(
            tasks_list_page.TasksListPageMisc.elements["CreateTaskBtn"]
        ).click()
        self.page.locator(self.TasksCreatePageMisc.elements["CreateTaskBtn"]).wait_for()
