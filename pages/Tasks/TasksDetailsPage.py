# pages/Tasks/TasksDetailsPage.py

from playwright.sync_api import Page
import time
import logging
from cloudia.ui.base_page import BasePage
from typing import Optional
import re
from pages.Tasks.TasksListPage import TasksListPage
from datetime import datetime


class TasksDetailsPage(BasePage):
    """Page object for Tasks Details page"""

    def __init__(self, page: Page):
        """Initialize the Tasks Details page object"""
        super().__init__(page)
        self.page = page
        # self.form_elements = TasksDetailsPageFormElements()
        # self.misc_elements = TasksDetailsPageMiscElements()
        self.NameLabel = "//span[@data-testid='kui-text' and text()='Name']"
        self.NameVal = (
            "//span[@data-testid='kui-text' and text()='Name']/following-sibling::span[1]"
        )
        self.TaskIDLabel = "//span[@data-testid='kui-text' and text()='Task ID']"
        self.TaskIDVal = "//span[@data-testid='kui-text' and text()='Task ID']/following-sibling::span[1]"
        self.StatusLabel = "//span[@data-testid='kui-text' and text()='Status']"
        self.StatusVal = "//span[@data-testid='kui-text' and text()='Status']/following-sibling::div[1]//div[@data-testid='kui-tooltip-trigger']"
        self.TypeLabel = "//span[@data-testid='kui-text' and text()='Type']"
        self.TypeVal = (
            "//span[@data-testid='kui-text' and text()='Type']/following-sibling::span[1]"
        )
        self.DescriptionLabel = "//span[@data-testid='kui-text' and text()='Description']"
        self.DescriptionVal = "//span[@data-testid='kui-text' and text()='Description']/following-sibling::span[1]"
        self.TagsLabel = "//span[@data-testid='kui-text' and text()='Tags']"
        self.TagsVal = (
            "//span[@data-testid='kui-text' and text()='Tags']/following-sibling::span[1]"
        )
        self.CreatedLabel = "//span[@data-testid='kui-text' and text()='Created']"
        self.CreatedVal = "//span[@data-testid='kui-text' and text()='Created']/following-sibling::span[1]"
        self.LastUpdatedLabel = "//span[@data-testid='kui-text' and text()='Last Updated']"
        self.LastUpdatedVal = "//span[@data-testid='kui-text' and text()='Last Updated']/following-sibling::span[1]"
        self.CleanupDateLabel = "//span[@data-testid='kui-text' and text()='Cleanup Date']"
        self.CleanupDateVal = "//span[@data-testid='kui-text' and text()='Cleanup Date']/following-sibling::span[1]"
        self.Delay_info = "//span[@data-testid='kui-text' and text()='Please wait. It may take a few minutes for logs to load.']"
        self.RefreshBtn = "//button[@aria-label='Refresh']"
        self.ResultCountText = "//span[contains(normalize-space(.), 'Result')]"
        self.CancelTaskBtn = "//button[text()='Cancel Task']"
        self.ConfirmCancelBtn = "//div[@data-testid='kui-modal-action']//button[@data-testid='kui-button' and text()='Cancel']/following-sibling::button[normalize-space()='Cancel Task']"
        self.SucessCancelMsg = (
            "//span[@data-testid='kui-text' and contains(text(), 'succesfully canceled')]"
        )
        self.ThreeDotBtn = "//button[@data-testid='kui-menu-trigger']"
        self.DeleteTaskBtn = "//div[text()='Delete Task']"
        self.CloneTaskBtn = "//div[text()='Clone Task']"
        self.ConfirmDeleteBtn = "//div[@data-testid='kui-modal-action']//button[@data-testid='kui-button' and text()='Cancel']/following-sibling::button[normalize-space()='Delete Task']"
        self.SucessDeleteMsg = (
            "//span[@data-testid='kui-text' and contains(text(), 'succesfully deleted')]"
        )
        self.UpdateAPIKeyBtn = "//div[@role='menuitem' and text()='Update API Key']"
        self.APIKeyInput = "//input[@name='NGC_API_KEY']"
        self.SaveChangesBtn = (
            "//button[@data-testid='kui-button' and normalize-space()='Save Changes']"
        )
        self.SucessApiKeyMsg = "//span[@data-testid='kui-text' and contains(text(), 'succesfully updated secrets')]"

        self.ManageSecretsBtn = "//div[@role='menuitem' and text()='Manage Secrets']"
        self.AddSecretBtn = (
            "//button[@data-testid='kui-button' and normalize-space()='Add Another Secret']"
        )
        self.SecretNameInput = "//input[@type='text' and @name='secrets[{0}].name' and @placeholder='Enter a key' and @data-testid='kui-text-input-element']"
        self.SecretValueInput = "//textarea[@data-testid='kui-text-area-element' and @name='secrets[{0}].value' and @placeholder='Enter a value as a text string or JSON']"
        self.SaveSecretBtn = (
            "//button[@data-testid='kui-button' and normalize-space()='Save Secrets']"
        )
        self.SucessSecretMsg = "//span[@data-testid='kui-text' and contains(text(), 'succesfully updated secrets')]"
        self.ModelMountPointsVal = "//span[@data-testid='kui-text' and normalize-space(text())='Model Mount Points']/following-sibling::div[@data-testid='kui-flex'][1]/span[@data-testid='kui-text']"
        self.ResourceMountPointsVal = "//span[@data-testid='kui-text' and normalize-space(text())='Resource Mount Points']/following-sibling::div[@data-testid='kui-flex'][1]/span[@data-testid='kui-text']"

    def switch_to_windows_view(self) -> None:
        """Switch to windows view."""
        button = self.page.get_by_role("button", name="Switch To Window View")
        button.wait_for(state="visible", timeout=10000)
        button.click()
        self.page.wait_for_load_state("load")
        self.page.wait_for_load_state("networkidle")
        self.page.wait_for_timeout(2000)

    def get_log_entry_by_index_windows_view(self, index: int):
        """
        Get a specific log entry by its index
        Args:
            index: The index value of the log entry
        Returns:
            Locator: The locator for the log entry at the specified index
        """
        return self.page.locator(f'[data-testid="kui-box"][data-index="{index}"]')

    def get_log_text_by_index_windows_view(self, index: int) -> str:
        """
        Get the text content of a log entry at the specified index
        Args:
            index: The index value of the log entry
        Returns:
            str: The text content of the log entry
        """
        log_entry = self.get_log_entry_by_index_windows_view(index)
        return log_entry.locator('[data-testid="kui-text"]').text_content()

    def verify_log_entry_exists_windows_view(self, index: int) -> bool:
        """
        Verify if a log entry exists at the specified index
        Args:
            index: The index value of the log entry
        Returns:
            bool: Whether the log entry exists
        """
        log_entry = self.get_log_entry_by_index(index)
        return log_entry.is_visible()

    def get_all_visible_logs_windows_view(self) -> list[str]:
        """
        Get text content from all visible log entries
        Returns:
            list[str]: List of log entry texts
        """
        logs = []
        index = 0
        while True:
            log_entry = self.get_log_entry_by_index_windows_view(index)
            if not log_entry.is_visible():
                break
            logs.append(self.get_log_text_by_index_windows_view(index))
            index += 1
        return logs

    def get_task_status(self) -> str:
        """
        Get the current task status by finding the Status label and getting its value
        Returns:
            str: Current status of the task (e.g., "QUEUED", "COMPLETED", "RUNNING", etc.)
        Raises:
            Exception: If status cannot be found or retrieved
        """
        logging.info("Getting current task status")
        try:
            # First ensure the Status label exists and is visible
            status_label = self.page.get_by_text("Status", exact=True)
            status_label.wait_for(state="visible", timeout=10000)  # 10 second timeout

            # Get the status value which is the next div element
            status_value = status_label.locator("xpath=following::div[1]")
            status_value.wait_for(state="visible", timeout=10000)

            status_text = status_value.inner_text().strip()
            if not status_text:
                raise Exception("Status text is empty")

            logging.info(f"Current status: {status_text}")
            return status_text
        except Exception as e:
            logging.error(f"Failed to get task status: {str(e)}")
            raise Exception("Could not find or retrieve task status") from e

    def wait_for_task_status(
        self,
        expected_status: str,
        timeout_seconds: int = 1800,
        check_interval_seconds: int = 30,
    ) -> None:
        """
        Wait for task to reach expected status, checking at specified intervals

        Args:
            expected_status (str): The status to wait for (e.g. "COMPLETED")
            timeout_seconds (int): Maximum time to wait in secondes
            check_interval_seconds (int): How often to check status in seconds

        Raises:
            TimeoutError: If task doesn't reach expected status within timeout
            Exception: If task reaches a failed state
        """
        logging.info(f"Waiting for task status to become {expected_status}")
        end_time = time.time() + timeout_seconds

        while time.time() < end_time:
            try:
                current_status = self.get_task_status()
                if current_status == expected_status:
                    logging.info(f"Task reached {expected_status} status")
                    return
                logging.info(f"Current status: {current_status}")
                # Check for failure states
                if expected_status in ["COMPLETED", "RUNNING"]:
                    if current_status in ["TIMED OUT", "ERRORED", "CANCELED"]:
                        raise Exception(f"Task failed with status: {current_status}")

                time.sleep(check_interval_seconds)
                self.page.reload()
            except Exception as e:
                # If get_task_status fails (e.g. during page reload), continue waiting
                logging.warning(f"Failed to get task status: {str(e)}")
                time.sleep(check_interval_seconds)
                self.page.reload()

        raise TimeoutError(
            f"Task did not reach {expected_status} status within {timeout_seconds} seconds. Current status: {current_status}"
        )

    def navigate_to_results_tab(self) -> None:
        """
        Navigate to the Results tab and wait for content to load
        """
        logging.info("Navigating to Results tab")
        # Using get_by_role for better accessibility
        results_tab = self.page.get_by_role("tab", name="Results")
        results_tab.click()

        # Wait for results table to be visible
        self.page.get_by_role("table").wait_for(state="visible")
        logging.info("Results tab loaded successfully")

    def check_no_results_for_queued_tasks(self) -> None:
        """
        Check if no results are present for queued tasks
        """
        self.page.wait_for_timeout(10000)
        logging.info("Checking if no results are present for queued tasks")
        no_results = "//span[normalize-space()='No Results']"
        no_results_msg = "//span[contains(text(), 'Results may not yet be available')]"
        check_flag = True
        if not self.page.locator(no_results).is_visible():
            check_flag = False
        if not self.page.locator(no_results_msg).is_visible():
            check_flag = False
        return check_flag

    def get_results_count(self, output_result: str) -> int:
        """
        Get the count of result files that start with output_result

        Returns:
            int: Number of result files starting with output_result

        Raises:
            Exception: If results cannot be counted
        """
        logging.info(f"Counting result files starting with {output_result}")
        try:
            # Wait for results table to be visible
            results_table = self.page.get_by_role("table")
            results_table.wait_for(state="visible", timeout=10000)
            logging.info("Results table is visible")
            self.page.wait_for_selector(
                "//button[text()=' Refresh']", state="visible", timeout=10000
            )

            # Get all rows in the table
            table_rows = self.page.locator("table tbody tr").all()

            # Count rows that start with output_result
            count = 0
            for row in table_rows:
                name_cell = row.locator("td").first
                cell_text = name_cell.inner_text().strip()
                if cell_text.startswith(output_result):
                    count += 1

            logging.info(f"Found {count} result files starting with {output_result}")
            return count

        except Exception as e:
            logging.error(f"Failed to count result files: {str(e)}")
            raise Exception("Could not count result files") from e

    def verify_results_number(
        self, expected_number: str, output_result: str = "output_result"
    ) -> None:
        """
        Verify the number of results in the Results tab

        Args:
            expected_number (str): Expected number of results

        Raises:
            Exception: If verification fails or number doesn't match
        """
        logging.info(f"Verifying {expected_number} results in Results tab")
        try:
            # Click on Results tab if not already selected
            results_tab = self.page.get_by_role("tab", name="Results")
            results_tab.wait_for(state="visible", timeout=10000)
            if "selected" not in results_tab.get_attribute("aria-selected"):
                results_tab.click()
                self.page.wait_for_load_state("networkidle", timeout=10000)

            # Get the actual count of result files
            actual_count = self.get_results_count(output_result)
            # Verify the count matches expected number
            if actual_count != int(expected_number):
                raise Exception(
                    f"Results count mismatch. Expected: {expected_number}, Found: {actual_count}"
                )

            logging.info(f"Successfully verified {actual_count} results")

        except Exception as e:
            logging.error(f"Failed to verify results number: {str(e)}")
            raise Exception("Could not verify results number") from e

    def navigate_to_ngc_registry(self) -> None:
        """
        navigate to NGC registry

        Verifies:
            - Registry link is present and clickable

        Returns:
            Page: The new page object for the registry tab

        Raises:
            Exception: If verification fails or elements are not found
        """
        logging.info("Verifying results and navigating to NGC registry")
        try:
            # Wait for results table to be visible
            # results_table = self.page.get_by_role("table")
            # results_table.wait_for(state="visible", timeout=10000)
            # logging.info("Results table is visible")

            # Wait for registry link to be visible
            registry_link = self.page.get_by_role(
                "link", name="View in Private Registry"
            ).first
            registry_link.wait_for(state="visible", timeout=10000)
            logging.info("Registry link is visible")

            # Create a promise to wait for the new page
            with self.page.context.expect_page() as new_page_info:
                # Click the link - this will open in a new tab
                registry_link.click()

            # Get the new page from the promise
            new_page = new_page_info.value

            # Wait for the new page to load
            new_page.wait_for_load_state("networkidle")
            new_page.wait_for_load_state("domcontentloaded")

            # Make the new page active
            new_page.bring_to_front()

            # Update the page reference
            self.page = new_page

            logging.info("Navigated to NGC registry in new tab")

        except Exception as e:
            logging.error(f"Failed to verify results or navigate to registry: {str(e)}")
            raise Exception("Could not verify results or navigate to registry") from e

    def verify_ngc_registry_results(self, model_name: str, output_result: str) -> None:
        """
        Verify specific model and output result entries are present in NGC registry page

        Args:
            model_name (str): Name of the model to verify (e.g. "uiauto")
            output_result (str): Name of the output result to verify (e.g. "output_result_0")

        Raises:
            Exception: If entries are not found or verification fails
        """
        logging.info(
            f"Verifying registry entries - Model: {model_name}, Output: {output_result}"
        )
        try:
            # Wait for page load with longer timeout
            self.page.wait_for_load_state("networkidle", timeout=30000)
            self.page.wait_for_load_state("domcontentloaded", timeout=30000)
            self.page.wait_for_load_state("load", timeout=30000)

            # Wait a bit for any dynamic content to load
            self.page.wait_for_timeout(2000)

            # Try multiple strategies to find the model name
            found_model = False
            try:
                # Try as a heading first
                self.page.get_by_role("heading", name=model_name).wait_for(
                    state="visible", timeout=5000
                )
                found_model = True
            except Exception:
                try:
                    # Try as a text box
                    self.page.locator(
                        f'[role="textbox"]:has-text("{model_name}")'
                    ).wait_for(state="visible", timeout=5000)
                    found_model = True
                except Exception:
                    try:
                        # Try as generic text
                        self.page.get_by_text(model_name, exact=True).first.wait_for(
                            state="visible", timeout=5000
                        )
                        found_model = True
                    except Exception:
                        # Try as any element containing the text
                        self.page.locator(f':has-text("{model_name}")').first.wait_for(
                            state="visible", timeout=5000
                        )
                        found_model = True

            if not found_model:
                raise Exception(f"Could not find model name: {model_name}")
            logging.info(f"Found model: {model_name}")

            # Verify the File Browser tab is selected
            try:
                file_browser_tab = self.page.get_by_role("tab", name="File Browser")
                file_browser_tab.wait_for(state="visible", timeout=5000)
                if "selected" not in file_browser_tab.get_attribute("aria-selected"):
                    file_browser_tab.click()
                    # Wait for tab content to load
                    self.page.wait_for_load_state("networkidle", timeout=10000)
                    self.page.wait_for_timeout(1000)
                logging.info("File Browser tab is selected")
            except Exception as e:
                logging.error(f"Failed to select File Browser tab: {str(e)}")
                raise Exception("Could not access File Browser tab") from e

            # Verify the version combobox shows the correct output result
            try:
                # Wait for and verify the version combobox
                version_combobox = self.page.get_by_role("combobox")
                version_combobox.wait_for(state="visible", timeout=5000)
                version_value = version_combobox.inner_text().strip()

                # Check if version value starts with the provided output_result
                if not version_value.startswith(output_result):
                    raise Exception(
                        f"Version combobox value does not start with '{output_result}'. Found: {version_value}"
                    )
                logging.info(
                    f"Verified version combobox shows correct output result: {version_value}"
                )
            except Exception as e:
                logging.error(f"Failed to verify version combobox: {str(e)}")
                raise Exception("Could not verify output result version") from e

            # Verify sample_file_0.txt in the file browser
            logging.info("Verifying sample_file in file browser")
            try:
                # Wait for the file browser table to be visible
                table = self.page.locator("table")
                table.wait_for(state="visible", timeout=10000)
                logging.info("File browser table is visible")

                # Find the row containing sample_file_0.txt
                file_cell = self.page.get_by_role("cell").filter(has_text="sample_file")
                file_cell.wait_for(state="visible", timeout=5000)
                logging.info("Found sample_file in the table")

            except Exception as e:
                logging.error(f"Failed to verify sample_file: {str(e)}")
                raise Exception("Could not verify sample_file in file browser") from e

            # Final wait for page stability
            self.page.wait_for_load_state("networkidle", timeout=10000)
            self.page.wait_for_load_state("domcontentloaded", timeout=10000)

        except Exception as e:
            logging.error(f"Failed to verify registry entries: {str(e)}")
            # Try to ensure page is stable even if verification failed
            try:
                self.page.wait_for_load_state("networkidle", timeout=5000)
                self.page.wait_for_load_state("load", timeout=5000)
            except Exception as e:
                pass
            raise Exception("Could not verify model or output result in registry") from e

    def get_task_id_from_url(self) -> str:
        """
        Extract task ID from current URL

        Returns:
            str: Task ID extracted from URL
        """
        current_url = self.page.url
        task_id = current_url.split("/")[-1]
        logging.info(f"Extracted task ID: {task_id}")
        return task_id

    def navigate_to_page(self, task_id: str = None, task_name: str = None):
        """Navigate to the task detail page of the given task.

        parameters:
        -----------
        task_id: `str`
            the uuid of the task
        task_name: `str`
            the name of the task

        Returns:
        --------
        None
        """

        # if task_id:
        #     self.taskId = task_id
        # if task_name:
        #     self.taskName = task_name
        tl_pag = TasksListPage(self.page)
        tl_pag.navigate_to_page()
        self.page.wait_for_timeout(2000)
        tl_pag.navigate_to_task_details_page(task_id, task_name)

    def update_api_key(self, api_key: str) -> bool:
        """Update the API key for the task.

        parameters:
        -----------
        api_key: `str`
        """
        self.page.locator(self.ThreeDotBtn).click()
        self.page.locator(self.UpdateAPIKeyBtn).click()
        logging.info("Clicked Update API Key button")
        self.page.locator(self.APIKeyInput).fill(api_key)
        logging.info(f"Filled API Key: {api_key}")
        self.page.locator(self.SaveChangesBtn).click()
        logging.info("Clicked Save Changes button")
        self.page.wait_for_load_state("load")
        self.page.locator(self.SucessApiKeyMsg).wait_for(state="visible")
        if self.page.locator(self.SucessApiKeyMsg).is_visible():
            return True
        else:
            return False

    def manage_secrets(self, secrets_list: list[dict]) -> bool:
        """Manage the secrets for the task.

        parameters:
        -----------
            secrets_list: `list`
            The list of secrets to manage
        """
        self.page.locator(self.ThreeDotBtn).click()
        self.page.locator(self.ManageSecretsBtn).click()
        logging.info("Clicked Manage Secrets button")
        for i, secret in enumerate(secrets_list):
            self.page.locator(self.AddSecretBtn).click()
            logging.info("Clicked Add Secret button")
            self.page.locator(self.SecretNameInput.format(i + 1)).fill(secret["key"])
            logging.info(f"Filled Secret Name: {secret['key']}")
            self.page.locator(self.SecretValueInput.format(i + 1)).fill(secret["value"])
            logging.info(f"Filled Secret Value: {secret['value']}")
        self.page.locator(self.SaveSecretBtn).click()
        logging.info("Clicked Save Secret button")
        self.page.wait_for_load_state("load")
        self.page.locator(self.SucessSecretMsg).wait_for(state="visible")
        if self.page.locator(self.SucessSecretMsg).is_visible():
            return True
        else:
            return False

    def modify_secrets(self, secrets_list: list[dict]) -> bool:
        """Modify the secrets for the task.

        parameters:
        -----------
            secrets_list: `list`
            The list of secrets to manage
        """
        self.page.locator(self.ThreeDotBtn).click()
        self.page.locator(self.ManageSecretsBtn).click()
        logging.info("Clicked Manage Secrets button")
        self.page.wait_for_timeout(5000)
        self.nameinput = "//div[@data-testid='kui-text-input-root'][.//input[@value='{0}']]"

        self.keyinput = "//div[@data-testid='kui-text-input-root'][.//input[@value='{0}']]/following-sibling::div[@data-testid='kui-text-area-root']//textarea[@data-testid='kui-text-area-element']"

        for secret in secrets_list:
            for key, value in secret.items():
                self.nameinputlocate = self.nameinput.format(key)
                logging.info(f"nameinputlocate: {self.nameinputlocate}")
                if self.page.locator(self.nameinputlocate).is_visible():
                    logging.info(f"Found Secret Name: {key}")
                    self.keyinputlocate = self.page.locator(self.keyinput.format(key))
                    self.keyinputlocate.fill(value)
                    logging.info(f"Filled Secret: {key}")
                else:
                    logging.info(f"Secret Name: {key} not found")
                    return False
        self.page.locator(self.SaveSecretBtn).click()
        logging.info("Clicked Save Secret button")
        self.page.wait_for_load_state("load")
        self.page.locator(self.SucessSecretMsg).wait_for(state="visible")
        if self.page.locator(self.SucessSecretMsg).is_visible():
            return True
        else:
            return False

    def check_label_exists(self, label_list):
        """Check if the label exists in the task details page.

        parameters:
        -----------
        label_list: `list`
            The list of labels to check
            label_list = [
            "Name",
            "TaskID",
            "Status",
            "Type",
            "Description",
            "Tags",
            "Created",
            "LastUpdated",
            "CleanupDate",
        ]
        """
        for label in label_list:
            label_element = f"{label}Label"
            locator_temp = getattr(self, label_element)
            if not self.page.locator(locator_temp).is_visible():
                logging.info(f"Label {label} does not exist")
                return False
        return True

    def get_need_data(self, label_list):
        """Get the need data from the task details page.

        parameters:
        -----------
        label_list: `list`
            The list of labels to get the data
            label_list = [
            "Name",
            "TaskID",
            "Status",
            "Type",
            "Description",
            "Tags",
            "Created",
            "LastUpdated",
            "CleanupDate",
        ]
        """
        data = {}
        for label in label_list:
            label_val_element = f"{label}Val"
            locator_temp = getattr(self, label_val_element)
            if self.page.locator(locator_temp).is_visible():
                data[label] = self.page.locator(locator_temp).text_content()
        return data

    def get_task_details(self) -> dict:
        """
        Get all details from the task details section

        Returns:
            dict: Dictionary containing task details with keys:
                - Name
                - Task ID
                - Status
                - Type
                - Container
                - Description
        """
        logging.info("Getting task details")
        try:
            details = {}
            # Wait for the Basic Details section to be visible
            self.page.get_by_text("Basic Details", exact=True).wait_for(state="visible")

            # Get Name
            name_element = self.page.get_by_text("Name", exact=True)
            if name_element.is_visible():
                name_value = (
                    name_element.locator("xpath=following::div[1]").inner_text().strip()
                )
                details["Name"] = name_value

            # Get Task ID
            task_id_element = self.page.get_by_text("Task ID", exact=True)
            if task_id_element.is_visible():
                task_id_value = (
                    task_id_element.locator("xpath=following::div[1]").inner_text().strip()
                )
                details["Task ID"] = task_id_value

            # Get Status (using existing get_task_status method)
            try:
                details["Status"] = self.get_task_status()
            except Exception:
                logging.warning("Could not get status using get_task_status method")
                # Fallback to direct status text
                status_element = self.page.get_by_text("Status", exact=True)
                if status_element.is_visible():
                    status_value = (
                        status_element.locator("xpath=following::div[1]")
                        .inner_text()
                        .strip()
                    )
                    details["Status"] = status_value

            # Get Type
            type_element = self.page.get_by_text("Type", exact=True)
            if type_element.is_visible():
                type_value = (
                    type_element.locator("xpath=following::div[1]").inner_text().strip()
                )
                details["Type"] = type_value

            # Get Container
            container_element = self.page.get_by_text("Container", exact=True)
            if container_element.is_visible():
                container_value = (
                    container_element.locator("xpath=following::div[1]")
                    .inner_text()
                    .strip()
                )
                details["Container"] = container_value

            # Get Description
            description_element = self.page.get_by_text("Description", exact=True)
            if description_element.is_visible():
                description_value = (
                    description_element.locator("xpath=following::div[1]")
                    .inner_text()
                    .strip()
                )
                details["Description"] = description_value

            logging.info(f"Successfully parsed task details: {details}")
            return details
        except Exception as e:
            logging.error(f"Failed to get task details: {str(e)}")
            raise Exception("Could not parse task details") from e

    def get_field_value(self, field_name: str) -> Optional[str]:
        """Helper method to get field value from a label element.

        Args:
            field_name (str): The exact text of the label to find

        Returns:
            Optional[str]: The value following the label, or None if not found or value is "—"
        """
        try:
            # Find the label element
            label = self.page.get_by_text(field_name, exact=True)
            if not label:
                logging.info(f"Could not find label for {field_name}")
                return None
            elif label.count() > 1:
                logging.info(f"Found multiple labels for {field_name}")
                label = label.nth(1)

            # Get the next sibling element containing the value
            value_element = label.locator("xpath=following-sibling::*[1]")
            if not value_element:
                logging.info(f"Could not find value element for {field_name}")
                return None

            value = value_element.inner_text()
            return None if value == "—" else value
        except Exception as e:
            logging.info(f"Error getting value for {field_name}: {str(e)}")
            return None

    def get_task_config_details(self) -> dict:
        """Get the configuration details from the task details page.

        Returns:
            dict: A dictionary containing the configuration details with the following structure:
            {
                'Container': str,
                'Models': List[str],
                'Model Mount Points': List[str],
                'Resources': List[str],
                'Resource Mount Points': List[str],
                'Environment Variables': Dict[str, str],  # Dictionary of env var key-value pairs
                'Secrets': List[str],
                'Run Command Overrides': str,
                'GPU Type': str,
                'Instance Type': str,
                'Clusters': str,
                'Max Runtime Duration': str,
                'Max Queued Duration': str,
                'Termination Grace Period': str,
                'Result Handling': str,
                'Result Path': str
            }
        """
        # Wait for Configuration Details section to be visible
        config_section = self.page.get_by_role("heading", name="Configuration Details")
        config_section.wait_for(state="visible", timeout=10000)

        # Initialize config dictionary with default values
        config = {
            "Container": None,
            "Models": [],
            "Model Mount Points": [],
            "Resources": [],
            "Resource Mount Points": [],
            "Environment Variables": {},  # Dictionary for env vars
            "Secrets": [],
            "Run Command Overrides": None,
            "GPU Type": None,
            "Instance Type": None,
            "Clusters": None,
            "Max Runtime Duration": None,
            "Max Queued Duration": None,
            "Termination Grace Period": None,
            "Result Handling": None,
            "Result Path": None,
        }

        # Get Container value
        config["Container"] = self.get_field_value("Container")

        # Get Models list
        models_container = self.page.get_by_text("Models", exact=True).locator(
            "xpath=following-sibling::div[1]"
        )
        if models_container:
            models = models_container.get_by_text(
                re.compile(r"/v2/org/.*?/models/.*?/files")
            ).all()
            config["Models"] = [model.inner_text() for model in models]

        # Get Resources list
        resources_container = self.page.get_by_text("Resources", exact=True).locator(
            "xpath=following-sibling::div[1]"
        )
        if resources_container:
            resources = resources_container.get_by_text(
                re.compile(r"/v2/org/.*?/resources/.*?/files")
            ).all()
            config["Resources"] = [resource.inner_text() for resource in resources]

        # Get Model Mount Points list
        config["Model Mount Points"] = self.page.locator(
            self.ModelMountPointsVal
        ).all_text_contents()

        # Get Resource Mount Points list
        config["Resource Mount Points"] = self.page.locator(
            self.ResourceMountPointsVal
        ).all_text_contents()

        # Get Environment Variables
        env_vars_container = self.page.get_by_text(
            "Environment Variables", exact=True
        ).locator("xpath=following-sibling::div[1]")
        logging.info(f"Environment Variables container: {env_vars_container}")
        if env_vars_container:
            value = env_vars_container.inner_text()
            logging.info(f"Environment Variables value: {value}")
            for line in value.split("\n"):
                if line and ":" in line:
                    key, val = line.split(":", 1)
                    config["Environment Variables"][key.strip()] = val.strip()

        # Get Secrets
        secrets_container = self.page.get_by_text("Secrets", exact=True).locator(
            "xpath=following-sibling::div[1]"
        )
        if secrets_container:
            secrets = secrets_container.get_by_role("text").all()
            config["Secrets"] = [
                secret.inner_text() for secret in secrets if secret.inner_text() != "—"
            ]

        # Get remaining single-value fields
        single_value_fields = [
            "Run Command Overrides",
            "GPU Type",
            "Instance Type",
            "Clusters",
            "Max Runtime Duration",
            "Max Queued Duration",
            "Termination Grace Period",
            "Result Handling",
            "Result Path",
        ]

        for field in single_value_fields:
            config[field] = self.get_field_value(field)

        logging.info(f"Successfully parsed task configuration details: {config}")

        return config

    def get_task_config_details_helmchart(self) -> dict:
        """Get the configuration details from the task details page.

        Returns:
            dict: A dictionary containing the configuration details with the following structure:
            {
                'Helm Chart': str,
                'Models': List[str],
                'Resources': List[str],
                'Secrets': List[str],
                'GPU Type': str,
                'Instance Type': str,
                'Helm Chart Overrides': str,
                'Max Runtime Duration': str,
                'Max Queued Duration': str,
                'Termination Grace Period': str,
                'Result Handling': str,
                'Result Path': str
            }
        """
        # Wait for Configuration Details section to be visible
        config_section = self.page.get_by_role("heading", name="Configuration Details")
        config_section.wait_for(state="visible", timeout=10000)

        # Initialize config dictionary with default values
        config = {
            "Helm Chart": None,
            "Models": [],
            "Resources": [],
            "Secrets": [],
            "GPU Type": None,
            "Instance Type": None,
            "Helm Chart Overrides": None,
            "Max Runtime Duration": None,
            "Max Queued Duration": None,
            "Termination Grace Period": None,
            "Result Handling": None,
            "Result Path": None,
        }

        # Get Container value
        config["Helm Chart"] = self.get_field_value("Helm Chart")

        # Get Models list
        models_container = self.page.get_by_text("Models", exact=True).locator(
            "xpath=following-sibling::div[1]"
        )
        if models_container:
            models = models_container.get_by_text(
                re.compile(r"/v2/org/.*?/models/.*?/files")
            ).all()
            config["Models"] = [model.inner_text() for model in models]

        # Get Resources list
        resources_container = self.page.get_by_text("Resources", exact=True).locator(
            "xpath=following-sibling::div[1]"
        )
        if resources_container:
            resources = resources_container.get_by_text(
                re.compile(r"/v2/org/.*?/resources/.*?/files")
            ).all()
            config["Resources"] = [resource.inner_text() for resource in resources]

        # Get Secrets
        secrets_container = self.page.get_by_text("Secrets", exact=True).locator(
            "xpath=following-sibling::div[1]"
        )
        if secrets_container:
            secrets = secrets_container.get_by_role("text").all()
            config["Secrets"] = [
                secret.inner_text() for secret in secrets if secret.inner_text() != "—"
            ]

        # Get remaining single-value fields
        single_value_fields = [
            "GPU Type",
            "Instance Type",
            "Helm Chart Overrides",
            "Max Runtime Duration",
            "Max Queued Duration",
            "Termination Grace Period",
            "Result Handling",
            "Result Path",
        ]

        for field in single_value_fields:
            config[field] = self.get_field_value(field)

        logging.info(f"Successfully parsed task configuration details: {config}")

        return config

    def navigate_to_logs_tab(self) -> None:
        """
        Navigate to the Logs tab and wait for content to load
        """
        logging.info("Navigating to Logs tab")
        # Using get_by_role for better accessibility
        logs_tab = self.page.get_by_role("tab", name="Logs")
        logs_tab.click()

        # Wait for Logs to be visible
        self.check_delay_info_visible()
        self.check_refresh_btn_visible()
        logging.info("Logs tab loaded successfully")

    def check_delay_info_visible(self):
        delay_info_element = self.page.locator(self.Delay_info)
        if delay_info_element.is_visible():
            return True
        else:
            return False

    def check_refresh_btn_visible(self):
        logging.info("Checking refresh btn visible...")
        self.page.locator(self.RefreshBtn).wait_for(state="visible", timeout=120000)
        self.page.wait_for_load_state(state="load")
        refresh_btn_element = self.page.locator(self.RefreshBtn)
        if refresh_btn_element.is_visible():
            return True
        else:
            return False

    def get_result_count(self):
        result_count_element = self.page.locator(self.ResultCountText).first
        if result_count_element.is_visible():
            count = int(result_count_element.text_content().split()[0])
            logging.info(f"Result count: {count}")
            return count
        else:
            return -1

    def check_results_tab_visible(self):
        results_tab = self.page.get_by_role("tab", name="Results")
        if results_tab.is_visible():
            return True
        else:
            return False

    def cancel_task(
        self,
    ) -> Page:
        """Perform action to cancel the current task."""
        self.page.locator(self.CancelTaskBtn).click()
        self.page.wait_for_load_state("load")
        self.page.locator(self.ConfirmCancelBtn).click()
        self.page.locator(self.SucessCancelMsg).wait_for(state="visible")
        if self.page.locator(self.SucessCancelMsg).is_visible():
            return True
        else:
            return False

    def delete_task(
        self,
    ) -> Page:
        """Perform action to delete the current task."""
        self.page.locator(self.ThreeDotBtn).click()
        self.page.wait_for_load_state("load")
        self.page.locator(self.DeleteTaskBtn).click()
        self.page.wait_for_load_state("load")
        self.page.locator(self.ConfirmDeleteBtn).click()
        self.page.locator(self.SucessDeleteMsg).wait_for(state="visible")
        if self.page.locator(self.SucessDeleteMsg).is_visible():
            return True
        else:
            return False

    def click_clone_task(
        self,
    ) -> Page:
        """Perform action to delete the current task."""
        self.page.locator(self.ThreeDotBtn).click()
        self.page.wait_for_load_state("load")
        self.page.locator(self.CloneTaskBtn).click()
        self.page.wait_for_load_state("load")

    def check_logs_in_windows_view(self) -> dict:
        """
        Check logs in windows view. Returns a dictionary containing:
        - has_logs: bool - True if logs are found
        - log_count: int - Number of logs found (0 if no logs)
        - no_logs_message: bool - True if "No logs" message is visible

        Returns:
            dict: Dictionary containing log check results
        """
        logging.info("Checking logs in windows view")
        result = {"has_logs": False, "log_count": 0, "no_logs_message": False}

        try:
            self.page.get_by_text("No logs found.").wait_for(state="visible", timeout=10000)
            result["no_logs_message"] = True
            result["has_logs"] = False
            result["log_count"] = 0
            return result
        except Exception as e:
            logging.info(f"Locating 'No logs found.' message failed: {str(e)}")

        try:  # Get all visible logs
            logs = self.get_all_visible_logs_windows_view()
            result["log_count"] = len(logs)
            result["has_logs"] = len(logs) > 0

            logging.info(f"Log check results: {result}")
            return result

        except Exception as e:
            logging.error(f"Error checking logs in windows view: {str(e)}")
            return result

    def get_tooltip_text_for_task_status(self) -> str:
        status_text = self.page.get_by_test_id("kui-app-shell-body").get_by_test_id(
            "kui-tooltip-trigger"
        )
        status_text.hover()
        tooltip = self.page.get_by_test_id("kui-tooltip-content")
        logging.info(tooltip.text_content())
        return tooltip.text_content()

    def verify_details_between_ui_and_api(self, expected_details):
        config = self.get_task_config_details_helmchart()
        details = self.get_need_data(
            label_list=[
                "Name",
                "TaskID",
                "Status",
                "Type",
                "Description",
                "Tags",
                "Created",
                "LastUpdated",
                "CleanupDate",
            ]
        )
        details.update(config)
        for item in details.keys():
            if item == "Name":
                assert details["Name"] == expected_details["name"]
            elif item == "Helm Chart":
                assert details["Helm Chart"] == expected_details["helmChart"]
            elif item == "TaskID":
                assert details["TaskID"] == expected_details["id"]
            # elif item == "Status":
            #     assert details["Status"] == expected_details["status"]
            elif item == "GPU Type":
                assert details["GPU Type"] == expected_details["gpuSpecification"]["gpu"]
            elif item == "Instance Type":
                assert (
                    details["Instance Type"]
                    == expected_details["gpuSpecification"]["instanceType"]
                )
            elif item == "Max Runtime Duration":
                assert str(details["Max Runtime Duration"].split(" ")[0]) == str(
                    expected_details["maxRuntimeDuration"][2:-1]
                )
            elif item == "Max Queued Duration":
                assert str(details["Max Queued Duration"].split(" ")[0]) == str(
                    expected_details["maxQueuedDuration"][2:-1]
                )
            elif item == "Termination Grace Period":
                assert str(details["Termination Grace Period"].split(" ")[0]) == str(
                    expected_details["terminationGracePeriodDuration"][2:-1]
                )
            elif item == "Models":
                expected_details_models = [
                    model["uri"] for model in expected_details["models"]
                ]
                assert len(expected_details_models) == len(details["Models"])
                for detail_model in details["Models"]:
                    assert detail_model in expected_details_models
            elif item == "Resources":
                expected_details_resources = [
                    resource["uri"] for resource in expected_details["resources"]
                ]
                assert len(expected_details_resources) == len(details["Resources"])
                for detail_resource in details["Resources"]:
                    assert detail_resource in expected_details_resources
            elif item == "Result Path" and expected_details.get("resultsLocation"):
                assert details["Result Path"] == expected_details["resultsLocation"]
            elif item == "Created":
                assert (
                    self.compare_datetime_strings(
                        iso_datetime=expected_details["createdAt"],
                        ui_datetime=details["Created"],
                    )
                    is True
                )
            elif item == "Secrets" and expected_details.get("secrets"):
                if "NGC_API_KEY" in expected_details["secrets"]:
                    expected_details["secrets"].remove("NGC_API_KEY")
                assert len(details["Secrets"]) == len(expected_details["secrets"])
                for secret in details["Secrets"]:
                    assert secret in expected_details["secrets"]

    def _parse_iso_datetime(self, iso_string: str) -> datetime:
        """
        Parse ISO datetime string manually to avoid dependency issues.

        Args:
            iso_string: ISO format datetime string (e.g., "2025-06-09T09:51:58.505Z")

        Returns:
            datetime: Parsed datetime object
        """
        # Remove 'Z' and milliseconds for simpler parsing
        clean_string = iso_string.replace("Z", "").split(".")[0]

        # Parse the clean datetime string
        return datetime.strptime(clean_string, "%Y-%m-%dT%H:%M:%S")

    def compare_datetime_strings(self, iso_datetime: str, ui_datetime: str) -> bool:
        """
        Compare datetime strings in different formats, ignoring seconds.
        Converts UI datetime (local time) to UTC for comparison with ISO datetime.

        Args:
            iso_datetime: ISO format datetime string (e.g., "2025-06-09T09:51:58.505Z") - UTC time
            ui_datetime: UI format datetime string (e.g., "06/09/2025, 9:51 AM") - Local time

        Returns:
            bool: True if the datetimes represent the same date and time (ignoring seconds), False otherwise
        """

        try:
            from datetime import timezone
            import time

            # Parse ISO datetime manually (UTC)
            iso_dt = self._parse_iso_datetime(iso_datetime)
            # Ensure ISO datetime is treated as UTC
            iso_dt_utc = iso_dt.replace(tzinfo=timezone.utc)

            # Parse UI datetime - handle different possible formats
            ui_formats = [
                "%m/%d/%Y, %I:%M %p",  # "06/09/2025, 9:51 AM"
                "%m/%d/%Y, %H:%M",  # "06/09/2025, 09:51"
                "%d/%m/%Y, %I:%M %p",  # "09/06/2025, 9:51 AM" (day/month format)
                "%Y-%m-%d, %I:%M %p",  # "2025-06-09, 9:51 AM"
            ]

            ui_dt = None
            for fmt in ui_formats:
                try:
                    ui_dt = datetime.strptime(ui_datetime, fmt)
                    break
                except ValueError:
                    continue

            if ui_dt is None:
                logging.error(f"Could not parse UI datetime format: {ui_datetime}")
                return False

            # Convert UI datetime from local time to UTC
            # Get the local timezone offset
            local_offset_seconds = time.timezone if time.daylight == 0 else time.altzone
            local_offset_hours = local_offset_seconds / 3600

            # Apply timezone offset to convert local time to UTC
            from datetime import timedelta

            ui_dt_utc = ui_dt + timedelta(hours=local_offset_hours)
            ui_dt_utc = ui_dt_utc.replace(tzinfo=timezone.utc)

            # Compare year, month, day, hour, minute (ignore seconds)
            iso_components = (
                iso_dt_utc.year,
                iso_dt_utc.month,
                iso_dt_utc.day,
                iso_dt_utc.hour,
                iso_dt_utc.minute,
            )
            ui_components = (
                ui_dt_utc.year,
                ui_dt_utc.month,
                ui_dt_utc.day,
                ui_dt_utc.hour,
                ui_dt_utc.minute,
            )

            is_equal = iso_components == ui_components

            if not is_equal:
                logging.error(
                    f"Datetime mismatch: ISO UTC {iso_components} != UI UTC {ui_components}"
                )
                logging.error(f"Original - ISO: {iso_datetime}, UI: {ui_datetime}")
                logging.error(f"Local timezone offset: {local_offset_hours} hours")
            else:
                logging.info(
                    f"Datetime match: {iso_datetime} == {ui_datetime} (after UTC conversion)"
                )

            return is_equal

        except Exception as e:
            logging.error(f"Error comparing datetime strings: {str(e)}")
            return False

    def get_telemetry_configuration_information(self):
        logs_endpoint = (
            self.page.locator("//span[text()='Logs Endpoint']/following-sibling::div")
            .nth(0)
            .text_content()
        )
        metrics_endpoint = (
            self.page.locator("//span[text()='Metrics Endpoint']/following-sibling::div")
            .nth(0)
            .text_content()
        )
        traces_endpoint = (
            self.page.locator("//span[text()='Traces Endpoint']/following-sibling::div")
            .nth(0)
            .text_content()
        )
        res_dict = {
            "logs_endpoint": logs_endpoint,
            "metrics_endpoint": metrics_endpoint,
            "traces_endpoint": traces_endpoint,
        }
        return res_dict

    def validate_error_message(self, error_message: str = None) -> bool:
        result = self.page.get_by_text(text=error_message)
        if result:
            return True
        else:
            return False
