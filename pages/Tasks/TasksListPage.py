from playwright.sync_api import Page
from cloudia.ui.base_page import BasePage

from component.tasks_list_page_components import (
    TasksListPageMisc,
)
import logging
from datetime import datetime

from config.consts import (
    SIDEBAR_APP_CLOUDFUNCTIONS,
    SIDEBAR_CLOUDFUNCTIONS_ENTRY_TASKS,
)
from typing import Optional


class TasksListPage(BasePage):
    """Contains all the elements and functionalities of the Task List Page

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object of the Playwright.
    """

    def __init__(
        self,
        page: Page,
    ):
        super().__init__(
            page, SIDEBAR_APP_CLOUDFUNCTIONS, SIDEBAR_CLOUDFUNCTIONS_ENTRY_TASKS
        )
        self.TasksListPageMisc = TasksListPageMisc(page)
        self.TaskNameBtn = "//button[text()[contains(., 'Task Name')]]"
        self.TaskStatusBtn = "//button[contains(text(), 'Status')]"
        self.CurrentSortStatus = "//button[contains(text(), 'Status')]/*[name()='svg']"
        self.CurrentTaskNameStatus = (
            "//button[text()[contains(., 'Task Name')]]/*[name()='svg']"
        )
        self.TaskIDBtn = "//button[text()[contains(., 'Task ID')]]"
        self.CurrentTaskIDStatus = (
            "//button[text()[contains(., 'Task ID')]]/*[name()='svg']"
        )
        self.TaskTypeBtn = "//button[text()[contains(., 'Type')]]"
        self.CurrentTaskTypeStatus = "//button[text()[contains(., 'Type')]]/*[name()='svg']"
        self.LastUpdateBtn = "//button[text()[contains(., 'Last Update')]]"
        self.CreatedDateBtn = "//button[text()[contains(., 'Created Date')]]"
        self.CurrentLastUpdateStatus = (
            "//button[text()[contains(., 'Last Update')]]/*[name()='svg']"
        )
        self.CurrentCreatedDateStatus = (
            "//button[text()[contains(., 'Created Date')]]/*[name()='svg']"
        )
        self.CleanupDateBtn = "//button[text()[contains(., 'Cleanup Date')]]"
        self.CurrentCleanupDateStatus = (
            "//button[text()[contains(., 'Cleanup Date')]]/*[name()='svg']"
        )
        self.Actions = "//th[text()='Actions']"
        self.FilterBtn = (
            "//button[@data-testid='kui-menu-trigger' and text()[contains(., 'Filter')]]"
        )
        self.ClearFilterBtn = "//button[text()[contains(., 'Clear Filters')]]"
        self.StatusFilter = (
            "//div[@data-testid='kui-menu-sub-trigger']//div[text()='Status']"
        )
        self.TypeFilter = "//div[@data-testid='kui-menu-sub-trigger']//div[text()='Type']"
        self.ApplyFilterBtn = "//button[text()[contains(., 'Apply Filter')]]"
        self.Results = "//span[contains(normalize-space(), 'Results')]"
        self.CancelBtn = "//input/following-sibling::button"
        self.TableItems = "//table//tbody//tr"
        self.ShowItems = "//span[text()='Show']/following-sibling::div//button"
        self.SelectEntrybyName = "//div[@data-testid='kui-select-item']/span[text()='{0}']"
        self.PrevPageButton = "//div[@data-testid='kui-pagination-number-range']/button[1]"
        self.NextPageButton = "//div[@data-testid='kui-pagination-number-range']/button[2]"
        self.CancelTaskBtn = "//div[@role='menuitem' and text()='Cancel Task']"
        self.ConfirmCancelBtn = (
            "//button[@data-testid='kui-button' and normalize-space()='Cancel Task']"
        )
        self.TaskNameByName = "//span[text()='{0}']/parent::a"
        self.TaskIDByID = "//td[@headers='data-view-header-id' and @title='{0}']"
        self.TaskStatusByID = "//td[@headers='data-view-header-id' and text()='{0}']/preceding-sibling::td[@headers='data-view-header-Status']/div"
        self.TaskStatusByName = "//table/tbody/tr/td[1]//span[text()='{0}']/ancestor::td/following-sibling::td[1]/div"
        self.DeleteTaskBtn = "//div[@role='menuitem' and text()='Delete Task']"
        self.CloneTaskBtn = "//div[@role='menuitem' and text()='Clone Task']"
        self.ConfirmDeleteBtn = (
            "//button[@data-testid='kui-button' and normalize-space()='Delete Task']"
        )
        self.UpdateAPIKeyBtn = "//div[@role='menuitem' and text()='Update API Key']"
        self.APIKeyInput = "//input[@name='NGC_API_KEY']"
        self.SaveChangesBtn = (
            "//button[@data-testid='kui-button' and normalize-space()='Save Changes']"
        )
        self.ErrorApiKeyMsg = "//span[@data-testid='kui-text' and contains(text(), '{0}')]"
        self.SucessApiKeyMsg = "//span[@data-testid='kui-text' and contains(text(), 'succesfully updated secrets')]"

        self.ManageSecretsBtn = "//div[@role='menuitem' and text()='Manage Secrets']"
        self.AddSecretBtn = (
            "//button[@data-testid='kui-button' and normalize-space()='Add Another Secret']"
        )
        self.SecretNameInput = "//input[@type='text' and @name='secrets[{0}].name' and @placeholder='Enter a key' and @data-testid='kui-text-input-element']"
        self.SecretValueInput = "//textarea[@data-testid='kui-text-area-element' and @name='secrets[{0}].value' and @placeholder='Enter a value as a text string or JSON']"
        self.SaveSecretBtn = (
            "//button[@data-testid='kui-button' and normalize-space()='Save Secrets']"
        )
        self.SucessSecretMsg = "//span[@data-testid='kui-text' and contains(text(), 'succesfully updated secrets')]"

        self.three_dot_btn = "//td[@headers='data-view-header-row-actions']//button[@data-testid='kui-menu-trigger']"
        self.Results = "//span[contains(normalize-space(), 'Results') or contains(normalize-space(), 'Result')]"
        self.Actions = "//th[text()='Actions']"
        self.NextPageButton = "//div[@data-testid='kui-pagination-number-range']/button[2]"
        self.status_list = [
            "Queued",
            "Launched",
            "Running",
            "Errored",
            "Canceled",
            "Timed Out",
            "Completed",
        ]
        self.type_list = [
            "Container",
            "Helm Chart",
        ]
        self.FilterBtn = (
            "//button[@data-testid='kui-menu-trigger' and text()[contains(., 'Filter')]]"
        )
        self.ClearFilterBtn = "//button[text()[contains(., 'Clear Filters')]]"
        self.StatusFilter = (
            "//div[@data-testid='kui-menu-sub-trigger']//div[text()='Status']"
        )
        self.ApplyFilterBtn = "//button[text()[contains(., 'Apply Filter')]]"
        self.search_table_input = "//input[starts-with(@placeholder, 'Search')]"
        self.match_one_result_span = "//span[text()='Result' or text()='Results']"

    def search_task_exact_match(self, keyword: str):
        self.page.wait_for_timeout(2000)
        self.page.locator(self.search_table_input).fill(keyword)
        self.page.wait_for_timeout(2000)
        self.page.locator(self.match_one_result_span).wait_for(state="visible")
        logging.info(f"Task found: {keyword}.")

    def cancel_first_row_task(self):
        self.page.wait_for_timeout(2000)
        self.page.locator(self.three_dot_btn).click()
        self.page.locator(self.CancelTaskBtn).click()
        self.page.locator(self.ConfirmCancelBtn).click()
        logging.info("cancelled the first row task")

    def wait_task_to_expected_status(
        self,
        task_id: str,
        expected_status: str,
        time_out: int,
        **kwargs,
    ) -> bool:
        """Wait the function to expected status in timeout.

        parameters:
        -----------
        task_name: `str`
            The name of the task
        expected_status: `str`
            The expected status of the task
        time_out: `int`
            The timeout in seconds

        Returns:
        --------
        `bool`
            True if the function is in expected status in timeout, otherwise False.
        """

        logging.info(f"Start waiting {task_id} to expected status {expected_status}...")
        logging.info(f"{self.TaskIDByID.format(task_id)}")
        while True:
            if self.page.locator(self.TaskIDByID.format(task_id)).count() == 1:
                logging.info(f"status:{self.TaskStatusByID.format(task_id)}")
                current_status = self.page.locator(
                    self.TaskStatusByID.format(task_id)
                ).text_content()

                logging.info(f"current_status: {current_status}")
                if current_status == expected_status:
                    logging.info(
                        f"Task {task_id} is in expected status {expected_status} now..."
                    )
                    return True
                else:
                    logging.info(
                        f"Waiting {task_id} to expected status {expected_status}, current as {current_status}, time left {time_out}s..."
                    )
                    self.page.wait_for_timeout(20000)
                    time_out -= 20
                    if time_out > 0:
                        continue
                    else:
                        return False

    def navigate_to_task_details_page(
        self,
        task_id: str = None,
        task_name: str = None,
    ) -> bool:
        """Go to the task details page by task uuid.

        parameters:
        -----------
        task_id: `str`
            The uuid of the task
        task_name: `str`
            The name of the task

        return:
        -----------
        `bool`
            True if the task details page is opened successfully, otherwise False.
        """
        logging.info(
            f"Navigating to task details page for task_id: {task_id} and task_name: {task_name}"
        )
        if task_id:
            self.page.locator(self.search_table_input).fill(task_id)
        elif task_name:
            self.page.locator(self.search_table_input).fill(task_name)
        self.page.locator(self.CancelBtn).wait_for(state="visible")
        self.page.wait_for_load_state("load")
        self.navigate_to_first_task_detail_page()
        try:
            self.page.wait_for_selector("text=Details")
            return True
        except Exception as e:
            logging.error(f"Failed to navigate to task details page: {e}")
            return False

    def navigate_to_first_task_detail_page(self):
        """Navigate to the first line of task details page

        Returns:
        --------
        None
        """
        task_item = self.page.locator(self.TableItems).first
        task_item.locator("//td").locator("nth=0").click()
        self.page.wait_for_load_state("load")

    def set_sort_task_by_status(self):
        """Set the current sort method of task status.

        Returns:
        --------
        No
        """

        self.page.locator(self.TaskStatusBtn).click()
        self.page.wait_for_load_state("load")

    def get_current_sort_task_by_status(self) -> str:
        """Get the current sort method of task status.

        Returns:
        --------
        `str`
            The name of data-icon-name, e.g., arrow-up or arrow-down.
        """
        svg_element = self.page.locator(self.CurrentSortStatus)
        icon_name = svg_element.get_attribute("data-icon-name")
        return icon_name

    def set_sort_task_by_task_name(self):
        """Set the current sort method of task name.

        Returns:
        --------
        No
        """

        self.page.locator(self.TaskNameBtn).click()
        self.page.wait_for_load_state("load")

    def get_current_sort_task_by_task_name(self) -> str:
        """Get the current sort method of task name.

        Returns:
        --------
        `str`
            The name of data-icon-name, e.g., arrow-up or arrow-down.
        """
        svg_element = self.page.locator(self.CurrentTaskNameStatus)
        icon_name = svg_element.get_attribute("data-icon-name")
        return icon_name

    def set_sort_task_by_task_id(self):
        """Set the current sort method of task id.

        Returns:
        --------
        No
        """

        self.page.locator(self.TaskIDBtn).click()
        self.page.wait_for_load_state("load")

    def get_current_sort_task_by_task_id(self) -> str:
        """Get the current sort method of task id.

        Returns:
        --------
        `str`
            The name of data-icon-name, e.g., arrow-up or arrow-down.
        """
        svg_element = self.page.locator(self.CurrentTaskIDStatus)
        icon_name = svg_element.get_attribute("data-icon-name")
        return icon_name

    def set_sort_task_by_type(self):
        """Set the current sort method of task type.

        Returns:
        --------
        No
        """

        self.page.locator(self.TaskTypeBtn).click()
        self.page.wait_for_load_state("load")

    def get_current_sort_task_by_type(self) -> str:
        """Get the current sort method of task type.

        Returns:
        --------
        `str`
            The name of data-icon-name, e.g., arrow-up or arrow-down.
        """
        svg_element = self.page.locator(self.CurrentTaskTypeStatus)
        icon_name = svg_element.get_attribute("data-icon-name")
        return icon_name

    def set_sort_task_by_last_update(self):
        """Set the current sort method of task last update.

        Returns:
        --------
        No
        """

        self.page.locator(self.LastUpdateBtn).click()
        self.page.wait_for_load_state("load")

    def set_sort_task_by_created_date(self):
        """Set the current sort method of task created date.

        Returns:
        --------
        No
        """

        self.page.locator(self.CreatedDateBtn).click()
        self.page.wait_for_load_state("load")

    def get_current_sort_task_by_created_date(self) -> str:
        """Get the current sort method of task created date.

        Returns:
        --------
        `str`
            The name of data-icon-name, e.g., arrow-up or arrow-down.
        """
        svg_element = self.page.locator(self.CurrentCreatedDateStatus)
        icon_name = svg_element.get_attribute("data-icon-name")
        return icon_name

    def get_current_sort_task_by_last_update(self) -> str:
        """Get the current sort method of task last update.

        Returns:
        --------
        `str`
            The name of data-icon-name, e.g., arrow-up or arrow-down.
        """
        svg_element = self.page.locator(self.CurrentLastUpdateStatus)
        icon_name = svg_element.get_attribute("data-icon-name")
        return icon_name

    def check_last_update_sorted(self, icon_name):
        """Check if the last update is sorted by the given icon name.

        Returns:
        --------
        `bool`
            True if the last update is sorted by the given icon name, otherwise False.
        """
        update_time = []
        for i in range(20):
            element = self.page.locator(
                "//td[@data-testid='kui-table-data-cell' and @headers='data-view-header-lastUpdated']"
            ).nth(i)
            text = element.text_content()
            update_time.append(text)
            print(f"{i+1}th last update: {text}")
        logging.info(f"update_time: {update_time}")
        if icon_name == "arrow-up":
            return self.check_time_order(update_time) == "ascending"
        elif icon_name == "arrow-down":
            return self.check_time_order(update_time) == "descending"
        else:
            return False

    def check_created_date_sorted(self, icon_name):
        """Check if the created date is sorted by the given icon name.

        Returns:
        --------
        `bool`
            True if the created date is sorted by the given icon name, otherwise False.
        """
        created_date = []
        for i in range(20):
            element = self.page.locator(
                "//td[@data-testid='kui-table-data-cell' and @headers='data-view-header-createdAt']"
            ).nth(i)
            text = element.text_content()
            created_date.append(text)
            print(f"{i+1}th created date: {text}")
        logging.info(f"created_date: {created_date}")
        if icon_name == "arrow-up":
            return self.check_time_order(created_date) == "ascending"
        elif icon_name == "arrow-down":
            return self.check_time_order(created_date) == "descending"
        else:
            return False

    def check_time_order(self, time_list):
        """Check the order of the time list.

        Args:
            time_list: A list of time strings in the format 'MM/DD/YYYY, HH:MM AM/PM'

        Returns:
            'ascending': The time list is sorted in ascending order
            'descending': The time list is sorted in descending order
            'unsorted': The time list is not sorted
            'equal': The time list is equal
        """
        if not time_list or len(time_list) < 2:
            return "equal"

        datetime_list = []
        for time_str in time_list:
            try:
                time_str = time_str.replace(":", ":")
                dt = datetime.strptime(time_str, "%m/%d/%Y, %I:%M %p")
                datetime_list.append(dt)
            except ValueError as e:
                raise ValueError(
                    f"Invalid time format: {time_str}. Expected format: MM/DD/YYYY, HH:MM AM/PM"
                ) from e

        if all(dt == datetime_list[0] for dt in datetime_list):
            return "equal"

        is_ascending = all(
            datetime_list[i] <= datetime_list[i + 1] for i in range(len(datetime_list) - 1)
        )

        is_descending = all(
            datetime_list[i] >= datetime_list[i + 1] for i in range(len(datetime_list) - 1)
        )

        if is_ascending and not is_descending:
            return "ascending"
        elif is_descending and not is_ascending:
            return "descending"
        else:
            return "unsorted"

    def set_sort_task_by_cleanup_date(self):
        """Set the current sort method of task cleanup date.

        Returns:
        --------
        No
        """

        self.page.locator(self.CleanupDateBtn).click()
        self.page.wait_for_load_state("load")

    def get_current_sort_task_by_cleanup_date(self) -> str:
        """Get the current sort method of task cleanup date.

        Returns:
        --------
        `str`
            The name of data-icon-name, e.g., arrow-up or arrow-down.
        """
        svg_element = self.page.locator(self.CurrentCleanupDateStatus)
        icon_name = svg_element.get_attribute("data-icon-name")
        return icon_name

    def click_three_dots_menu(self):
        """Click the three dots menu button for the first task in the list."""
        self.page.locator(self.three_dot_btn).first.click()
        logging.info("Clicked three dots menu button")

    def click_delete_task(self):
        """Click the Delete Task option in the three dots menu."""
        self.page.locator(self.DeleteTaskBtn).click()
        logging.info("Clicked Delete Task button")

    def click_clone_task(self):
        """Click the Clone Task option in the three dots menu."""
        self.page.locator(self.CloneTaskBtn).click()
        logging.info("Clicked Clone Task button")

    def confirm_delete_task(self):
        """Click the Delete Task button in the confirmation modal."""
        self.page.locator(self.ConfirmDeleteBtn).click()
        logging.info("Confirmed task deletion")

    def update_api_key(
        self,
        api_key: str,
        is_valid_key: Optional[bool] = True,
        fail_msg: Optional[str] = None,
    ):
        """Update the API key for the task.
        fail_msg: The message to display if the API key is not updated successfully.
        ["Access Denied", "Authentication Failed"]
        """
        self.page.locator(self.UpdateAPIKeyBtn).click()
        logging.info("Clicked Update API Key button")
        self.page.locator(self.APIKeyInput).fill(api_key)
        logging.info(f"Filled API Key: {api_key}")
        self.page.locator(self.SaveChangesBtn).click()
        logging.info("Clicked Save Changes button")
        self.page.wait_for_load_state("load")
        if is_valid_key:
            self.page.locator(self.SucessApiKeyMsg).wait_for(state="visible")
            if self.page.locator(self.SucessApiKeyMsg).is_visible():
                return True
            else:
                return False
        else:
            self.page.locator(self.ErrorApiKeyMsg.format(fail_msg)).wait_for(
                state="visible"
            )
            if self.page.locator(self.ErrorApiKeyMsg.format(fail_msg)).is_visible():
                return True
            else:
                return False

    def manage_secrets(
        self,
        secrets_list: list[dict],
    ):
        """Manage the secrets for the task."""
        self.page.locator(self.ManageSecretsBtn).click()
        logging.info("Clicked Manage Secrets button")
        for i, secret in enumerate(secrets_list):
            self.page.locator(self.AddSecretBtn).click()
            logging.info("Clicked Add Secret button")
            self.page.locator(self.SecretNameInput.format(i + 1)).fill(secret["key"])
            logging.info(f"Filled Secret Name: {secret['key']}")
            self.page.locator(self.SecretValueInput.format(i + 1)).fill(secret["value"])
            logging.info(f"Filled Secret Value: {secret['value']}")
        self.page.locator(self.SaveSecretBtn).click()
        logging.info("Clicked Save Secret button")
        self.page.wait_for_load_state("load")
        self.page.locator(self.SucessSecretMsg).wait_for(state="visible")
        if self.page.locator(self.SucessSecretMsg).is_visible():
            return True
        else:
            return False

    def modify_secrets(
        self,
        secrets_list: list[dict],
    ):
        """Modify the secrets for the task."""
        self.page.locator(self.ManageSecretsBtn).click()
        self.page.wait_for_load_state("load")
        self.page.wait_for_timeout(5000)
        logging.info("Clicked Manage Secrets button")
        self.nameinput = "//div[@data-testid='kui-text-input-root'][.//input[@value='{0}']]"

        self.keyinput = "//div[@data-testid='kui-text-input-root'][.//input[@value='{0}']]/following-sibling::div[@data-testid='kui-text-area-root']//textarea[@data-testid='kui-text-area-element']"

        for secret in secrets_list:
            for key, value in secret.items():
                self.nameinputlocate = self.nameinput.format(key)
                logging.info(f"nameinputlocate: {self.nameinputlocate}")
                if self.page.locator(self.nameinputlocate).is_visible():
                    logging.info(f"Found Secret Name: {key}")
                    self.keyinputlocate = self.page.locator(self.keyinput.format(key))
                    self.keyinputlocate.fill(value)
                    logging.info(f"Filled Secret: {key}")
                else:
                    logging.info(f"Secret Name: {key} not found")
                    return False
        self.page.locator(self.SaveSecretBtn).click()
        logging.info("Clicked Save Secret button")
        self.page.wait_for_load_state("load")
        self.page.locator(self.SucessSecretMsg).wait_for(state="visible")
        if self.page.locator(self.SucessSecretMsg).is_visible():
            return True
        else:
            return False

    def get_total_task_count(self) -> int:
        """Get the total tasks count.

        Returns:
        --------
        `int`
            The total task count.
        """
        self.page.wait_for_timeout(2000)
        text = self.page.locator(self.Results).inner_text()
        return int(text.split()[0])

    def check_action_exists(self):
        if self.page.locator(self.Actions).is_visible():
            return True
        else:
            return False

    def set_filter_by_status(self, status):
        """Set the filter by status.

        Parameters:
        -----------
        status: `str`
            The status to filter by.

        Returns:
        --------
        `int`
            The number of filtered results.
        """
        if status in self.status_list:
            self.page.locator(self.FilterBtn).click()
            self.page.locator(self.StatusFilter).click()
            status_elements = f"//span[@class='c-dpLRfC' and text()='{status}']"
            self.page.locator(status_elements).click()
            self.page.locator(self.ApplyFilterBtn).click()
            self.page.wait_for_load_state("load")
            self.page.wait_for_timeout(3000)
            results = self.page.locator(self.Results).inner_text()
            if self.check_status_filter_by_status(status, int(results.split()[0])):
                self.page.locator(self.ClearFilterBtn).click()
                return int(results.split()[0]), True
            else:
                logging.info(f"Failed to filter by status: {status}")
                self.page.locator(self.ClearFilterBtn).click()
                return int(results.split()[0]), False
        else:
            raise ValueError(f"Invalid status: {status}")

    def set_filter_by_type(self, type):
        """Set the filter by type.

        Parameters:
        -----------
        type: `str`
            The type to filter by.

        Returns:
        --------
        `int`
            The number of filtered results.
        """
        if type in self.type_list:
            self.page.locator(self.FilterBtn).click()
            self.page.locator(self.TypeFilter).click()
            type_elements = f"//span[@class='c-dpLRfC' and text()='{type}']"
            self.page.locator(type_elements).click()
            self.page.locator(self.ApplyFilterBtn).click()
            self.page.wait_for_load_state("load")
            self.page.wait_for_timeout(3000)
            results = self.page.locator(self.Results).inner_text()
            if self.check_type_filter_by_type(type, int(results.split()[0])):
                self.page.locator(self.ClearFilterBtn).click()
                return int(results.split()[0]), True
            else:
                logging.info(f"Failed to filter by type: {type}")
                self.page.locator(self.ClearFilterBtn).click()
                return int(results.split()[0]), False
        else:
            raise ValueError(f"Invalid type: {type}")

    def check_type_filter_by_type(self, type, results):
        """Check if the type filter is working.

        Returns:
        --------
        `bool`
        """
        if type in self.type_list:
            if results == 0:
                return True

            if results > 20:
                self.set_show_items(100)

            total_pages = (results + 99) // 100
            current_page = 1
            logging.info(f"total_pages: {total_pages}")
            logging.info(f"results: {results}")
            while current_page <= total_pages:
                items_on_current_page = min(100, results - (current_page - 1) * 100)
                for i in range(items_on_current_page):
                    type_element = self.page.locator(
                        "//td[@headers='data-view-header-Type']"
                    ).nth(i)
                    type_text = type_element.text_content().upper()
                    if type_text != type.upper():
                        return False
                if current_page < total_pages:
                    logging.info(f"current_page: {current_page}")
                    self.page.locator(self.NextPageButton).click()
                    self.page.wait_for_load_state("load")
                    self.page.wait_for_timeout(3000)
                current_page += 1
            return True
        else:
            raise ValueError(f"Invalid type: {type}")

    def check_status_filter_by_status(self, status, results):
        """Check if the status filter is working.

        Returns:
        --------
        `bool`
        """
        if status in self.status_list:
            if results == 0:
                return True

            if results > 20:
                self.set_show_items(100)

            total_pages = (results + 99) // 100
            current_page = 1
            logging.info(f"total_pages: {total_pages}")
            logging.info(f"results: {results}")
            while current_page <= total_pages:
                items_on_current_page = min(100, results - (current_page - 1) * 100)
                for i in range(items_on_current_page):
                    status_element = self.page.locator(
                        "//td[@headers='data-view-header-Status']"
                    ).nth(i)
                    status_text = status_element.text_content().upper()
                    if status_text != status.upper():
                        return False
                if current_page < total_pages:
                    logging.info(f"current_page: {current_page}")
                    self.page.locator(self.NextPageButton).click()
                    self.page.wait_for_load_state("load")
                    self.page.wait_for_timeout(3000)
                current_page += 1
            return True
        else:
            raise ValueError(f"Invalid status: {status}")

    def set_show_items(self, expected_show_items):
        """Set the number of items to show.

        Returns:
        --------
        `bool`
        """
        self.page.locator(self.ShowItems).click()
        self.page.locator(self.SelectEntrybyName.format(expected_show_items)).click()
        self.page.wait_for_load_state("load")
        self.page.wait_for_timeout(3000)

    def check_task_type(self, type):
        """Check if the task type is correct.

        Returns:
        --------
        `bool`
        """
        if type in self.type_list:
            self.page.locator(self.FilterBtn).click()
            self.page.locator(self.TypeFilter).click()
            type_elements = f"//span[@class='c-dpLRfC' and text()='{type}']"
            self.page.locator(type_elements).click()
            self.page.locator(self.ApplyFilterBtn).click()
            self.page.wait_for_load_state("load")
            self.page.wait_for_timeout(3000)
            self.navigate_to_first_task_detail_page()
            type_val = "//span[@data-testid='kui-text' and text()='Type']/following-sibling::span[1]"
            self.page.locator(type_val).wait_for(state="visible")
            task_type = self.page.locator(type_val).text_content()
            self.navigate_to_page()
            if task_type == type:
                return True
            else:
                return False

        else:
            raise ValueError(f"Invalid type: {type}")
