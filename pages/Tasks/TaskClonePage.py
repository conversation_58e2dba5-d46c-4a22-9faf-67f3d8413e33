from pages.Tasks.TasksCreatePage import TasksCreatePage
from typing import Optional, Dict
import logging
import re
from playwright.sync_api import expect
from element.tasks_create_page_elements import (
    TaskCreateOptionENUM,
)
from utils.common.tools import convert_duration_to_iso_format


class TasksClonePage(TasksCreatePage):
    def __init__(self, page):
        super().__init__(page)

    def verify_clone_details(
        self, expected_details, function_type: Optional[str] = "container"
    ):
        """Verify that the cloned task details match the expected values.

        Args:
            expected_details (dict): Expected task details to verify against
            function_type (Optional[str]): Type of function, defaults to "container"

        Raises:
            Exception: If an expected key is not found on the clone page
            AssertionError: If any values don't match expectations
        """
        # Get all actual values from the clone page
        if function_type == "helm":
            actual_details = self._get_actual_details(function_type="helm")
        else:
            actual_details = self._get_actual_details()

        # Remove keys that should not be verified
        keys_to_remove = ["id", "ncaId", "status", "createdAt"]
        for key in keys_to_remove:
            expected_details.pop(
                key, None
            )  # Using pop with None as default to avoid KeyError

        # Verify expected details match actual details
        if function_type == "helm":
            self._verify_expected_details(
                expected_details, actual_details, function_type="helm"
            )
        else:
            self._verify_expected_details(
                expected_details, actual_details, function_type="container"
            )

        # Verify any extra fields are empty
        self._verify_extra_fields_empty(actual_details)

        logging.info("Clone details verification completed successfully")

    def _get_maximum_runtime_duration(self):
        """Get the maximum runtime duration from the clone page."""
        try:
            duration_value = self.page.locator(
                "//label[contains(text(), 'Maximum Runtime Duration')]/../..//button[@data-testid='kui-select-trigger']//span[not(@aria-hidden)]"
            )

            expect(duration_value).to_be_visible(timeout=5000)
            return convert_duration_to_iso_format(duration_value.inner_text().strip())
        except TimeoutError:
            logging.error("Timeout waiting for runtime duration value")
            return ""

    def _get_maximum_queued_duration(self):
        """Get the maximum queued duration from the clone page."""
        try:
            duration_value = self.page.locator(
                "//label[contains(text(), 'Maximum Queued Duration')]/../..//button[@data-testid='kui-select-trigger']//span[not(@aria-hidden)]"
            )

            expect(duration_value).to_be_visible(timeout=5000)
            return convert_duration_to_iso_format(duration_value.inner_text().strip())
        except TimeoutError:
            logging.error("Timeout waiting for queued duration value")
            return ""

    def _get_termination_grace_period_duration(self):
        """Get the termination grace period duration from the clone page."""
        try:
            duration_value = self.page.locator(
                "//label[contains(text(), 'Termination Grace Period Duration')]/../..//button[@data-testid='kui-select-trigger']//span[not(@aria-hidden)]"
            )

            expect(duration_value).to_be_visible(timeout=5000)
            return convert_duration_to_iso_format(duration_value.inner_text().strip())
        except TimeoutError:
            logging.error("Timeout waiting for termination grace period duration value")
            return ""

    def _get_gpu_specfication(self):
        """Get the GPU specification from the clone page."""
        gpu_specification = {}
        try:
            gpu_type_input = self.page.locator("input[name='gpu']")
            instance_type_input = self.page.locator("input[name='instanceType']")

            # For hidden inputs, use to_be_attached instead of to_be_visible
            expect(gpu_type_input).to_be_attached(timeout=5000)
            expect(instance_type_input).to_be_attached(timeout=5000)

            gpu_specification["gpu"] = gpu_type_input.input_value()
            gpu_specification["instanceType"] = instance_type_input.input_value()
            gpu_specification["clusters"] = self._get_clusters_value()
        except TimeoutError:
            logging.error("Timeout waiting for GPU specification inputs")
            gpu_specification["gpu_type"] = ""
            gpu_specification["instance_type"] = ""
            gpu_specification["clusters"] = ""
        return gpu_specification

    def _get_clusters_value(self) -> list:
        """Get the clusters value from the clone page."""
        clusters = []
        try:
            clusters_container = self.page.get_by_test_id("kui-multi-filter").filter(
                has_text="Clusters"
            )
            expect(clusters_container).to_be_visible(timeout=5000)

            combobox = clusters_container.get_by_role("combobox")
            placeholder_id = combobox.get_attribute("aria-describedby")
            value = clusters_container.locator(f"#{placeholder_id}").inner_text()

            if value and "GFN" not in value:
                clusters.append(value)
        except TimeoutError:
            logging.error("Timeout waiting for clusters dropdown")
        return clusters

    def _get_actual_details(self, function_type: Optional[str] = "container"):
        """Get all field values from the clone page."""
        actual_details = {}

        # Wait for page to be ready
        self.page.get_by_placeholder("Enter a name").wait_for(state="visible")

        # Get task name
        actual_details["name"] = self.page.get_by_placeholder("Enter a name").input_value()

        # Get GPU Type
        gpu_type_element = self.page.get_by_label("GPU Type")
        if gpu_type_element.is_visible():
            actual_details["gpu_type"] = gpu_type_element.input_value()

        # Get Instance Type
        instance_type_element = self.page.get_by_label("Instance Type")
        if instance_type_element.is_visible():
            actual_details["instance_type"] = instance_type_element.input_value()

        # Get Models
        actual_details["models"] = self._get_model_details()
        actual_details["resources"] = self._get_resource_details()
        actual_details["secrets"] = self._get_secret_details()
        if function_type == "container":
            actual_details["containerEnvironment"] = self._get_environment_variables()
        actual_details["gpuSpecification"] = self._get_gpu_specfication()
        actual_details["maxRuntimeDuration"] = self._get_maximum_runtime_duration()
        actual_details["maxQueuedDuration"] = self._get_maximum_queued_duration()
        actual_details[
            "terminationGracePeriodDuration"
        ] = self._get_termination_grace_period_duration()
        if function_type == "container":
            actual_details["containerImage"] = self._get_container_image()
        else:
            logging.info("*******************")
            logging.info(self._get_helm_chart())
            actual_details["helmChart"] = self._get_helm_chart()
        actual_details["personalKey"] = self._get_personal_key()

        # Get Result Upload settings
        result_upload = self.page.locator(
            "[data-testid='kui-radio'][id='resultHandlingStrategyUpload']"
        )
        try:
            expect(result_upload).to_be_attached(timeout=5000)
            actual_details["resultHandlingStrategy"] = (
                "UPLOAD" if result_upload.is_checked() else "NONE"
            )
            if result_upload.is_checked():
                actual_details["resultsLocation"] = self._get_result_location_model_name()
        except TimeoutError:
            logging.error("Timeout waiting for upload radio button")
            actual_details["resultHandlingStrategy"] = "NONE"

        logging.info(f"Found actual details: {actual_details}")
        return actual_details

    def _get_container_image(self):
        """Get the container image from the clone page.

        Returns:
            str: Container image in format 'nvcr.io/[registry]/[image]:[tag]'
        """
        try:
            container_input = self.page.locator("input[name='container']")
            container_tag = self.page.locator("input[name='containerTag']")

            # Wait for elements to be attached to DOM
            container_input.wait_for(state="attached", timeout=5000)
            container_tag.wait_for(state="attached", timeout=5000)

            # Check if elements have values
            if not container_input.input_value():
                return ""

            # Format container image with nvcr.io prefix
            container = container_input.input_value()
            if container:
                container = container.replace(":", "/")

            tag = container_tag.input_value()
            if not tag:
                return container

            return f"{container}:{tag}"
        except TimeoutError as e:
            logging.error(f"Timeout waiting for container input: {str(e)}")
            return ""

    def _get_model_version(self, index: int) -> str:
        """Get model version with proper waiting and error handling."""
        try:
            locator = self.page.locator(f"input[name='models.{index}.version']")
            expect(locator).to_have_value(value=re.compile(r".+"), timeout=5000)
            version = locator.input_value()
            logging.info(f"Found model version: {version}")
            # Returns the input value if it exists, otherwise returns empty string
            return version or ""
        except Exception as e:
            logging.error(f"Error getting model version at index {index}: {str(e)}")
            return ""

    def _get_resource_version(self, index: int) -> str:
        """Get resource version with proper waiting and error handling."""
        try:
            locator = self.page.locator(f"input[name='resources.{index}.version']")
            expect(locator).to_have_value(value=re.compile(r".+"), timeout=5000)
            version = locator.input_value()
            logging.info(f"Found resource version: {version}")
            return version or ""
        except Exception as e:
            logging.error(f"Error getting resource version at index {index}: {str(e)}")
            return ""

    def _get_model_details(self):
        """Get the model details from the clone page."""

        model_details = []
        self.page.locator(self.Form.elements.ModelExpandBtn).click()
        model_count = (
            self.page.get_by_test_id("kui-label").get_by_text("Model", exact=True).count()
        )

        for index in range(model_count):
            model_details.append(
                {
                    "name": self.page.locator(
                        f"input[name='models.{index}.name']"
                    ).input_value(),
                    "version": self._get_model_version(index),
                    "uri": self.page.locator(
                        f"input[name='models.{index}.resource']"
                    ).input_value(),
                }
            )
        return model_details

    def _get_resource_details(self):
        """Get the resource details from the clone page."""
        resource_details = []
        self.page.wait_for_timeout(1000)
        self.page.locator(self.Form.elements.ResourceExpandBtn).click()
        resource_count = (
            self.page.get_by_test_id("kui-label")
            .get_by_text("Resource", exact=True)
            .count()
        )

        for index in range(resource_count):
            resource_details.append(
                {
                    "name": self.page.locator(
                        f"input[name='resources.{index}.name']"
                    ).input_value(),
                    "version": self._get_resource_version(index),
                    "uri": self.page.locator(
                        f"input[name='resources.{index}.resource']"
                    ).input_value(),
                }
            )
        return resource_details

    def _get_container_run_command(self):
        """Get the container run command from the clone page."""
        self.page.locator(self.Form.elements.RunCommandExpandBtn).click()
        return self.page.locator(self.Form.elements.RunCommandTextArea).input_value()

    def _get_environment_variables(self):
        """Get the environment variables from the clone page."""
        self.page.locator(self.Form.elements.EnvExpandBtn).click()
        env_details = []
        count = self.page.locator(
            "input[name^='containerEnvironment.'][name$='.key']"
        ).count()
        logging.info(f"Found {count} environment key inputs")

        for index in range(count):
            key = self._get_env_key(index)
            value = self._get_env_value(index)
            if key:
                env_details.append({"key": key, "value": value})
        return env_details

    def _get_env_key(self, index: int) -> str:
        """Get the environment key from the clone page."""
        try:
            key_locator = self.page.locator(
                f"input[name='containerEnvironment.{index}.key']"
            )
            key_locator.wait_for(state="attached", timeout=5000)
            return key_locator.input_value() or ""
        except Exception as e:
            logging.error(f"Error getting environment key at index {index}: {str(e)}")
            return ""

    def _get_env_value(self, index: int) -> str:
        """Get the environment value from the clone page."""
        try:
            value_locator = self.page.locator(
                f"input[name='containerEnvironment.{index}.value']"
            )
            value_locator.wait_for(state="attached", timeout=5000)
            return value_locator.input_value() or ""
        except Exception as e:
            logging.error(f"Error getting environment value at index {index}: {str(e)}")
            return ""

    def _get_secret_details(self):
        """Get the secret details from the clone page."""
        secret_details = ["NGC_API_KEY"]
        self.page.locator(self.Form.elements.SecretsExpandBtn).click()

        if self.page.get_by_placeholder("Enter a key").count() > 0:
            return secret_details

        secret_count = (
            self.page.get_by_test_id("kui-label").get_by_text("Key", exact=True).count()
        )

        for index in range(secret_count):
            secret_details.append(
                self.page.locator(f"input[name='secrets.{index}.key']").input_value()
            )
        return secret_details

    def _verify_expected_details(
        self, expected_details, actual_details, function_type: Optional[str] = "container"
    ):
        """Verify expected details match actual details."""
        if function_type == "helm":
            verified_key = [
                "models",
                "resources",
                "secrets",
                "gpuSpecification",
                "maxRuntimeDuration",
                "maxQueuedDuration",
                "terminationGracePeriodDuration",
                "helmChart",
                "resultHandlingStrategy",
                "resultsLocation",
            ]
        else:
            verified_key = [
                "models",
                "resources",
                "secrets",
                "containerEnvironment",
                "gpuSpecification",
                "maxRuntimeDuration",
                "maxQueuedDuration",
                "terminationGracePeriodDuration",
                "containerImage",
                "resultHandlingStrategy",
                "resultsLocation",
            ]

        # Compare values for each verified key
        for key in verified_key:
            if key in expected_details and key in actual_details:
                expected = expected_details[key]
                actual = actual_details[key]

                # adaptor for specific key
                if key == "models":
                    self._compare_model(expected, actual)

                elif key == "resources":
                    self._compare_resource(expected, actual)

                elif key == "gpuSpecification":
                    assert (
                        expected.get("gpu") == actual.get("gpu")
                    ), f"Mismatch in {key}: expected '{expected.get('gpu')}', got '{actual.get('gpu')}'"
                    assert (
                        expected.get("instanceType") == actual.get("instanceType")
                    ), f"Mismatch in {key}: expected '{expected.get('instanceType')}', got '{actual.get('instanceType')}'"
                    # assert (
                    #     expected.get("clusters") == actual.get("clusters")
                    # ), f"Mismatch in {key}: expected '{expected.get('clusters')}', got '{actual.get('clusters')}'"

                elif key == "containerImage" or key == "resultsLocation":
                    assert (
                        actual in expected
                    ), f"Mismatch in {key}: expected '{expected}', got '{actual}'"
                else:
                    assert (
                        expected == actual
                    ), f"Mismatch in {key}: expected '{expected}', got '{actual}'"

    def _compare_model_or_resource(self, target, expected, actual):
        assert len(expected) == len(
            actual
        ), f"Mismatch in {target} count: expected {len(expected)}, got {len(actual)}"
        # Compare each {target}'s properties
        for i in range(len(expected)):
            expected_target = expected[i]
            actual_target = actual[i]
            assert (
                expected_target.get("name") == actual_target.get("name")
            ), f"Mismatch in {target} {i} name: expected {expected_target.get('name')}, got {actual_target.get('name')}"
            assert (
                expected_target.get("version") == actual_target.get("version")
            ), f"Mismatch in {target} {i} version: expected {expected_target.get('version')}, got {actual_target.get('version')}"

            actual_target_uri = actual_target.get("uri", "").split("/")
            assert (
                actual_target_uri[0] in expected_target.get("uri", "")
            ), f"Mismatch in {target} {i} uri: expected {expected_target.get('uri')}, got {actual_target.get('uri')}"
            assert (
                actual_target_uri[1] in expected_target.get("uri", "")
            ), f"Mismatch in {target} {i} uri: expected {expected_target.get('uri')}, got {actual_target.get('uri')}"

    def _compare_model(self, expected, actual):
        self._compare_model_or_resource("model", expected, actual)

    def _compare_resource(self, expected, actual):
        self._compare_model_or_resource("resource", expected, actual)

    def _get_personal_key(self):
        """Get the personal key from the clone page."""
        try:
            key_input = self.page.locator(self.Form.elements.ApiKeyInput)
            key_input.wait_for(state="attached", timeout=5000)
            return key_input.input_value() or ""
        except Exception as e:
            logging.error(f"Error getting personal key: {str(e)}")
            return ""

    def _verify_extra_fields_empty(self, actual_details):
        """Verify any extra fields not in expected_details are empty."""
        extra_fields = ["name", "personalKey"]
        for key in extra_fields:
            value = actual_details.get(key)
            assert (
                value is None or value == ""
            ), f"Extra field {key} has value '{value}', expected None or empty string"

    def clone_task(
        self,
        name: str,
        upload_results: bool = True,
        clusters: Optional[str] = None,
        ResultsUpload_model_name: Optional[str] = None,
        Personal_key: Optional[str] = None,
        model: Optional[list] = None,
        gpu_type: Optional[str] = None,
        instance_type: Optional[str] = None,
        resource: Optional[list] = None,
        secrets: Optional[Dict] = None,
        env: Optional[Dict] = None,
        command: Optional[str] = None,
        generate_personal_key: Optional[bool] = False,
        generate_person_key_list: Optional[list] = None,
        maximum_runtime_duration: Optional[str] = None,
        maximum_queued_duration: Optional[str] = None,
        termination_grace_period_duration: Optional[str] = None,
    ):
        logging.info(f"Cloning task with name: {name}")

        self._create_task(
            name=name,
            container=None,
            tag=None,
            gpu_type=gpu_type,
            instance_type=instance_type,
            upload_results=upload_results,
            clusters=clusters,
            maximum_runtime_duration=maximum_runtime_duration,
            maximum_queued_duration=maximum_queued_duration,
            termination_grace_period_duration=termination_grace_period_duration,
            ResultsUpload_model_name=ResultsUpload_model_name,
            Personal_key=Personal_key,
            model=model,
            resource=resource,
            secrets=secrets,
            env=env,
            command=command,
            generate_personal_key=generate_personal_key,
            generate_person_key_list=generate_person_key_list,
            clone_task=True,
            taskCreateOption=TaskCreateOptionENUM.CREATTASKSUCCESS,
        )

    def _create_task(
        self,
        name: str,
        container: str,
        tag: str,
        gpu_type: str,
        instance_type: str,
        upload_results: bool = True,
        taskCreateOption: TaskCreateOptionENUM = TaskCreateOptionENUM.CREATTASKSUCCESS,
        clusters: Optional[str] = None,
        maximum_runtime_duration: Optional[str] = None,
        maximum_queued_duration: Optional[str] = None,
        termination_grace_period_duration: Optional[str] = None,
        ResultsUpload_model_name: Optional[str] = None,
        Personal_key: Optional[str] = None,
        model: Optional[list] = None,
        resource: Optional[list] = None,
        secrets: Optional[Dict] = None,
        env: Optional[Dict] = None,
        command: Optional[str] = None,
        generate_personal_key: Optional[bool] = False,
        generate_person_key_list: Optional[list] = None,
        clone_task: Optional[bool] = False,
    ):
        """Create a new container function.
        parameters:
        -----------
        name : `str`
            The name of the task.
        container : `str`
            The container in the task.
        tag : `str`
            The tag of container in the task.
        gpu_type : `str`
            The gpu_type this task will be deployed.
        instance_type : `str`
            The instance_type this task will be deployed.
        upload_results: 'bool', default is Ture.
            True: Upload results to NGC private registry.
            False: Don't upload results to NGC private registry.
        taskCreateOption: 'TaskCreateOptionENUM', default is TaskCreateOptionENUM.CREATTASKSUCCESS
            choose the specific TaskCreateOptionENUM for negative cases.
        maximum_runtime_duration: 'str' optional
            maximum_runtime_duration set in UI
        maximum_queued_duration:'str' optional
            maximum_queued_duration set in UI
        termination_grace_period_duration:'str' optional
            termination_grace_period_duration set in UI
        ResultsUpload_model_name:'str' optional
            If set upload_results = Ture, need to configure ResultsUpload_model_name
        Personal_key:'str' optional
            Use existing key to fill in UI
        model : `list`, optional
            Model info as a list, each element is a list of model, model_version and model_name, default is None.
        resource: `list`, optional
            Resource info as a list, each element is a list of model, model_version and model_name, default is None.
        secrets: `Dict`, optional
            Secrets info as a dict.
        env : `Dict`, optional
            Environments, key value pairs, default is None.
        command : `str`, optional
            The run command of the task, default is None.
        generate_personal_key: 'bool', optional
            If need to generate key in the create task page, set it as True.
            Default is False
        generate_person_key_list: 'list', optional
            If need to generate key in the create task page. provide the generate_person_key_list.
        Default is None
        Returns:
        --------
        None
        """

        logging.info(f"Creating Task {name}...")
        # Fill Function Name
        self.page.wait_for_timeout(5000)
        input_box = self.page.locator(self.Form.elements.NameInput)
        input_box.wait_for(state="visible")
        input_box.fill(value=name, timeout=1000)
        logging.info(f"Input box value: {input_box.input_value()}")
        assert input_box.input_value() == name
        if clusters:
            self.page.wait_for_load_state("networkidle")
            # clusters_input = self.get_gpu_spec_dropdown_arrow("Clusters")
            clusters_input = self.page.locator(
                "//div[text()='Select a cluster(s)']/../following-sibling::*"
            )
            clusters_input.click()
            self.page.wait_for_load_state("load")
            self.page.get_by_role("option", name=clusters).click()
        if upload_results is False:
            self.page.locator(self.Form.elements.ResultsUploadNoneChoose).click()
        else:
            if generate_personal_key is False:
                self.page.locator(self.Form.elements.ResultsUploadModelNameInput).fill(
                    ResultsUpload_model_name
                )
                self.page.locator(self.Form.elements.ApiKeyInput).fill(Personal_key)
            else:
                self.page.locator(self.Form.elements.GenerateKeyButton).click()
                self.page.wait_for_load_state("load")
                self.page.locator(self.Form.elements.KeyNameInput).fill(
                    generate_person_key_list[0]
                )
                KeyExpirationBox_element = (
                    self.Form.TerminationGracePeriodDropDownSelector.elements[
                        "KeyExpirationBox"
                    ]
                )
                self.Form.KeyExpirationDropDownSelector.select(
                    KeyExpirationBox_element, generate_person_key_list[1]
                )
                self.page.locator(self.Form.elements.GenerateKeyConfirmButton).click()
                self.page.wait_for_load_state("load")
                self.page.locator(self.Form.elements.CopyKeyAndClose).click()
                self.page.wait_for_load_state("load")
                copied_key = self.page.evaluate("navigator.clipboard.readText()")
                self.page.locator(self.Form.elements.ApiKeyInput).fill(copied_key)
        if taskCreateOption == TaskCreateOptionENUM.NEGATIVE_MODEL_NAME:
            self.page.wait_for_selector(
                self.Form.elements.TaskCreateBannerNegativeModelName,
                state="visible",
            )
            return
        # Click Create Function Button
        if clone_task:
            self.page.get_by_role("button", name="Clone Task").click()
        else:
            self.page.locator(self.Form.elements.CreateTaskBtn).click()
        if taskCreateOption == TaskCreateOptionENUM.CREATTASKSUCCESS:
            self.page.wait_for_selector(
                self.Form.elements.TaskCreateBanner, state="visible"
            )
            self.page.wait_for_selector(self.Form.elements.TaskCreateBanner, state="hidden")
        elif (
            taskCreateOption == TaskCreateOptionENUM.NONEFORMAXRUNTIMEDURATIONGFN
            or taskCreateOption == TaskCreateOptionENUM.RUNTIMELARGERTHAN8
        ):
            self.page.wait_for_selector(
                self.Form.elements.TaskCreateBannerNonForMaxruntimeGFN, state="visible"
            )
            self.page.wait_for_selector(
                self.Form.elements.TaskCreateBannerNonForMaxruntimeGFN, state="hidden"
            )
        elif taskCreateOption == TaskCreateOptionENUM.GRACELARGERTHANRUNTIME:
            self.page.wait_for_selector(
                self.Form.elements.TaskCreateBannerGraceperiodlargerthanmaxruntimeGFN,
                state="visible",
            )
            self.page.wait_for_selector(
                self.Form.elements.TaskCreateBannerGraceperiodlargerthanmaxruntimeGFN,
                state="hidden",
            )
        elif taskCreateOption == TaskCreateOptionENUM.WITHOUTPR_OR_EXPIRED:
            self.page.wait_for_selector(
                self.Form.elements.TaskCreateBannerKeyWithoutPROrExpiredKey,
                state="visible",
            )
            self.page.wait_for_selector(
                self.Form.elements.TaskCreateBannerKeyWithoutPROrExpiredKey,
                state="hidden",
            )
        else:
            raise TypeError("Invalid taskCreateOption!")

    def get_gpu_spec_dropdown_arrow(self, label_text):
        return (
            self.page.get_by_test_id("kui-flex")
            .filter(has_text="GPU Specifications")
            .get_by_test_id("kui-multiselect")
            .filter(has_text=label_text)
            .locator('svg[data-icon-name="shapes-chevron-down"]')
        )

    def _get_result_location_model_name(self) -> str:
        """Get the result location model name with proper error handling."""
        try:
            model_name_input = self.page.locator(
                "[data-testid='kui-text-input-element'][name='resultLocationModelName']"
            )
            model_name_input.wait_for(state="attached", timeout=5000)
            return model_name_input.input_value() or ""
        except Exception as e:
            logging.error(f"Error getting result location model name: {str(e)}")
            return ""

    def _get_helm_chart(self) -> str:
        try:
            self.page.locator("//label[text()='Helm Chart']").wait_for(state="visible")
            self.page.locator("//label[text()='Helm Chart Version']").wait_for(
                state="visible"
            )
            helm_chart_value = self.page.query_selector(
                'input[name="helmChart"]'
            ).get_attribute(name="value")
            helm_tag_value = self.page.query_selector(
                'input[name="helmVersion"]'
            ).get_attribute(name="value")

            # add extra url for helm chart
            helm_chart_value = helm_chart_value.replace(":", "/charts/")

            from config.consts import CURRENT_ENV

            if CURRENT_ENV == "staging":
                prefix = "https://helm.stg.ngc.nvidia.com"
            else:
                prefix = "https://helm.ngc.nvidia.com"
            return f"{prefix}/{helm_chart_value}-{helm_tag_value}.tgz"

        except TimeoutError as e:
            logging.error(f"Timeout waiting for helm chart input: {str(e)}")
            return ""
