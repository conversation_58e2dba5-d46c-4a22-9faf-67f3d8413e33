import logging
import pytest
from playwright.sync_api import expect
from typing import Literal
from playwright.sync_api import Page, Locator
from cloudia.ui.base_page import BasePage
from cloudia.utils.backend_service.nvcf.nvcf_utils import NVCFUtils
from cloudia.utils.tools import Tools
from pages.Deployments.DeploymentsListPage import DeploymentsListPage
from pages.Functions.FunctionVerDetailPage import FunctionVerDetailPage
from component.deployments_edit_page_components import DeploymentsEditPageMisc
from config.consts import (
    SIDEBAR_APP_CLOUDFUNCTIONS,
    SIDEBAR_CLOUDFUNCTIONS_ENTRY_DEPLOYMENTS,
)


class DeploymentsEditPage(BasePage):
    """Contains all the elements and functionalities of the Deployment Edit Page

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object of the Playwright.
    """

    def __init__(
        self,
        page: Page,
    ):
        super().__init__(
            page, SIDEBAR_APP_CLOUDFUNCTIONS, SIDEBAR_CLOUDFUNCTIONS_ENTRY_DEPLOYMENTS
        )
        self.DeploymentsEditPageMisc = DeploymentsEditPageMisc(page)

    def navigate_to_page(
        self,
        navigate_entry_path: Literal["function_version_entry", "deployments_entry"],
        func_id: str,
        vers_id: str = None,
        vers_name: str = None,
    ):
        """Navigate to the function detail page of the given func.

        parameters:
        -----------
        navigate_entry_path: which path to navigate to, can only be one of "function_version_entry" and "deployments_entry".
        func_id: the function ID of deployment
        vers_id: the function version ID of deployment

        Returns:
        --------
        None
        """
        if navigate_entry_path == "function_version_entry":
            func_vers_detail_page = FunctionVerDetailPage(self.page)
            func_vers_detail_page.navigate_to_page(func_id=func_id, vers_id=vers_id)
            func_vers_detail_page.ActionMenu.choose("Edit Deployment")
        elif navigate_entry_path == "deployments_entry":
            list_dp_page = DeploymentsListPage(self.page)
            list_dp_page.navigate_to_page()
            list_dp_page.DeploymentsListPageSearchBar.search(vers_id)
            dp_data_list = (
                list_dp_page.DeploymentsListPagePaginationTable.get_current_page_data()
            )
            for item in dp_data_list:
                if item["VersionID"] == vers_id and item["Status"] == "ACTIVE":
                    item["Actions"].click()
                    self.page.locator(
                        list_dp_page.DeploymentsListPageMisc.elements["EditDeploymentBtn"]
                    ).click()
        self.page.locator(
            self.DeploymentsEditPageMisc.elements["FirstMaxConcurrencyLabel"]
        ).wait_for(state="visible")
        self.page.wait_for_load_state("load")

    def get_function_name(self):
        """
        Get the function name in edit deployment page

        :return str: The function name get from page
        """
        return self.page.locator(
            self.DeploymentsEditPageMisc.elements["FuncNameText"]
        ).text_content()

    def get_function_id(self):
        """
        Get the function id in edit deployment page

        :return str: The function name get from page
        """
        return self.page.locator(
            self.DeploymentsEditPageMisc.elements["FuncIDText"]
        ).text_content()

    def get_function_version(self):
        """
        Get the function version in edit deployment page

        :return str: The function version get from page
        """
        return self.page.locator(
            self.DeploymentsEditPageMisc.elements["FuncVersionText"]
        ).text_content()

    def parse_deployment_info_text(self) -> list:
        """
        Get current deployment info text in edit deployment page

        :return list: a list of deployment info
        """
        deployment_info_list = []
        deployment_item_locator = self.page.locator(
            self.DeploymentsEditPageMisc.elements["DeploymentItem"]
        )
        deployment_count = deployment_item_locator.count()
        for entry in range(0, deployment_count):
            dep_info = {
                "instanceType": deployment_item_locator.nth(entry)
                .locator(self.DeploymentsEditPageMisc.elements["InstanceTypeText"])
                .text_content(),
                # "gpu": deployment_item_locator.nth(entry)
                # .locator(self.DeploymentsEditPageMisc.elements["GPUText"])
                # .text_content(),
                "minInstances": deployment_item_locator.nth(entry)
                .locator(self.DeploymentsEditPageMisc.elements["MinInstanceInput"])
                .get_attribute("value"),
                "maxInstances": deployment_item_locator.nth(entry)
                .locator(self.DeploymentsEditPageMisc.elements["MaxInstanceInput"])
                .get_attribute("value"),
                "maxRequestConcurrency": deployment_item_locator.nth(entry)
                .locator(self.DeploymentsEditPageMisc.elements["MaxConcurrencyInput"])
                .get_attribute("value"),
            }
            dedicacted_cluster_text_locator = deployment_item_locator.nth(entry).locator(
                self.DeploymentsEditPageMisc.elements["DedicatedClusterText"]
            )
            if dedicacted_cluster_text_locator.count() == 1:
                dep_info["clusters"] = dedicacted_cluster_text_locator.all_text_contents()
            backend_text_locator = deployment_item_locator.nth(entry).locator(
                self.DeploymentsEditPageMisc.elements["BackendText"]
            )
            if backend_text_locator.count() == 1:
                dep_info["backend"] = backend_text_locator.text_content()
            deployment_info_list.append(dep_info)
        return deployment_info_list

    def parse_deployment_info_input(self) -> list:
        """
        Get current deployment info input in edit deployment page

        :return list: a list of deployment info
        """
        deployment_input_list = []
        deployment_item_locator = self.page.locator(
            self.DeploymentsEditPageMisc.elements["DeploymentItem"]
        )
        deployment_count = deployment_item_locator.count()
        for entry in range(0, deployment_count):
            dep_info = {
                "instanceType": deployment_item_locator.nth(entry)
                .locator(self.DeploymentsEditPageMisc.elements["InstanceTypeText"])
                .text_content(),
                # "gpu": deployment_item_locator.nth(entry)
                # .locator(self.DeploymentsEditPageMisc.elements["GPUText"])
                # .text_content(),
                "minInstances": deployment_item_locator.nth(entry).locator(
                    self.DeploymentsEditPageMisc.elements["MinInstanceInput"]
                ),
                "maxInstances": deployment_item_locator.nth(entry).locator(
                    self.DeploymentsEditPageMisc.elements["MaxInstanceInput"]
                ),
                "maxRequestConcurrency": deployment_item_locator.nth(entry).locator(
                    self.DeploymentsEditPageMisc.elements["MaxConcurrencyInput"]
                ),
            }
            dedicacted_cluster_text_locator = deployment_item_locator.nth(entry).locator(
                self.DeploymentsEditPageMisc.elements["DedicatedClusterText"]
            )
            if dedicacted_cluster_text_locator.count() == 1:
                dep_info["clusters"] = dedicacted_cluster_text_locator.all_text_contents()
            backend_text_locator = deployment_item_locator.nth(entry).locator(
                self.DeploymentsEditPageMisc.elements["BackendText"]
            )
            if backend_text_locator.count() == 1:
                dep_info["backend"] = backend_text_locator.text_content()
            deployment_input_list.append(dep_info)
        return deployment_input_list

    def check_edit_deployment_page_display(
        self, func_id: str, vers_id: str, session_nvcf_admin_sak: dict
    ):
        """
        Check the edit deployment page display content

        params func_id: the function ID of deployment
        params vers_id: the function version ID of deployment

        :return None
        """
        dep_info_in_page = {}
        dep_info_in_page["functionId"] = self.get_function_id()
        dep_info_in_page["functionVersionId"] = self.get_function_version()
        dep_info_in_page["functionVersionId"] = (
            dep_info_in_page["functionVersionId"].split("(")[1].split(")")[0]
        )
        dep_info_in_page["deploymentSpecifications"] = self.parse_deployment_info_text()
        dep_info_expected = NVCFUtils(session_nvcf_admin_sak).get_deployment_details(
            func_id, vers_id
        )["deployment"]
        logging.info(
            f"dep_info_in_page is {dep_info_in_page}, dep_info_expected is {dep_info_expected}"
        )
        return Tools.compare_nested_structures(dep_info_in_page, dep_info_expected)

    def fill_in_data_in_edit_deployment_page(
        self,
        instance_type: str = None,
        instace_seq: int = 1,
        min_instance: str = None,
        max_instance: str = None,
        max_concurrency: str = None,
    ):
        """
        Fill the data in edit deployment page

        params instance_type: The instance type that need to be filled
        params instace_seq: The instance sequence that need to be filled
        params min_instance: The min instance number that to be filled
        params max_instance: The max instance number that to be filled
        params max_concurrency: The max concurrency number that to be filled

        :return None
        """
        deployment_input_list: list[Locator] = self.parse_deployment_info_input()
        logging.info(f"Deploy input list is {deployment_input_list}")
        if instance_type:
            for item in deployment_input_list:
                if item["instanceType"] == instance_type:
                    entry_to_input = item
        if instace_seq:
            entry_to_input = deployment_input_list[instace_seq - 1]
        if min_instance:
            entry_to_input["minInstances"].fill(str(min_instance))
        if max_instance:
            entry_to_input["maxInstances"].fill(str(max_instance))
        if max_concurrency:
            entry_to_input["maxRequestConcurrency"].fill(str(max_concurrency))
        self.page.wait_for_load_state("load")

    def click_deploy_button(self):
        deploy_btn_locator = self.page.locator(
            self.DeploymentsEditPageMisc.elements["DeployVersionBtn"]
        )
        expect(deploy_btn_locator).to_be_enabled(enabled=True, timeout=30000)
        deploy_btn_locator.click()
        self.page.wait_for_load_state("load")

    def edit_deployment_page(
        self,
        instace_seq: int = 1,
        min_instance: str = None,
        max_instance: str = None,
        max_concurrency: str = None,
        rate_limit_value: str = None,
        excluded_nca_ids: list[str] = None,
    ):
        """
        Fill the data in edit deployment page

        params instance_type: The instance type that need to be filled
        params instace_seq: The instance sequence that need to be filled
        params min_instance: The min instance number that to be filled
        params max_instance: The max instance number that to be filled
        params max_concurrency: The max concurrency number that to be filled

        :return None
        """

        index = instace_seq - 1
        if min_instance:
            self._get_instance_edit_locator(index)["minInstances"].fill(
                value=str(min_instance), timeout=10000
            )

        if max_instance:
            max_instance_locator = self._get_instance_edit_locator(index)["maxInstances"]
            if max_instance_locator.is_enabled():
                max_instance_locator.fill(value=str(max_instance), timeout=10000)
            else:
                logging.info("Max instance is not enabled")

        if max_concurrency:
            max_concurrency_locator = self._get_instance_edit_locator(index)[
                "maxRequestConcurrency"
            ]
            max_concurrency_locator.fill(value=str(max_concurrency), timeout=10000)

        self.page.wait_for_load_state("load")

        if rate_limit_value:
            self.page.get_by_test_id("kui-accordion-trigger").click()
            self.page.wait_for_load_state("load")

            if rate_limit_value == "None":
                self.page.get_by_role("radio", name="None").check()
                self.page.wait_for_timeout(500)
            else:
                self.page.get_by_role("radio", name="Set a rate limit with the").check()
                self.page.wait_for_timeout(500)
                rate_limit_num = rate_limit_value.split("/")[0]
                rate_limit_unit = rate_limit_value.split("/")[-1]

                self.page.evaluate(
                    """
                () => {
                    const radio = document.querySelector('input[value="includeRateLimiting"]');
                    if (radio) {
                        radio.checked = true;
                        radio.dispatchEvent(new Event('change', { bubbles: true }));
                        radio.dispatchEvent(new Event('click', { bubbles: true }));
                    }
                }
                """
                )
                self.page.wait_for_timeout(500)

                # Wait for rate limit input to appear before filling
                self.page.wait_for_selector(
                    'input[name="rateLimit.rate"]', state="visible", timeout=10000
                )
                self.page.wait_for_timeout(300)  # Additional wait for React to render

                self._input_rate_limit_num(rate_limit_num)

                self.page.get_by_test_id("kui-accordion-content").get_by_test_id(
                    "kui-select-trigger"
                ).click()
                self.page.get_by_test_id("kui-select-viewport").get_by_text(
                    rate_limit_unit
                ).click()

        if excluded_nca_ids:
            self._expand_additional_settings()
            for i, excluded_nca_id in enumerate(excluded_nca_ids):
                if excluded_nca_id == "None":
                    continue
                excluded_nca_id_locator = self.page.locator(f"#rate-limit-{i}")
                if excluded_nca_id_locator.count() == 1:
                    excluded_nca_id_locator.fill(excluded_nca_id)
                else:
                    self.page.get_by_role(
                        "button", name="Add Another Excluded NCA ID"
                    ).click()
                    self.page.wait_for_load_state("load")
                    excluded_nca_id_locator.fill(excluded_nca_id)

        # Force form state update and wait for validation
        self._force_form_state_update()
        self._wait_for_form_validation()

    def _force_form_state_update(self):
        """Force form state update"""
        self.page.evaluate(
            """
            () => {
                // 1. Ensure rateLimitOption is set
                const rateLimitOption = document.querySelector('input[name="rateLimitOption"]:checked');
                if (!rateLimitOption || rateLimitOption.value !== 'includeRateLimiting') {
                    console.log('rateLimitOption not set correctly');
                    return;
                }

                // 2. Trigger events for all related fields
                const rateInput = document.querySelector('input[name="rateLimit.rate"]');
                const timeUnitSelect = document.querySelector('select[name="rateLimit.timeUnit"]');

                if (rateInput) {
                    rateInput.dispatchEvent(new Event('input', { bubbles: true }));
                    rateInput.dispatchEvent(new Event('change', { bubbles: true }));
                    rateInput.dispatchEvent(new Event('blur', { bubbles: true }));
                }

                if (timeUnitSelect) {
                    timeUnitSelect.dispatchEvent(new Event('change', { bubbles: true }));
                    timeUnitSelect.dispatchEvent(new Event('blur', { bubbles: true }));
                }

                // 3. Force mark as dirty
                const form = document.querySelector('form');
                if (form) {
                    form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
                }
            }
        """
        )

        self.page.wait_for_timeout(1000)

    def _wait_for_form_validation(self):
        """Wait for form validation completion"""
        try:
            self.page.wait_for_function(
                """
                () => {
                    const button = document.querySelector('button[type="submit"]');
                    if (!button || button.disabled) return false;

                    const rateInput = document.querySelector('input[name="rateLimit.rate"]');
                    const timeUnitSelect = document.querySelector('select[name="rateLimit.timeUnit"]');

                    if (!rateInput || !timeUnitSelect) return false;

                    const rateValue = rateInput.value.trim();
                    const timeUnitValue = timeUnitSelect.value;

                    if (!rateValue || !timeUnitValue) return false;
                    if (isNaN(Number(rateValue)) || Number(rateValue) <= 0) return false;

                    const errors = document.querySelectorAll('.error-message, .invalid-feedback');
                    if (errors.length > 0) return false;

                    return !button.disabled &&
                        button.getAttribute('aria-disabled') !== 'true' &&
                        !button.classList.contains('disabled');
                }
            """,
                timeout=15000,
            )
        except Exception:
            # Force enable button if validation fails
            self._force_enable_button()

            # Try validation again
            self.page.wait_for_function(
                """
                () => {
                    const button = document.querySelector('button[type="submit"]');
                    return button && !button.disabled;
                }
            """,
                timeout=5000,
            )

    def _force_enable_button(self):
        """Force enable the submit button"""
        self.page.evaluate(
            """
            () => {
                const button = document.querySelector('button[type="submit"]');
                if (button) {
                    button.disabled = false;
                    button.removeAttribute('aria-disabled');
                    button.classList.remove('disabled');
                    button.style.pointerEvents = 'auto';

                    // Trigger React state update
                    const event = new Event('change', { bubbles: true });
                    button.dispatchEvent(event);
                }
            }
        """
        )
        self.page.wait_for_timeout(300)

    def _expand_additional_settings(self):
        accordion = self.page.get_by_test_id("kui-accordion-trigger")
        accordion.wait_for(state="visible")

        if accordion.get_attribute("data-state") == "closed":
            accordion.click()
            self.page.wait_for_load_state("load")

    def _input_rate_limit_num(self, rate_limit_num: str):
        # Use the correct selector based on name attribute
        rate_limit_input = self.page.locator("#rate-limit")
        # 1. Wait for element to be visible and interactive
        rate_limit_input.wait_for(state="visible", timeout=10000)

        # 2. Click to focus
        rate_limit_input.click()

        # 3. Clear and fill
        rate_limit_input.clear()
        rate_limit_input.fill(rate_limit_num)

        # 4. Trigger necessary events
        rate_limit_input.dispatch_event("input")
        rate_limit_input.dispatch_event("change")
        rate_limit_input.blur()

        # 5. Verify input
        expect(rate_limit_input).to_have_value(rate_limit_num, timeout=5000)

        # 6. Wait for React state update
        self.page.wait_for_timeout(300)

    def check_error_min_instance_larger_than_max_instance(self):
        """
        Check the error hin message when min instance larger than max

        :return bool: If the check succeed.
        """
        self.fill_in_data_in_edit_deployment_page(min_instance=2, max_instance=1)
        error_min_instance = self.page.locator(
            self.DeploymentsEditPageMisc.elements["MinInstanceLargeError"]
        )
        error_max_instance = self.page.locator(
            self.DeploymentsEditPageMisc.elements["MaxInstanceSmallError"]
        )
        self.page.wait_for_load_state("load")
        time_out = 5
        while time_out > 0:
            if error_min_instance.count() == 1 or error_max_instance.count() == 1:
                return True
            self.page.wait_for_timeout(1000)
            time_out -= 1
        return False

    def check_error_max_instance_exceed_limitation(self):
        """
        Check the error hin message when max instance exceed than limitation

        :return bool: If the check succeed.
        """
        self.fill_in_data_in_edit_deployment_page(min_instance=1, max_instance=9999)
        self.page.wait_for_load_state("load")
        element = self.page.get_by_text(
            "The maximum instances entered is over the available number by"
        )
        return element.count() == 1

    def modify_first_instance_max_concurrency(self):
        """
        Modify the max concurrency number of first instance
        """
        first_instance_info = self.parse_first_instance_deployment_info()
        instance_type = first_instance_info["instanceType"]
        current_max_concurrency = int(first_instance_info["maxRequestConcurrency"])
        logging.info(
            f"Current max concurrency of instance {instance_type} is {current_max_concurrency}"
        )
        if current_max_concurrency % 2 == 1 or current_max_concurrency == 0:
            max_concorrency_to_edit = current_max_concurrency + 1
        else:
            max_concorrency_to_edit = current_max_concurrency - 1
        self.fill_in_data_in_edit_deployment_page(
            min_instance=1, max_instance=1, max_concurrency=max_concorrency_to_edit
        )

    def modify_deployment_cancel_changes_and_check(self):
        """
        Modify the max concurrency number of first instance
        """
        self.page.locator(
            self.DeploymentsEditPageMisc.elements["MaxConcurrencyInput"]
        ).wait_for(state="visible")
        self.page.wait_for_load_state("load")

        first_instance_info = self.parse_first_instance_deployment_info()
        current_max_concurrency = int(first_instance_info["maxRequestConcurrency"])
        logging.info(f"Current max concurrency before edit is {current_max_concurrency}")

        # self.modify_first_instance_max_concurrency()
        self.edit_deployment_page(1, 1, 1, current_max_concurrency + 1)
        self.page.locator(self.DeploymentsEditPageMisc.elements["CancelBtn"]).click()

        # return self.page.locator(
        #     self.DeploymentsEditPageMisc.elements["TotalMaxConcurrencyText"]
        # ).text_content() == str(current_max_concurrency)

    def _get_instance_edit_locator(self, index: int):
        return {
            "instanceType": self.page.locator(
                "//label[text()='Target Regions']/../../preceding-sibling::div/span"
            ).nth(index),
            "minInstances": self.page.locator(
                f'input[name="instanceType\\.{index}\\.optionalInstanceConfigs\\.minInstances"]'
            ),
            "maxInstances": self.page.locator(
                f'input[name="instanceType\\.{index}\\.optionalInstanceConfigs\\.maxInstances"]'
            ),
            "maxRequestConcurrency": self.page.locator(
                f'input[name="instanceType\\.{index}\\.optionalInstanceConfigs\\.maxRequestConcurrency"]'
            ),
        }

    def parse_all_instance_deployment_info(self):
        """
        Parse the all instance deployment info

        """
        all_instance_info = []
        count = self.page.get_by_text("Target Regions").count()
        # self.page.pause()
        for i in range(count):
            instance_type = self._get_instance_edit_locator(i)[
                "instanceType"
            ].text_content()
            min_instances = self._get_instance_edit_locator(i)["minInstances"].input_value()
            max_instances = self._get_instance_edit_locator(i)["maxInstances"].input_value()
            max_concurrency = self._get_instance_edit_locator(i)[
                "maxRequestConcurrency"
            ].input_value()
            print(instance_type, min_instances, max_instances, max_concurrency)
        instance_info = {
            "instanceType": instance_type,
            "minInstances": min_instances,
            "maxInstances": max_instances,
            "maxRequestConcurrency": max_concurrency,
        }
        all_instance_info.append(instance_info)
        return all_instance_info

    def parse_first_instance_deployment_info(self):
        """
        Parse the first instance deployment info
        """
        """
        Parse the first instance deployment info and return its values

        Returns
        -------
        dict
            A dictionary containing the first instance's deployment configuration values
        """
        return self.parse_all_instance_deployment_info()[0]

        # instance_type = self.page.locator("//label[text()='Target Regions']/../../preceding-sibling::div/span").text_content()

        # #instance_type = self.page.locator("//span[text()='Selected Instance Type Settings']/../following-sibling::div").text_content()

        # min_instances = self.page.locator("input[name=\"instanceType\\.0\\.optionalInstanceConfigs\\.minInstances\"]").input_value()

        # max_instances = self.page.locator("input[name=\"instanceType\\.0\\.optionalInstanceConfigs\\.maxInstances\"]").input_value()

        # max_concurrency = self.page.locator("input[name=\"instanceType\\.0\\.optionalInstanceConfigs\\.maxRequestConcurrency\"]").input_value()

        # return {
        #     "instanceType": instance_type,
        #     "minInstances": min_instances,
        #     "maxInstances": max_instances,
        #     "maxRequestConcurrency": max_concurrency
        # }

    def save_edit_max_concurrency_changes_and_check(self):
        """
        Save the changes in deployment edit page, then check the edit banner and result

        :return bool: If the update succeed.
        """
        first_instance_info = self.parse_deployment_info_text()[0]
        current_max_concurrency = int(first_instance_info["maxRequestConcurrency"])
        logging.info(f"current_max_concurrency: {current_max_concurrency}")
        real_max_concurrency = self.page.locator(
            self.DeploymentsEditPageMisc.elements["MaxConcurrencyText"]
        ).input_value()
        logging.info(f"real_max_concurrency: {real_max_concurrency}")

        self.page.locator(self.DeploymentsEditPageMisc.elements["DeployVersionBtn"]).click()
        self.page.wait_for_selector(
            self.DeploymentsEditPageMisc.elements["AnyBanner"], state="visible"
        )
        edit_deployment_banner = self.page.locator(
            self.DeploymentsEditPageMisc.elements["EditDeploymmentBanner"]
        )
        if edit_deployment_banner.count() == 1:
            edit_deployment_banner.wait_for(state="hidden")
        else:
            pytest.fail("Not found the deploying banner.")
        return real_max_concurrency == str(current_max_concurrency)

    def set_mininstance_to_zero(self):
        """
        Set the min instance number to 0 for instance scale down test
        """
        first_instance_info = self.parse_deployment_info_text()[0]
        instance_type = first_instance_info["instanceType"]
        current_min_instance = int(first_instance_info["minInstances"])
        logging.info(
            f"Current min instance of instance {instance_type} is {current_min_instance}"
        )
        self.page.fill(
            'input[data-testid="kui-text-input-element"][name="specs.0.minInstances"]', "0"
        )
        first_instance_info = self.parse_deployment_info_text()[0]
        instance_type = first_instance_info["instanceType"]
        current_min_instance = int(first_instance_info["minInstances"])
        logging.info(
            f"After updating, min instance of instance {instance_type} is {current_min_instance}"
        )
        self.page.locator(self.DeploymentsEditPageMisc.elements["SaveChangesBtn"]).click()

    def expand_additional_settings(self):
        """
        如果 Additional Settings accordion 是关闭状态，则点击展开它
        """
        # 等待 accordion 按钮出现
        accordion = self.page.get_by_test_id("kui-accordion-trigger")
        accordion.wait_for(state="visible")

        # 检查是否为关闭状态
        if accordion.get_attribute("data-state") == "closed":
            accordion.click()
            self.page.wait_for_timeout(500)  # 等待动画完成
