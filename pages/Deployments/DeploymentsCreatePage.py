import logging
import json
import re
from typing import Optional

from playwright.sync_api import Page
from cloudia.ui.base_page import BasePage

from cloudia.utils.backend_service.nvcf.nvcf_utils import NVCFUtils
from pages.Deployments.DeploymentsListPage import DeploymentsListPage
from pages.Functions.FunctionsListPage import FunctionListPage
from component.deployments_create_page_components import (
    DeploymentsCreatePageMisc,
    DeploymentsCreatePageFunctionNameSelector,
    DeploymentsCreatePageFunctionVersionSelector,
    DeploymentsCreatePageInstanceTypePagePagination,
    DeploymentsCreatePageInstanceRegionsFilterDropDownSelector,
    DeploymentsCreatePageInstanceClustersFilterDropDownSelector,
    DeploymentsCreatePageInstanceAttributesFilterDropDownSelector,
    DeploymentsCreatePageDeploymentSpecificationsForm,
)
from config.consts import (
    SIDEBAR_APP_CLOUDFUNCTIONS,
    SIDEBAR_CLOUDFUNCTIONS_ENTRY_DEPLOYMENTS,
)


class DeploymentsCreatePage(BasePage):
    """Contains all the elements and functionalities of the Deployment Page

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object of the Playwright.
    """

    def __init__(
        self,
        page: Page,
    ):
        super().__init__(
            page, SIDEBAR_APP_CLOUDFUNCTIONS, SIDEBAR_CLOUDFUNCTIONS_ENTRY_DEPLOYMENTS
        )
        self.DeploymentsCreatePageMisc = DeploymentsCreatePageMisc(page)
        self.FunctionNameSelector = DeploymentsCreatePageFunctionNameSelector(page)
        self.FunctionVersionSelector = DeploymentsCreatePageFunctionVersionSelector(page)
        self.InstanceTypePagePagination = DeploymentsCreatePageInstanceTypePagePagination(
            page
        )
        self.InstanceRegionsFilterDropDownSelector = (
            DeploymentsCreatePageInstanceRegionsFilterDropDownSelector(page)
        )
        self.InstanceClustersFilterDropDownSelector = (
            DeploymentsCreatePageInstanceClustersFilterDropDownSelector(page)
        )
        self.InstanceAttributesFilterDropDownSelector = (
            DeploymentsCreatePageInstanceAttributesFilterDropDownSelector(page)
        )
        self.DeploymentSpecificationsForm = (
            DeploymentsCreatePageDeploymentSpecificationsForm(page)
        )

    def navigate_to_page(self):
        """Navigate to the function detail page of the given func.

        parameters:
        -----------

        Returns:
        --------
        None
        """
        list_dp_page = DeploymentsListPage(self.page)
        list_dp_page.navigate_to_page()
        self.page.wait_for_timeout(3000)
        self.page.locator(
            list_dp_page.DeploymentsListPageMisc.elements["TotalInvocationsTitle"]
        ).wait_for()
        self.page.locator(
            list_dp_page.DeploymentsListPageMisc.elements["DeployFuncVersBtn"]
        ).nth(0).click()

        self.page.get_by_role("button", name="Deploy Version").nth(0).wait_for()

    def _input_with_keyboard_events(
        self, selector: str, value: str, clear_first: bool = True
    ):
        """Helper method for inputs that require keyboard events to trigger validation.

        Args:
            selector: The selector for the input element
            value: The value to input
            clear_first: Whether to clear the input first (default: True)
        """
        input_element = self.page.locator(selector)
        input_element.click()  # Ensure focus

        if clear_first:
            # Clear existing value
            input_element.clear()  # Delete selection
            self.page.wait_for_timeout(100)  # Small delay after clearing

        # Input new value
        input_element.press_sequentially(str(value))
        self.page.wait_for_timeout(500)  # Wait for validation

        # Trigger blur event to ensure validation
        input_element.evaluate("el => el.blur()")
        self.page.wait_for_timeout(500)  # Wait for validation after blur

    def deploy_function_version(
        self,
        page: Page,
        func_id: str,
        func_vers_id: str,
        gpu_list: list[str],
        wait_until_deployed: bool = True,
        edit_configuration_in_review: bool = False,
        helm_chart_overrides_data: dict = None,
        instance_regions_filter_list: list[str] = None,
        instance_clusters_filter_list: list[str] = None,
        instance_attributes_filter_list: list[str] = None,
        instance_detail_list: list[dict] = None,
        session_nvcf_admin_sak: dict = None,
        rate_limit: Optional[int] = None,
    ):
        """Deploy a function version with parameters

        parameters:
        -----------
        page (Required): The page that need to be used
        func_id (Required): The function ID that need to be deploy
        func_vers_id (Required): The function version ID that need to be deploy
        gpu_list (Required): The GPU list that need to be chose in multiple choose
        wait_until_deployed (Optional): if need to wait until deployed
        edit_configuration_in_review (Optional): if need to edit the configuration in review page
        helm_chart_overrides_data (Optional): the helm chart overrides data when deploy helm chart functions
        instance_regions_list (Optional): the instance filter regions list
        instance_clusters_filter_list (Optional): the instance cluster filter list
        instance_attributes_list (Optional): the instance filter attributes list
        instance_list (Optional): a list of each instance as a dict, dict keys include:
            name (Required): the name of instance
            target_region_list (Optional): the target list to deploy regions of this instance
            min_instances (Optional): the min number of instances to deploy
            max_instances (Optional): the max number of instances to deploy
            max_concurrency (Optional): max concurrency of the deployment
            target_attributes_list (Optional): the target list to deploy attributes of this instance
        """
        if (
            "https://nvcf.ngc.nvidia.com/deployments/create" in page.url
            or "https://nvcf.canary.ngc.nvidia.com/deployments/create" in page.url
            or "https://nvcf.stg.ngc.nvidia.com/deployments/create" in page.url
        ):
            logging.info(f"Current URL {page.url} is what you want to enter. Skipping...")
        else:
            logging.info("Attempting to enter function deployment page first...")
            self.navigate_to_page()

        self.page.wait_for_timeout(10000)
        create_func_page_turn_flag = False
        # Select Function Name and Function Version
        func_name_content = (
            "//label[text()='Function Name' or text()='Function ID']/following::div[1]"
        )
        func_vers_content = "//label[text()='Function Version' or text()='Function Version ID']/following::div[1]"

        func_name_input_element = self.FunctionNameSelector.elements["FuncNameInput"]
        func_vers_input_element = self.FunctionVersionSelector.elements["FuncVersionInput"]

        if self.page.locator(func_name_content).text_content() == "Select a function":
            self.FunctionNameSelector.search_and_select(func_name_input_element, func_id)
        else:
            create_func_page_turn_flag = True
            func_id = self.page.locator(func_name_content).text_content()
            logging.info(
                f"Function name {func_id} already exists in the deployment create page. Skipping..."
            )

        self.page.wait_for_timeout(1000)

        if (
            self.page.locator(func_vers_content).text_content()
            == "Select a function version"
        ):
            self.FunctionNameSelector.search_and_select(
                func_vers_input_element, func_vers_id
            )
        else:
            func_vers_id_full = self.page.locator(func_vers_content).text_content()
            func_vers_id = func_vers_id_full.split("(")[-1].split(")")[0]
            logging.info(
                f"Function version {func_vers_id} already exists in the deployment create page. Skipping..."
            )

        # Select GPU types
        for item in gpu_list:
            gpu_checkbox_element = self.DeploymentsCreatePageMisc.elements[
                "GPUTypeCheckInputbyName"
            ].format(item)
            self.page.locator(gpu_checkbox_element).wait_for(timeout=60000)
            if self.page.locator(gpu_checkbox_element).count() == 1:
                self.page.locator(gpu_checkbox_element).click()
            else:
                logging.error(
                    f"Cannot find the GPU type {item} checkbox in deployment create page."
                )

        # Additional wait to ensure all dropdowns are enabled
        self.page.wait_for_timeout(5000)
        if instance_regions_filter_list:
            instance_regions_filter_element = (
                self.InstanceRegionsFilterDropDownSelector.elements[
                    "InstanceTypeRegionsFilterSelector"
                ]
            )

            self.InstanceRegionsFilterDropDownSelector.multiple_select(
                drop_down_element=instance_regions_filter_element,
                select_entries_list=instance_regions_filter_list,
            )

        # Select Instance type Clusters if needed
        if instance_clusters_filter_list:
            instance_clusters_filter_element = (
                self.InstanceClustersFilterDropDownSelector.elements[
                    "InstanceTypeClustersFilterSelector"
                ]
            )
            self.InstanceClustersFilterDropDownSelector.multiple_select(
                instance_clusters_filter_element, instance_clusters_filter_list
            )

        # Select Instance type Attributes if needed
        if instance_attributes_filter_list:
            instance_attributes_filter_element = (
                self.InstanceAttributesFilterDropDownSelector.elements[
                    "InstanceTypeAttributesFilterSelector"
                ]
            )
            self.InstanceAttributesFilterDropDownSelector.multiple_select(
                instance_attributes_filter_element, instance_attributes_filter_list
            )

        # Select instance type if needed
        if instance_detail_list:
            instance_names_list = [d["name"] for d in instance_detail_list if "name" in d]
            self.page.locator(
                self.DeploymentsCreatePageMisc.elements["RefreshBtn"]
            ).wait_for(state="visible")
            logging.info(f"Instance name LIST {instance_names_list}")
            self.InstanceTypePagePagination.choose_instances_items_in_multiple_selector(
                instance_names_list
            )

        # Select deployment specifications, if any key exists except 'name', deployment specifications should be filled
        if instance_detail_list:
            for instance_dict in instance_detail_list:
                if any(value for key, value in instance_dict.items() if key != "name"):
                    self.DeploymentSpecificationsForm.fill_in_deployment_specifications(
                        instance_dict
                    )

        # Fill in the helm chart override data if needed
        if helm_chart_overrides_data:
            if (
                not self.page.get_by_test_id("kui-accordion-trigger").get_attribute(
                    "aria-expanded"
                )
                == "true"
            ):
                self.page.get_by_test_id("kui-accordion-trigger").click()
                self.page.wait_for_load_state("load")

            self.page.locator(
                self.DeploymentsCreatePageMisc.elements["HelmChartOverridesInput"]
            ).fill(json.dumps(helm_chart_overrides_data))

        self.page.wait_for_load_state("load")
        self.page.wait_for_load_state("networkidle", timeout=30000)

        if rate_limit:
            rate_limit_value = rate_limit.get("rate_limit_value")
            exclued_nca_ids = rate_limit.get("exclued_nca_ids")
            helm_chart_override = rate_limit.get("helm_chart_override")
        else:
            rate_limit_value = None
            exclued_nca_ids = None
            helm_chart_override = None

        if rate_limit:
            if (
                not self.page.get_by_test_id("kui-accordion-trigger").get_attribute(
                    "aria-expanded"
                )
                == "true"
            ):
                self.page.get_by_test_id("kui-accordion-trigger").click()
                self.page.wait_for_load_state("load")
            if rate_limit_value is None or rate_limit_value == "":
                self.page.get_by_role("radio", name="None").click()

            if rate_limit_value:
                try:
                    if rate_limit_value is None or rate_limit_value == "":
                        self.page.get_by_role("radio", name="None").click()
                    else:
                        rate_limit_num = rate_limit_value.split("/")[0]
                        rate_limit_unit = rate_limit_value.split("/")[-1]
                        self.page.get_by_role(
                            "radio", name="Set a rate limit with the"
                        ).click()
                        self.page.wait_for_load_state("load")
                        self.page.wait_for_load_state("networkidle")

                        # 使用辅助方法来处理rate limit输入
                        self._input_with_keyboard_events(
                            "#rate-limit", rate_limit_num, True
                        )

                        try:
                            rate_limit_float = float(rate_limit_num)
                            if rate_limit_float < 0 or rate_limit_float != int(
                                rate_limit_float
                            ):
                                assert (
                                    self.page.get_by_test_id("kui-text")
                                    .get_by_text("Rate must be a number")
                                    .is_visible()
                                ), "Expected error message 'Rate must be a number' not found"
                                self.page.go_back(wait_until="load")
                                return
                        except ValueError:
                            raise ValueError("Rate limit must be a non-negative integer")

                        self.page.get_by_test_id("kui-accordion-content").get_by_test_id(
                            "kui-select-trigger"
                        ).click()
                        self.page.get_by_test_id("kui-select-viewport").get_by_text(
                            rate_limit_unit
                        ).click()
                except Exception as e:
                    logging.error(f"Error setting rate limit: {e}")

            if exclued_nca_ids:
                for i, excluded_nca_id in enumerate(exclued_nca_ids):
                    if excluded_nca_id == "None":
                        continue
                    self.page.locator(f"#rate-limit-{i}").fill(excluded_nca_id)
                    if i < len(exclued_nca_ids) - 1:
                        self.page.get_by_role(
                            "button", name="Add Another Excluded NCA ID"
                        ).click()

            if helm_chart_override:
                self.page.locator(
                    self.DeploymentsCreatePageMisc.elements["HelmChartOverridesInput"]
                ).fill(json.dumps(helm_chart_override))
                # self.page.get_by_role("textbox").filter(
                #     has_text='{ "key_one": "<value>", "'
                # ).locator("div").fill(json.dumps(helm_chart_override))

        deploy_button = self.page.get_by_role("button", name="Deploy Version")
        deploy_button.wait_for(state="visible", timeout=30000)
        if deploy_button.is_disabled():
            for attempt in range(10):
                self.page.wait_for_timeout(2000)
                is_disabled = deploy_button.is_disabled()
                if not is_disabled:
                    break

            if attempt == 9:
                raise Exception("Deploy button is still disabled after 20 seconds")
            deploy_button.click()
        else:
            deploy_button.click()
        banner_text = self.page.locator('[data-testid="ngc-toast"]').inner_text()
        print(f"Banner text: {banner_text}")
        assert (
            "is being deployed" in banner_text
        ), f"Deployment is not being deployed with banner text: {banner_text}"
        self.page.wait_for_selector(
            self.DeploymentsCreatePageMisc.elements["DeployingBanner"], state="hidden"
        )

        # Check the deployment in delpoyment list page
        function_name = NVCFUtils(session_nvcf_admin_sak).get_function_name_by_ids(
            func_id, func_vers_id
        )
        func_list_page = FunctionListPage(page)
        func_list_page.navigate_to_page()
        assert func_list_page.FunctionPagination.check_if_function_exists_by_search(
            function_name
        ), "The deployed function not listed."

        # Wait until the deployment to be deployed
        if wait_until_deployed:
            logging.info(f"Waiting function version {func_vers_id} to be deployed")
            deployment_list_page = DeploymentsListPage(self.page)
            assert deployment_list_page.wait_deployment_to_expected_status(
                func_vers_id, "ACTIVE"
            ), "Deploying failed. "
        else:
            logging.info("No need to wait the function deployed")

        if create_func_page_turn_flag:
            return func_id, func_vers_id

    def deploy_function_version_failed(
        self,
        page: Page,
        func_id: str,
        func_vers_id: str,
        gpu_list: list[str],
        wait_until_deployed: bool = True,
        edit_configuration_in_review: bool = False,
        helm_chart_overrides_data: dict = None,
        instance_regions_filter_list: list[str] = None,
        instance_clusters_filter_list: list[str] = None,
        instance_attributes_filter_list: list[str] = None,
        instance_detail_list: list[dict] = None,
        session_nvcf_admin_sak: dict = None,
        is_return_back: bool = False,
    ):
        """Deploy a function version with parameters

        parameters:
        -----------
        page (Required): The page that need to be used
        func_id (Required): The function ID that need to be deploy
        func_vers_id (Required): The function version ID that need to be deploy
        gpu_list (Required): The GPU list that need to be chose in multiple choose
        wait_until_deployed (Optional): if need to wait until deployed
        edit_configuration_in_review (Optional): if need to edit the configuration in review page
        helm_chart_overrides_data (Optional): the helm chart overrides data when deploy helm chart functions
        instance_regions_list (Optional): the instance filter regions list
        instance_clusters_filter_list (Optional): the instance cluster filter list
        instance_attributes_list (Optional): the instance filter attributes list
        instance_list (Optional): a list of each instance as a dict, dict keys include:
            name (Required): the name of instance
            target_region_list (Optional): the target list to deploy regions of this instance
            min_instances (Optional): the min number of instances to deploy
            max_instances (Optional): the max number of instances to deploy
            max_concurrency (Optional): max concurrency of the deployment
            target_attributes_list (Optional): the target list to deploy attributes of this instance
        """
        if (
            "https://nvcf.ngc.nvidia.com/deployments/create" in page.url
            or "https://nvcf.canary.ngc.nvidia.com/deployments/create" in page.url
        ):
            logging.info(f"Current URL {page.url} is what you want to enter. Skipping...")
        else:
            logging.info("Attempting to enter function deployment page first...")
            self.navigate_to_page()

        # Select Function Name and Function Version
        func_name_input_element = self.FunctionNameSelector.elements["FuncNameInput"]
        func_vers_input_element = self.FunctionVersionSelector.elements["FuncVersionInput"]

        self.FunctionNameSelector.search_and_select(func_name_input_element, func_id)

        self.FunctionNameSelector.search_and_select(func_vers_input_element, func_vers_id)

        # Select GPU types
        for item in gpu_list:
            gpu_checkbox_element = self.DeploymentsCreatePageMisc.elements[
                "GPUTypeCheckInputbyName"
            ].format(item)
            self.page.locator(gpu_checkbox_element).wait_for(timeout=60000)
            if self.page.locator(gpu_checkbox_element).count() == 1:
                self.page.locator(gpu_checkbox_element).click()
            else:
                logging.error(
                    f"Cannot find the GPU type {item} checkbox in deployment create page."
                )

        # Select Instance type Regions if needed
        if instance_regions_filter_list:
            instance_regions_filter_element = (
                self.InstanceRegionsFilterDropDownSelector.elements[
                    "InstanceTypeRegionsFilterSelector"
                ]
            )
            self.InstanceRegionsFilterDropDownSelector.multiple_select(
                instance_regions_filter_element, instance_regions_filter_list
            )

        # Select Instance type Clusters if needed
        if instance_clusters_filter_list:
            instance_clusters_filter_element = (
                self.InstanceClustersFilterDropDownSelector.elements[
                    "InstanceTypeClustersFilterSelector"
                ]
            )
            self.InstanceClustersFilterDropDownSelector.multiple_select(
                instance_clusters_filter_element, instance_clusters_filter_list
            )

        # Select Instance type Attributes if needed
        if instance_attributes_filter_list:
            instance_attributes_filter_element = (
                self.InstanceAttributesFilterDropDownSelector.elements[
                    "InstanceTypeAttributesFilterSelector"
                ]
            )
            self.InstanceAttributesFilterDropDownSelector.multiple_select(
                instance_attributes_filter_element, instance_attributes_filter_list
            )

        # Select instance type if needed
        if instance_detail_list:
            instance_names_list = [d["name"] for d in instance_detail_list if "name" in d]
            self.page.locator(
                self.DeploymentsCreatePageMisc.elements["RefreshBtn"]
            ).wait_for(state="visible")
            logging.info(f"Instance name LIST {instance_names_list}")
            self.InstanceTypePagePagination.choose_instances_items_in_multiple_selector(
                instance_names_list
            )

        # Select deployment specifications, if any key exists except 'name', deployment specifications should be filled
        if instance_detail_list:
            for instance_dict in instance_detail_list:
                if any(value for key, value in instance_dict.items() if key != "name"):
                    self.DeploymentSpecificationsForm.fill_in_deployment_specifications(
                        instance_dict
                    )

        # Fill in the helm chart override data if needed
        if helm_chart_overrides_data:
            if (
                not self.page.get_by_test_id("kui-accordion-trigger").get_attribute(
                    "aria-expanded"
                )
                == "true"
            ):
                self.page.get_by_test_id("kui-accordion-trigger").click()
                self.page.wait_for_load_state("load")

            self.page.locator(
                self.DeploymentsCreatePageMisc.elements["HelmChartOverridesInput"]
            ).fill(json.dumps(helm_chart_overrides_data))

        self.DeploymentsCreatePageMisc.deploy_version_btn()
        if is_return_back:
            return
        # Check the deployment in delpoyment list page
        function_name = NVCFUtils(session_nvcf_admin_sak).get_function_name_by_ids(
            func_id, func_vers_id
        )
        func_list_page = FunctionListPage(page)
        func_list_page.navigate_to_page()
        assert func_list_page.FunctionPagination.check_if_function_exists_by_search(
            function_name
        ), "The deployed function not listed."

        # Wait until the deployment to be deployed
        if wait_until_deployed:
            logging.info(f"Waiting function version {func_vers_id} to be deployed")
            deployment_list_page = DeploymentsListPage(self.page)
            assert deployment_list_page.wait_deployment_to_expected_status(
                func_vers_id, "ERROR"
            ), "Deploying not as expected. "
        else:
            logging.info("No need to wait the function deployed")

    def check_if_function_listed_in_deployment_candidate(
        self,
        func_id: str,
        func_vers_id: str,
    ):
        self.navigate_to_page()

        # Select Function Name and Function Version
        func_name_input_element = self.FunctionNameSelector.elements["FuncNameInput"]
        func_vers_input_element = self.FunctionVersionSelector.elements["FuncVersionInput"]

        if_found_func_id = self.FunctionNameSelector.check_if_select_candidate_exists(
            func_name_input_element, func_id
        )

        if_found_func_vers_id = self.FunctionNameSelector.check_if_select_candidate_exists(
            func_vers_input_element, func_vers_id
        )

        return if_found_func_id and if_found_func_vers_id

    def check_error_hint_when_beyond_max_instances(
        self,
        func_id: str,
        func_vers_id: str,
        gpu_list: list[str],
    ):
        """Check the error hint when deploy instances beyond the max instances number

        parameters:
        -----------
        page (Required): The page that need to be used
        func_id (Required): The function ID that need to be deploy
        func_vers_id (Required): The function version ID that need to be deploy
        gpu_list (Required): The GPU list that need to be chose in multiple choose

        return bool: If the error hint found as expected
        """
        self.navigate_to_page()

        # Select Function Name and Function Version
        func_name_input_element = self.FunctionNameSelector.elements["FuncNameInput"]
        func_vers_input_element = self.FunctionVersionSelector.elements["FuncVersionInput"]

        self.FunctionNameSelector.search_and_select(func_name_input_element, func_id)
        self.FunctionNameSelector.search_and_select(func_vers_input_element, func_vers_id)

        # Select GPU types
        for item in gpu_list:
            gpu_checkbox_element = self.DeploymentsCreatePageMisc.elements[
                "GPUTypeCheckInputbyName"
            ].format(item)
            self.page.locator(gpu_checkbox_element).wait_for(timeout=60000)
            if self.page.locator(gpu_checkbox_element).count() == 1:
                self.page.locator(gpu_checkbox_element).click()
            else:
                logging.error(
                    f"Cannot find the GPU type {item} checkbox in deployment create page."
                )

        self.page.locator(
            self.FunctionVersionSelector.elements["FirstMaxInstancesInput"]
        ).fill("9999")
        hint_error = self.page.locator(
            self.FunctionVersionSelector.elements["ErrorHintText"]
        ).text_content()
        pattern = r"^Over allocation$"
        return re.match(pattern, hint_error)

    def check_the_pop_up_validation_issues_in_create_deployment_page(self):
        """
        Check the pop up validation issues in create task page
        return: check_flag: True or False
                error_message: list of check error message
                real_message: list of helm reval error message
        """
        check_flag = True
        error_message = []
        real_message = []
        # Check issues count
        issues_count = self.page.locator('xpath=//p[contains(text(), "Issues (")]')
        if not issues_count.is_visible():
            error_message.append("Issues count not visible")
            check_flag = False
        logging.info("✓ Issues count verified")
        issues_count_text = issues_count.text_content()
        issues_count_text = issues_count_text.split("(")[1].split(")")[0]
        logging.info(f"issues_count_text: {issues_count_text}")
        next_button = self.page.locator(
            "xpath=//button[@data-testid='kui-button']//*[local-name()='svg' and @data-icon-name='shapes-chevron-right']"
        ).last
        while True:
            issue_code = self.page.locator(
                "//p[contains(text(), 'Issues (')]/../following-sibling::*//pre//code"
            ).all()
            for code in issue_code:
                issue_code_text = code.text_content()
                logging.info(f"issue_code_text: {issue_code_text}")
                if self.page.get_by_text(issue_code_text).count() == 2:
                    real_message.append(issue_code_text)
                else:
                    error_message.append(f"issue_code_text: {issue_code_text} not found")
                    check_flag = False
            if not next_button.is_disabled():
                next_button.click()
                self.page.wait_for_load_state("load")
            else:
                break
        if len(real_message) != int(issues_count_text):
            error_message.append(
                f"real_message: {real_message} not found, expected: {issues_count_text}"
            )
            check_flag = False

        # Check copy button exists
        copy_button = self.page.locator(
            "xpath=//button[@data-testid='kui-button']//*[local-name()='svg' and @data-icon-name='common-copy-generic']"
        ).last
        if not copy_button.is_visible():
            error_message.append("Copy button not visible")
            check_flag = False
        logging.info("✓ Copy button found")

        # Check navigation buttons
        prev_button = self.page.locator(
            "xpath=//button[@data-testid='kui-button']//*[local-name()='svg' and @data-icon-name='shapes-chevron-left']"
        ).last

        page_number = self.page.locator(
            "xpath=//button[@data-testid='kui-button' and text()='1']"
        ).last

        if not prev_button.is_visible():
            error_message.append("Previous button not found")
            check_flag = False
        if not next_button.is_visible():
            error_message.append("Next button not found")
            check_flag = False
        if not page_number.is_visible():
            error_message.append("Page number button not found")
            check_flag = False
        logging.info("✓ Navigation buttons verified")

        # Check last updated timestamp
        timestamp = self.page.locator('xpath=//p[contains(text(), "Last updated:")]')
        if not timestamp.is_visible():
            error_message.append("Timestamp not visible")
            check_flag = False
        logging.info(f"✓ Last updated timestamp: {timestamp.text_content()}")

        # Check Helm Chart Security Guidelines link
        security_link = self.page.locator(
            'xpath=//a[contains(text(), "Helm Chart Security Guidelines")]'
        ).last
        if not security_link.is_visible():
            error_message.append("Security guidelines link not visible")
            check_flag = False
        logging.info("✓ Security guidelines link verified")

        with self.page.context.expect_page() as new_page_info:
            security_link.click()
        new_page = new_page_info.value
        new_page.wait_for_selector(
            "//h3[text()='Limitations']", state="visible", timeout=100000
        )
        new_page.bring_to_front()

        new_url = new_page.url
        logging.info(f"New page URL: {new_url}")
        expected_path = "https://docs.nvidia.com/cloud-functions/user-guide/latest/cloud-function/function-creation.html#limitations"
        if expected_path not in new_url:
            error_message.append(
                f"Expected URL path '{expected_path}' not found in '{new_url}'"
            )
            check_flag = False
        logging.info(f"✓ Expected URL path '{expected_path}' found in '{new_url}'")

        self.page.bring_to_front()

        # Check Okay button
        okay_button = self.page.locator(
            'xpath=//div[@data-testid="kui-modal-action"]//button[@data-testid="kui-button"]'
        )
        if not okay_button.is_visible():
            error_message.append("Okay button not visible")
            check_flag = False
        logging.info("✓ Okay button verified")
        okay_button.click()

        return check_flag, error_message, real_message

    def check_the_view_validation_issues_in_create_deployment_page(self):
        check_flag = True
        error_message = []
        self.page.locator("//button[text()=' View Validation Issues']").click()

        dialog_title = self.page.locator(
            'xpath=//div[@data-testid="kui-modal-title" and contains(text(), "View Validation Issues")]'
        )
        if not dialog_title.is_visible():
            error_message.append("Dialog title not visible")
            check_flag = False
        logging.info("✓ View Validation Issues dialog verified")

        # Check issues count
        issues_count = self.page.locator('xpath=//p[contains(text(), "Issues (")]')
        if not issues_count.is_visible():
            error_message.append("Issues count not visible")
            check_flag = False
        logging.info("✓ Issues count verified")

        # Check last updated timestamp
        timestamp = self.page.locator('xpath=//p[contains(text(), "Last updated:")]')
        if not timestamp.is_visible():
            error_message.append("Timestamp not visible")
            check_flag = False
        logging.info(f"✓ Last updated timestamp: {timestamp.text_content()}")

        # Click copy button for the issue
        copy_button = self.page.locator(
            'xpath=//button[@data-testid="kui-button"]//*[local-name()="svg" and @data-icon-name="common-copy-generic"]'
        ).last
        if not copy_button.is_visible():
            error_message.append("Copy button not visible")
            check_flag = False
        copy_button.click()
        logging.info("✓ Copy button clicked")

        # Check navigation buttons
        prev_button = self.page.locator(
            "xpath=//button[@data-testid='kui-button']//*[local-name()='svg' and @data-icon-name='shapes-chevron-left']"
        ).last
        next_button = self.page.locator(
            "xpath=//button[@data-testid='kui-button']//*[local-name()='svg' and @data-icon-name='shapes-chevron-right']"
        ).last
        page_number = self.page.locator(
            "xpath=//button[@data-testid='kui-button' and text()='1']"
        ).last

        if not prev_button.is_visible():
            error_message.append("Previous button not found")
            check_flag = False
        if not next_button.is_visible():
            error_message.append("Next button not found")
            check_flag = False
        if not page_number.is_visible():
            error_message.append("Page number button not found")
            check_flag = False
        logging.info("✓ Navigation buttons verified")

        # Click Helm Chart Security Guidelines link
        security_link = self.page.locator(
            'xpath=//a[contains(text(), "Helm Chart Security Guidelines")]'
        ).last
        if not security_link.is_visible():
            error_message.append("Security guidelines link not visible")
            check_flag = False

        # Click the link and wait for new page
        with self.page.context.expect_page() as new_page_info:
            security_link.click()
        new_page = new_page_info.value

        # Wait for the new page to load
        new_page.wait_for_selector(
            "//h3[text()='Limitations']", state="visible", timeout=100000
        )
        new_page.bring_to_front()

        # Verify the URL
        new_url = new_page.url
        logging.info(f"✓ New page URL: {new_url}")
        expected_path = "https://docs.nvidia.com/cloud-functions/user-guide/latest/cloud-function/function-creation.html#limitations"
        if expected_path not in new_url:
            error_message.append(
                f"Expected URL path '{expected_path}' not found in '{new_url}'"
            )
            check_flag = False
        logging.info("✓ Security guidelines page verified")

        # Close the new page and return to original page
        new_page.close()
        self.page.bring_to_front()

        # Verify we're back on the original dialog
        self.page.wait_for_selector(
            'xpath=//div[@role="dialog" and @data-testid="kui-modal-container"]',
            timeout=5000,
        )
        logging.info("✓ Returned to original dialog")

        # Step 9: Click Okay button to close dialog
        logging.info("Step 9: Clicking Okay button...")
        okay_button = self.page.locator(
            'xpath=//div[@data-testid="kui-modal-action"]//button[@data-testid="kui-button"]'
        )
        if not okay_button.is_visible():
            error_message.append("Okay button not visible")
            check_flag = False
        okay_button.click()
        logging.info("✓ Okay button clicked")

        logging.info("🎉 All validation issues workflow tests passed!")
        return check_flag, error_message
