import logging

from playwright.sync_api import Page
from cloudia.ui.base_page import BasePage

from component.deployments_list_page_components import (
    DeploymentsListPageMisc,
    DeploymentsListPageSearchBar,
    DeploymentsListPagePaginationTable,
)

from config.consts import (
    SIDEBAR_APP_CLOUDFUNCTIONS,
    SIDEBAR_CLOUDFUNCTIONS_ENTRY_DEPLOYMENTS,
)


class DeploymentsListPage(BasePage):
    """Contains all the elements and functionalities of the Deployment Page

    parameters:
    -----------
    page: `playwright.sync_api.page.Page`
        The page object of the Playwright.
    """

    def __init__(
        self,
        page: Page,
    ):
        super().__init__(
            page, SIDEBAR_APP_CLOUDFUNCTIONS, SIDEBAR_CLOUDFUNCTIONS_ENTRY_DEPLOYMENTS
        )
        self.DeploymentsListPageMisc = DeploymentsListPageMisc(page)
        self.DeploymentsListPageSearchBar = DeploymentsListPageSearchBar(page)
        self.DeploymentsListPagePaginationTable = DeploymentsListPagePaginationTable(page)

    def wait_deployment_to_expected_status(
        self, func_vers_id: str, expected_status: str, time_out: int = 1800
    ):
        """Wait the deployment to expected status

        parameters:
        -----------
        func_vers_id (Required): The function version ID
        expected_status (Required): The expected status to wait
        time_out (Optional): the instance filter regions list
        """
        self.navigate_to_page()

        try:
            self.DeploymentsListPageSearchBar.search(func_vers_id)
        except Exception as e:
            print(e)

        while time_out > 0:
            deployments_list: list = (
                self.DeploymentsListPagePaginationTable.get_current_page_data()
            )

            if not deployments_list or len(deployments_list) == 0:
                assert False, f"The deployment with function version ID {func_vers_id} is not listed in the deployments list"

            for deployment_entry in deployments_list:
                if deployment_entry["VersionID"] == func_vers_id:
                    current_status: str = deployment_entry["Status"]
                    if current_status.lower() == expected_status.lower():
                        logging.info(
                            f"The deployment with function version ID {func_vers_id} already in status {current_status}, waiting over."
                        )
                        return True
                    elif current_status == "ERROR":
                        logging.info(
                            f"The deployment with function version ID {func_vers_id} failed with ERROR status, failed."
                        )
                        return False
                    else:
                        logging.info(
                            f"Current status with function version ID {func_vers_id} is {current_status}."
                        )
            self.page.locator(self.DeploymentsListPageMisc.elements["RefreshBtn"]).click()
            logging.info(
                f"Waiting for another 20 seconds, left time is {time_out} seconds."
            )
            self.page.wait_for_timeout(20000)
            time_out -= 20
        logging.info(f"Waiting time over, failing with function version ID {func_vers_id}")
        return False

    def check_no_unexpected_tab_in_page(self):
        """Check there is no expected tabs in the page

        parameters:
        -----------
        func_vers_id (Required): The function version ID
        expected_status (Required): The expected status to wait
        time_out (Optional): the instance filter regions list
        """
        self.navigate_to_page()
        self.page.locator(self.DeploymentsListPageMisc.elements["RefreshBtn"]).wait_for()
        return (
            self.page.locator(
                self.DeploymentsListPageMisc.elements["MyFunctionsBtn"]
            ).count()
            == 0
            and self.page.locator(
                self.DeploymentsListPageMisc.elements["SharedFunctionsBtn"]
            ).count()
            == 0
            and self.page.locator(
                self.DeploymentsListPageMisc.elements["CreateByNVIDIABtn"]
            ).count()
            == 0
        )
