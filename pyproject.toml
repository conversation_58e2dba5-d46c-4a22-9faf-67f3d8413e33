[tool.poetry]
name = "nvcf-ui-test"
version = "0.1.0"
description = "For NVCF UI Testing based on Cloudia"
authors = ["<PERSON> <ch<PERSON><PERSON>@nvidia.com>"]
package-mode = false

[tool.poetry.dependencies]
python = "^3.10"
poetry = "^1.8.3"
cloudia = {git = "https://gitlab-master.nvidia.com/cloud-service-qa/cloudia/cloudia.git"}
cryptography = "^43.0.0"
Jinja2 = "^3.1.2"
opencv-python = "^*********"
scikit-image = "^0.25.0"
pytest-vscodedebug = "^0.1.0"
colorama = "^0.4.6"
tabulate = "^0.9.0"
allure-pytest = "^2.14.2"
pyautogui = "^0.9.54"
pytest-repeat = "^0.9.4"

[tool.poetry.dev-dependencies]
pre-commit = "^3.8.0"

[tool.pytest.ini_options]
testpaths = "testcases"
addopts = "-v --color=yes --capture=tee-sys"
python_files = "test_*.py"
python_classes = "Test*"
cache_dir = ".pytest_cache"
render_collapsed = "all"
markers = [
    "P0: For P0 level test cases",
    "P1: For P1 level test cases",
    "P2: For P2 level test cases",
    "Sanity: For Sanity test cases",
    "canary: For Canary test cases",
    "prod: For the case will run full test on Production Service",
    "prod_sanity: For the case will run sanity test on Production Service",
    "CloudFunctions: For Cloud Functions test cases",
    "nvct: For nvct ui cases running on prod and canary",
    "edgecase: For edgecase auoted in the ORG that owned by fleetcommandnv+auto+owner",
    "streaming_test: For streaming test cases",
    "streaming_invoke: For streaming invoke test cases",
    "nvcf_worker_lls_dev: For nvcf worker lls dev test cases",
]
log_level = "WARNING"
log_cli = "1"
log_cli_level = "INFO"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"
log_cli_format = "%(asctime)s | %(levelname)s | %(message)s"
log_file = "reports/pytest.log"
log_file_level = "DEBUG"
log_file_date_format = "%Y-%m-%d %H:%M:%S"
log_file_format = "%(asctime)s | %(levelname)s | %(filename)s:%(lineno)s | %(funcName)s | %(message)s"
junit_logging = "all"
junit_family = "xunit2"
junit_log_passing_tests = "true"

filterwarnings = [
    "ignore::_pytest.warning_types.PytestUnknownMarkWarning"
]
timeout = "2400"
timeout_method = "signal"


[tool.ruff]
line-length = 92
indent-width = 4

# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    ".vscode/",
    "cookies",
    "data",
    "log",
    "reports",
    "snippetdata",
]

# Assume Python 3.8
target-version = "py38"

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
select = ["E4", "E7", "E9", "F"]
ignore = []

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"
