# This file is a template, and might need editing before it works on your project.
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages

variables:
  GIT_SUBMODULE_STRATEGY: recursive
  CC_LIST: $CC_LIST
  RECIPIENTS: $RECIPIENTS

stages:
  - Pre-commit Hooks
  - Build
  - Test

default:
  tags:
    - type/docker

pre-commit:
  stage: Pre-commit Hooks
  image:
    name: python:3.10
  variables:
    PRE_COMMIT_HOME: ${CI_PROJECT_DIR}/.cache/pre-commit
  cache:
    key: pre-commit
    paths:
      - ${PRE_COMMIT_HOME}
  script:
    - pip install -q --upgrade pip
    - pip install -q pre-commit
    - pre-commit install
    - pre-commit run --all-files
  only:
    - merge_requests

copy_config:
  stage: Build
  variables:
    NGC_UI_CONFIG_FILE: ${CI_PROJECT_DIR}/ngc_ui.config
    NGC_UI_PROD_CONFIG_FILE: ${CI_PROJECT_DIR}/ngc_prod_ui.config
  script:
    - if [ -f ${NGC_UI_CONFIG_FILE} ]; then rm ${NGC_UI_CONFIG_FILE}; fi
    - wget $CONFIG_URL -O ${NGC_UI_CONFIG_FILE}
    - if [ -f ${NGC_UI_PROD_CONFIG_FILE} ]; then rm ${NGC_UI_PROD_CONFIG_FILE}; fi
    - wget ${PROD_CONFIG_URL} -O ${NGC_UI_PROD_CONFIG_FILE}
  cache:
    key: config-cache
    unprotect: true
    paths:
      - ${NGC_UI_CONFIG_FILE}
      - ${NGC_UI_PROD_CONFIG_FILE}
  only:
    - web
    - schedules

pytest_collect_test:
  stage: Test
  image:
    name: mcr.microsoft.com/playwright/python:jammy
  before_script:
    - pip --version
    - pip install -q --upgrade pip
    - pip install poetry
    - poetry install
    - poetry run playwright install-deps
    - poetry run playwright install
  script:
    - poetry run pytest --collect-only
  only:
    - merge_requests

smoke_test:
  stage: Test
  image:
    name: mcr.microsoft.com/playwright/python:jammy
  variables:
    RUNTIME_CONFIG: ${CI_PROJECT_DIR}/ngc_ui.config
  before_script:
    - pip --version
    - pip install -q --upgrade pip
    - pip install poetry
    - poetry install
    - poetry run playwright install-deps
    - poetry run playwright install
  dependencies:
    - copy_config
  script:
    - poetry run pytest --capture=tee-sys
      --junit-xml=./reports/smoke_test.xml
      --html=./reports/smoke_test.html --self-contained-html
      -m Smoke
  only:
    - schedules
    - web
  cache:
    key: config-cache
    unprotect: true
    paths:
      - ${RUNTIME_CONFIG}
  artifacts:
    name: "smoke-test-report"
    when: always
    paths:
      - reports/smoke_test.html
      - reports/smoke_test.xml
      - log/*
    reports:
      junit: reports/smoke_test.xml

prod_tests:
  stage: Test
  image:
    name: mcr.microsoft.com/playwright/python:jammy
  variables:
    RUNTIME_CONFIG: ${CI_PROJECT_DIR}/ngc_prod_ui.config
  before_script:
    - pip --version
    - pip install -q --upgrade pip
    - pip install poetry
    - poetry install
    - poetry run playwright install-deps
    - poetry run playwright install
  dependencies:
    - copy_config
  script:
    - poetry run pytest --capture=tee-sys
      --junit-xml=./reports/prod_report.xml
      --html=./reports/prod_report.html
      --self-contained-html
      -m prod
  only:
    - schedules
    - web
  cache:
    key: config-cache
    unprotect: true
    paths:
      - ${RUNTIME_CONFIG}
  artifacts:
    name: "prod-test-report"
    when: always
    paths:
      - reports/prod_report.html
      - reports/prod_report.xml
      - log/*
    reports:
      junit: reports/prod_report.xml
